{
  "files.autoSave": "afterDelay",
  "files.eol": "\n",
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  "javascript.suggestionActions.enabled": true,
  "typescript.suggestionActions.enabled": true,
  "javascript.updateImportsOnFileMove.enabled": "always",
  "editor.detectIndentation": false,
  "eslint.validate": [
    "javascript"
  ],
  "launch": {
    "configurations": [],
    "compounds": []
  },
  "editor.wrappingIndent": "deepIndent",
  // Prevent double-formatting and potential ESLint <> Prettier conflicts (ESLint formatting will be used instead)
  "javascript.format.semicolons": "insert",
  "editor.tabSize": 2,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "explorer.confirmDragAndDrop": false,
  // Run formatter when you save code changes
  "editor.formatOnSave": true,
  // Disable default formatting (ESLint formatting will be used instead)
  "[javascript]": {
    "editor.formatOnSave": false
  },
  "[javascriptreact]": {
    "editor.formatOnSave": false
  },
  // Auto-fix issues with ESLint when you save code changes
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.addMissingImports": "explicit",
    "source.organizeImports": "explicit"
  },
  // Prevent double-formatting and potential ESLint <> Prettier conflicts (ESLint formatting will be used instead)
  "prettier.disableLanguages": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
  ],
  "explorer.confirmDelete": false,
  "eslint.alwaysShowStatus": true,
  "eslint.format.enable": true,
  "[typescriptreact]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[typescript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "git.ignoreMissingGitWarning": true,
  "gitlens.advanced.messages": {
    "suppressGitMissingWarning": true
  },
  "terminal.integrated.defaultProfile.windows": "Ubuntu (WSL)",
  "git.autofetch": true,
  "git.confirmSync": false,
  "remote.SSH.configFile": "~/.ssh/config",
  "typescript.preferences.importModuleSpecifier": "relative",
  "security.workspace.trust.untrustedFiles": "open",
  "liveServer.settings.donotVerifyTags": true,
  "liveServer.settings.donotShowInfoMsg": true,
  "workbench.settings.openDefaultSettings": true,
  "eslint.workingDirectories": [
    {
      "mode": "auto"
    }
  ],
  "window.customTitleBarVisibility": "auto",
  "cSpell.words": [
    "datasource",
    "dtos",
    "Incoterms"
  ],
}
