---
to: "src/components/<%= h.fileName(name) %>/request/<%= h.updateStatusDtoFileName(name) %>.ts"
unless_exists: true
skip_if: <%= !blocks.includes('UpdateStatusDto') %>
---
<%

 ClassName = h.ClassName(name);
 UpdateStatusDtoName = h.UpdateStatusDtoName(name);

%>import { ApiProperty } from '@nestjs/swagger';
import { BaseDto } from 'src/core/dto/base.dto';

export class <%= UpdateStatusDtoName %> extends BaseDto {
  @ApiProperty()
  ids: number[];
}
