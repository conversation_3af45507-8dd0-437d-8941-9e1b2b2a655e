---
to: "src/components/<%= h.fileName(name) %>/request/<%= h.updateDtoFileName(name) %>.ts"
unless_exists: true
skip_if: <%= !blocks.includes('UpdateDto') %>
---
<%
CreateDtoName = h.CreateDtoName(name);
UpdateDtoName = h.UpdateDtoName(name);

UpdateDtoName = h.UpdateDtoName(name);
createDtoFileName = h.createDtoFileName(name);
%>import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt } from 'class-validator';
import { <%= CreateDtoName %> } from './<%= createDtoFileName %>';

export class <%= UpdateDtoName %> extends <%= CreateDtoName %> {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;
}
