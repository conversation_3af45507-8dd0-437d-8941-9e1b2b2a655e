---
to: "src/components/<%= h.fileName(name) %>/response/<%= h.getListResponseDtoFileName(name) %>.ts"
unless_exists: true
skip_if: <%= !blocks.includes('GetListResponseDto') %>
---
<%

 ClassName = h.ClassName(name);
 GetListResponseDtoName = h.GetListResponseDtoName(name);

%>import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseCommonResponseDto } from '../../../common/dtos/response/base.common.dto';

export class <%= GetListResponseDtoName %> extends BaseCommonResponseDto {
  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty()
  @Expose()
  status: number;
}
