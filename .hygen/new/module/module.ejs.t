---
to: "src/components/<%= h.fileName(name) %>/<%= h.moduleFileName(name) %>.ts"
unless_exists: true
skip_if: <%= !blocks.includes('Module') %>
---
<%
  ModuleName = h.ModuleName(name);

  ControllerName = h.ControllerName(name);
  controllerFileName = h.controllerFileName(name);

  RepositoryName = h.RepositoryName(name);
  repositoryFileName = h.repositoryFileName(name);

  ServiceName = h.ServiceName(name);
  serviceFileName = h.serviceFileName(name);

  EntityName = h.EntityName(name);
  entityName = h.changeCase.camel(EntityName);
  entityFileName = h.entityFileName(name);
%>import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { <%= ControllerName %> } from './controller/<%= controllerFileName %>';
import { <%= EntityName %> } from './entities/<%= entityFileName %>';
import { <%= RepositoryName %> } from './repository/<%= repositoryFileName %>';
import { <%= ServiceName %> } from './service/<%= serviceFileName %>';

@Module({
  imports: [TypeOrmModule.forFeature([<%= EntityName %>]), AnotherServiceModule],
  providers: [<%= ServiceName %>, <%= RepositoryName %>],
  exports: [<%= ServiceName %>],
  controllers: [<%= ControllerName %>],
})
export class <%= ModuleName %> {}
