---
to: "src/components/<%= h.fileName(name) %>/service/<%= h.serviceFileName(name) %>.ts"
unless_exists: true
skip_if: <%= !blocks.includes('Service') %>
---
<%

 ClassName = h.ClassName(name);
 fieldName = h.changeCase.camel(ClassName);

 DtoName = h.DtoName(name);
 dtoFileName = h.dtoFileName(name);

 EntityName = h.EntityName(name);
 entityName = h.changeCase.camel(EntityName);
 entityFileName = h.entityFileName(name);

 ServiceName = h.ServiceName(name);
 serviceInterfaceFileName = h.serviceInterfaceFileName(name);
  repositoryInterfaceFileName = h.repositoryInterfaceFileName(name);

 UpdateDtoName = h.UpdateDtoName(name);
 updateDtoFileName = h.updateDtoFileName(name);
 updateDtoName = h.changeCase.camel(UpdateDtoName);

 fileName = h.fileName(name);

 RepositoryName = h.RepositoryName(name);
 repositoryName = h.changeCase.camel(RepositoryName);
 RepositoryNameInterface = h.RepositoryNameInterface(name);
 repositoryFileName = h.repositoryFileName(name);

 createFunctionName = 'create' + ClassName;
 updateFunctionName = 'update' + ClassName;
 deleteFunctionName = 'delete' + ClassName;
 getAllFunctionName = 'getList' + ClassName;
 getSingleFunctionName = 'getDetail' + ClassName;
 controllerName = moduleName + 'Controller';
 serviceName = moduleName + 'Service';
 CreateDtoName = h.CreateDtoName(name);
 createDtoFileName = h.createDtoFileName(name);

  ServiceNameInterface = h.ServiceNameInterface(name);

 GetDetailResponseDtoName = h.GetDetailResponseDtoName(name);
 GetListResponseDtoName = h.GetListResponseDtoName(name);

 getListResponseDtoFileName = h.getListResponseDtoFileName(name);
  getDetailResponseDtoFileName = h.getDetailResponseDtoFileName(name);

  name = h.Name(name);
%>import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';
import { DataSource } from 'typeorm';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { <%= CreateDtoName %> } from './../request/<%= createDtoFileName %>';
import { <%= DeleteDtoName %> } from './../request/<%= deleteDtoFileName %>';
import { <%= GetDetailDtoName %> } from './../request/<%= getDetailDtoFileName %>';
import { <%= GetListDtoName %> } from './../request/<%= getListDtoFileName %>';
import { <%= UpdateDtoName %> } from './../request/<%= updateDtoFileName %>';
import { <%= UpdateStatusDtoName %> } from './../request/<%= updateStatusDtoFileName %>';
import { <%= EntityName %> } from '../entities/<%= entityFileName %>';

import { <%= GetListResponseDtoName%> } from './../response/<%= getListResponseDtoFileName %>';
import { <%= GetDetailResponseDtoName%> } from './../response/<%= getDetailResponseDtoFileName %>';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { BaseService } from '../../../common/service/base.service';
import { UserService } from '../../another-service/services/user-service';
import { <%= RepositoryName %> } from '../repository/<%= fileName %>.repository';

@Injectable()
export class <%= ServiceName %> extends BaseService {
  private readonly logger = new Logger(<%= ServiceName %>.name);

  constructor(
    private readonly <%= repositoryName %>: <%= RepositoryName %>,

    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,
  ) {
    super(userService);
  }

  async create(request: <%= CreateDtoName %>): Promise<any> {
    const existCode = await this.<%= repositoryName %>.findOneByCode(request.code);
    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    const <%= entityName %> = this.<%= repositoryName %>.createEntity(request);
    const <%= name %> = await this.<%= repositoryName %>.create(<%= entityName %>);

    return new ResponseBuilder(<%= name %>)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: <%= GetListDtoName %>): Promise<any> {
    const { page } = request;
    const { data, count } = await this.<%= repositoryName %>.getList(request);

    const dataMapUser = await this.mapUserInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: <%= GetDetailDtoName %>): Promise<any> {
    const <%= name %> = await this.<%= repositoryName %>.getDetail(request);
    if (isEmpty(<%= name %>)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMapUser = await this.mapUserInfoToResponse([<%= name %>]);

    return new ResponseBuilder(dataMapUser ? dataMapUser[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: <%= UpdateDtoName %>): Promise<any> {
    const { id } = request;
    const <%= name %> = await this.<%= repositoryName %>.findOneById(id);

    if (isEmpty(<%= name %>)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate Code unique
    const existCode = await this.<%= repositoryName %>.findOneByCode(request.code);
    if (!isEmpty(existCode) && existCode.id !== <%= name %>.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    const dataUpdate = this.<%= repositoryName %>.updateEntity(request, <%= name %>);
    const data = await this.<%= repositoryName %>.update(dataUpdate);

    return new ResponseBuilder(data)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: <%= DeleteDtoName %>): Promise<any> {
    const { id } = request;
    const <%= name %> = await this.<%= repositoryName %>.findOneById(id);

    if (isEmpty(<%= name %>)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.<%= repositoryName %>.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(request: <%= UpdateStatusDtoName %>): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.<%= repositoryName %>.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.<%= repositoryName %>.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(request: <%= UpdateStatusDtoName %>): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.<%= repositoryName %>.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.<%= repositoryName %>.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async findOneById(id: number): Promise<<%= EntityName %>> {
    return await this.<%= repositoryName %>.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<<%= EntityName %>[]> {
    return await this.<%= repositoryName %>.findAllByIds(ids);
  }
}
