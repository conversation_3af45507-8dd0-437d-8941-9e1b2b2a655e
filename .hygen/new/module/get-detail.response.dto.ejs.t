---
to: "src/components/<%= h.fileName(name) %>/response/<%= h.getDetailResponseDtoFileName(name) %>.ts"
unless_exists: true
skip_if: <%= !blocks.includes('GetDetailResponseDto') %>
---
<%

 ClassName = h.ClassName(name);
 GetDetailResponseDtoName = h.GetDetailResponseDtoName(name);

%>import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseCommonResponseDto } from '../../../common/dtos/response/base.common.dto';

export class <%= GetDetailResponseDtoName %> extends BaseCommonResponseDto {
  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty()
  @Expose()
  status: number;
}
