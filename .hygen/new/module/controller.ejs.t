---
to: "src/components/<%= h.fileName(name) %>/controller/<%= h.controllerFileName(name) %>.ts"
unless_exists: true
skip_if: <%= !blocks.includes('Controller') %>
---
<%

 EntityName = h.EntityName(name);
 entityName = h.changeCase.camel(EntityName);

 ClassName = h.ClassName(name);
 TableName = h.TableName(name);
 paramIdName = 'id';
 paramIdFieldName = h.changeCase.camel(ClassName) + 'Id';
 camelName = h.changeCase.camel(TableName)

 moduleName = h.moduleName(name);
 fileName = h.fileName(name);
 capitalizeName = h.inflection.capitalize(fileName + 's')
 ControllerName = h.ControllerName(name);
 ServiceName = h.ServiceName(name);
 ServiceNameInterface = h.ServiceNameInterface(name);
 serviceName = h.changeCase.camel(ServiceName);
 createFunctionName = 'create';
 updateFunctionName = 'update';
 activeFunctionName = 'active';
 inactiveFunctionName = 'inactive';
 deleteFunctionName = 'delete';
 getAllFunctionName = 'getList';
 getDetailFunctionName = 'getDetail';
 CreateDtoName = h.CreateDtoName(name);
 createDtoName = h.changeCase.camel(CreateDtoName);
 UpdateDtoName = h.UpdateDtoName(name);
 updateDtoName = h.changeCase.camel(UpdateDtoName);
 UpdateStatusDtoName = h.UpdateStatusDtoName(name);
 updateStatusDtoName = h.changeCase.camel(UpdateStatusDtoName);
 DtoName = h.DtoName(name);
 createDtoFileName = h.createDtoFileName(name);
 dtoFileName = h.dtoFileName(name);

  serviceFileName = h.serviceFileName(name);
  serviceInterfaceFileName = h.serviceInterfaceFileName(name);

  GetListDtoName = h.GetListDtoName(name);
  GetDetailDtoName = h.GetDetailDtoName(name);
  CreateDtoName = h.CreateDtoName(name);
  UpdateDtoName = h.UpdateDtoName(name);
  UpdateStatusDtoName = h.UpdateStatusDtoName(name);
  DeleteDtoName = h.DeleteDtoName(name);

  getListDtoFileName = h.getListDtoFileName(name);
  getDetailDtoFileName = h.getDetailDtoFileName(name);
  createDtoFileName = h.createDtoFileName(name);
  updateDtoFileName = h.updateDtoFileName(name);
  updateStatusDtoFileName = h.updateStatusDtoFileName(name);
  deleteDtoFileName = h.deleteDtoFileName(name);
%>import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { <%= CreateDtoName %> } from '../request/<%= createDtoFileName %>';
import { <%= DeleteDtoName %> } from '../request/<%= deleteDtoFileName %>';
import { <%= GetDetailDtoName %> } from '../request/<%= getDetailDtoFileName %>';
import { <%= GetListDtoName %> } from '../request/<%= getListDtoFileName %>';
import { <%= UpdateDtoName %> } from '../request/<%= updateDtoFileName %>';
import { <%= UpdateStatusDtoName %> } from '../request/<%= updateStatusDtoFileName %>';
import { <%= ServiceName %> } from '../service/<%= fileName %>.service';

@Controller('<%= h.inflection.dasherize(fileName).toLowerCase() %>s')
export class <%= ControllerName %> {
  constructor(private readonly <%= serviceName %>: <%= ServiceName %>) {}

  @Get('/:<%= paramIdName %>')
  @ApiOperation({
    tags: ['<%= capitalizeName %>'],
    summary: 'Chi tiết <%= capitalizeName %>',
    description: 'Chi tiết <%= capitalizeName %>',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async <%= getDetailFunctionName %>(
    @Param() param: <%= GetDetailDtoName %>,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.<%= serviceName %>.getDetail(request);
  }

  @Get('/list')
  @ApiOperation({
    tags: ['<%= capitalizeName %>'],
    summary: 'Danh sách <%= capitalizeName %>',
    description: 'Danh sách <%= capitalizeName %>',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public <%= getAllFunctionName %>(@Query() query: <%= GetListDtoName %>): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.<%= serviceName %>.getList(request);
  }

  @Post('/create')
  @ApiOperation({
    tags: ['<%= capitalizeName %>'],
    summary: 'Tạo <%= capitalizeName %> mới',
    description: 'Tạo <%= capitalizeName %> mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public <%= createFunctionName %>(@Body() payload: <%= CreateDtoName %>) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.<%= serviceName %>.create(request);
  }

  @Put()
  @ApiOperation({
    tags: ['<%= capitalizeName %>'],
    summary: 'Cập nhật <%= capitalizeName %>',
    description: 'Cập nhật <%= capitalizeName %>',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async <%= updateFunctionName %>(@Body() body: <%= UpdateDtoName %>): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.<%= serviceName %>.update(request);
  }

  @Put('/active')
  @ApiOperation({
    tags: ['<%= capitalizeName %>'],
    summary: 'Cập nhật <%= capitalizeName %> Status Active',
    description: 'Cập nhật <%= capitalizeName %> Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async <%= activeFunctionName %>(
    @Body() body: <%= UpdateStatusDtoName %>,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.<%= serviceName %>.active(request);
  }

  @Put('/inactive')
  @ApiOperation({
    tags: ['<%= capitalizeName %>'],
    summary: 'Cập nhật <%= capitalizeName %> Status Inactive',
    description: 'Cập nhật <%= capitalizeName %> Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async <%= inactiveFunctionName %>(
    @Body() body: <%= UpdateStatusDtoName %>,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.<%= serviceName %>.inactive(request);
  }

  @Delete('/:<%= paramIdName %>')
  @ApiOperation({
    tags: ['<%= capitalizeName %>'],
    summary: 'Xóa <%= capitalizeName %>',
    description: 'Xóa <%= capitalizeName %>',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public <%= deleteFunctionName %>(@Param() param: <%= DeleteDtoName %>): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.<%= serviceName %>.delete(request);
  }
}
