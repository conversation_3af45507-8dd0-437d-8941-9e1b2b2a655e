---
to: "src/components/<%= h.fileName(name) %>/repository/<%= h.repositoryFileName(name) %>.ts"
unless_exists: true
skip_if: <%= !blocks.includes('Repository') %>
---
<%
  RepositoryNameInterface = h.RepositoryNameInterface(name);
  RepositoryName = h.RepositoryName(name);
  repositoryName = h.changeCase.camel(RepositoryName);
  repositoryInterfaceFileName = h.repositoryInterfaceFileName(name);

  QueryBuilderName = h.QueryBuilderName(name);
  QueryBuilderName = h.QueryBuilderName(name);

  EntityName = h.EntityName(name);
  entityName = h.changeCase.camel(EntityName);
  entityFileName = h.entityFileName(name);

  CreateDtoName = h.CreateDtoName(name);
  UpdateDtoName = h.UpdateDtoName(name);
  GetListDtoName = h.GetListDtoName(name);
  GetDetailDtoName = h.GetDetailDtoName(name);
  UpdateStatusDtoName = h.UpdateStatusDtoName(name);

  createDtoFileName = h.createDtoFileName(name);
  updateDtoFileName = h.updateDtoFileName(name);
  getListDtoFileName = h.getListDtoFileName(name);
  getDetailDtoFileName = h.getDetailDtoFileName(name);
  updateStatusDtoFileName = h.updateStatusDtoFileName(name);
%>import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { isDateString } from 'class-validator';
import { StatusEnum } from '../../../common/enums/status.enum';
import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { escapeCharForSearch } from '../../../utils/common';
import { <%= EntityName %> } from '../entities/<%= entityFileName %>';
import { <%= CreateDtoName %> } from './../request/<%= createDtoFileName %>';
import { <%= GetDetailDtoName %> } from '../request/<%= getDetailDtoFileName %>';
import { <%= GetListDtoName %> } from '../request/<%= getListDtoFileName %>';
import { <%= UpdateStatusDtoName %> } from '../request/<%= updateStatusDtoFileName %>';
import { <%= UpdateDtoName %> } from '../request/<%= updateDtoFileName %>';

@Injectable()
export class <%= RepositoryName %> extends BaseAbstractRepository<<%=EntityName%>> {
  constructor(
    @InjectRepository(<%=EntityName%>)
    private readonly <%=repositoryName%>: Repository<<%=EntityName%>>,
  ) {
    super(<%=repositoryName%>);
  }

  createEntity(request: <%=CreateDtoName%>): <%=EntityName%> {
    const <%=entityName%> = new <%=EntityName%>();
    <%=entityName%>.code = request.code;
    <%=entityName%>.name = request.name;
    <%=entityName%>.description = request.description;
    <%=entityName%>.status = StatusEnum.ACTIVE;
    <%=entityName%>.createdBy = request.userId;

    return <%=entityName%>;
  }

  updateEntity(
    request: <%=UpdateDtoName%>,
    entity: <%=EntityName%>,
  ): <%=EntityName%> {
    entity.name = request.name;
    entity.description = request.description;
    entity.updatedBy = request.userId;

    return entity;
  }

  async getList(request: <%= GetListDtoName %>): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.<%=repositoryName%>.createQueryBuilder('<%=QueryBuilderName%>');

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("<%=QueryBuilderName%>"."code") like lower(:code) escape '\\' OR
              lower("<%=QueryBuilderName%>"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('<%=QueryBuilderName%>.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'code':
            query.andWhere(
              `lower("<%=QueryBuilderName%>"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'name':
            query.andWhere(
              `lower("<%=QueryBuilderName%>"."name") like lower(:name) escape '\\'`,
              {
                name: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'description':
            query.andWhere(
              `lower("<%=QueryBuilderName%>"."description") like lower(:description) escape '\\'`,
              {
                description: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'status':
            query.andWhere('"<%=QueryBuilderName%>"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'statuses':
            query.andWhere('"<%=QueryBuilderName%>"."status" IN (:...statuses)', {
              statuses: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'createdById':
            query.andWhere('"<%=QueryBuilderName%>"."created_by" = :createdById', {
              createdById: Number(value),
            });
            break;
          case 'createdByIds':
            query.andWhere('"<%=QueryBuilderName%>"."created_by" IN (:...createdByIds)', {
              createdByIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'createdAt':
            const createFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const createTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"<%=QueryBuilderName%>"."created_at" BETWEEN :createFrom AND :createTo`,
              {
                createFrom,
                createTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"<%=QueryBuilderName%>"."updated_at" BETWEEN :updateFrom AND :upateTo`,
              {
                updateFrom,
                upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'name':
            query.addOrderBy('"<%=QueryBuilderName%>"."name"', order);
            break;
          case 'code':
            query.addOrderBy('"<%=QueryBuilderName%>"."code"', order);
            break;
          case 'status':
            query.addOrderBy('"<%=QueryBuilderName%>"."status"', order);
            break;
          case 'description':
            query.addOrderBy('"<%=QueryBuilderName%>"."description"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"<%=QueryBuilderName%>"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"<%=QueryBuilderName%>"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"<%=QueryBuilderName%>"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('<%=QueryBuilderName%>.id', 'DESC');
    }

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const [data, count] = await query.getManyAndCount();

    return { data, count };
  }

  async getDetail(
    request: <%= GetDetailDtoName %>,
  ): Promise<<%=EntityName%>> {
    const { id } = request;

    const data = await this.<%=repositoryName%>.findOne({
      where: { id: id },
    });

    return data;
  }

  async active(request: <%= UpdateStatusDtoName %>): Promise<any> {
    const { ids } = request;
    const data = await this.<%=repositoryName%>
      .createQueryBuilder()
      .update(<%=EntityName%>)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(request: <%= UpdateStatusDtoName %>): Promise<any> {
    const { ids } = request;
    const data = await this.<%=repositoryName%>
      .createQueryBuilder()
      .update(<%=EntityName%>)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }
}
