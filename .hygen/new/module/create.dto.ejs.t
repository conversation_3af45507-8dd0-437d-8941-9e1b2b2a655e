---
to: "src/components/<%= h.fileName(name) %>/request/<%= h.createDtoFileName(name) %>.ts"
unless_exists: true
skip_if: <%= !blocks.includes('CreateDto') %>
---
<%

 ClassName = h.ClassName(name);
 CreateDtoName = h.CreateDtoName(name);

%>import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, MaxLength } from 'class-validator';

export class <%= CreateDtoName %> extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  code: string;

  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  description?: string;
}
