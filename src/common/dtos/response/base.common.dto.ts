import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { UserResponseDto } from './user.common.dto';

export class BaseCommonResponseDto {
  @ApiProperty()
  @Expose()
  id: number;

  @ApiProperty({ type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  createdBy: UserResponseDto;

  @ApiProperty()
  @Expose()
  createdAt: Date;

  @ApiProperty({ type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  updatedBy: UserResponseDto;

  @ApiProperty()
  @Expose()
  updatedAt: Date;
}
