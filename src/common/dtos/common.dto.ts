import { ApiProperty } from '@nestjs/swagger';
import { IsISO8601 } from 'class-validator';

export class CommonResourceDto {
  id?: string;

  dto?: any;
}

export class FilterRangeArgs {
  @ApiProperty()
  @IsISO8601()
  from: Date;
  @ApiProperty()
  @IsISO8601()
  to: Date;
}

import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber } from 'class-validator';

export enum SortArgs {
  DESC = 'desc',
  ASC = 'asc',
}

export class PaginationDto {
  @ApiProperty({ default: 10 })
  @IsNumber()
  limit = 10;

  @ApiProperty({ default: 0 })
  @IsNumber()
  page = 1;
}

export class BasePaginationDto {
  @ApiPropertyOptional({ type: () => PaginationDto })
  pagination: PaginationDto;
}
