import { plainToInstance } from 'class-transformer';
import { isEmpty, keyBy } from 'lodash';
import { UserService } from '../../components/another-service/services/user-service';

export class BaseService {
  constructor(protected readonly userService: UserService) {}

  async mapUserInfoToResponse(data: any[]): Promise<any[]> {
    const response: any[] = [];

    const ids: number[] = Array.from(
      new Set(
        data
          .flatMap((item) => [
            item.updatedBy,
            item.createdBy,
            item.approvedBy,
            item.requestedBy,
            item.inspectionBy,
            item.pic,
            item.auditorId,
            item.evaluator,
            item.appearanceInspectionBy,
            item.measurementInspectionBy,
            item.xrfInspectionBy,
            item.countermeasureCreatorId,
            item.sortingBy,
            item.sortedBy,
            item.importedBy,
            item.exportedBy,
            item.evaluationBy,
            item.resolvedBy,
            item.inspectionBy2,
            item.tcrsCreatedBy,
            ...(item.inspectionUserIds ? item.inspectionUserIds : []),
          ])
          .filter((id) => id !== null && id !== undefined),
      ),
    );

    const userList = await this.userService.getListUserByIds({ userIds: ids });
    if (isEmpty(userList)) return data;

    const userMap = keyBy(userList, 'id');
    data?.map((item) => {
      const itemResponse = plainToInstance(Object, item);

      if (userMap[item.createdBy]) {
        (itemResponse as any).createdBy = {
          id: userMap[item.createdBy]?.id,
          username: userMap[item.createdBy]?.username,
          fullName: userMap[item.createdBy]?.fullName,
          code: userMap[item.createdBy]?.code,
          department: userMap[item.createdBy]?.departmentSettings,
        };
      }

      if (userMap[item.updatedBy]) {
        (itemResponse as any).updatedBy = {
          id: userMap[item.updatedBy]?.id,
          username: userMap[item.updatedBy]?.username,
          fullName: userMap[item.updatedBy]?.fullName,
          code: userMap[item.updatedBy]?.code,
          department: userMap[item.updatedBy]?.departmentSettings,
        };
      }

      if (userMap[item.approvedBy]) {
        (itemResponse as any).approvedBy = {
          id: userMap[item.approvedBy]?.id,
          username: userMap[item.approvedBy]?.username,
          fullName: userMap[item.approvedBy]?.fullName,
          code: userMap[item.approvedBy]?.code,
          department: userMap[item.approvedBy]?.departmentSettings,
        };
      }

      if (userMap[item.requestedBy]) {
        (itemResponse as any).requestedBy = {
          id: userMap[item.requestedBy]?.id,
          username: userMap[item.requestedBy]?.username,
          fullName: userMap[item.requestedBy]?.fullName,
          code: userMap[item.requestedBy]?.code,
          department: userMap[item.requestedBy]?.departmentSettings,
        };
      }

      if (userMap[item.inspectionBy]) {
        (itemResponse as any).inspectionBy = {
          id: userMap[item.inspectionBy]?.id,
          username: userMap[item.inspectionBy]?.username,
          fullName: userMap[item.inspectionBy]?.fullName,
          code: userMap[item.inspectionBy]?.code,
          department: userMap[item.inspectionBy]?.departmentSettings,
        };
      }

      if (userMap[item.inspectionBy2]) {
        (itemResponse as any).inspectionBy2 = {
          id: userMap[item.inspectionBy2]?.id,
          username: userMap[item.inspectionBy2]?.username,
          fullName: userMap[item.inspectionBy2]?.fullName,
          code: userMap[item.inspectionBy2]?.code,
          department: userMap[item.inspectionBy2]?.departmentSettings,
        };
      }

      if (userMap[item.tcrsCreatedBy]) {
        (itemResponse as any).tcrsCreatedBy = {
          id: userMap[item.tcrsCreatedBy]?.id,
          username: userMap[item.tcrsCreatedBy]?.username,
          fullName: userMap[item.tcrsCreatedBy]?.fullName,
          code: userMap[item.tcrsCreatedBy]?.code,
          department: userMap[item.tcrsCreatedBy]?.departmentSettings,
        };
      }

      if (userMap[item.pic]) {
        (itemResponse as any).pic = {
          id: userMap[item.pic]?.id,
          username: userMap[item.pic]?.username,
          fullName: userMap[item.pic]?.fullName,
          code: userMap[item.pic]?.code,
          department: userMap[item.pic]?.departmentSettings,
        };
      }
      if (userMap[item.evaluator]) {
        (itemResponse as any).evaluator = {
          id: userMap[item.evaluator]?.id,
          username: userMap[item.evaluator]?.username,
          fullName: userMap[item.evaluator]?.fullName,
          code: userMap[item.evaluator]?.code,
          department: userMap[item.evaluator]?.departmentSettings,
        };
      }
      if (userMap[item.auditorId]) {
        (itemResponse as any).auditor = {
          id: userMap[item.auditorId]?.id,
          username: userMap[item.auditorId]?.username,
          fullName: userMap[item.auditorId]?.fullName,
          code: userMap[item.auditorId]?.code,
          department: userMap[item.auditorId]?.departmentSettings,
        };
      }
      if (userMap[item.appearanceInspectionBy]) {
        (itemResponse as any).appearanceInspectionBy = {
          id: userMap[item.appearanceInspectionBy]?.id,
          username: userMap[item.appearanceInspectionBy]?.username,
          fullName: userMap[item.appearanceInspectionBy]?.fullName,
          code: userMap[item.appearanceInspectionBy]?.code,
          department: userMap[item.appearanceInspectionBy]?.departmentSettings,
        };
      }
      if (userMap[item.measurementInspectionBy]) {
        (itemResponse as any).measurementInspectionBy = {
          id: userMap[item.measurementInspectionBy]?.id,
          username: userMap[item.measurementInspectionBy]?.username,
          fullName: userMap[item.measurementInspectionBy]?.fullName,
          code: userMap[item.measurementInspectionBy]?.code,
          department: userMap[item.measurementInspectionBy]?.departmentSettings,
        };
      }
      if (userMap[item.xrfInspectionBy]) {
        (itemResponse as any).xrfInspectionBy = {
          id: userMap[item.xrfInspectionBy]?.id,
          username: userMap[item.xrfInspectionBy]?.username,
          fullName: userMap[item.xrfInspectionBy]?.fullName,
          code: userMap[item.xrfInspectionBy]?.code,
          department: userMap[item.xrfInspectionBy]?.departmentSettings,
        };
      }
      if (userMap[item.countermeasureCreatorId]) {
        (itemResponse as any).countermeasureCreator = {
          id: userMap[item.countermeasureCreatorId]?.id,
          username: userMap[item.countermeasureCreatorId]?.username,
          fullName: userMap[item.countermeasureCreatorId]?.fullName,
          code: userMap[item.countermeasureCreatorId]?.code,
          department: userMap[item.countermeasureCreatorId]?.departmentSettings,
        };
      }
      if (userMap[item.sortingBy]) {
        (itemResponse as any).sortingBy = {
          id: userMap[item.sortingBy]?.id,
          username: userMap[item.sortingBy]?.username,
          fullName: userMap[item.sortingBy]?.fullName,
          code: userMap[item.sortingBy]?.code,
          department: userMap[item.sortingBy]?.departmentSettings,
        };
      }

      if (userMap[item.sortedBy]) {
        (itemResponse as any).sortedBy = {
          id: userMap[item.sortedBy]?.id,
          username: userMap[item.sortedBy]?.username,
          fullName: userMap[item.sortedBy]?.fullName,
          code: userMap[item.sortedBy]?.code,
          department: userMap[item.sortedBy]?.departmentSettings,
        };
      }

      if (userMap[item.importedBy]) {
        (itemResponse as any).importedBy = {
          id: userMap[item.importedBy]?.id,
          username: userMap[item.importedBy]?.username,
          fullName: userMap[item.importedBy]?.fullName,
          code: userMap[item.importedBy]?.code,
          department: userMap[item.importedBy]?.departmentSettings,
        };
      }

      if (userMap[item.exportedBy]) {
        (itemResponse as any).exportedBy = {
          id: userMap[item.exportedBy]?.id,
          username: userMap[item.exportedBy]?.username,
          fullName: userMap[item.exportedBy]?.fullName,
          code: userMap[item.exportedBy]?.code,
          department: userMap[item.exportedBy]?.departmentSettings,
        };
      }

      if (userMap[item.evaluationBy]) {
        (itemResponse as any).evaluationBy = {
          id: userMap[item.evaluationBy]?.id,
          username: userMap[item.evaluationBy]?.username,
          fullName: userMap[item.evaluationBy]?.fullName,
          code: userMap[item.evaluationBy]?.code,
          department: userMap[item.evaluationBy]?.departmentSettings,
        };
      }

      if (userMap[item.resolvedBy]) {
        (itemResponse as any).resolvedBy = {
          id: userMap[item.resolvedBy]?.id,
          username: userMap[item.resolvedBy]?.username,
          fullName: userMap[item.resolvedBy]?.fullName,
          code: userMap[item.resolvedBy]?.code,
          department: userMap[item.resolvedBy]?.departmentSettings,
        };
      }

      if (item.inspectionUserIds) {
        (itemResponse as any).inspectionUsers = item.inspectionUserIds?.map(
          (id: number) => {
            return {
              id: userMap[id]?.id,
              username: userMap[id]?.username,
              fullName: userMap[id]?.fullName,
              code: userMap[id]?.code,
              department: userMap[id]?.departmentSettings,
            };
          },
        );
      }

      response.push(itemResponse);
    });

    return response;
  }

  async mapUserInfoByCode(data: any[]): Promise<any[]> {
    const response: any[] = [];

    const codes: number[] = Array.from(
      new Set(
        data
          .flatMap((item) => [item.inspectionBy])
          .filter((code) => code !== null && code !== undefined),
      ),
    );

    const userList = await this.userService.getListUserByCodes({
      userCodes: codes,
    });
    if (isEmpty(userList)) return data;

    const userMap = keyBy(userList, 'code');
    data?.map((item) => {
      const itemResponse = plainToInstance(Object, item);
      if (userMap[item.inspectionBy]) {
        (itemResponse as any).inspectionBy = {
          id: userMap[item.inspectionBy]?.id,
          username: userMap[item.inspectionBy]?.username,
          fullName: userMap[item.inspectionBy]?.fullName,
          code: userMap[item.inspectionBy]?.code,
          department: userMap[item.inspectionBy]?.departmentSettings,
        };
      }
      response.push(itemResponse);
    });

    return response;
  }

  removeSevenHours(data: any) {
    const date = new Date(data);
    date.setHours(date.getHours() - 7);
    return date;
  }
}
