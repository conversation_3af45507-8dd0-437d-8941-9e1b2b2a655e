import { HttpException, HttpStatus } from '@nestjs/common';
import { ValidationError } from 'class-validator';
import { I18nRequestScopeService, I18nService } from 'nestjs-i18n';

export class AppException extends HttpException {
  extra?: any;
  timestamp?: Date;
  method?: string;
  path?: string;

  constructor(name: string, message: string, status: number, extra?: any) {
    super(message, status);
    this.extra = extra;
    this.name = name;
  }
}

function notFoundError(code: string, message: string, extra?: any) {
  return new AppException(code, message, HttpStatus.NOT_FOUND, extra);
}

function businessError(code: string, message: string, extra?: any) {
  return new AppException(code, message, HttpStatus.BAD_REQUEST, extra);
}

function authError(code: string, message: string, extra?: any) {
  return new AppException(code, message, HttpStatus.UNAUTHORIZED, extra);
}

function forbiddenError(code: string, message: string, extra?: any) {
  return new AppException(code, message, HttpStatus.FORBIDDEN, extra);
}

export const ErrorData = {
  Auth: {
    unauthenticated: authError('A000', 'Unauthenticated'),
  },
  Forbidden: {
    permission_denied: (input?: { message: string; extra?: any }) =>
      forbiddenError(
        'F000',
        input?.message || 'permission denied',
        input?.extra,
      ),
  },
  Business: {
    purchaseOrderMustHaveAtLeastOneItems: businessError(
      'B001',
      'purchaseOrder must have at least one items',
    ),
  },
  NotFound: {
    resource_not_found: notFoundError('N000', 'resource not found'),
  },
  Validate: {
    userBadInput: (errors?: ValidationError[]) =>
      businessError(
        'V000',
        'User bad input',
        errors?.map((validationError: ValidationError) => {
          return {
            property: validationError.property,
            value: validationError.value,
            constraints: Object.entries(validationError.constraints || {}).map(
              (constraint) => {
                return {
                  name: constraint[0],
                  message: constraint[1],
                };
              },
            ),
          };
        }),
      ),
    fileUploadMaxSize: (i18n: I18nRequestScopeService) =>
      businessError('V016', i18n.translate('error.FILE_UPLOAD_MAX_SIZE')),
    itemDoesNotExist: (i18n: I18nService) =>
      businessError('V017', i18n.translate('error.ITEM_IS_NOT_EXISTS')),
    ticketClaimDoesNotExist: (i18n: I18nService) =>
      businessError(
        'V018',
        i18n.translate('error.TICKET_CLAIM_DOES_NOT_EXISTS'),
      ),
    vendorDoesNotExist: (i18n: I18nService) =>
      businessError('V018', i18n.translate('error.VENDOR_DOES_NOT_EXIST')),
    claimUnitPriceDoesNotExist: (i18n: I18nService) =>
      businessError(
        'V018',
        i18n.translate('error.CLAIM_UNIT_PRICE_DOES_NOT_EXISTS'),
      ),
    claimUnitPriceExistByClaimPeriod: (
      i18n: I18nService,
      claimPeriod: string,
    ) =>
      businessError(
        'V018',
        i18n.translate('error.CLAIM_UNIT_PRICE_EXISTS_BY_CLAIM_PERIOD', {
          args: { claimPeriod: claimPeriod },
        }),
      ),
    claimUnitPriceExistByDateRange: (
      i18n: I18nService,
      fromDate: Date,
      toDate: Date,
    ) =>
      businessError(
        'V018',
        i18n.translate('error.CLAIM_UNIT_PRICE_EXISTS_BY_DATE_RANGE', {
          args: { fromDate: fromDate, toDate: toDate },
        }),
      ),
    productionProcesAlreadyExistItem: (i18n: I18nService, itemCodes: string) =>
      businessError(
        'V019',
        i18n.translate('error.PRODUCTION_PROCESS_ALREADY_EXISTS_ITEM', {
          args: { itemCodes: itemCodes },
        }),
      ),
    canNotCreateUpdateTicketClaimInDate: (i18n: I18nService) =>
      businessError(
        'V018',
        i18n.translate('error.CAN_NOT_CREATE_UPDATE_TICKET_CLAIM_IN_DATE'),
      ),

    claimUnitPriceDoesNotExistByClaimPeriod: (
      i18n: I18nService,
      claimPeriod: string,
    ) =>
      businessError(
        'V018',
        i18n.translate(
          'error.CLAIM_UNIT_PRICE_DOES_NOT_EXISTS_BY_CLAIM_PERIOD',
          {
            args: { claimPeriod: claimPeriod },
          },
        ),
      ),

    claimFeeTicketExistByClaimPeriod: (
      i18n: I18nService,
      claimPeriod: string,
    ) =>
      businessError(
        'V018',
        i18n.translate('error.CLAIM_FEE_TICKET_EXISTS_BY_CLAIM_PERIOD', {
          args: { claimPeriod: claimPeriod },
        }),
      ),
    vendorAuditPlanDoesNotExist: (i18n: I18nService) =>
      businessError(
        'V018',
        i18n.translate('error.VENDOR_AUDIT_PLAN_DOES_NOT_EXISTS'),
      ),
    fileHeaderLengthTemplateError: (i18n: I18nService) =>
      businessError(
        'V018',
        i18n.translate('error.HEADER_LENGTH_TEMPLATE_ERROR'),
      ),
    fileHeaderTemplateError: (i18n: I18nService) =>
      businessError('V018', i18n.translate('error.HEADER_TEMPLATE_ERROR')),
    fileUploadDataNotFound: (i18n: I18nService) =>
      notFoundError('N000', i18n.translate('error.NOT_FOUND')),
    fileUploadDataError: (i18n: I18nService) =>
      businessError('V018', i18n.translate('error.FILE_ERROR')),
    fileNoDataError: (i18n: I18nService) =>
      businessError('V018', i18n.translate('error.FILE_NO_DATA_ERROR')),
    fileSheetNameError: (i18n: I18nService) =>
      businessError('V018', i18n.translate('error.FILE_SHEET_NAME_ERROR')),
    fileTooLargeError: (i18n: I18nService) =>
      businessError('V018', i18n.translate('error.FILE_TOO_LARGE')),
    fileOqcTicketError: (i18n: I18nService) =>
      businessError('V018', i18n.translate('error.FILE_OQC_TICKET_ERROR')),
  },
};
