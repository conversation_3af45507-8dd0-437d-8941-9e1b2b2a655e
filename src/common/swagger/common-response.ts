import { Type, applyDecorators } from '@nestjs/common';
import { ApiOkResponse, ApiProperty, getSchemaPath } from '@nestjs/swagger';

export class Paging {
  @ApiProperty()
  total: number;
  @ApiProperty()
  page: number;
}

export class CollectionPaging<T> {
  @ApiProperty()
  items: T[];

  @ApiProperty()
  meta: Paging;
}

export interface ApiPagingInput<T> {
  type: T;
  description?: string;
}

/**
 * items: data, meta: { total, page }
 *
 * @param input
 * @param example
 * @returns
 */

export const ApiPagingResponse = <T extends Type<any>>(
  input: ApiPagingInput<T>,
  example?: any,
) => {
  return applyDecorators(
    ApiOkResponse({
      description: input.description,
      schema: {
        example: example,
        allOf: [
          { $ref: getSchemaPath(CollectionPaging) },
          {
            properties: {
              items: { $ref: getSchemaPath(input.type) },
            },
          },
        ],
      },
    }),
  );
};
