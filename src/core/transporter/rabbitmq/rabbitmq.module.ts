import { ConfigService } from '@config/config.service';
import { Module } from '@nestjs/common';
import { ClientsModule, ClientsModuleOptions } from '@nestjs/microservices';
import { QMS_QUEUE_MODULE } from './rabbitmq.constants';
import { RabbitMqService } from './rabbitmq.service';

const configService = new ConfigService();
@Module({
  imports: [
    ClientsModule.register([
      {
        name: QMS_QUEUE_MODULE,
        ...configService.get('rabbitOption'),
      },
    ] as ClientsModuleOptions),
  ],
  providers: [
    {
      provide: 'RabbitMqServiceInterface',
      useClass: RabbitMqService,
    },
  ],
  exports: [
    ClientsModule.register([
      {
        name: QMS_QUEUE_MODULE,
        ...configService.get('rabbitOption'),
      },
    ] as ClientsModuleOptions),
    {
      provide: 'RabbitMqServiceInterface',
      useClass: RabbitMqService,
    },
  ],
})
export class RabbitMqModule {}
