# RabbitMQ Demo với Retry Mechanism

## Tổng quan

Demo đơn giản về microservice communication sử dụng RabbitMQ với:
- ✅ `@MessagePattern` ở service layer (không phải controller)
- ✅ <PERSON><PERSON> chế retry tự động khi gửi lỗi
- ✅ Exponential backoff strategy
- ✅ Configurable max retries
- ✅ Failure simulation để test retry

## Cấu trúc Demo

```
simple-message.service.ts    # Service với @MessagePattern và retry logic
simple-message.controller.ts # Controller để test API
```

## Cách hoạt động

### 1. Flow gửi message với retry:

```
API Call → Service.sendEmailWithRetry() → RabbitMQ Client.send() 
    ↓ (nếu lỗi)
Retry Logic → Exponential Backoff → Retry → Success/Failure
```

### 2. Flow nhận message:

```
RabbitMQ → @MessagePattern('send-email-notification') → processEmail()
    ↓ (nếu lỗi)
Throw RpcException → Trigger retry mechanism
```

## API Endpoints

### Demo Endpoints (GET)
```bash
# Test email với retry
curl -X GET http://localhost:3001/simple-message/send-email

# Test notification với retry  
curl -X GET http://localhost:3001/simple-message/send-notification

# Test batch sending
curl -X GET http://localhost:3001/simple-message/test-batch

# Health check
curl -X GET http://localhost:3001/simple-message/health

# Info
curl -X GET http://localhost:3001/simple-message/info
```

### Custom Endpoints (POST)
```bash
# Custom email
curl -X POST http://localhost:3001/simple-message/send-email \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Test Email",
    "body": "Test message",
    "maxRetries": 5
  }'

# Custom notification
curl -X POST http://localhost:3001/simple-message/send-notification \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "title": "Test Notification",
    "content": "Test content",
    "type": "push",
    "maxRetries": 3
  }'
```

## Message Patterns

### 1. Email Notification
```typescript
@MessagePattern('send-email-notification', Transport.RMQ)
async handleEmailNotification(data: EmailMessage) {
  // Process email với 30% failure rate để test retry
}
```

### 2. General Notification
```typescript
@MessagePattern('send-notification', Transport.RMQ)
async handleNotification(data: NotificationMessage) {
  // Process notification với 25% failure rate để test retry
}
```

## Retry Strategy

### Exponential Backoff
- Retry 1: 1 second
- Retry 2: 2 seconds  
- Retry 3: 4 seconds
- Retry 4: 8 seconds
- Max delay: 30 seconds

### Configuration
```typescript
// Default: 3 retries
maxRetries: 3

// Custom retries
maxRetries: 5
```

## Logs để theo dõi

Khi chạy demo, bạn sẽ thấy logs như:

```
[SimpleMessageService] 📧 Sending email: <EMAIL> - Test Subject
[SimpleMessageService] 🚀 Attempt 1/4 for send-email-notification
[SimpleMessageService] ❌ Email processing failed: SMTP server temporarily unavailable
[SimpleMessageService] ⚠️ Attempt 1 failed, retrying in 1000ms
[SimpleMessageService] 🚀 Attempt 2/4 for send-email-notification
[SimpleMessageService] ✅ Success on attempt 2 for send-email-notification
```

## Test Scenarios

### 1. Test thành công ngay lần đầu
```bash
curl -X GET http://localhost:3001/simple-message/send-email
# Xem logs: có thể thành công ngay hoặc retry vài lần
```

### 2. Test với nhiều retries
```bash
curl -X POST http://localhost:3001/simple-message/send-email \
  -H "Content-Type: application/json" \
  -d '{"to":"<EMAIL>","subject":"Test","body":"Test","maxRetries":5}'
```

### 3. Test batch để thấy pattern
```bash
curl -X GET http://localhost:3001/simple-message/test-batch
# Gửi 8 messages, xem success rate và retry patterns
```

## Failure Simulation

- **Email**: 30% failure rate
- **Notification**: 25% failure rate

Errors được simulate:
- SMTP server temporarily unavailable
- Invalid email address format  
- Network timeout
- Rate limit exceeded
- Authentication failed

## Monitoring

### Success Response
```json
{
  "success": true,
  "message": "Email sent successfully",
  "data": {
    "success": true,
    "emailId": "email_123",
    "sentAt": "2024-01-01T10:00:00.000Z",
    "retryCount": 2
  }
}
```

### Failure Response (after all retries)
```json
{
  "success": false,
  "message": "Email failed after all retries",
  "error": "Failed after 4 attempts: SMTP server temporarily unavailable"
}
```

## Tùy chỉnh

### Thay đổi failure rate
Trong `simple-message.service.ts`:
```typescript
// Giảm failure rate để test ít retry hơn
if (Math.random() < 0.1) { // 10% thay vì 30%
```

### Thay đổi retry delay
```typescript
private calculateRetryDelay(retryCount: number): number {
  return Math.min(500 * Math.pow(2, retryCount - 1), 10000); // Nhanh hơn
}
```

## Kết luận

Demo này cho thấy:
1. **Microservice pattern** với RabbitMQ
2. **Retry mechanism** tự động
3. **Error handling** và recovery
4. **Monitoring** qua logs và responses
5. **Configurable** retry strategy

Phù hợp để áp dụng vào production với real services!
