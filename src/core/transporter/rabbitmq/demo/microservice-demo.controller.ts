import { Controller, Get, Logger, Inject } from '@nestjs/common';
import { MessagePattern, Transport, ClientProxy } from '@nestjs/microservices';
import { QMS_QUEUE_MODULE } from '../rabbitmq.constants';

// Message interfaces
export interface NotificationMessage {
  id: string;
  userId: string;
  type: 'email' | 'sms' | 'push';
  title: string;
  content: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface UserDataMessage {
  userId: string;
  action: 'create' | 'update' | 'delete';
  userData: {
    name: string;
    email: string;
    phone?: string;
    department?: string;
  };
  timestamp: string;
}

export interface OrderProcessMessage {
  orderId: string;
  customerId: string;
  items: Array<{
    productId: string;
    quantity: number;
    price: number;
  }>;
  totalAmount: number;
  status: 'pending' | 'processing' | 'completed' | 'cancelled';
  timestamp: string;
}

@Controller('microservice-demo')
export class MicroserviceDemoController {
  private readonly logger = new Logger(MicroserviceDemoController.name);

  constructor(
    @Inject(QMS_QUEUE_MODULE)
    private readonly client: ClientProxy,
  ) {}

  // ==================== SENDER SIDE (Service A) ====================
  
  @Get('send-notification')
  async sendNotification() {
    try {
      const notificationData: NotificationMessage = {
        id: `notif_${Date.now()}`,
        userId: 'user_123',
        type: 'email',
        title: 'Welcome to our platform!',
        content: 'Thank you for joining us. We are excited to have you on board.',
        timestamp: new Date().toISOString(),
        metadata: {
          source: 'registration',
          priority: 'high',
          template: 'welcome_email'
        }
      };

      this.logger.log(`Sending notification: ${JSON.stringify(notificationData)}`);
      
      // Gửi message đến service khác
      const result = await this.client.send('send-notification', notificationData).toPromise();
      
      return {
        success: true,
        message: 'Notification sent successfully',
        data: {
          sent: notificationData,
          response: result
        }
      };
    } catch (error) {
      this.logger.error(`Failed to send notification: ${error.message}`);
      return {
        success: false,
        message: 'Failed to send notification',
        error: error.message
      };
    }
  }

  @Get('send-user-data')
  async sendUserData() {
    try {
      const userData: UserDataMessage = {
        userId: 'user_456',
        action: 'update',
        userData: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+84123456789',
          department: 'Engineering'
        },
        timestamp: new Date().toISOString()
      };

      this.logger.log(`Sending user data: ${JSON.stringify(userData)}`);
      
      const result = await this.client.send('process-user-data', userData).toPromise();
      
      return {
        success: true,
        message: 'User data sent successfully',
        data: {
          sent: userData,
          response: result
        }
      };
    } catch (error) {
      this.logger.error(`Failed to send user data: ${error.message}`);
      return {
        success: false,
        message: 'Failed to send user data',
        error: error.message
      };
    }
  }

  @Get('send-order')
  async sendOrder() {
    try {
      const orderData: OrderProcessMessage = {
        orderId: `order_${Date.now()}`,
        customerId: 'customer_789',
        items: [
          {
            productId: 'prod_001',
            quantity: 2,
            price: 25.99
          },
          {
            productId: 'prod_002',
            quantity: 1,
            price: 15.50
          }
        ],
        totalAmount: 67.48,
        status: 'pending',
        timestamp: new Date().toISOString()
      };

      this.logger.log(`Sending order: ${JSON.stringify(orderData)}`);
      
      const result = await this.client.send('process-order', orderData).toPromise();
      
      return {
        success: true,
        message: 'Order sent successfully',
        data: {
          sent: orderData,
          response: result
        }
      };
    } catch (error) {
      this.logger.error(`Failed to send order: ${error.message}`);
      return {
        success: false,
        message: 'Failed to send order',
        error: error.message
      };
    }
  }

  // ==================== RECEIVER SIDE (Service B) ====================

  @MessagePattern('send-notification', Transport.RMQ)
  async handleNotification(data: NotificationMessage) {
    this.logger.log(`📧 Received notification message: ${JSON.stringify(data)}`);
    
    try {
      // Simulate notification processing
      await this.processNotification(data);
      
      this.logger.log(`✅ Notification processed successfully for user: ${data.userId}`);
      
      return {
        success: true,
        message: 'Notification processed successfully',
        processedAt: new Date().toISOString(),
        notificationId: data.id
      };
    } catch (error) {
      this.logger.error(`❌ Failed to process notification: ${error.message}`);
      throw error;
    }
  }

  @MessagePattern('process-user-data', Transport.RMQ)
  async handleUserData(data: UserDataMessage) {
    this.logger.log(`👤 Received user data message: ${JSON.stringify(data)}`);
    
    try {
      // Simulate user data processing
      await this.processUserData(data);
      
      this.logger.log(`✅ User data processed successfully for user: ${data.userId}`);
      
      return {
        success: true,
        message: 'User data processed successfully',
        processedAt: new Date().toISOString(),
        userId: data.userId,
        action: data.action
      };
    } catch (error) {
      this.logger.error(`❌ Failed to process user data: ${error.message}`);
      throw error;
    }
  }

  @MessagePattern('process-order', Transport.RMQ)
  async handleOrder(data: OrderProcessMessage) {
    this.logger.log(`🛒 Received order message: ${JSON.stringify(data)}`);
    
    try {
      // Simulate order processing
      await this.processOrder(data);
      
      this.logger.log(`✅ Order processed successfully: ${data.orderId}`);
      
      return {
        success: true,
        message: 'Order processed successfully',
        processedAt: new Date().toISOString(),
        orderId: data.orderId,
        status: 'processing',
        estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
      };
    } catch (error) {
      this.logger.error(`❌ Failed to process order: ${error.message}`);
      throw error;
    }
  }

  // ==================== PROCESSING METHODS ====================

  private async processNotification(data: NotificationMessage): Promise<void> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Simulate different processing based on notification type
    switch (data.type) {
      case 'email':
        this.logger.log(`📧 Sending email to user ${data.userId}: ${data.title}`);
        break;
      case 'sms':
        this.logger.log(`📱 Sending SMS to user ${data.userId}: ${data.content}`);
        break;
      case 'push':
        this.logger.log(`🔔 Sending push notification to user ${data.userId}: ${data.title}`);
        break;
    }
    
    // Simulate occasional failures for testing
    if (Math.random() < 0.1) {
      throw new Error('Notification service temporarily unavailable');
    }
  }

  private async processUserData(data: UserDataMessage): Promise<void> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 300));
    
    this.logger.log(`Processing ${data.action} action for user: ${data.userData.name}`);
    
    // Simulate database operations
    switch (data.action) {
      case 'create':
        this.logger.log(`Creating new user: ${data.userData.email}`);
        break;
      case 'update':
        this.logger.log(`Updating user: ${data.userData.email}`);
        break;
      case 'delete':
        this.logger.log(`Deleting user: ${data.userId}`);
        break;
    }
    
    // Simulate occasional failures
    if (Math.random() < 0.05) {
      throw new Error('Database connection error');
    }
  }

  private async processOrder(data: OrderProcessMessage): Promise<void> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 800));
    
    this.logger.log(`Processing order ${data.orderId} for customer ${data.customerId}`);
    this.logger.log(`Order contains ${data.items.length} items, total: $${data.totalAmount}`);
    
    // Simulate inventory check
    for (const item of data.items) {
      this.logger.log(`Checking inventory for product ${item.productId}: ${item.quantity} units`);
    }
    
    // Simulate payment processing
    this.logger.log(`Processing payment for $${data.totalAmount}`);
    
    // Simulate occasional failures
    if (Math.random() < 0.08) {
      throw new Error('Payment processing failed');
    }
  }

  // ==================== INFO ENDPOINTS ====================

  @Get('info')
  getInfo() {
    return {
      success: true,
      message: 'Microservice Demo API Information',
      data: {
        description: 'Demo showing microservice communication using RabbitMQ with @MessagePattern',
        senderEndpoints: {
          'GET /microservice-demo/send-notification': 'Send a notification message',
          'GET /microservice-demo/send-user-data': 'Send user data for processing',
          'GET /microservice-demo/send-order': 'Send order for processing'
        },
        messagePatterns: {
          'send-notification': 'Handles notification messages',
          'process-user-data': 'Handles user data processing',
          'process-order': 'Handles order processing'
        },
        usage: [
          '1. Call GET endpoints to send messages',
          '2. Check logs to see message processing',
          '3. Response includes both sent data and processing result'
        ]
      }
    };
  }
}
