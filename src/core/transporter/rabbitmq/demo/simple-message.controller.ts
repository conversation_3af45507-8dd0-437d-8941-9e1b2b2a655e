import { <PERSON>, Get, Post, Body, Logger } from '@nestjs/common';
import { SimpleMessageService } from './simple-message.service';

// DTOs for requests
export class SendEmailDto {
  to: string;
  subject: string;
  body: string;
  maxRetries?: number;
}

export class SendNotificationDto {
  userId: string;
  title: string;
  content: string;
  type: 'email' | 'sms' | 'push';
  maxRetries?: number;
}

@Controller('simple-message')
export class SimpleMessageController {
  private readonly logger = new Logger(SimpleMessageController.name);

  constructor(private readonly messageService: SimpleMessageService) {}

  // ==================== DEMO ENDPOINTS ====================

  @Get('send-email')
  async sendDemoEmail() {
    try {
      const result = await this.messageService.sendEmailWithRetry({
        to: '<EMAIL>',
        subject: 'Demo Email with Retry',
        body: 'This is a demo email to test RabbitMQ with retry mechanism.',
        maxRetries: 3,
      });

      return {
        success: true,
        message: 'Demo email sent successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Demo email failed: ${error.message}`);
      return {
        success: false,
        message: 'Demo email failed after all retries',
        error: error.message,
      };
    }
  }

  @Get('send-notification')
  async sendDemoNotification() {
    try {
      const result = await this.messageService.sendNotificationWithRetry({
        userId: 'demo_user_123',
        title: 'Demo Notification',
        content: 'This is a demo notification to test RabbitMQ with retry mechanism.',
        type: 'push',
        maxRetries: 3,
      });

      return {
        success: true,
        message: 'Demo notification sent successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Demo notification failed: ${error.message}`);
      return {
        success: false,
        message: 'Demo notification failed after all retries',
        error: error.message,
      };
    }
  }

  // ==================== CUSTOM ENDPOINTS ====================

  @Post('send-email')
  async sendCustomEmail(@Body() dto: SendEmailDto) {
    try {
      const result = await this.messageService.sendEmailWithRetry({
        to: dto.to,
        subject: dto.subject,
        body: dto.body,
        maxRetries: dto.maxRetries || 3,
      });

      return {
        success: true,
        message: 'Email sent successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Custom email failed: ${error.message}`);
      return {
        success: false,
        message: 'Email failed after all retries',
        error: error.message,
      };
    }
  }

  @Post('send-notification')
  async sendCustomNotification(@Body() dto: SendNotificationDto) {
    try {
      const result = await this.messageService.sendNotificationWithRetry({
        userId: dto.userId,
        title: dto.title,
        content: dto.content,
        type: dto.type,
        maxRetries: dto.maxRetries || 3,
      });

      return {
        success: true,
        message: 'Notification sent successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Custom notification failed: ${error.message}`);
      return {
        success: false,
        message: 'Notification failed after all retries',
        error: error.message,
      };
    }
  }

  // ==================== BATCH TESTING ====================

  @Get('test-batch')
  async testBatch() {
    const results = [];
    
    // Test multiple emails
    for (let i = 1; i <= 5; i++) {
      try {
        const result = await this.messageService.sendEmailWithRetry({
          to: `test${i}@example.com`,
          subject: `Batch Test Email ${i}`,
          body: `This is batch test email number ${i}`,
          maxRetries: 2,
        });
        results.push({ type: 'email', index: i, success: true, data: result });
      } catch (error) {
        results.push({ type: 'email', index: i, success: false, error: error.message });
      }
    }

    // Test multiple notifications
    for (let i = 1; i <= 3; i++) {
      try {
        const result = await this.messageService.sendNotificationWithRetry({
          userId: `batch_user_${i}`,
          title: `Batch Test Notification ${i}`,
          content: `This is batch test notification number ${i}`,
          type: 'push',
          maxRetries: 2,
        });
        results.push({ type: 'notification', index: i, success: true, data: result });
      } catch (error) {
        results.push({ type: 'notification', index: i, success: false, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return {
      success: true,
      message: 'Batch test completed',
      summary: {
        total: results.length,
        successful: successCount,
        failed: failureCount,
        successRate: `${((successCount / results.length) * 100).toFixed(1)}%`,
      },
      results,
    };
  }

  // ==================== HEALTH CHECK ====================

  @Get('health')
  async getHealth() {
    try {
      const health = await this.messageService.getServiceHealth();
      return {
        success: true,
        data: health,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Health check failed',
        error: error.message,
      };
    }
  }

  // ==================== INFO ====================

  @Get('info')
  getInfo() {
    return {
      success: true,
      message: 'Simple Message Demo with Retry Mechanism',
      data: {
        description: 'Demo RabbitMQ microservice communication with automatic retry on failure',
        features: [
          'Email notifications with retry',
          'Push notifications with retry',
          'Exponential backoff strategy',
          'Configurable max retries',
          'Automatic failure simulation for testing',
        ],
        endpoints: {
          'GET /simple-message/send-email': 'Send demo email with retry',
          'GET /simple-message/send-notification': 'Send demo notification with retry',
          'POST /simple-message/send-email': 'Send custom email with retry',
          'POST /simple-message/send-notification': 'Send custom notification with retry',
          'GET /simple-message/test-batch': 'Test batch sending with retry',
          'GET /simple-message/health': 'Service health check',
        },
        messagePatterns: {
          'send-email-notification': 'Handles email processing with retry',
          'send-notification': 'Handles notification processing with retry',
        },
        retryStrategy: {
          defaultMaxRetries: 3,
          backoffStrategy: 'exponential',
          delays: ['1s', '2s', '4s', '8s', 'max 30s'],
          failureSimulation: 'Email: 30%, Notification: 25%',
        },
        testCommands: [
          'curl -X GET http://localhost:3001/simple-message/send-email',
          'curl -X GET http://localhost:3001/simple-message/send-notification',
          'curl -X GET http://localhost:3001/simple-message/test-batch',
          'curl -X POST http://localhost:3001/simple-message/send-email -H "Content-Type: application/json" -d \'{"to":"<EMAIL>","subject":"Test","body":"Test message","maxRetries":5}\'',
        ],
        logWatching: [
          'Watch logs to see retry attempts',
          'Success/failure patterns',
          'Exponential backoff delays',
          'Final success or failure after max retries',
        ],
      },
    };
  }
}
