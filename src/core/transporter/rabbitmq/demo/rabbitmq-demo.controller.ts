import { Body, Controller, Get, Logger, Post } from '@nestjs/common';
import { MessagePattern, Transport } from '@nestjs/microservices';
import {
  DataSyncMessage,
  EmailMessage,
  RabbitMqDemoService,
  UserNotificationMessage,
} from './rabbitmq-demo.service';

// DTOs for API requests
export class SendNotificationDto {
  userId: string;
  type: 'email' | 'sms' | 'push';
  title: string;
  content: string;
  metadata?: Record<string, any>;
}

export class SendEmailDto {
  to: string;
  subject: string;
  body: string;
  attachments?: string[];
}

export class SyncDataDto {
  entityType: string;
  entityId: string;
  action: 'create' | 'update' | 'delete';
  data: Record<string, any>;
}

export class BatchNotificationDto {
  notifications: SendNotificationDto[];
}

@Controller('rabbitmq-demo')
export class RabbitMqDemoController {
  private readonly logger = new Logger(RabbitMqDemoController.name);

  constructor(private readonly demoService: RabbitMqDemoService) {}

  @Post('notification')
  @MessagePattern('send-notification', Transport.RMQ)
  async sendNotification(@Body() dto: SendNotificationDto) {
    try {
      const notification: UserNotificationMessage = {
        ...dto,
        metadata: dto.metadata || {},
      };

      const result = await this.demoService.sendUserNotification(notification);

      return {
        success: true,
        message: 'Notification sent successfully',
        data: { sent: result },
      };
    } catch (error) {
      this.logger.error(`Failed to send notification: ${error.message}`);
      return {
        success: false,
        message: 'Failed to send notification',
        error: error.message,
      };
    }
  }

  @Post('email')
  async sendEmail(@Body() dto: SendEmailDto) {
    try {
      const email: EmailMessage = {
        ...dto,
        attachments: dto.attachments || [],
      };

      const result = await this.demoService.sendEmail(email);

      return {
        success: true,
        message: 'Email queued successfully',
        data: { queued: result },
      };
    } catch (error) {
      this.logger.error(`Failed to queue email: ${error.message}`);
      return {
        success: false,
        message: 'Failed to queue email',
        error: error.message,
      };
    }
  }

  @Post('data-sync')
  async syncData(@Body() dto: SyncDataDto) {
    try {
      const syncMessage: DataSyncMessage = {
        ...dto,
        timestamp: new Date().toISOString(),
      };

      const result = await this.demoService.syncData(syncMessage);

      return {
        success: true,
        message: 'Data sync message sent successfully',
        data: { sent: result },
      };
    } catch (error) {
      this.logger.error(`Failed to send data sync message: ${error.message}`);
      return {
        success: false,
        message: 'Failed to send data sync message',
        error: error.message,
      };
    }
  }

  @Post('batch-notifications')
  async sendBatchNotifications(@Body() dto: BatchNotificationDto) {
    try {
      const notifications: UserNotificationMessage[] = dto.notifications.map(
        (n) => ({
          ...n,
          metadata: n.metadata || {},
        }),
      );

      const results = await this.demoService.sendBatchNotifications(
        notifications,
      );
      const successCount = results.filter((r) => r).length;

      return {
        success: true,
        message: `Batch notifications processed: ${successCount}/${results.length} successful`,
        data: {
          total: results.length,
          successful: successCount,
          failed: results.length - successCount,
          results,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to send batch notifications: ${error.message}`);
      return {
        success: false,
        message: 'Failed to send batch notifications',
        error: error.message,
      };
    }
  }

  @Post('consumers/setup')
  async setupConsumers() {
    try {
      const consumerTags = await Promise.all([
        this.demoService.setupUserNotificationConsumer(),
        this.demoService.setupEmailConsumer(),
        this.demoService.setupDataSyncConsumer(),
      ]);

      return {
        success: true,
        message: 'All consumers setup successfully',
        data: {
          userNotificationConsumer: consumerTags[0],
          emailConsumer: consumerTags[1],
          dataSyncConsumer: consumerTags[2],
        },
      };
    } catch (error) {
      this.logger.error(`Failed to setup consumers: ${error.message}`);
      return {
        success: false,
        message: 'Failed to setup consumers',
        error: error.message,
      };
    }
  }

  @Post('consumers/error-prone')
  async setupErrorProneConsumer() {
    try {
      const consumerTag = await this.demoService.setupErrorProneConsumer();

      return {
        success: true,
        message: 'Error-prone consumer setup successfully for testing',
        data: { consumerTag },
      };
    } catch (error) {
      this.logger.error(
        `Failed to setup error-prone consumer: ${error.message}`,
      );
      return {
        success: false,
        message: 'Failed to setup error-prone consumer',
        error: error.message,
      };
    }
  }

  @Post('test-error-handling')
  async testErrorHandling() {
    try {
      // Send test messages to error-prone queue
      const testMessages = [
        { id: 1, data: 'Test message 1' },
        { id: 2, data: 'Test message 2' },
        { id: 3, data: 'Test message 3' },
        { id: 4, data: 'Test message 4' },
        { id: 5, data: 'Test message 5' },
      ];

      const results = [];
      for (const message of testMessages) {
        try {
          // Using the base service to send to error-prone queue
          const result = await this.demoService['rabbitMqService'].addToQueue(
            'error_prone_queue',
            message,
          );
          results.push({ message, sent: result });
        } catch (error) {
          results.push({ message, sent: false, error: error.message });
        }
      }

      return {
        success: true,
        message: 'Test messages sent to error-prone queue for retry testing',
        data: { results },
      };
    } catch (error) {
      this.logger.error(`Failed to test error handling: ${error.message}`);
      return {
        success: false,
        message: 'Failed to test error handling',
        error: error.message,
      };
    }
  }

  @Get('health')
  async getHealth() {
    try {
      const health = await this.demoService.getQueueHealth();

      return {
        success: true,
        message: 'Health check completed',
        data: health,
      };
    } catch (error) {
      this.logger.error(`Health check failed: ${error.message}`);
      return {
        success: false,
        message: 'Health check failed',
        error: error.message,
      };
    }
  }

  @Get('examples')
  getExamples() {
    return {
      success: true,
      message: 'RabbitMQ Demo API Examples',
      data: {
        endpoints: {
          'POST /rabbitmq-demo/notification': {
            description: 'Send a user notification with retry mechanism',
            example: {
              userId: 'user123',
              type: 'email',
              title: 'Welcome!',
              content: 'Welcome to our platform!',
              metadata: { source: 'registration' },
            },
          },
          'POST /rabbitmq-demo/email': {
            description: 'Queue an email for sending',
            example: {
              to: '<EMAIL>',
              subject: 'Test Email',
              body: 'This is a test email',
              attachments: [],
            },
          },
          'POST /rabbitmq-demo/data-sync': {
            description: 'Send data synchronization message',
            example: {
              entityType: 'user',
              entityId: 'user123',
              action: 'update',
              data: { name: 'John Doe', email: '<EMAIL>' },
            },
          },
          'POST /rabbitmq-demo/batch-notifications': {
            description: 'Send multiple notifications in batch',
            example: {
              notifications: [
                {
                  userId: 'user1',
                  type: 'email',
                  title: 'Notification 1',
                  content: 'Content 1',
                },
                {
                  userId: 'user2',
                  type: 'sms',
                  title: 'Notification 2',
                  content: 'Content 2',
                },
              ],
            },
          },
          'POST /rabbitmq-demo/consumers/setup': {
            description: 'Setup all demo consumers',
          },
          'POST /rabbitmq-demo/test-error-handling': {
            description: 'Test retry mechanism with error-prone messages',
          },
          'GET /rabbitmq-demo/health': {
            description: 'Check RabbitMQ connection health',
          },
        },
      },
    };
  }
}
