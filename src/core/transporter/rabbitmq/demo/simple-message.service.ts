import { Injectable, Logger, Inject } from '@nestjs/common';
import { MessagePattern, Transport, ClientProxy, RpcException } from '@nestjs/microservices';
import { QMS_QUEUE_MODULE } from '../rabbitmq.constants';

// Message interfaces
export interface EmailMessage {
  id: string;
  to: string;
  subject: string;
  body: string;
  retryCount?: number;
  maxRetries?: number;
}

export interface NotificationMessage {
  id: string;
  userId: string;
  title: string;
  content: string;
  type: 'email' | 'sms' | 'push';
  retryCount?: number;
  maxRetries?: number;
}

@Injectable()
export class SimpleMessageService {
  private readonly logger = new Logger(SimpleMessageService.name);

  constructor(
    @Inject(QMS_QUEUE_MODULE)
    private readonly client: ClientProxy,
  ) {}

  // ==================== SENDER METHODS ====================

  async sendEmailWithRetry(emailData: Omit<EmailMessage, 'id' | 'retryCount'>): Promise<any> {
    const message: EmailMessage = {
      id: `email_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      retryCount: 0,
      maxRetries: emailData.maxRetries || 3,
      ...emailData,
    };

    this.logger.log(`📧 Sending email: ${message.to} - ${message.subject}`);
    
    return this.sendWithRetryLogic('send-email-notification', message);
  }

  async sendNotificationWithRetry(notificationData: Omit<NotificationMessage, 'id' | 'retryCount'>): Promise<any> {
    const message: NotificationMessage = {
      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      retryCount: 0,
      maxRetries: notificationData.maxRetries || 3,
      ...notificationData,
    };

    this.logger.log(`🔔 Sending notification: ${message.userId} - ${message.title}`);
    
    return this.sendWithRetryLogic('send-notification', message);
  }

  // ==================== RETRY LOGIC ====================

  private async sendWithRetryLogic(pattern: string, message: any): Promise<any> {
    const maxRetries = message.maxRetries || 3;
    let currentRetry = message.retryCount || 0;

    while (currentRetry <= maxRetries) {
      try {
        this.logger.log(`🚀 Attempt ${currentRetry + 1}/${maxRetries + 1} for ${pattern}`);
        
        const result = await this.client.send(pattern, {
          ...message,
          retryCount: currentRetry,
        }).toPromise();
        
        this.logger.log(`✅ Success on attempt ${currentRetry + 1} for ${pattern}`);
        return result;
        
      } catch (error) {
        currentRetry++;
        
        if (currentRetry > maxRetries) {
          this.logger.error(`❌ Failed after ${maxRetries + 1} attempts for ${pattern}: ${error.message}`);
          throw new Error(`Failed after ${maxRetries + 1} attempts: ${error.message}`);
        }
        
        const delay = this.calculateRetryDelay(currentRetry);
        this.logger.warn(`⚠️ Attempt ${currentRetry} failed for ${pattern}, retrying in ${delay}ms: ${error.message}`);
        
        await this.sleep(delay);
      }
    }
  }

  private calculateRetryDelay(retryCount: number): number {
    // Exponential backoff: 1s, 2s, 4s, 8s...
    return Math.min(1000 * Math.pow(2, retryCount - 1), 30000); // Max 30 seconds
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // ==================== MESSAGE PATTERN HANDLERS ====================

  @MessagePattern('send-email-notification', Transport.RMQ)
  async handleEmailNotification(data: EmailMessage) {
    this.logger.log(`📧 Processing email notification: ${data.id} (Retry: ${data.retryCount || 0})`);
    this.logger.log(`📧 To: ${data.to}, Subject: ${data.subject}`);
    
    try {
      // Simulate email processing
      await this.processEmail(data);
      
      this.logger.log(`✅ Email processed successfully: ${data.id}`);
      
      return {
        success: true,
        message: 'Email sent successfully',
        emailId: data.id,
        sentAt: new Date().toISOString(),
        recipient: data.to,
        retryCount: data.retryCount || 0,
      };
      
    } catch (error) {
      this.logger.error(`❌ Email processing failed: ${data.id} - ${error.message}`);
      
      // Throw RpcException để trigger retry mechanism
      throw new RpcException({
        message: `Email processing failed: ${error.message}`,
        emailId: data.id,
        retryCount: data.retryCount || 0,
      });
    }
  }

  @MessagePattern('send-notification', Transport.RMQ)
  async handleNotification(data: NotificationMessage) {
    this.logger.log(`🔔 Processing notification: ${data.id} (Retry: ${data.retryCount || 0})`);
    this.logger.log(`🔔 User: ${data.userId}, Type: ${data.type}, Title: ${data.title}`);
    
    try {
      // Simulate notification processing
      await this.processNotification(data);
      
      this.logger.log(`✅ Notification processed successfully: ${data.id}`);
      
      return {
        success: true,
        message: 'Notification sent successfully',
        notificationId: data.id,
        sentAt: new Date().toISOString(),
        userId: data.userId,
        type: data.type,
        retryCount: data.retryCount || 0,
      };
      
    } catch (error) {
      this.logger.error(`❌ Notification processing failed: ${data.id} - ${error.message}`);
      
      // Throw RpcException để trigger retry mechanism
      throw new RpcException({
        message: `Notification processing failed: ${error.message}`,
        notificationId: data.id,
        retryCount: data.retryCount || 0,
      });
    }
  }

  // ==================== PROCESSING METHODS ====================

  private async processEmail(data: EmailMessage): Promise<void> {
    // Simulate processing time
    await this.sleep(200 + Math.random() * 300);
    
    // Simulate failure rate (30% chance of failure for testing retry)
    if (Math.random() < 0.3) {
      const errors = [
        'SMTP server temporarily unavailable',
        'Invalid email address format',
        'Email service rate limit exceeded',
        'Network timeout',
        'Authentication failed',
      ];
      
      const randomError = errors[Math.floor(Math.random() * errors.length)];
      throw new Error(randomError);
    }
    
    this.logger.log(`📧 Email sent to ${data.to}: ${data.subject}`);
  }

  private async processNotification(data: NotificationMessage): Promise<void> {
    // Simulate processing time
    await this.sleep(150 + Math.random() * 250);
    
    // Simulate failure rate (25% chance of failure for testing retry)
    if (Math.random() < 0.25) {
      const errors = [
        'Push notification service unavailable',
        'User device not registered',
        'SMS gateway error',
        'Rate limit exceeded',
        'Invalid user ID',
      ];
      
      const randomError = errors[Math.floor(Math.random() * errors.length)];
      throw new Error(randomError);
    }
    
    this.logger.log(`🔔 ${data.type} notification sent to user ${data.userId}: ${data.title}`);
  }

  // ==================== UTILITY METHODS ====================

  async getServiceHealth(): Promise<any> {
    return {
      service: 'SimpleMessageService',
      status: 'healthy',
      timestamp: new Date().toISOString(),
      features: [
        'Email notifications with retry',
        'Push notifications with retry',
        'Exponential backoff retry strategy',
        'Configurable max retries',
      ],
    };
  }
}
