import { Modu<PERSON> } from '@nestjs/common';
import { RabbitMqModule } from '../rabbitmq.module';
import { MicroserviceDemoController } from './microservice-demo.controller';
import { RabbitMqDemoController } from './rabbitmq-demo.controller';
import { RabbitMqDemoService } from './rabbitmq-demo.service';
import { SimpleDemoController } from '../examples/simple-demo.controller';

@Module({
  imports: [RabbitMqModule],
  controllers: [RabbitMqDemoController, MicroserviceDemoController, SimpleDemoController],
  providers: [RabbitMqDemoService],
  exports: [RabbitMqDemoService],
})
export class RabbitMqDemoModule {}
