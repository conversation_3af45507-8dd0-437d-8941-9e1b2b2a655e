import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SimpleDemoController } from '../examples/simple-demo.controller';
import { RabbitMqModule } from '../rabbitmq.module';
import { MicroserviceDemoController } from './microservice-demo.controller';
import { RabbitMqDemoController } from './rabbitmq-demo.controller';
import { RabbitMqDemoService } from './rabbitmq-demo.service';
import { SimpleMessageController } from './simple-message.controller';
import { SimpleMessageService } from './simple-message.service';

@Module({
  imports: [RabbitMqModule],
  controllers: [
    RabbitMqDemoController,
    MicroserviceDemoController,
    SimpleDemoController,
    SimpleMessageController,
  ],
  providers: [RabbitMqDemoService, SimpleMessageService],
  exports: [RabbitMqDemoService, SimpleMessageService],
})
export class RabbitMqDemoModule {}
