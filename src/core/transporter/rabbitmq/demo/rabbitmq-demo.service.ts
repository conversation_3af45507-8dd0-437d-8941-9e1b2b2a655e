import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  CreateConsumerOptionsDto,
  PublishMessageOptionsDto,
} from '../dto/create-consumer-optional.dto';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  RabbitMqServiceInterface,
} from '../interfaces/rabbitmq.service.interface';
import { MESSAGE_QUEUE } from '../rabbitmq.constants';

// Demo message interfaces
export interface UserNotificationMessage {
  userId: string;
  type: 'email' | 'sms' | 'push';
  title: string;
  content: string;
  metadata?: Record<string, any>;
}

export interface EmailMessage {
  to: string;
  subject: string;
  body: string;
  attachments?: string[];
}

export interface DataSyncMessage {
  entityType: string;
  entityId: string;
  action: 'create' | 'update' | 'delete';
  data: Record<string, any>;
  timestamp: string;
}

@Injectable()
export class RabbitMqDemoService {
  private readonly logger = new Logger(RabbitMqDemoService.name);

  constructor(
    @Inject('RabbitMqServiceInterface')
    private readonly rabbitMqService: RabbitMqServiceInterface,
  ) {}

  // Demo: Publishing messages with different retry strategies
  async sendUserNotification(
    notification: UserNotificationMessage,
  ): Promise<boolean> {
    try {
      const options: PublishMessageOptionsDto = {
        retries: 3,
        delay: 2000,
        exponentialBackoff: true,
        maxDelay: 30000,
        persistent: true,
        priority: 5,
      };

      const result = await this.rabbitMqService.publishWithRetry(
        MESSAGE_QUEUE.DEMO.USER_NOTIFICATION,
        notification,
        options,
      );

      this.logger.log(
        `User notification sent successfully: ${notification.userId}`,
      );
      return result;
    } catch (error) {
      this.logger.error(`Failed to send user notification: ${error.message}`);
      throw error;
    }
  }

  async sendEmail(email: EmailMessage): Promise<boolean> {
    try {
      // Simple publish without retry for emails
      const result = await this.rabbitMqService.addToQueue(
        MESSAGE_QUEUE.DEMO.EMAIL_QUEUE,
        email,
        {
          persistent: true,
          expiration: 3600000, // 1 hour
        },
      );

      this.logger.log(`Email queued successfully: ${email.to}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to queue email: ${error.message}`);
      throw error;
    }
  }

  async syncData(data: DataSyncMessage): Promise<boolean> {
    try {
      const options: PublishMessageOptionsDto = {
        retries: 5,
        delay: 1000,
        exponentialBackoff: true,
        maxDelay: 60000,
        persistent: true,
        priority: 8, // High priority for data sync
      };

      const result = await this.rabbitMqService.publishWithRetry(
        MESSAGE_QUEUE.DEMO.DATA_SYNC,
        data,
        options,
      );

      this.logger.log(
        `Data sync message sent: ${data.entityType}:${data.entityId}`,
      );
      return result;
    } catch (error) {
      this.logger.error(`Failed to send data sync message: ${error.message}`);
      throw error;
    }
  }

  // Demo: Setting up consumers with different retry strategies
  async setupUserNotificationConsumer(): Promise<string> {
    const handler: MessageHandler<UserNotificationMessage> = async (
      message,
    ) => {
      this.logger.log(
        `Processing user notification: ${JSON.stringify(message)}`,
      );

      // Simulate processing
      await this.processUserNotification(message);

      this.logger.log(
        `User notification processed successfully: ${message.userId}`,
      );
    };

    const options: CreateConsumerOptionsDto = {
      retries: 3,
      delay: 5000,
      exponentialBackoff: true,
      maxDelay: 300000,
      prefetch: 1,
      enableDLQ: true,
    };

    return await this.rabbitMqService.createConsumer(
      MESSAGE_QUEUE.DEMO.USER_NOTIFICATION,
      handler,
      options,
    );
  }

  async setupEmailConsumer(): Promise<string> {
    const handler: MessageHandler<EmailMessage> = async (message) => {
      this.logger.log(`Processing email: ${message.to}`);

      // Simulate email sending
      await this.sendEmailToProvider(message);

      this.logger.log(`Email sent successfully: ${message.to}`);
    };

    const options: CreateConsumerOptionsDto = {
      retries: 2,
      delay: 3000,
      exponentialBackoff: false,
      prefetch: 5, // Process multiple emails concurrently
      enableDLQ: true,
    };

    return await this.rabbitMqService.createConsumer(
      MESSAGE_QUEUE.DEMO.EMAIL_QUEUE,
      handler,
      options,
    );
  }

  async setupDataSyncConsumer(): Promise<string> {
    const handler: MessageHandler<DataSyncMessage> = async (message) => {
      this.logger.log(
        `Processing data sync: ${message.entityType}:${message.entityId}`,
      );

      // Simulate data synchronization
      await this.synchronizeData(message);

      this.logger.log(
        `Data synchronized successfully: ${message.entityType}:${message.entityId}`,
      );
    };

    const options: CreateConsumerOptionsDto = {
      retries: 5,
      delay: 2000,
      exponentialBackoff: true,
      maxDelay: 120000,
      prefetch: 1, // Process one at a time for data consistency
      enableDLQ: true,
    };

    return await this.rabbitMqService.createConsumer(
      MESSAGE_QUEUE.DEMO.DATA_SYNC,
      handler,
      options,
    );
  }

  // Demo: Error simulation for testing retry mechanism
  async setupErrorProneConsumer(): Promise<string> {
    const handler: MessageHandler<any> = async (message) => {
      this.logger.log(
        `Processing error-prone message: ${JSON.stringify(message)}`,
      );

      // Simulate random failures for testing retry mechanism
      if (Math.random() < 0.7) {
        // 70% failure rate
        throw new Error(
          'Simulated processing error for testing retry mechanism',
        );
      }

      this.logger.log(`Message processed successfully after potential retries`);
    };

    const options: CreateConsumerOptionsDto = {
      retries: 3,
      delay: 1000,
      exponentialBackoff: true,
      maxDelay: 10000,
      prefetch: 1,
      enableDLQ: true,
    };

    return await this.rabbitMqService.createConsumer(
      'error_prone_queue',
      handler,
      options,
    );
  }

  // Demo: Batch message publishing
  async sendBatchNotifications(
    notifications: UserNotificationMessage[],
  ): Promise<boolean[]> {
    const results: boolean[] = [];

    for (const notification of notifications) {
      try {
        const result = await this.sendUserNotification(notification);
        results.push(result);
      } catch (error) {
        this.logger.error(
          `Failed to send notification for user ${notification.userId}: ${error.message}`,
        );
        results.push(false);
      }
    }

    return results;
  }

  // Simulate processing methods
  private async processUserNotification(
    message: UserNotificationMessage,
  ): Promise<void> {
    // Simulate processing time
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Simulate occasional failures for testing
    if (message.type === 'email' && Math.random() < 0.1) {
      throw new Error('Email service temporarily unavailable');
    }
  }

  private async sendEmailToProvider(message: EmailMessage): Promise<void> {
    // Simulate email sending time
    await new Promise((resolve) => setTimeout(resolve, 200));

    // Simulate occasional failures
    if (Math.random() < 0.05) {
      throw new Error('Email provider API error');
    }
  }

  private async synchronizeData(message: DataSyncMessage): Promise<void> {
    // Simulate data sync time
    await new Promise((resolve) => setTimeout(resolve, 300));

    // Simulate occasional failures
    if (message.action === 'delete' && Math.random() < 0.15) {
      throw new Error('Database connection error during delete operation');
    }
  }

  // Health check and monitoring
  async getQueueHealth(): Promise<{
    isConnected: boolean;
    activeConsumers: number;
  }> {
    const isConnected = await this.rabbitMqService.healthCheck();
    const activeConsumers = 0; // In a real implementation, you'd track this

    return {
      isConnected,
      activeConsumers,
    };
  }
}
