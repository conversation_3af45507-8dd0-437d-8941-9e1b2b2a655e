export class CreateConsumerOptionsDto {
  retries?: number;
  delay?: number;
  prefetch?: number;
  exponentialBackoff?: boolean;
  maxDelay?: number;
  deadLetterQueue?: string;
  enableDLQ?: boolean;
}

export class PublishMessageOptionsDto {
  retries?: number;
  delay?: number;
  exponentialBackoff?: boolean;
  maxDelay?: number;
  persistent?: boolean;
  expiration?: number;
  priority?: number;
}
