export const QMS_QUEUE = 'qms_queue';
export const QMS_QUEUE_MODULE = 'qms_queue_module';
export const EXPIRATION = 43200000; // 12 hours

// Retry configuration constants
export const RETRY_CONFIG = {
  DEFAULT_RETRIES: 3,
  DEFAULT_DELAY: 5000, // 5 seconds
  DEFAULT_MAX_DELAY: 300000, // 5 minutes
  EXPONENTIAL_BACKOFF_MULTIPLIER: 2,
  DEFAULT_PREFETCH: 1,
};

// Queue suffixes
export const QUEUE_SUFFIXES = {
  DELAY: '_delay',
  DLQ: '_dlq',
  RETRY: '_retry',
};

// Exchange names
export const EXCHANGES = {
  MAIN: 'qms_main_exchange',
  DLQ: 'qms_dlq_exchange',
  RETRY: 'qms_retry_exchange',
};

export const MESSAGE_QUEUE = {
  JOB: {
    GET_DEVICE_TEMPLATE_SCHEDULE: 'GET_DEVICE_TEMPLATE_SCHEDULE',
    GENERATE_JOB_SCHEDULE: 'GENERATE_JOB_SCHEDULE',
    AUTO_COMPLETE_JOB: 'AUTO_COMPLETE_JOB',
    GET_LEADER: 'GET_LEADER',
    UPDATE_DATA_DOWNTIME_REPORT: 'UPDATE_DATA_DOWNTIME_REPORT',
    SYNC_SUPPLY_USED_BY_DATE: 'SYNC_SUPPLY_USED_BY_DATE',
    SYNC_DEVICE_MAINTENANCE_REPORT: 'SYNC_DEVICE_MAINTENANCE_REPORT',
    SYNC_SUPPLY_USED_WHEN_COMPLETE: 'SYNC_SUPPLY_USED_WHEN_COMPLETE',
  },
  DEMO: {
    USER_NOTIFICATION: 'user_notification',
    EMAIL_QUEUE: 'email_queue',
    SMS_QUEUE: 'sms_queue',
    DATA_SYNC: 'data_sync',
  },
};
