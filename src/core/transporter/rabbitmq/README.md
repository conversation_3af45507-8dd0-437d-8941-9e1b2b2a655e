# RabbitMQ Service với Cơ chế Retry

## Tổng quan

RabbitMQ Service đã được triển khai lại với các tính năng nâng cao:

- ✅ **Cơ chế retry với exponential backoff**
- ✅ **Dead Letter Queue (DLQ) để xử lý message thất bại**
- ✅ **Auto-reconnection khi mất kết nối**
- ✅ **Health check và monitoring**
- ✅ **Type-safe message handlers**
- ✅ **Flexible publishing options**
- ✅ **Queue management utilities**

## Cấu trúc thư mục

```
src/core/transporter/rabbitmq/
├── dto/
│   └── create-consumer-optional.dto.ts    # DTOs cho options
├── interfaces/
│   └── rabbitmq.service.interface.ts      # Interface definitions
├── demo/
│   ├── rabbitmq-demo.controller.ts        # Demo API endpoints
│   ├── rabbitmq-demo.service.ts           # Demo service
│   └── rabbitmq-demo.module.ts            # Demo module
├── examples/
│   └── job-processing.service.ts          # Example sử dụng trong job processing
├── rabbitmq.constants.ts                  # Constants và cấu hình
├── rabbitmq.service.ts                    # Main service implementation
├── rabbitmq.module.ts                     # Module configuration
└── README.md                              # Tài liệu này
```

## Cách sử dụng

### 1. Import Module

```typescript
import { RabbitMqModule } from '@core/transporter/rabbitmq/rabbitmq.module';

@Module({
  imports: [RabbitMqModule],
  // ...
})
export class YourModule {}
```

### 2. Inject Service

```typescript
import { RabbitMqServiceInterface } from '@core/transporter/rabbitmq/interfaces/rabbitmq.service.interface';

@Injectable()
export class YourService {
  constructor(
    @Inject('RabbitMqServiceInterface')
    private readonly rabbitMqService: RabbitMqServiceInterface,
  ) {}
}
```

### 3. Publishing Messages

#### Publish đơn giản

```typescript
await this.rabbitMqService.addToQueue('my_queue', {
  userId: 'user123',
  action: 'send_email',
  data: { email: '<EMAIL>' },
});
```

#### Publish với retry

```typescript
const options = {
  retries: 3,
  delay: 5000,
  exponentialBackoff: true,
  maxDelay: 300000,
  persistent: true,
  priority: 5,
};

await this.rabbitMqService.publishWithRetry('my_queue', message, options);
```

### 4. Creating Consumers

```typescript
const handler = async (message: YourMessageType) => {
  console.log('Processing message:', message);
  // Xử lý message ở đây
  // Nếu có lỗi, throw error để trigger retry mechanism
};

const options = {
  retries: 3,
  delay: 5000,
  exponentialBackoff: true,
  maxDelay: 300000,
  prefetch: 1,
  enableDLQ: true,
};

const consumerTag = await this.rabbitMqService.createConsumer(
  'my_queue',
  handler,
  options,
);
```

## Demo API

Để test các chức năng, bạn có thể sử dụng demo API:

### Setup Demo Module

Thêm vào `app.module.ts`:

```typescript
import { RabbitMqDemoModule } from '@core/transporter/rabbitmq/demo/rabbitmq-demo.module';

@Module({
  imports: [
    // ... other modules
    RabbitMqDemoModule,
  ],
})
export class AppModule {}
```

### Demo Endpoints

1. **GET /rabbitmq-demo/examples** - Xem tất cả examples
2. **POST /rabbitmq-demo/notification** - Gửi notification
3. **POST /rabbitmq-demo/email** - Queue email
4. **POST /rabbitmq-demo/data-sync** - Sync data
5. **POST /rabbitmq-demo/consumers/setup** - Setup consumers
6. **POST /rabbitmq-demo/test-error-handling** - Test retry mechanism
7. **GET /rabbitmq-demo/health** - Health check

### Example Requests

#### Gửi notification

```bash
curl -X POST http://localhost:3001/rabbitmq-demo/notification \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "type": "email",
    "title": "Welcome!",
    "content": "Welcome to our platform!",
    "metadata": { "source": "registration" }
  }'
```

#### Setup consumers

```bash
curl -X POST http://localhost:3001/rabbitmq-demo/consumers/setup
```

#### Test error handling

```bash
curl -X POST http://localhost:3001/rabbitmq-demo/test-error-handling
```

## Cấu hình

### Environment Variables

```bash
RABBITMQ_CONNECTION=amqp://admin:password@rabbitmq:5672
```

### Retry Configuration

Có thể tùy chỉnh trong `rabbitmq.constants.ts`:

```typescript
export const RETRY_CONFIG = {
  DEFAULT_RETRIES: 3,
  DEFAULT_DELAY: 5000, // 5 seconds
  DEFAULT_MAX_DELAY: 300000, // 5 minutes
  EXPONENTIAL_BACKOFF_MULTIPLIER: 2,
  DEFAULT_PREFETCH: 1,
};
```

## Tính năng nâng cao

### 1. Exponential Backoff

Delay sẽ tăng theo cấp số nhân sau mỗi lần retry:

- Retry 1: 5s
- Retry 2: 10s
- Retry 3: 20s
- ...

### 2. Dead Letter Queue (DLQ)

Messages thất bại sau khi hết số lần retry sẽ được chuyển vào DLQ để xử lý manual.

### 3. Auto-reconnection

Service sẽ tự động reconnect khi mất kết nối với RabbitMQ.

### 4. Health Check

```typescript
const isHealthy = await this.rabbitMqService.healthCheck();
```

### 5. Queue Management

```typescript
// Assert queue
await this.rabbitMqService.assertQueue('my_queue');

// Delete queue
await this.rabbitMqService.deleteQueue('my_queue');

// Purge queue
await this.rabbitMqService.purgeQueue('my_queue');
```

## Best Practices

1. **Sử dụng type-safe handlers**

   ```typescript
   const handler: MessageHandler<YourMessageType> = async (message) => {
     // TypeScript sẽ check type của message
   };
   ```

2. **Cấu hình retry phù hợp**

   - Critical jobs: retries = 5, exponentialBackoff = true
   - Normal jobs: retries = 3, exponentialBackoff = true
   - Low priority: retries = 1, exponentialBackoff = false

3. **Sử dụng DLQ cho production**

   ```typescript
   const options = {
     enableDLQ: true, // Always enable in production
     retries: 3,
   };
   ```

4. **Monitor health**
   ```typescript
   // Setup health check endpoint
   @Get('health/rabbitmq')
   async checkRabbitMqHealth() {
     return await this.rabbitMqService.healthCheck();
   }
   ```

## Troubleshooting

### Connection Issues

- Kiểm tra RABBITMQ_CONNECTION environment variable
- Đảm bảo RabbitMQ server đang chạy
- Check network connectivity

### Message Processing Errors

- Xem logs để identify error patterns
- Check DLQ cho messages thất bại
- Adjust retry configuration nếu cần

### Performance Issues

- Tăng prefetch count cho high-throughput queues
- Sử dụng multiple consumers cho parallel processing
- Monitor queue lengths

## Demo Microservice Pattern

### 1. Microservice Demo Controller

Endpoint: `/microservice-demo/*`

**Sender endpoints:**

- `GET /microservice-demo/send-notification` - Gửi notification message
- `GET /microservice-demo/send-user-data` - Gửi user data message
- `GET /microservice-demo/send-order` - Gửi order message

**Message Patterns (Receivers):**

- `@MessagePattern('send-notification', Transport.RMQ)` - Nhận notification
- `@MessagePattern('process-user-data', Transport.RMQ)` - Nhận user data
- `@MessagePattern('process-order', Transport.RMQ)` - Nhận order

### 2. Notification Service Demo

Endpoint: `/notification-demo/*`

**Demo endpoints:**

- `GET /notification-demo/send-email` - Demo email notification
- `GET /notification-demo/send-sms` - Demo SMS notification
- `GET /notification-demo/send-push` - Demo push notification
- `GET /notification-demo/send-bulk` - Demo bulk notification

**Custom endpoints:**

- `POST /notification-demo/send-email` - Custom email
- `POST /notification-demo/send-sms` - Custom SMS
- `POST /notification-demo/send-push` - Custom push
- `POST /notification-demo/send-bulk` - Custom bulk

**Message Patterns:**

- `@MessagePattern('send-email-notification', Transport.RMQ)`
- `@MessagePattern('send-sms-notification', Transport.RMQ)`
- `@MessagePattern('send-push-notification', Transport.RMQ)`
- `@MessagePattern('send-bulk-notification', Transport.RMQ)`

### 3. Cách test Demo

#### Test Microservice Pattern:

```bash
# Gửi notification
curl -X GET http://localhost:3001/microservice-demo/send-notification

# Gửi user data
curl -X GET http://localhost:3001/microservice-demo/send-user-data

# Gửi order
curl -X GET http://localhost:3001/microservice-demo/send-order
```

#### Test Notification Service:

```bash
# Demo email
curl -X GET http://localhost:3001/notification-demo/send-email

# Custom email
curl -X POST http://localhost:3001/notification-demo/send-email \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Test Email",
    "body": "This is a test email",
    "priority": "high"
  }'
```

### 4. Logs để theo dõi

Khi gọi API, bạn sẽ thấy logs:

```
[MicroserviceDemoController] Sending notification: {"id":"notif_123",...}
[MicroserviceDemoController] 📧 Received notification message: {"id":"notif_123",...}
[MicroserviceDemoController] ✅ Notification processed successfully for user: user_123
```

## Migration từ version cũ

Nếu bạn đang sử dụng version cũ, cần update:

1. **Update import paths**
2. **Update method signatures** (createConsumer giờ return consumerTag)
3. **Update consumer handlers** (sử dụng MessageHandler type)
4. **Add error handling** cho retry mechanism

Xem `examples/job-processing.service.ts` và `examples/notification.service.ts` để biết cách migrate existing code.
