import { <PERSON>, Get, Inject, Logger } from '@nestjs/common';
import { ClientProxy, MessagePattern, Transport } from '@nestjs/microservices';
import { QMS_QUEUE_MODULE } from '../rabbitmq.constants';

interface SimpleMessage {
  id: string;
  content: string;
  timestamp: string;
  from: string;
}

@Controller('simple-demo')
export class SimpleDemoController {
  private readonly logger = new Logger(SimpleDemoController.name);

  constructor(
    @Inject(QMS_QUEUE_MODULE)
    private readonly client: ClientProxy,
  ) {}

  // ==================== SENDER SIDE ====================

  @Get('send-message')
  async sendMessage() {
    try {
      const message: SimpleMessage = {
        id: `msg_${Date.now()}`,
        content: 'Hello from Service A!',
        timestamp: new Date().toISOString(),
        from: 'simple-demo-controller',
      };

      this.logger.log(`🚀 Sending message: ${JSON.stringify(message)}`);

      // Gửi message đến service khác thông qua RabbitMQ
      const result = await this.client
        .send('process-simple-message', message)
        .toPromise();

      this.logger.log(
        `✅ Message sent successfully, response: ${JSON.stringify(result)}`,
      );

      return {
        success: true,
        message: 'Message sent successfully via RabbitMQ',
        data: {
          sent: message,
          response: result,
        },
      };
    } catch (error) {
      console.log('🚀 ~ SimpleDemoController ~ sendMessage ~ error:', error);
      this.logger.error(`❌ Failed to send message: ${error.message}`);
      return {
        success: false,
        message: 'Failed to send message',
        error: error.message,
      };
    }
  }

  @Get('send-notification')
  async sendNotification() {
    try {
      const notification = {
        id: `notif_${Date.now()}`,
        userId: 'user_123',
        title: 'Test Notification',
        content: 'This is a test notification from RabbitMQ demo',
        type: 'info',
        timestamp: new Date().toISOString(),
      };

      this.logger.log(
        `🔔 Sending notification: ${JSON.stringify(notification)}`,
      );

      const result = await this.client
        .send('process-notification', notification)
        .toPromise();

      this.logger.log(
        `✅ Notification sent successfully, response: ${JSON.stringify(
          result,
        )}`,
      );

      return {
        success: true,
        message: 'Notification sent successfully via RabbitMQ',
        data: {
          sent: notification,
          response: result,
        },
      };
    } catch (error) {
      this.logger.error(`❌ Failed to send notification: ${error.message}`);
      return {
        success: false,
        message: 'Failed to send notification',
        error: error.message,
      };
    }
  }

  // ==================== RECEIVER SIDE ====================

  // @MessagePattern('process-simple-message', Transport.RMQ)
  async handleSimpleMessage(data: SimpleMessage) {
    this.logger.log(`📨 Received simple message: ${JSON.stringify(data)}`);

    try {
      // Simulate message processing
      await new Promise((resolve) => setTimeout(resolve, 500));

      this.logger.log(`✅ Simple message processed successfully: ${data.id}`);

      return {
        success: true,
        message: 'Simple message processed successfully',
        processedAt: new Date().toISOString(),
        messageId: data.id,
        originalContent: data.content,
        processedBy: 'simple-demo-controller',
      };
    } catch (error) {
      this.logger.error(
        `❌ Failed to process simple message: ${error.message}`,
      );
      throw error;
    }
  }

  @MessagePattern('process-notification', Transport.RMQ)
  async handleNotification(data: any) {
    this.logger.log(`🔔 Received notification: ${JSON.stringify(data)}`);

    try {
      // Simulate notification processing
      await new Promise((resolve) => setTimeout(resolve, 300));

      this.logger.log(`✅ Notification processed successfully: ${data.id}`);

      return {
        success: true,
        message: 'Notification processed successfully',
        processedAt: new Date().toISOString(),
        notificationId: data.id,
        userId: data.userId,
        processedBy: 'simple-demo-controller',
      };
    } catch (error) {
      this.logger.error(`❌ Failed to process notification: ${error.message}`);
      throw error;
    }
  }

  // ==================== INFO ENDPOINT ====================

  @Get('info')
  getInfo() {
    return {
      success: true,
      message: 'Simple RabbitMQ Demo Information',
      data: {
        description:
          'Simple demo showing microservice communication using RabbitMQ with @MessagePattern',
        endpoints: {
          'GET /simple-demo/send-message': 'Send a simple message via RabbitMQ',
          'GET /simple-demo/send-notification':
            'Send a notification via RabbitMQ',
          'GET /simple-demo/info': 'Get demo information',
        },
        messagePatterns: {
          'process-simple-message': 'Handles simple messages',
          'process-notification': 'Handles notifications',
        },
        flow: [
          '1. Call GET /simple-demo/send-message',
          '2. Service sends message via RabbitMQ client.send()',
          '3. Same service receives message via @MessagePattern',
          '4. Message is processed and response is returned',
          '5. Check logs to see the complete flow',
        ],
        testCommands: [
          'curl -X GET http://localhost:3001/simple-demo/send-message',
          'curl -X GET http://localhost:3001/simple-demo/send-notification',
        ],
      },
    };
  }
}
