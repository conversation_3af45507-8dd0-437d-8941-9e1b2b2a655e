import { Inject, Injectable, Logger } from '@nestjs/common';
import { ClientProxy, MessagePattern, Transport } from '@nestjs/microservices';
import { QMS_QUEUE_MODULE } from '../rabbitmq.constants';

// Message interfaces for notification service
export interface EmailNotificationMessage {
  id: string;
  to: string;
  subject: string;
  body: string;
  template?: string;
  variables?: Record<string, any>;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  scheduledAt?: string;
  metadata?: Record<string, any>;
}

export interface SMSNotificationMessage {
  id: string;
  to: string;
  message: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  scheduledAt?: string;
  metadata?: Record<string, any>;
}

export interface PushNotificationMessage {
  id: string;
  userId: string;
  title: string;
  body: string;
  data?: Record<string, any>;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  scheduledAt?: string;
  metadata?: Record<string, any>;
}

export interface BulkNotificationMessage {
  id: string;
  type: 'email' | 'sms' | 'push';
  recipients: string[];
  content: {
    subject?: string;
    title?: string;
    body: string;
    template?: string;
    variables?: Record<string, any>;
  };
  priority: 'low' | 'normal' | 'high' | 'urgent';
  scheduledAt?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    @Inject(QMS_QUEUE_MODULE)
    private readonly client: ClientProxy,
  ) {}

  // ==================== SENDER METHODS ====================

  async sendEmailNotification(
    emailData: Omit<EmailNotificationMessage, 'id'>,
  ): Promise<any> {
    try {
      const message: EmailNotificationMessage = {
        id: `email_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...emailData,
      };

      this.logger.log(`Sending email notification to: ${message.to}`);

      const result = await this.client
        .send('send-email-notification', message)
        .toPromise();

      this.logger.log(`Email notification sent successfully: ${message.id}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to send email notification: ${error.message}`);
      throw error;
    }
  }

  async sendSMSNotification(
    smsData: Omit<SMSNotificationMessage, 'id'>,
  ): Promise<any> {
    try {
      const message: SMSNotificationMessage = {
        id: `sms_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...smsData,
      };

      this.logger.log(`Sending SMS notification to: ${message.to}`);

      const result = await this.client
        .send('send-sms-notification', message)
        .toPromise();

      this.logger.log(`SMS notification sent successfully: ${message.id}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to send SMS notification: ${error.message}`);
      throw error;
    }
  }

  async sendPushNotification(
    pushData: Omit<PushNotificationMessage, 'id'>,
  ): Promise<any> {
    try {
      const message: PushNotificationMessage = {
        id: `push_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...pushData,
      };

      this.logger.log(`Sending push notification to user: ${message.userId}`);

      const result = await this.client
        .send('send-push-notification', message)
        .toPromise();

      this.logger.log(`Push notification sent successfully: ${message.id}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to send push notification: ${error.message}`);
      throw error;
    }
  }

  async sendBulkNotification(
    bulkData: Omit<BulkNotificationMessage, 'id'>,
  ): Promise<any> {
    try {
      const message: BulkNotificationMessage = {
        id: `bulk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...bulkData,
      };

      this.logger.log(
        `Sending bulk ${message.type} notification to ${message.recipients.length} recipients`,
      );

      const result = await this.client
        .send('send-bulk-notification', message)
        .toPromise();

      this.logger.log(`Bulk notification sent successfully: ${message.id}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to send bulk notification: ${error.message}`);
      throw error;
    }
  }

  // ==================== RECEIVER METHODS (Message Patterns) ====================

  @MessagePattern('send-email-notification', Transport.RMQ)
  async handleEmailNotification(data: EmailNotificationMessage) {
    this.logger.log(`📧 Processing email notification: ${data.id}`);
    this.logger.log(`📧 To: ${data.to}, Subject: ${data.subject}`);

    try {
      // Simulate email sending process
      await this.processEmailNotification(data);

      this.logger.log(
        `✅ Email notification processed successfully: ${data.id}`,
      );

      return {
        success: true,
        message: 'Email notification sent successfully',
        notificationId: data.id,
        sentAt: new Date().toISOString(),
        recipient: data.to,
        type: 'email',
      };
    } catch (error) {
      this.logger.error(
        `❌ Failed to process email notification: ${error.message}`,
      );
      throw error;
    }
  }

  @MessagePattern('send-sms-notification', Transport.RMQ)
  async handleSMSNotification(data: SMSNotificationMessage) {
    this.logger.log(`📱 Processing SMS notification: ${data.id}`);
    this.logger.log(
      `📱 To: ${data.to}, Message: ${data.message.substring(0, 50)}...`,
    );

    try {
      // Simulate SMS sending process
      await this.processSMSNotification(data);

      this.logger.log(`✅ SMS notification processed successfully: ${data.id}`);

      return {
        success: true,
        message: 'SMS notification sent successfully',
        notificationId: data.id,
        sentAt: new Date().toISOString(),
        recipient: data.to,
        type: 'sms',
      };
    } catch (error) {
      this.logger.error(
        `❌ Failed to process SMS notification: ${error.message}`,
      );
      throw error;
    }
  }

  @MessagePattern('send-push-notification', Transport.RMQ)
  async handlePushNotification(data: PushNotificationMessage) {
    this.logger.log(`🔔 Processing push notification: ${data.id}`);
    this.logger.log(`🔔 To User: ${data.userId}, Title: ${data.title}`);

    try {
      // Simulate push notification sending process
      await this.processPushNotification(data);

      this.logger.log(
        `✅ Push notification processed successfully: ${data.id}`,
      );

      return {
        success: true,
        message: 'Push notification sent successfully',
        notificationId: data.id,
        sentAt: new Date().toISOString(),
        recipient: data.userId,
        type: 'push',
      };
    } catch (error) {
      this.logger.error(
        `❌ Failed to process push notification: ${error.message}`,
      );
      throw error;
    }
  }

  @MessagePattern('send-bulk-notification', Transport.RMQ)
  async handleBulkNotification(data: BulkNotificationMessage) {
    this.logger.log(`📢 Processing bulk notification: ${data.id}`);
    this.logger.log(
      `📢 Type: ${data.type}, Recipients: ${data.recipients.length}`,
    );

    try {
      // Simulate bulk notification sending process
      await this.processBulkNotification(data);

      this.logger.log(
        `✅ Bulk notification processed successfully: ${data.id}`,
      );

      return {
        success: true,
        message: 'Bulk notification sent successfully',
        notificationId: data.id,
        sentAt: new Date().toISOString(),
        recipientCount: data.recipients.length,
        type: data.type,
      };
    } catch (error) {
      this.logger.error(
        `❌ Failed to process bulk notification: ${error.message}`,
      );
      throw error;
    }
  }

  // ==================== PROCESSING METHODS ====================

  private async processEmailNotification(
    data: EmailNotificationMessage,
  ): Promise<void> {
    // Simulate email processing time based on priority
    const delay = this.getProcessingDelay(data.priority);
    await new Promise((resolve) => setTimeout(resolve, delay));

    this.logger.log(
      `📧 Email sent to ${data.to} with subject: ${data.subject}`,
    );

    // Simulate template processing if provided
    if (data.template && data.variables) {
      this.logger.log(
        `📧 Using template: ${data.template} with variables: ${JSON.stringify(
          data.variables,
        )}`,
      );
    }

    // Simulate occasional failures
    if (Math.random() < 0.05) {
      throw new Error('Email service provider error');
    }
  }

  private async processSMSNotification(
    data: SMSNotificationMessage,
  ): Promise<void> {
    // Simulate SMS processing time
    const delay = this.getProcessingDelay(data.priority);
    await new Promise((resolve) => setTimeout(resolve, delay));

    this.logger.log(`📱 SMS sent to ${data.to}: ${data.message}`);

    // Simulate occasional failures
    if (Math.random() < 0.03) {
      throw new Error('SMS gateway error');
    }
  }

  private async processPushNotification(
    data: PushNotificationMessage,
  ): Promise<void> {
    // Simulate push notification processing time
    const delay = this.getProcessingDelay(data.priority);
    await new Promise((resolve) => setTimeout(resolve, delay));

    this.logger.log(
      `🔔 Push notification sent to user ${data.userId}: ${data.title}`,
    );

    if (data.data) {
      this.logger.log(`🔔 Push data: ${JSON.stringify(data.data)}`);
    }

    // Simulate occasional failures
    if (Math.random() < 0.02) {
      throw new Error('Push notification service error');
    }
  }

  private async processBulkNotification(
    data: BulkNotificationMessage,
  ): Promise<void> {
    // Simulate bulk processing time (longer for more recipients)
    const baseDelay = this.getProcessingDelay(data.priority);
    const bulkDelay = baseDelay + data.recipients.length * 10; // 10ms per recipient
    await new Promise((resolve) => setTimeout(resolve, bulkDelay));

    this.logger.log(
      `📢 Bulk ${data.type} notification sent to ${data.recipients.length} recipients`,
    );

    // Log some sample recipients
    const sampleRecipients = data.recipients.slice(0, 3);
    this.logger.log(
      `📢 Sample recipients: ${sampleRecipients.join(', ')}${
        data.recipients.length > 3 ? '...' : ''
      }`,
    );

    // Simulate occasional failures
    if (Math.random() < 0.08) {
      throw new Error('Bulk notification service overloaded');
    }
  }

  private getProcessingDelay(priority: string): number {
    switch (priority) {
      case 'urgent':
        return 100;
      case 'high':
        return 200;
      case 'normal':
        return 500;
      case 'low':
        return 1000;
      default:
        return 500;
    }
  }
}
