import { Injectable, Logger, Inject } from '@nestjs/common';
import { RabbitMqServiceInterface, MessageHandler } from '../interfaces/rabbitmq.service.interface';
import { MESSAGE_QUEUE } from '../rabbitmq.constants';
import { CreateConsumerOptionsDto, PublishMessageOptionsDto } from '../dto/create-consumer-optional.dto';

// Job message interfaces
export interface DeviceTemplateScheduleJob {
  deviceId: string;
  templateId: string;
  scheduleTime: string;
  priority: number;
  metadata?: Record<string, any>;
}

export interface JobScheduleGenerationJob {
  planId: string;
  startDate: string;
  endDate: string;
  parameters: Record<string, any>;
}

export interface AutoCompleteJob {
  jobId: string;
  completionCriteria: {
    timeThreshold?: number;
    statusConditions?: string[];
  };
  notificationSettings?: {
    email?: string[];
    webhook?: string;
  };
}

export interface SupplyUsedSyncJob {
  date: string;
  facilityId: string;
  supplyTypes: string[];
  batchSize?: number;
}

@Injectable()
export class JobProcessingService {
  private readonly logger = new Logger(JobProcessingService.name);

  constructor(
    @Inject('RabbitMqServiceInterface')
    private readonly rabbitMqService: RabbitMqServiceInterface,
  ) {}

  // Job publishing methods with retry strategies
  async scheduleDeviceTemplateJob(job: DeviceTemplateScheduleJob): Promise<boolean> {
    try {
      const options: PublishMessageOptionsDto = {
        retries: 3,
        delay: 5000,
        exponentialBackoff: true,
        maxDelay: 300000,
        persistent: true,
        priority: job.priority || 5,
      };

      const result = await this.rabbitMqService.publishWithRetry(
        MESSAGE_QUEUE.JOB.GET_DEVICE_TEMPLATE_SCHEDULE,
        {
          ...job,
          scheduledAt: new Date().toISOString(),
          jobType: 'DEVICE_TEMPLATE_SCHEDULE',
        },
        options,
      );

      this.logger.log(`Device template schedule job queued: ${job.deviceId}-${job.templateId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to schedule device template job: ${error.message}`);
      throw error;
    }
  }

  async generateJobSchedule(job: JobScheduleGenerationJob): Promise<boolean> {
    try {
      const options: PublishMessageOptionsDto = {
        retries: 5,
        delay: 2000,
        exponentialBackoff: true,
        maxDelay: 600000, // 10 minutes max delay
        persistent: true,
        priority: 8, // High priority for schedule generation
      };

      const result = await this.rabbitMqService.publishWithRetry(
        MESSAGE_QUEUE.JOB.GENERATE_JOB_SCHEDULE,
        {
          ...job,
          requestedAt: new Date().toISOString(),
          jobType: 'GENERATE_JOB_SCHEDULE',
        },
        options,
      );

      this.logger.log(`Job schedule generation queued: ${job.planId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to queue job schedule generation: ${error.message}`);
      throw error;
    }
  }

  async scheduleAutoCompleteJob(job: AutoCompleteJob): Promise<boolean> {
    try {
      const options: PublishMessageOptionsDto = {
        retries: 2,
        delay: 10000,
        exponentialBackoff: false,
        persistent: true,
        priority: 3,
      };

      const result = await this.rabbitMqService.publishWithRetry(
        MESSAGE_QUEUE.JOB.AUTO_COMPLETE_JOB,
        {
          ...job,
          scheduledAt: new Date().toISOString(),
          jobType: 'AUTO_COMPLETE_JOB',
        },
        options,
      );

      this.logger.log(`Auto complete job scheduled: ${job.jobId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to schedule auto complete job: ${error.message}`);
      throw error;
    }
  }

  async syncSupplyUsedByDate(job: SupplyUsedSyncJob): Promise<boolean> {
    try {
      const options: PublishMessageOptionsDto = {
        retries: 4,
        delay: 3000,
        exponentialBackoff: true,
        maxDelay: 180000,
        persistent: true,
        priority: 6,
      };

      const result = await this.rabbitMqService.publishWithRetry(
        MESSAGE_QUEUE.JOB.SYNC_SUPPLY_USED_BY_DATE,
        {
          ...job,
          requestedAt: new Date().toISOString(),
          jobType: 'SYNC_SUPPLY_USED_BY_DATE',
          batchSize: job.batchSize || 100,
        },
        options,
      );

      this.logger.log(`Supply used sync job queued: ${job.date}-${job.facilityId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to queue supply used sync job: ${error.message}`);
      throw error;
    }
  }

  // Consumer setup methods
  async setupDeviceTemplateScheduleConsumer(): Promise<string> {
    const handler: MessageHandler<DeviceTemplateScheduleJob & { scheduledAt: string; jobType: string }> = async (message) => {
      this.logger.log(`Processing device template schedule job: ${message.deviceId}-${message.templateId}`);
      
      try {
        // Simulate device template schedule processing
        await this.processDeviceTemplateSchedule(message);
        
        this.logger.log(`Device template schedule job completed: ${message.deviceId}-${message.templateId}`);
      } catch (error) {
        this.logger.error(`Device template schedule job failed: ${error.message}`);
        throw error; // Let retry mechanism handle it
      }
    };

    const options: CreateConsumerOptionsDto = {
      retries: 3,
      delay: 5000,
      exponentialBackoff: true,
      maxDelay: 300000,
      prefetch: 1,
      enableDLQ: true,
    };

    return await this.rabbitMqService.createConsumer(
      MESSAGE_QUEUE.JOB.GET_DEVICE_TEMPLATE_SCHEDULE,
      handler,
      options,
    );
  }

  async setupJobScheduleGenerationConsumer(): Promise<string> {
    const handler: MessageHandler<JobScheduleGenerationJob & { requestedAt: string; jobType: string }> = async (message) => {
      this.logger.log(`Processing job schedule generation: ${message.planId}`);
      
      try {
        // Simulate job schedule generation
        await this.generateScheduleForPlan(message);
        
        this.logger.log(`Job schedule generation completed: ${message.planId}`);
      } catch (error) {
        this.logger.error(`Job schedule generation failed: ${error.message}`);
        throw error;
      }
    };

    const options: CreateConsumerOptionsDto = {
      retries: 5,
      delay: 2000,
      exponentialBackoff: true,
      maxDelay: 600000,
      prefetch: 1, // Process one at a time for consistency
      enableDLQ: true,
    };

    return await this.rabbitMqService.createConsumer(
      MESSAGE_QUEUE.JOB.GENERATE_JOB_SCHEDULE,
      handler,
      options,
    );
  }

  async setupSupplyUsedSyncConsumer(): Promise<string> {
    const handler: MessageHandler<SupplyUsedSyncJob & { requestedAt: string; jobType: string }> = async (message) => {
      this.logger.log(`Processing supply used sync: ${message.date}-${message.facilityId}`);
      
      try {
        // Simulate supply used data synchronization
        await this.syncSupplyUsedData(message);
        
        this.logger.log(`Supply used sync completed: ${message.date}-${message.facilityId}`);
      } catch (error) {
        this.logger.error(`Supply used sync failed: ${error.message}`);
        throw error;
      }
    };

    const options: CreateConsumerOptionsDto = {
      retries: 4,
      delay: 3000,
      exponentialBackoff: true,
      maxDelay: 180000,
      prefetch: 2, // Allow some concurrency for sync jobs
      enableDLQ: true,
    };

    return await this.rabbitMqService.createConsumer(
      MESSAGE_QUEUE.JOB.SYNC_SUPPLY_USED_BY_DATE,
      handler,
      options,
    );
  }

  // Batch job processing
  async scheduleMultipleDeviceTemplateJobs(jobs: DeviceTemplateScheduleJob[]): Promise<boolean[]> {
    const results: boolean[] = [];
    
    for (const job of jobs) {
      try {
        const result = await this.scheduleDeviceTemplateJob(job);
        results.push(result);
      } catch (error) {
        this.logger.error(`Failed to schedule job for device ${job.deviceId}: ${error.message}`);
        results.push(false);
      }
    }
    
    return results;
  }

  // Simulation methods (replace with actual business logic)
  private async processDeviceTemplateSchedule(job: DeviceTemplateScheduleJob & { scheduledAt: string }): Promise<void> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Simulate occasional failures for testing retry mechanism
    if (Math.random() < 0.1) {
      throw new Error('Device template service temporarily unavailable');
    }
    
    // Here you would implement actual device template scheduling logic
    this.logger.debug(`Device template schedule processed for device ${job.deviceId}`);
  }

  private async generateScheduleForPlan(job: JobScheduleGenerationJob & { requestedAt: string }): Promise<void> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Simulate occasional failures
    if (Math.random() < 0.05) {
      throw new Error('Schedule generation service error');
    }
    
    // Here you would implement actual schedule generation logic
    this.logger.debug(`Schedule generated for plan ${job.planId}`);
  }

  private async syncSupplyUsedData(job: SupplyUsedSyncJob & { requestedAt: string }): Promise<void> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Simulate occasional failures
    if (Math.random() < 0.08) {
      throw new Error('Database synchronization error');
    }
    
    // Here you would implement actual supply used data sync logic
    this.logger.debug(`Supply used data synced for ${job.date}`);
  }

  // Health and monitoring
  async getJobProcessingHealth(): Promise<{ isConnected: boolean; queueStats: Record<string, any> }> {
    const isConnected = await this.rabbitMqService.healthCheck();
    
    // In a real implementation, you would gather queue statistics
    const queueStats = {
      deviceTemplateScheduleQueue: { pending: 0, processing: 0 },
      jobScheduleGenerationQueue: { pending: 0, processing: 0 },
      supplyUsedSyncQueue: { pending: 0, processing: 0 },
    };
    
    return {
      isConnected,
      queueStats,
    };
  }
}
