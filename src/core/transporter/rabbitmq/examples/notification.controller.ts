import { Body, Controller, Get, Logger, Post } from '@nestjs/common';
import { NotificationService } from './notification.service';

// DTOs for API requests
export class SendEmailDto {
  to: string;
  subject: string;
  body: string;
  template?: string;
  variables?: Record<string, any>;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  scheduledAt?: string;
  metadata?: Record<string, any>;
}

export class SendSMSDto {
  to: string;
  message: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  scheduledAt?: string;
  metadata?: Record<string, any>;
}

export class SendPushDto {
  userId: string;
  title: string;
  body: string;
  data?: Record<string, any>;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  scheduledAt?: string;
  metadata?: Record<string, any>;
}

export class SendBulkDto {
  type: 'email' | 'sms' | 'push';
  recipients: string[];
  content: {
    subject?: string;
    title?: string;
    body: string;
    template?: string;
    variables?: Record<string, any>;
  };
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  scheduledAt?: string;
  metadata?: Record<string, any>;
}

@Controller('notification-demo')
export class NotificationController {
  private readonly logger = new Logger(NotificationController.name);

  constructor(private readonly notificationService: NotificationService) {}

  // ==================== SENDER ENDPOINTS ====================

  @Get('send-email')
  async sendEmailDemo() {
    try {
      const result = await this.notificationService.sendEmailNotification({
        to: '<EMAIL>',
        subject: 'Welcome to our platform!',
        body: 'Thank you for joining us. We are excited to have you on board.',
        template: 'welcome_email',
        variables: {
          userName: 'John Doe',
          companyName: 'VTI Company',
          activationLink: 'https://example.com/activate/123',
        },
        priority: 'high',
        metadata: {
          source: 'registration',
          campaign: 'welcome_series',
        },
      });

      return {
        success: true,
        message: 'Email notification sent successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to send email: ${error.message}`);
      return {
        success: false,
        message: 'Failed to send email notification',
        error: error.message,
      };
    }
  }

  @Get('send-sms')
  async sendSMSDemo() {
    try {
      const result = await this.notificationService.sendSMSNotification({
        to: '+84123456789',
        message:
          'Your verification code is: 123456. This code will expire in 5 minutes.',
        priority: 'urgent',
        metadata: {
          type: 'verification',
          userId: 'user_123',
        },
      });

      return {
        success: true,
        message: 'SMS notification sent successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to send SMS: ${error.message}`);
      return {
        success: false,
        message: 'Failed to send SMS notification',
        error: error.message,
      };
    }
  }

  @Get('send-push')
  async sendPushDemo() {
    try {
      const result = await this.notificationService.sendPushNotification({
        userId: 'user_456',
        title: 'New Order Received',
        body: 'You have received a new order #12345. Tap to view details.',
        data: {
          orderId: '12345',
          amount: '150.00',
          currency: 'USD',
          action: 'view_order',
        },
        priority: 'high',
        metadata: {
          type: 'order_notification',
          category: 'business',
        },
      });

      return {
        success: true,
        message: 'Push notification sent successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to send push notification: ${error.message}`);
      return {
        success: false,
        message: 'Failed to send push notification',
        error: error.message,
      };
    }
  }

  @Get('send-bulk')
  async sendBulkDemo() {
    try {
      const result = await this.notificationService.sendBulkNotification({
        type: 'email',
        recipients: [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ],
        content: {
          subject: 'System Maintenance Notice',
          body: 'We will be performing scheduled maintenance on our system from 2:00 AM to 4:00 AM UTC. During this time, some services may be temporarily unavailable.',
          template: 'maintenance_notice',
          variables: {
            maintenanceStart: '2:00 AM UTC',
            maintenanceEnd: '4:00 AM UTC',
            supportEmail: '<EMAIL>',
          },
        },
        priority: 'normal',
        metadata: {
          type: 'system_announcement',
          category: 'maintenance',
        },
      });

      return {
        success: true,
        message: 'Bulk notification sent successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to send bulk notification: ${error.message}`);
      return {
        success: false,
        message: 'Failed to send bulk notification',
        error: error.message,
      };
    }
  }

  // ==================== CUSTOM ENDPOINTS ====================

  @Post('send-email')
  async sendCustomEmail(@Body() dto: SendEmailDto) {
    try {
      const result = await this.notificationService.sendEmailNotification({
        ...dto,
        priority: dto.priority || 'normal',
      });

      return {
        success: true,
        message: 'Email notification sent successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to send custom email: ${error.message}`);
      return {
        success: false,
        message: 'Failed to send email notification',
        error: error.message,
      };
    }
  }

  @Post('send-sms')
  async sendCustomSMS(@Body() dto: SendSMSDto) {
    try {
      const result = await this.notificationService.sendSMSNotification({
        ...dto,
        priority: dto.priority || 'normal',
      });

      return {
        success: true,
        message: 'SMS notification sent successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to send custom SMS: ${error.message}`);
      return {
        success: false,
        message: 'Failed to send SMS notification',
        error: error.message,
      };
    }
  }

  @Post('send-push')
  async sendCustomPush(@Body() dto: SendPushDto) {
    try {
      const result = await this.notificationService.sendPushNotification({
        ...dto,
        priority: dto.priority || 'normal',
      });

      return {
        success: true,
        message: 'Push notification sent successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(
        `Failed to send custom push notification: ${error.message}`,
      );
      return {
        success: false,
        message: 'Failed to send push notification',
        error: error.message,
      };
    }
  }

  @Post('send-bulk')
  async sendCustomBulk(@Body() dto: SendBulkDto) {
    try {
      const result = await this.notificationService.sendBulkNotification({
        ...dto,
        priority: dto.priority || 'normal',
      });

      return {
        success: true,
        message: 'Bulk notification sent successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(
        `Failed to send custom bulk notification: ${error.message}`,
      );
      return {
        success: false,
        message: 'Failed to send bulk notification',
        error: error.message,
      };
    }
  }

  // ==================== INFO ENDPOINT ====================

  @Get('info')
  getInfo() {
    return {
      success: true,
      message: 'Notification Demo API Information',
      data: {
        description:
          'Demo showing notification service using RabbitMQ with @MessagePattern',
        endpoints: {
          'GET /notification-demo/send-email': 'Send demo email notification',
          'GET /notification-demo/send-sms': 'Send demo SMS notification',
          'GET /notification-demo/send-push': 'Send demo push notification',
          'GET /notification-demo/send-bulk': 'Send demo bulk notification',
          'POST /notification-demo/send-email':
            'Send custom email notification',
          'POST /notification-demo/send-sms': 'Send custom SMS notification',
          'POST /notification-demo/send-push': 'Send custom push notification',
          'POST /notification-demo/send-bulk': 'Send custom bulk notification',
        },
        messagePatterns: {
          'send-email-notification': 'Handles email notifications',
          'send-sms-notification': 'Handles SMS notifications',
          'send-push-notification': 'Handles push notifications',
          'send-bulk-notification': 'Handles bulk notifications',
        },
        usage: [
          '1. Call GET endpoints for demo notifications',
          '2. Use POST endpoints with custom data',
          '3. Check logs to see message processing',
          '4. Response includes processing results',
        ],
        examples: {
          email: {
            to: '<EMAIL>',
            subject: 'Test Email',
            body: 'This is a test email',
            priority: 'normal',
          },
          sms: {
            to: '+84123456789',
            message: 'Test SMS message',
            priority: 'normal',
          },
          push: {
            userId: 'user123',
            title: 'Test Push',
            body: 'This is a test push notification',
            priority: 'normal',
          },
          bulk: {
            type: 'email',
            recipients: ['<EMAIL>', '<EMAIL>'],
            content: {
              subject: 'Bulk Email',
              body: 'This is a bulk email',
            },
            priority: 'normal',
          },
        },
      },
    };
  }
}
