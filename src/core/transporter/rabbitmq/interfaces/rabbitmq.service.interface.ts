import {
  CreateConsumerOptionsDto,
  PublishMessageOptionsDto,
} from '../dto/create-consumer-optional.dto';

export interface MessageHandler<T = any> {
  (message: T): Promise<void>;
}

export interface RabbitMqServiceInterface {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  isConnected(): boolean;

  // Publishing methods
  addToQueue(
    queueName: string,
    message: object,
    options?: PublishMessageOptionsDto,
  ): Promise<boolean>;
  publishWithRetry(
    queueName: string,
    message: object,
    options?: PublishMessageOptionsDto,
  ): Promise<boolean>;

  // Consumer methods
  createConsumer<T = any>(
    queueName: string,
    callback: MessageHandler<T>,
    options?: CreateConsumerOptionsDto,
  ): Promise<string>;
  removeConsumer(consumerTag?: string): Promise<void>;

  // Queue management
  assertQueue(queueName: string, options?: any): Promise<void>;
  deleteQueue(queueName: string): Promise<void>;
  purgeQueue(queueName: string): Promise<void>;

  // Health check
  healthCheck(): Promise<boolean>;

  // Legacy method for backward compatibility
  close(data?: any): Promise<any>;
}
