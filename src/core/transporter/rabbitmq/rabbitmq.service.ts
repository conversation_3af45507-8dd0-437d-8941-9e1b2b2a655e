import { ConfigService } from '@config/config.service';
import {
  Injectable,
  Logger,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { connect, ConsumeMessage } from 'amqplib';
import {
  CreateConsumerOptionsDto,
  PublishMessageOptionsDto,
} from './dto/create-consumer-optional.dto';
import {
  Message<PERSON><PERSON>ler,
  RabbitMqServiceInterface,
} from './interfaces/rabbitmq.service.interface';
import { EXPIRATION, QUEUE_SUFFIXES, RETRY_CONFIG } from './rabbitmq.constants';

@Injectable()
export class RabbitMqService
  implements RabbitMqServiceInterface, OnModuleInit, OnModuleDestroy
{
  private readonly logger = new Logger(RabbitMqService.name);
  private connection: any;
  private channel: any;
  private consumerTags: Map<string, string> = new Map();
  private readonly configService: ConfigService;
  private reconnectAttempts = 0;
  private readonly maxReconnectAttempts = 5;
  private reconnectDelay = 5000;

  constructor() {
    this.configService = new ConfigService();
  }

  async onModuleInit() {
    await this.connect();
  }

  async onModuleDestroy() {
    await this.disconnect();
  }

  async connect(): Promise<void> {
    try {
      this.connection = await connect(
        this.configService.get('rabbitOption').options.urls[0],
      );

      this.channel = await this.connection.createChannel();

      // Setup connection event handlers
      this.connection.on('error', (err) => {
        this.logger.error(`RabbitMQ connection error: ${err.message}`);
        this.handleReconnection();
      });

      this.connection.on('close', () => {
        this.logger.warn('RabbitMQ connection closed');
        this.handleReconnection();
      });

      this.channel.on('error', (err) => {
        this.logger.error(`RabbitMQ channel error: ${err.message}`);
      });

      this.logger.debug(`RabbitMQ connection is successful.`);
      this.reconnectAttempts = 0;
    } catch (error) {
      this.logger.error(`RabbitMQ connection error: ${error.message}`);
      await this.handleReconnection();
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.channel) {
        await this.channel.close();
      }
      if (this.connection) {
        await this.connection.close();
      }
      this.logger.debug('RabbitMQ disconnected successfully');
    } catch (error) {
      this.logger.error(`Error disconnecting from RabbitMQ: ${error.message}`);
    }
  }

  isConnected(): boolean {
    return !!(
      this.connection &&
      !this.connection.connection.destroyed &&
      this.channel
    );
  }

  private async handleReconnection(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.logger.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    this.logger.log(
      `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`,
    );

    setTimeout(async () => {
      try {
        await this.connect();
      } catch (error) {
        this.logger.error(`Reconnection failed: ${error.message}`);
      }
    }, this.reconnectDelay);
  }

  async addToQueue(
    queueName: string,
    message: object,
    options?: PublishMessageOptionsDto,
  ): Promise<boolean> {
    try {
      if (!this.isConnected()) {
        throw new Error('RabbitMQ is not connected');
      }

      await this.assertQueue(queueName);

      const publishOptions = {
        persistent: options?.persistent ?? true,
        expiration: options?.expiration ?? EXPIRATION,
        priority: options?.priority ?? 0,
      };

      const result = this.channel.sendToQueue(
        queueName,
        Buffer.from(JSON.stringify(message)),
        publishOptions,
      );

      this.logger.debug(`Message sent to queue ${queueName}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to send message to queue ${queueName}: ${error.message}`,
      );
      throw error;
    }
  }

  async publishWithRetry(
    queueName: string,
    message: object,
    options?: PublishMessageOptionsDto,
  ): Promise<boolean> {
    const retryOptions = {
      retries: options?.retries ?? RETRY_CONFIG.DEFAULT_RETRIES,
      delay: options?.delay ?? RETRY_CONFIG.DEFAULT_DELAY,
      exponentialBackoff: options?.exponentialBackoff ?? true,
      maxDelay: options?.maxDelay ?? RETRY_CONFIG.DEFAULT_MAX_DELAY,
    };

    let attempt = 0;
    let currentDelay = retryOptions.delay;

    while (attempt <= retryOptions.retries) {
      try {
        return await this.addToQueue(queueName, message, options);
      } catch (error) {
        attempt++;

        if (attempt > retryOptions.retries) {
          this.logger.error(
            `Failed to publish message after ${retryOptions.retries} retries: ${error.message}`,
          );
          throw error;
        }

        this.logger.warn(
          `Publish attempt ${attempt} failed, retrying in ${currentDelay}ms: ${error.message}`,
        );

        await new Promise((resolve) => setTimeout(resolve, currentDelay));

        if (retryOptions.exponentialBackoff) {
          currentDelay = Math.min(
            currentDelay * RETRY_CONFIG.EXPONENTIAL_BACKOFF_MULTIPLIER,
            retryOptions.maxDelay,
          );
        }
      }
    }

    return false;
  }

  // Queue management methods
  async assertQueue(queueName: string, options?: any): Promise<void> {
    try {
      await this.channel.assertQueue(queueName, {
        durable: true,
        ...options,
      });
    } catch (error) {
      this.logger.error(
        `Failed to assert queue ${queueName}: ${error.message}`,
      );
      throw error;
    }
  }

  async deleteQueue(queueName: string): Promise<void> {
    try {
      await this.channel.deleteQueue(queueName);
      this.logger.debug(`Queue ${queueName} deleted`);
    } catch (error) {
      this.logger.error(
        `Failed to delete queue ${queueName}: ${error.message}`,
      );
      throw error;
    }
  }

  async purgeQueue(queueName: string): Promise<void> {
    try {
      await this.channel.purgeQueue(queueName);
      this.logger.debug(`Queue ${queueName} purged`);
    } catch (error) {
      this.logger.error(`Failed to purge queue ${queueName}: ${error.message}`);
      throw error;
    }
  }

  async createConsumer<T = any>(
    queueName: string,
    callback: MessageHandler<T>,
    options: CreateConsumerOptionsDto = {
      retries: RETRY_CONFIG.DEFAULT_RETRIES,
      delay: RETRY_CONFIG.DEFAULT_DELAY,
      prefetch: RETRY_CONFIG.DEFAULT_PREFETCH,
      exponentialBackoff: true,
      maxDelay: RETRY_CONFIG.DEFAULT_MAX_DELAY,
      enableDLQ: true,
    },
  ): Promise<string> {
    try {
      if (!this.isConnected()) {
        throw new Error('RabbitMQ is not connected');
      }

      this.logger.debug(`Registering consumer for queue: ${queueName}`);

      // Setup retry and DLQ infrastructure
      await this.setupRetryInfrastructure(queueName, options);

      // Assert main queue
      await this.assertQueue(queueName);

      // Set prefetch count
      await this.channel.prefetch(
        options.prefetch || RETRY_CONFIG.DEFAULT_PREFETCH,
      );

      // Create consumer
      const consumerResult = await this.channel.consume(
        queueName,
        async (message: ConsumeMessage | null) => {
          if (!message) return;

          try {
            const messageContent = JSON.parse(message.content.toString()) as T;
            await callback(messageContent);
            this.channel.ack(message);
            this.logger.debug(
              `Message processed successfully from queue: ${queueName}`,
            );
          } catch (error) {
            this.logger.error(
              `Error processing message from queue ${queueName}: ${error.message}`,
            );
            await this.handleMessageError(message, queueName, options, error);
          }
        },
        {
          noAck: false,
        },
      );

      const consumerTag = consumerResult.consumerTag;
      this.consumerTags.set(queueName, consumerTag);

      this.logger.log(
        `Consumer created for queue ${queueName} with tag: ${consumerTag}`,
      );
      return consumerTag;
    } catch (error) {
      this.logger.error(
        `Failed to create consumer for queue ${queueName}: ${error.message}`,
      );
      throw error;
    }
  }

  async removeConsumer(consumerTag?: string): Promise<void> {
    try {
      if (consumerTag) {
        await this.channel.cancel(consumerTag);
        // Remove from our tracking map
        for (const [queueName, tag] of this.consumerTags.entries()) {
          if (tag === consumerTag) {
            this.consumerTags.delete(queueName);
            break;
          }
        }
        this.logger.log(`Consumer ${consumerTag} has been removed.`);
      } else {
        // Remove all consumers
        for (const [queueName, tag] of this.consumerTags.entries()) {
          await this.channel.cancel(tag);
          this.logger.log(
            `Consumer ${tag} for queue ${queueName} has been removed.`,
          );
        }
        this.consumerTags.clear();
      }
    } catch (error) {
      this.logger.error(`Failed to remove consumer: ${error.message}`);
      throw error;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      return this.isConnected();
    } catch (error) {
      this.logger.error(`Health check failed: ${error.message}`);
      return false;
    }
  }

  // Helper methods for retry infrastructure
  private async setupRetryInfrastructure(
    queueName: string,
    options: CreateConsumerOptionsDto,
  ): Promise<void> {
    if (!options.retries || options.retries <= 0) return;

    const delayQueueName = queueName + QUEUE_SUFFIXES.DELAY;
    const dlqName = queueName + QUEUE_SUFFIXES.DLQ;

    // Setup delay queue for retries
    if (options.delay && options.delay > 0) {
      await this.channel.assertQueue(delayQueueName, {
        durable: true,
        arguments: {
          'x-dead-letter-exchange': '',
          'x-dead-letter-routing-key': queueName,
          'x-message-ttl': options.delay,
        },
      });
    }

    // Setup dead letter queue if enabled
    if (options.enableDLQ) {
      await this.channel.assertQueue(dlqName, {
        durable: true,
      });
    }
  }

  private async handleMessageError(
    message: ConsumeMessage,
    queueName: string,
    options: CreateConsumerOptionsDto,
    error: any,
  ): Promise<void> {
    const headers = message.properties.headers || {};
    const retryCount = headers['x-retry-count'] || 0;
    const maxRetries = options.retries || RETRY_CONFIG.DEFAULT_RETRIES;

    if (retryCount >= maxRetries) {
      // Max retries reached
      if (options.enableDLQ) {
        // Send to dead letter queue
        const dlqName = queueName + QUEUE_SUFFIXES.DLQ;
        await this.channel.sendToQueue(dlqName, Buffer.from(message.content), {
          headers: {
            ...headers,
            'x-retry-count': retryCount,
            'x-original-queue': queueName,
            'x-error-message': error.message,
            'x-failed-at': new Date().toISOString(),
          },
        });
        this.logger.warn(
          `Message sent to DLQ ${dlqName} after ${retryCount} retries`,
        );
      } else {
        this.logger.error(
          `Message discarded after ${retryCount} retries: ${error.message}`,
        );
      }
      this.channel.ack(message);
    } else {
      // Retry the message
      const delayQueueName = queueName + QUEUE_SUFFIXES.DELAY;
      let delay = options.delay || RETRY_CONFIG.DEFAULT_DELAY;

      // Apply exponential backoff if enabled
      if (options.exponentialBackoff) {
        delay = Math.min(
          delay *
            Math.pow(RETRY_CONFIG.EXPONENTIAL_BACKOFF_MULTIPLIER, retryCount),
          options.maxDelay || RETRY_CONFIG.DEFAULT_MAX_DELAY,
        );
      }

      // Update TTL for this specific retry
      await this.channel.assertQueue(delayQueueName, {
        durable: true,
        arguments: {
          'x-dead-letter-exchange': '',
          'x-dead-letter-routing-key': queueName,
          'x-message-ttl': delay,
        },
      });

      await this.channel.sendToQueue(
        delayQueueName,
        Buffer.from(message.content),
        {
          headers: {
            ...headers,
            'x-retry-count': retryCount + 1,
          },
        },
      );

      this.logger.warn(
        `Message retry ${
          retryCount + 1
        }/${maxRetries} scheduled with delay ${delay}ms`,
      );
      this.channel.ack(message);
    }
  }

  // Legacy method for backward compatibility
  async close(data?: any): Promise<any> {
    await this.disconnect();
    return data;
  }
}
