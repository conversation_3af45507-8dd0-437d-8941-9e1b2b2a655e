import { ConfigService } from '@config/config.service';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Channel, connect } from 'amqplib';
import { CreateConsumerOptionsDto } from './dto/create-consumer-optional.dto';
import { RabbitMqServiceInterface } from './interfaces/rabbitmq.service.interface';
import { EXPIRATION } from './rabbitmq.constants';

@Injectable()
export class RabbitMqService implements RabbitMqServiceInterface, OnModuleInit {
  private readonly logger = new Logger(RabbitMqService.name);
  private connection;
  private channel: Channel;
  protected consumerTag: string | undefined; // Thay đổi kiểu dữ liệu
  private readonly configService: ConfigService;

  constructor() {
    this.configService = new ConfigService();
  }

  async onModuleInit() {
    await this.connect();
  }

  async connect() {
    try {
      this.connection = await connect(
        this.configService.get('rabbitOption').options.urls[0],
      );

      this.channel = await this.connection.createChannel();

      this.logger.debug(`RabbitMq connection is successful.`);
    } catch (error) {
      this.logger.error(`RabbitMq connection error: ${error.message}`);
    }
  }

  async addToQueue(queueName: string, message: object) {
    await this.channel.assertQueue(queueName);
    return this.channel.sendToQueue(
      queueName,
      Buffer.from(JSON.stringify(message)),
      {
        expiration: EXPIRATION,
      },
    );
  }

  async createConsumer(
    queueName: string,
    callback: (id: string) => Promise<any>,
    options: CreateConsumerOptionsDto = {
      retries: 3,
      delay: 5000,
      prefetch: 1,
    },
  ) {
    this.logger.debug('REGISTED CONSUMER');
    const delayQueueName = queueName + '_delay';

    if (options.retries > 0 && options.delay > 0) {
      await this.channel.assertQueue(delayQueueName, {
        durable: true,
        arguments: {
          'x-dead-letter-exchange': '',
          'x-dead-letter-routing-key': queueName,
          'x-message-ttl': options.delay,
        },
      });
    }

    await this.channel.assertQueue(queueName);
    this.channel.prefetch(options.prefetch);

    this.channel.consume(
      queueName,
      async (message) => {
        try {
          await callback(JSON.parse(message.content.toString()));
          this.channel.ack(message);
        } catch (error) {
          console.error(error);
          const headers = message.properties.headers || {};
          const retries = headers['x-retry-count'] || 0;

          if (!options.delay || retries >= options.retries) {
            this.channel.ack(message);
          } else {
            this.channel.sendToQueue(
              delayQueueName,
              Buffer.from(message.content),
              {
                headers: { 'x-retry-count': retries + 1 },
              },
            );
            this.channel.ack(message);
          }
        }
      },
      {
        noAck: false,
      },
    );
  }
  async removeConsumer() {
    if (this.consumerTag) {
      console.log('=========this.consumerTag=========', this.consumerTag);
      await this.channel.cancel(this.consumerTag);
      this.consumerTag = undefined; // Reset consumerTag
      this.logger.log('Consumer has been removed.');
    } else {
      this.logger.warn('No consumer to remove..');
    }
  }

  async close() {
    await this.channel.close();
    await this.connection.close();
  }
}
