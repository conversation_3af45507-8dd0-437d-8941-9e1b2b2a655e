import { BootModule } from '@nestcloud/boot';
import { BOOT, CONSUL } from '@nestcloud/common';
import { ConsulModule } from '@nestcloud/consul';
import { ServiceModule } from '@nestcloud/service';
import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { I18nJsonLoader, I18nModule, QueryResolver } from 'nestjs-i18n';
import * as path from 'path';
import { DatabaseModule } from '../database/database.module';
import { AuthModule } from './components/auth/auth.module';
import { FileModule } from './components/file/file.module';
import { HttpClientModule } from './components/http-client/http-client.module';
import { KongGatewayModule } from './components/kong-gateway/kong-gateway.module';
import { UserModule } from './components/user/user.module';
import { AuthorizationGuard } from './guards/authorization.guard';
import { ExceptionInterceptor } from './interceptors/exception.interceptor';
import { ValidationPipe } from './pipe/validator.pipe';
import { NatsClientModule } from './transporter/nats-transporter/nats-client.module';
import { RabbitMqModule } from './transporter/rabbitmq/rabbitmq.module';

@Global()
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    RabbitMqModule,
    DatabaseModule,
    NatsClientModule,
    HttpClientModule,
    UserModule,
    AuthModule,
    FileModule,
    I18nModule.forRoot({
      fallbackLanguage: 'vi',
      loader: I18nJsonLoader,
      loaderOptions: {
        path: path.join(__dirname, '../i18n/'),
        watch: true,
      },
      resolvers: [{ use: QueryResolver, options: ['lang', 'locale', 'l'] }],
    }),
    BootModule.forRoot({
      filePath: path.resolve(__dirname, '../../config.yaml'),
    }),
    EventEmitterModule.forRoot(),
    ConsulModule.forRootAsync({ inject: [BOOT] }),
    ServiceModule.forRootAsync({ inject: [BOOT, CONSUL] }),
    KongGatewayModule.forRootAsync(),
  ],
  providers: [
    // { provide: APP_INTERCEPTOR, useClass: LoggingInterceptor },
    {
      provide: APP_PIPE,
      useClass: ValidationPipe,
    },
    {
      provide: APP_GUARD,
      useClass: AuthorizationGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ExceptionInterceptor,
    },
  ],
  exports: [
    NatsClientModule,
    HttpClientModule,
    AuthModule,
    FileModule,
    UserModule,
    I18nModule,
  ],
})
export class CoreModule {}
