import CorsPlugin from '@fastify/cors';
import fastifyMultipart from '@fastify/multipart';
import { NestFactory } from '@nestjs/core';
import { Transport } from '@nestjs/microservices';
import {
  FastifyAdapter,
  NestFastifyApplication,
} from '@nestjs/platform-fastify';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { useContainer } from 'class-validator';
import 'winston-daily-rotate-file';
import { AppModule } from './app.module';
import { APIPrefix } from './common';
import { ConfigService } from './config/config.service';
import { NATS_AUTH, NatsConfig } from './config/nats.config';
import { ExceptionInterceptor } from './core/interceptors/exception.interceptor';
import { FilterQueryPipe } from './core/pipe/filter-query.pipe';
import { SortQueryPipe } from './core/pipe/sort-query.pipe';

export const SRC_DIR = __dirname;

async function bootstrap() {
  const app = await createApp();
  setGlobalPrefix(app);
  setupSwagger(app);
  setupCors(app);
  setupMiddleware(app);
  useContainer(app.select(AppModule), { fallbackOnErrors: true });
  await setupMicroservices(app);
  await run(app);
}
const configService = new ConfigService();
bootstrap();

function setupMiddleware(app: NestFastifyApplication) {
  app.useGlobalPipes(new SortQueryPipe());
  app.useGlobalPipes(new FilterQueryPipe());
  app.useGlobalInterceptors(new ExceptionInterceptor());
  return app;
}

async function run(app: NestFastifyApplication) {
  await app.listen(new ConfigService().get('httpPort'), '0.0.0.0');
  console.log(
    `🚀🚀 🚀  Server ready and listening at ==> http://localhost:${new ConfigService().get(
      'containerPort',
    )}/api/v1/qms/swagger-docs 🚀 🚀 🚀 `,
  );
  return app;
}

function setupCors(app: NestFastifyApplication) {
  let corsOptions = {};
  if (configService.get('corsOrigin')) {
    corsOptions = {
      origin: new ConfigService().get('corsOrigin'),
    };
  }

  app.register(CorsPlugin, corsOptions);
  return app;
}

function setGlobalPrefix(app: NestFastifyApplication) {
  app.setGlobalPrefix(APIPrefix.Version);
  return app;
}

function setupSwagger(app: NestFastifyApplication) {
  const options = new DocumentBuilder()
    .setTitle('Quotation API docs')
    .addBearerAuth()
    .setVersion('1.0')
    .addSecurity('bearer', {
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'jwt',
      in: 'header',
    })
    .addSecurityRequirements('bearer')
    .build();

  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('api/v1/qms/swagger-docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });
  return app;
}

function createApp(): Promise<NestFastifyApplication> {
  const fastifyAdapter = new FastifyAdapter();
  fastifyAdapter.register(fastifyMultipart, {
    attachFieldsToBody: true,
    addToBody: true,
  });

  return NestFactory.create<NestFastifyApplication>(
    AppModule,
    fastifyAdapter,
    {},
  );
}

async function setupMicroservices(
  app: NestFastifyApplication,
): Promise<NestFastifyApplication> {
  // NATS Microservice
  app.connectMicroservice(
    {
      transport: Transport.NATS,
      options: {
        servers: NatsConfig().servers,
        queue: NATS_AUTH,
      },
    },
    { inheritAppConfig: true },
  );

  // RabbitMQ Microservice
  app.connectMicroservice(
    {
      transport: Transport.RMQ,
      options: {
        urls: [
          process.env.RABBITMQ_CONNECTION ||
            'amqp://admin:snp2021213@rabbitmq:5672',
        ],
        queue: 'qms_queue',
        queueOptions: {
          durable: true,
        },
      },
    },
    { inheritAppConfig: true },
  );

  await app.startAllMicroservices();
  return app;
}
