import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ComponentsModule } from './components/components.module';
import { InspectionTcrsModule } from './components/inspection-tcrs/inspection-tcrs.module';
import { CoreModule } from './core/core.module';
import { RabbitMqDemoModule } from './core/transporter/rabbitmq/demo/rabbitmq-demo.module';

@Module({
  imports: [
    CoreModule,
    ComponentsModule,
    InspectionTcrsModule,
    RabbitMqDemoModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
