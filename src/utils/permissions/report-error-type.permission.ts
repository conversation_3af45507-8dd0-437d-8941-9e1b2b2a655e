import { ACTIVE_ENUM, FORMAT_CODE_PERMISSION } from '../../constant/common';

export const REPORT_ERROR_TYPE_GROUP_PERMISSION = {
  name: 'Báo cáo phân loại lỗi kiểm tra tiêu chuẩn',
  code: FORMAT_CODE_PERMISSION + 'REPORT_ERROR_TYPE_GROUP',
  status: ACTIVE_ENUM.ACTIVE,
};

const STATUS = ACTIVE_ENUM.ACTIVE;
const GROUP = REPORT_ERROR_TYPE_GROUP_PERMISSION.code;

export const LIST_REPORT_ERROR_TYPE_PERMISSION = {
  code: FORMAT_CODE_PERMISSION + 'LIST_REPORT_ERROR_TYPE',
  name: 'Danh sách báo cáo phân loại lỗi kiểm tra tiêu chuẩn',
  groupPermissionSettingCode: GROUP,
  status: STATUS,
};

export const EXPORT_REPORT_ERROR_TYPE_PERMISSION = {
  code: FORMAT_CODE_PERMISSION + 'EXPORT_REPORT_ERROR_TYPE',
  name: '<PERSON><PERSON><PERSON> báo cáo phân loại lỗi kiểm tra tiêu chuẩn',
  groupPermissionSettingCode: GROUP,
  status: STATUS,
};

export const REPORT_ERROR_TYPE_PERMISSION = [
  LIST_REPORT_ERROR_TYPE_PERMISSION,
  EXPORT_REPORT_ERROR_TYPE_PERMISSION,
];
