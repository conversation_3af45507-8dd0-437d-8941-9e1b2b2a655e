import { ACTIVE_ENUM, FORMAT_CODE_PERMISSION } from '../../constant/common';

export const REPORT_3D5S_ERROR_GROUP_PERMISSION = {
  name: 'Báo cáo phân loại lỗi 3D5S',
  code: FORMAT_CODE_PERMISSION + 'REPORT_3D5S_ERROR_GROUP',
  status: ACTIVE_ENUM.ACTIVE,
};

const STATUS = ACTIVE_ENUM.ACTIVE;
const GROUP = REPORT_3D5S_ERROR_GROUP_PERMISSION.code;

export const LIST_REPORT_3D5S_ERROR_PERMISSION = {
  code: FORMAT_CODE_PERMISSION + 'LIST_REPORT_3D5S_ERROR_RESULT',
  name: '<PERSON>h sách báo cáo phân loại lỗi 3D5S',
  groupPermissionSettingCode: GROUP,
  status: STATUS,
};

export const EXPORT_REPORT_3D5S_ERROR_PERMISSION = {
  code: FORMAT_CODE_PERMISSION + 'EXPORT_REPORT_3D5S_ERROR_RESULT',
  name: '<PERSON><PERSON><PERSON> báo cáo phân loại lỗi 3D5S',
  groupPermissionSettingCode: GROUP,
  status: STATUS,
};

export const REPORT_3D5S_ERROR_PERMISSION = [
  LIST_REPORT_3D5S_ERROR_PERMISSION,
  EXPORT_REPORT_3D5S_ERROR_PERMISSION,
];
