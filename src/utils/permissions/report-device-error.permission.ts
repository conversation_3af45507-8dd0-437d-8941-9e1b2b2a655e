import { ACTIVE_ENUM, FORMAT_CODE_PERMISSION } from '../../constant/common';

export const REPORT_DEVICE_ERROR_GROUP_PERMISSION = {
  name: '<PERSON><PERSON><PERSON> cáo tổng hợp thiết bị chưa đủ điều kiện đăng ký',
  code: FORMAT_CODE_PERMISSION + 'REPORT_DEVICE_ERROR_GROUP',
  status: ACTIVE_ENUM.ACTIVE,
};

const STATUS = ACTIVE_ENUM.ACTIVE;
const GROUP = REPORT_DEVICE_ERROR_GROUP_PERMISSION.code;

export const LIST_REPORT_DEVICE_ERROR_PERMISSION = {
  code: FORMAT_CODE_PERMISSION + 'LIST_REPORT_DEVICE_ERROR',
  name: '<PERSON>h sách báo cáo tổng hợp thiết bị chưa đủ điều kiện đăng ký',
  groupPermissionSettingCode: GROUP,
  status: STATUS,
};

export const EXPORT_REPORT_DEVICE_ERROR_PERMISSION = {
  code: FORMAT_CODE_PERMISSION + 'EXPORT_REPORT_DEVICE_ERROR',
  name: 'Xuất báo cáo tổng hợp thiết bị chưa đủ điều kiện đăng ký',
  groupPermissionSettingCode: GROUP,
  status: STATUS,
};

export const REPORT_DEVICE_ERROR_PERMISSION = [
  LIST_REPORT_DEVICE_ERROR_PERMISSION,
  EXPORT_REPORT_DEVICE_ERROR_PERMISSION,
];
