import * as moment from 'moment';

/**
 * Format moment date to start of day string
 * @param momentDate - Moment date object
 * @returns Formatted date string with time set to start of day
 */
function formatToStartOfDay(momentDate: moment.Moment): Date {
  return new Date(momentDate.format('YYYY-MM-DD') + 'T00:00:00.000Z');
}

/**
 * Format moment date to end of day string
 * @param momentDate - Moment date object
 * @returns Formatted date string with time set to end of day
 */
function formatToEndOfDay(momentDate: moment.Moment): Date {
  return new Date(momentDate.format('YYYY-MM-DD') + 'T23:59:59.999Z');
}

/**
 * Hàm tính ngày bắt đầu và kết thúc của tuần thứ x trong năm y sử dụng moment
 * @param {number} week - <PERSON>ầ<PERSON> thứ x (từ 1-53)
 * @param {number} year - Năm y
 * @returns {Object} Đ<PERSON><PERSON> tượng chứa ngày bắt đầu và kết thúc của tuần
 */
export function getWeekDates(week: number, year: number) {
  if (week < 1 || week > 53) {
    throw new Error('Tuần phải nằm trong khoảng từ 1 đến 53');
  }

  if (year < 1900 || year > 2100) {
    throw new Error('Năm phải nằm trong khoảng từ 1900 đến 2100');
  }

  const startDate = moment().year(year).isoWeek(week).startOf('isoWeek');
  const endDate = moment(startDate).endOf('isoWeek');

  // Format lại cho đúng với dữ liệu đã có trong database
  const startDateObj = formatToStartOfDay(startDate);
  const endDateObj = formatToEndOfDay(endDate);

  return {
    startDate: startDateObj,
    endDate: endDateObj,
    startDateObj: startDate.toDate(),
    endDateObj: endDate.toDate(),
    startDateFull: startDate.format('dddd, DD/MM/YYYY'),
    endDateFull: endDate.format('dddd, DD/MM/YYYY'),
    month: startDate.month() + 1,
  };
}

/**
 * Hàm tính ngày bắt đầu và kết thúc của tháng trước và tháng hiện tại, cùng 2 tuần trước
 * @param {number} week - Tuần hiện tại
 * @param {number} year - Năm hiện tại
 * @param {number} month - Tháng hiện tại
 * @returns {Object} Đối tượng chứa ngày bắt đầu và kết thúc của tháng trước, tháng hiện tại và 2 tuần trước
 */
export function getLastMonthAndWeek(week: number, year: number, month: number) {
  // Tính toán năm và tháng cho tháng trước
  const previousMonth = month === 1 ? 12 : month - 1;
  const previousMonthYear = month === 1 ? year - 1 : year;

  // Tháng hiện tại chứa tuần week
  const currentMonth = month;
  const currentMonthYear = year;

  // Tính toán năm và tuần cho 2 tuần trước
  const twoWeeksAgoYear = week <= 2 ? year - 1 : year;
  const twoWeeksAgoWeek = week <= 2 ? (week === 1 ? 52 : 53) : week - 2;

  const oneWeekAgoWeek = week === 1 ? 53 : week - 1;
  const oneWeekAgoYear = week === 1 ? year - 1 : year;

  // Lấy ngày bắt đầu và kết thúc của tháng trước
  const previousMonthStart = moment()
    .year(previousMonthYear)
    .month(previousMonth - 1)
    .startOf('month');
  const previousMonthEnd = moment()
    .year(previousMonthYear)
    .month(previousMonth - 1)
    .endOf('month');

  // Lấy ngày bắt đầu và kết thúc của tháng hiện tại
  const currentMonthStart = moment()
    .year(currentMonthYear)
    .month(currentMonth - 1)
    .startOf('month');
  const currentMonthEnd = moment()
    .year(currentMonthYear)
    .month(currentMonth - 1)
    .endOf('month');

  // Lấy ngày bắt đầu và kết thúc của tuần 1 (2 tuần trước)
  const twoWeeksAgoStart = moment()
    .year(twoWeeksAgoYear)
    .isoWeek(twoWeeksAgoWeek)
    .startOf('isoWeek');
  const twoWeeksAgoEnd = moment()
    .year(twoWeeksAgoYear)
    .isoWeek(twoWeeksAgoWeek)
    .endOf('isoWeek');

  // Lấy ngày bắt đầu và kết thúc của tuần 2 (1 tuần trước)
  const oneWeekAgoStart = moment()
    .year(oneWeekAgoYear)
    .isoWeek(oneWeekAgoWeek)
    .startOf('isoWeek');
  const oneWeekAgoEnd = moment()
    .year(oneWeekAgoYear)
    .isoWeek(oneWeekAgoWeek)
    .endOf('isoWeek');

  return {
    startMonth1: formatToStartOfDay(previousMonthStart),
    endMonth1: formatToEndOfDay(previousMonthEnd),
    startMonth2: formatToStartOfDay(currentMonthStart),
    endMonth2: formatToEndOfDay(currentMonthEnd),
    startWeek1: formatToStartOfDay(twoWeeksAgoStart),
    endWeek1: formatToEndOfDay(twoWeeksAgoEnd),
    startWeek2: formatToStartOfDay(oneWeekAgoStart),
    endWeek2: formatToEndOfDay(oneWeekAgoEnd),
  };
}

export const dateHelper = {
  getWeekDates,
  getLastMonthAndWeek,
  formatToEndOfDay,
  formatToStartOfDay,
};
