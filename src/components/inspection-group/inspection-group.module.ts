import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { ItemQcModule } from '../item-qc/item-qc.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { InspectionGroupController } from './controller/inspection-group.controller';
import { InspectionGroupEntity } from './entities/inspection-group.entity';
import { InspectionGroupRepository } from './repository/inspection-group.repository';
import { InspectionGroupService } from './service/inspection-group.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([InspectionGroupEntity]),
    AnotherServiceModule,
    MasterDataReferenceModule,
    ItemQcModule,
  ],
  providers: [InspectionGroupService, InspectionGroupRepository],
  exports: [InspectionGroupService],
  controllers: [InspectionGroupController],
})
export class InspectionGroupModule {}
