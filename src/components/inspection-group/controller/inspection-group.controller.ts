import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  ACTIVE_INSPECTION_GROUP_PERMISSION,
  CREATE_INSPECTION_GROUP_PERMISSION,
  DELETE_INSPECTION_GROUP_PERMISSION,
  DETAIL_INSPECTION_GROUP_PERMISSION,
  INACTIVE_INSPECTION_GROUP_PERMISSION,
  LIST_INSPECTION_GROUP_PERMISSION,
  UPDATE_INSPECTION_GROUP_PERMISSION,
} from '../../../utils/permissions/inspection-group.permission';
import { CreateInspectionGroupRequestDto } from '../request/create-inspection-group.request.dto';
import { DeleteInspectionGroupRequestDto } from '../request/delete-inspection-group.request.dto';
import { GetDetailInspectionGroupRequestDto } from '../request/get-detail-inspection-group.request.dto';
import { GetListInspectionGroupRequestDto } from '../request/get-list-inspection-group.request.dto';
import { UpdateInspectionGroupRequestDto } from '../request/update-inspection-group.request.dto';
import { UpdateStatusInspectionGroupRequestDto } from '../request/update-status-inspection-group.request.dto';
import { InspectionGroupService } from '../service/inspection-group.service';

@Controller('inspection-groups')
export class InspectionGroupController {
  constructor(
    private readonly inspectionGroupService: InspectionGroupService,
  ) {}

  @PermissionCode(DETAIL_INSPECTION_GROUP_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Inspection-groups'],
    summary: 'Chi tiết Inspection-groups',
    description: 'Chi tiết Inspection-groups',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailInspectionGroupRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionGroupService.getDetail(request);
  }

  @PermissionCode(LIST_INSPECTION_GROUP_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Inspection-groups'],
    summary: 'Danh sách Inspection-groups',
    description: 'Danh sách Inspection-groups',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListInspectionGroupRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionGroupService.getList(request);
  }

  @PermissionCode(CREATE_INSPECTION_GROUP_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Inspection-groups'],
    summary: 'Tạo Inspection-groups mới',
    description: 'Tạo Inspection-groups mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateInspectionGroupRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionGroupService.create(request);
  }

  @PermissionCode(UPDATE_INSPECTION_GROUP_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Inspection-groups'],
    summary: 'Cập nhật Inspection-groups',
    description: 'Cập nhật Inspection-groups',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateInspectionGroupRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionGroupService.update(request);
  }

  @PermissionCode(ACTIVE_INSPECTION_GROUP_PERMISSION.code)
  @Put('/active')
  @ApiOperation({
    tags: ['Inspection-groups'],
    summary: 'Cập nhật Inspection-groups Status Active',
    description: 'Cập nhật Inspection-groups Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async active(
    @Body() body: UpdateStatusInspectionGroupRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionGroupService.active(request);
  }

  @PermissionCode(INACTIVE_INSPECTION_GROUP_PERMISSION.code)
  @Put('/inactive')
  @ApiOperation({
    tags: ['Inspection-groups'],
    summary: 'Cập nhật Inspection-groups Status Inactive',
    description: 'Cập nhật Inspection-groups Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async inactive(
    @Body() body: UpdateStatusInspectionGroupRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionGroupService.inactive(request);
  }

  @PermissionCode(DELETE_INSPECTION_GROUP_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Inspection-groups'],
    summary: 'Xóa Inspection-groups',
    description: 'Xóa Inspection-groups',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(@Param() param: DeleteInspectionGroupRequestDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionGroupService.delete(request);
  }
}
