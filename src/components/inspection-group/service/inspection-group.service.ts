import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';
import { DataSource, In } from 'typeorm';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { InspectionGroupEntity } from '../entities/inspection-group.entity';
import { CreateInspectionGroupRequestDto } from './../request/create-inspection-group.request.dto';
import { DeleteInspectionGroupRequestDto } from './../request/delete-inspection-group.request.dto';
import { GetDetailInspectionGroupRequestDto } from './../request/get-detail-inspection-group.request.dto';
import { GetListInspectionGroupRequestDto } from './../request/get-list-inspection-group.request.dto';
import { UpdateInspectionGroupRequestDto } from './../request/update-inspection-group.request.dto';
import { UpdateStatusInspectionGroupRequestDto } from './../request/update-status-inspection-group.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { ValidateResultCommonDto } from '../../../common/dtos/validate.result.common.dto';
import { StatusEnum } from '../../../common/enums/status.enum';
import { BaseService } from '../../../common/service/base.service';
import { CHECK_TYPE_ENUM } from '../../../constant/common';
import { UserService } from '../../another-service/services/user-service';
import { ItemQcService } from '../../item-qc/service/item-qc.service';
import { MasterDataReferenceService } from '../../master-data-reference/service/master-data-reference.service';
import { InspectionGroupRepository } from '../repository/inspection-group.repository';

@Injectable()
export class InspectionGroupService extends BaseService {
  private readonly logger = new Logger(InspectionGroupService.name);

  constructor(
    private readonly inspectionGroupRepository: InspectionGroupRepository,

    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly masterDataReferenceService: MasterDataReferenceService,

    private readonly itemQcService: ItemQcService,
  ) {
    super(userService);
  }

  async validateSaveInspectionGroup(
    request: CreateInspectionGroupRequestDto,
  ): Promise<ValidateResultCommonDto> {
    // Validate chemical type is not null
    if (
      request?.checkType === CHECK_TYPE_ENUM.ENVIRONMENTAL_TOXICITY &&
      (request?.chemicalType === null || request?.chemicalType === undefined)
    ) {
      return {
        result: true,
        messageError: 'error.CHEMICAL_TYPE_IS_NOT_NULL',
      };
    }

    // Validate qc request type is not null
    if (
      request?.checkType !== CHECK_TYPE_ENUM.ENVIRONMENTAL_TOXICITY &&
      !request?.qcRequestTypeId
    ) {
      return {
        result: true,
        messageError: 'error.QC_REQUEST_TYPE_IS_NOT_NULL',
      };
    }

    // Validate exist qc-request-types
    if (
      request?.qcRequestTypeId !== null &&
      request?.qcRequestTypeId !== undefined
    ) {
      const qcRequestType =
        await this.masterDataReferenceService.findOneQcRequestTypeById(
          request?.qcRequestTypeId,
        );
      if (
        isEmpty(qcRequestType) ||
        qcRequestType.status === StatusEnum.IN_ACTIVE
      ) {
        return {
          result: true,
          messageError: 'error.QC_REQUEST_TYPE_IS_NOT_EXISTS',
        };
      }
    }

    return { result: false, messageError: '' };
  }

  async create(request: CreateInspectionGroupRequestDto): Promise<any> {
    const existCode = await this.inspectionGroupRepository.findOneByCode(
      request.code,
    );

    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate inspection-groups
    const resultValidate = await this.validateSaveInspectionGroup(request);
    if (resultValidate?.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(resultValidate?.messageError))
        .build();
    }

    const inspectionGroupEntity =
      this.inspectionGroupRepository.createEntity(request);
    const inspectionGroup = await this.inspectionGroupRepository.create(
      inspectionGroupEntity,
    );

    return new ResponseBuilder(inspectionGroup)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListInspectionGroupRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.inspectionGroupRepository.getList(
      request,
    );

    const response = await this.mapUserInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailInspectionGroupRequestDto): Promise<any> {
    const existInspectionGroup =
      await this.inspectionGroupRepository.findOneById(request?.id);
    if (isEmpty(existInspectionGroup)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const inspectionGroup = await this.inspectionGroupRepository.getDetail(
      request,
    );
    const response = await this.mapUserInfoToResponse([inspectionGroup]);

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateInspectionGroupRequestDto): Promise<any> {
    const { id } = request;
    const inspectionGroup = await this.inspectionGroupRepository.findOneById(
      id,
    );

    if (isEmpty(inspectionGroup)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate inspection-groups in used
    const usedList = await this.validateInspectionGroupInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.INSPECTION_GROUP_IN_USED'),
        )
        .build();
    }

    // Validate Code unique
    const existCode = await this.inspectionGroupRepository.findOneByCode(
      request.code,
    );

    if (!isEmpty(existCode) && existCode.id !== inspectionGroup.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate inspection-groups
    const resultValidate = await this.validateSaveInspectionGroup(request);
    if (resultValidate?.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(resultValidate?.messageError))
        .build();
    }

    const dataUpdate = this.inspectionGroupRepository.updateEntity(
      request,
      inspectionGroup,
    );
    const data = await this.inspectionGroupRepository.update(dataUpdate);

    return new ResponseBuilder(data)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteInspectionGroupRequestDto): Promise<any> {
    const { id } = request;
    const inspectionGroup = await this.inspectionGroupRepository.findOneById(
      id,
    );

    if (isEmpty(inspectionGroup)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate inspection-groups in used
    const usedList = await this.validateInspectionGroupInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.INSPECTION_GROUP_IN_USED'),
        )
        .build();
    }

    await this.inspectionGroupRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(request: UpdateStatusInspectionGroupRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.inspectionGroupRepository.findAllByIds(
      ids,
    );

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.inspectionGroupRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(request: UpdateStatusInspectionGroupRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.inspectionGroupRepository.findAllByIds(
      ids,
    );

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.inspectionGroupRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async validateInspectionGroupInUsed(id: number): Promise<boolean> {
    // Validate used in inspection-types
    const inspectionTypeList =
      await this.masterDataReferenceService.findAllInspectionTypeByInspectionGroupIds(
        [id],
      );
    if (!isEmpty(inspectionTypeList)) {
      return true;
    }

    // Validate used in item-qcs
    const itemQcList = await this.itemQcService.findAllByInspectionGroupIds([
      id,
    ]);
    if (!isEmpty(itemQcList)) {
      return true;
    }

    // Validate used in checksheet-details
    const checksheetList =
      await this.masterDataReferenceService.findAllChecksheetByInspectionGroupIds(
        [id],
      );
    if (!isEmpty(checksheetList)) {
      return true;
    }

    // Validate used in another service
    const isUse =
      await this.masterDataReferenceService.validateDataMasterUseAnotherService(
        new ValidateMasterRequestDto('inspection_group_id', id),
      );
    if (isUse) {
      return true;
    }

    return false;
  }

  async findOneById(id: number): Promise<InspectionGroupEntity> {
    return await this.inspectionGroupRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<InspectionGroupEntity[]> {
    return await this.inspectionGroupRepository.findAllByIds(ids);
  }

  async findAllByQcRequestTypeIds(
    qcRequestTypeIds: number[],
  ): Promise<InspectionGroupEntity[]> {
    const filterCondition: any = {
      qcRequestTypeId: In(qcRequestTypeIds),
    };
    return await this.inspectionGroupRepository.findByCondition(
      filterCondition,
    );
  }

  async getInspectionGroupByInspectionTypeCodes(
    inspectionTypeCodes: string[],
  ): Promise<number> {
    return await this.inspectionGroupRepository.getInspectionGroupByInspectionTypeCodes(
      inspectionTypeCodes,
    );
  }
}
