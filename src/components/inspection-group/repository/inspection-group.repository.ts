import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { isDateString } from 'class-validator';
import { Repository } from 'typeorm';
import { StatusEnum } from '../../../common/enums/status.enum';

import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import {
  escapeCharForSearch,
  parseJSONValueField,
} from '../../../utils/common';
import { InspectionGroupEntity } from '../entities/inspection-group.entity';
import { GetDetailInspectionGroupRequestDto } from '../request/get-detail-inspection-group.request.dto';
import { GetListInspectionGroupRequestDto } from '../request/get-list-inspection-group.request.dto';
import { UpdateInspectionGroupRequestDto } from '../request/update-inspection-group.request.dto';
import { UpdateStatusInspectionGroupRequestDto } from '../request/update-status-inspection-group.request.dto';
import { CreateInspectionGroupRequestDto } from './../request/create-inspection-group.request.dto';

@Injectable()
export class InspectionGroupRepository extends BaseAbstractRepository<InspectionGroupEntity> {
  constructor(
    @InjectRepository(InspectionGroupEntity)
    private readonly inspectionGroupRepository: Repository<InspectionGroupEntity>,
  ) {
    super(inspectionGroupRepository);
  }

  createEntity(
    request: CreateInspectionGroupRequestDto,
  ): InspectionGroupEntity {
    const inspectionGroupEntity = new InspectionGroupEntity();
    inspectionGroupEntity.code = request.code;
    inspectionGroupEntity.name = request.name;
    inspectionGroupEntity.qcRequestTypeId = request.qcRequestTypeId;
    inspectionGroupEntity.chemicalType = request.chemicalType;
    inspectionGroupEntity.checkType = request.checkType;
    inspectionGroupEntity.oqcDeviceType = request.oqcDeviceType;
    inspectionGroupEntity.description = request.description;
    inspectionGroupEntity.status = StatusEnum.ACTIVE;
    inspectionGroupEntity.createdBy = request.userId;
    inspectionGroupEntity.updatedBy = request.userId;

    return inspectionGroupEntity;
  }

  updateEntity(
    request: UpdateInspectionGroupRequestDto,
    entity: InspectionGroupEntity,
  ): InspectionGroupEntity {
    entity.name = request.name;
    entity.qcRequestTypeId = request.qcRequestTypeId;
    entity.chemicalType = request.chemicalType;
    entity.checkType = request.checkType;
    entity.oqcDeviceType = request.oqcDeviceType;
    entity.description = request.description;
    entity.updatedBy = request.userId;

    return entity;
  }

  async getList(request: GetListInspectionGroupRequestDto): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.inspectionGroupRepository
      .createQueryBuilder('inspectiongroup')
      .select([
        'inspectiongroup.id AS id',
        'inspectiongroup.code AS "code"',
        'inspectiongroup.name AS "name"',
        'inspectiongroup.qc_request_type_id AS "qcRequestTypeId"',
        'inspectiongroup.check_type AS "checkType"',
        'inspectiongroup.chemical_type AS "chemicalType"',
        'inspectiongroup.oqc_device_type AS "oqcDeviceType"',
        'inspectiongroup.description AS "description"',
        'inspectiongroup.status AS "status"',
        'inspectiongroup.created_by AS "createdBy"',
        'inspectiongroup.created_at AS "createdAt"',
        'inspectiongroup.updated_by AS "updatedBy"',
        'inspectiongroup.updated_at AS "updatedAt"',
      ])
      .leftJoin(
        'qc_request_types',
        'qrt',
        'inspectiongroup.qc_request_type_id = qrt.id',
      )
      .addSelect([
        `CASE WHEN COUNT(qrt.id) = 0 THEN '{}' ELSE
          (SELECT qrt.id as id,qrt.code as code,qrt.name as name,qrt.qc_type as qcType FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "qcRequestType"`,
      ]);

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("inspectiongroup"."code") like lower(:code) escape '\\' OR
              lower("inspectiongroup"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('inspectiongroup.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'code':
            query.andWhere(
              `lower("inspectiongroup"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'name':
            query.andWhere(
              `lower("inspectiongroup"."name") like lower(:name) escape '\\'`,
              {
                name: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'oqcDeviceType':
            query.andWhere(
              '"inspectiongroup"."oqc_device_type" = :oqcDeviceType',
              {
                oqcDeviceType: Number(value),
              },
            );
            break;
          case 'chemicalType':
            query.andWhere(
              '"inspectiongroup"."chemical_type" = :chemicalType',
              {
                chemicalType: Number(value),
              },
            );
            break;
          case 'oqcDeviceTypes':
            query.andWhere(
              '"inspectiongroup"."oqc_device_type" IN (:...oqcDeviceTypes)',
              {
                oqcDeviceTypes: value.split(',').map((id) => Number(id)),
              },
            );
            break;
          case 'checkType':
            query.andWhere('"inspectiongroup"."check_type" = :checkType', {
              checkType: Number(value),
            });
            break;
          case 'checkTypes':
            query.andWhere(
              '"inspectiongroup"."check_type" IN (:...checkTypes)',
              {
                checkTypes: value.split(',').map((id) => Number(id)),
              },
            );
            break;
          case 'qcType':
            query.andWhere('qrt.qc_type = :qcType', {
              qcType: Number(value),
            });
            break;
          case 'qcTypes':
            query.andWhere('qrt.qc_type IN (:...qcTypes)', {
              qcTypes: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'description':
            query.andWhere(
              `lower("inspectiongroup"."description") like lower(:description) escape '\\'`,
              {
                description: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'status':
            query.andWhere('"inspectiongroup"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'qcRequestTypeId':
            query.andWhere(
              '"inspectiongroup"."qc_request_type_id" = :qcRequestTypeId',
              {
                qcRequestTypeId: Number(value),
              },
            );
            break;
          case 'qcRequestTypeIds':
            query.andWhere(
              '"inspectiongroup"."qc_request_type_id" IN (:...qcRequestTypeIds)',
              {
                qcRequestTypeIds: value.split(',').map((id) => Number(id)),
              },
            );
            break;
          case 'createdById':
            query.andWhere('"inspectiongroup"."created_by" = :createdById', {
              createdById: Number(value),
            });
            break;
          case 'createdByIds':
            query.andWhere(
              '"inspectiongroup"."created_by" IN (:...createdByIds)',
              {
                createdByIds: value.split(',').map((id) => Number(id)),
              },
            );
            break;
          case 'createdAt':
            const createFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const createTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"inspectiongroup"."created_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: createFrom,
                dateTo: createTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"inspectiongroup"."updated_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: updateFrom,
                dateTo: upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'name':
            query.addOrderBy('"inspectiongroup"."name"', order);
            break;
          case 'code':
            query.addOrderBy('"inspectiongroup"."code"', order);
            break;
          case 'checkType':
            query.addOrderBy('"inspectiongroup"."check_type"', order);
            break;
          case 'chemicalType':
            query.addOrderBy('"inspectiongroup"."chemical_type"', order);
            break;
          case 'oqcDeviceType':
            query.addOrderBy('"inspectiongroup"."oqc_device_type"', order);
            break;
          case 'status':
            query.addOrderBy('"inspectiongroup"."status"', order);
            break;
          case 'qcRequestType':
            query.addOrderBy('"qrt"."id"', order);
            break;
          case 'qcRequestTypeName':
            query.addOrderBy('"qrt"."name"', order);
            break;
          case 'qcType':
            query.addOrderBy('"qrt"."qc_type"', order);
            break;
          case 'description':
            query.addOrderBy('"inspectiongroup"."description"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"inspectiongroup"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"inspectiongroup"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"inspectiongroup"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('inspectiongroup.id', 'DESC');
    }

    query
      .groupBy('inspectiongroup.id')
      .addGroupBy('inspectiongroup.code')
      .addGroupBy('inspectiongroup.name')
      .addGroupBy('inspectiongroup.qc_request_type_id')
      .addGroupBy('inspectiongroup.check_type')
      .addGroupBy('inspectiongroup.chemical_type')
      .addGroupBy('inspectiongroup.oqc_device_type')
      .addGroupBy('inspectiongroup.description')
      .addGroupBy('inspectiongroup.status')
      .addGroupBy('inspectiongroup.created_by')
      .addGroupBy('inspectiongroup.created_at')
      .addGroupBy('inspectiongroup.updated_by')
      .addGroupBy('inspectiongroup.updated_at')
      .addGroupBy('qrt.id')
      .addGroupBy('qrt.code')
      .addGroupBy('qrt.qc_type')
      .addGroupBy('qrt.name');

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const data = await query.getRawMany();
    const count = await query.getCount();

    return {
      data: data?.map((item) => {
        return {
          ...item,
          qcRequestType: parseJSONValueField(item.qcRequestType),
        };
      }),
      count: count,
    };
  }

  async getDetail(
    request: GetDetailInspectionGroupRequestDto,
  ): Promise<InspectionGroupEntity> {
    const { id } = request;

    const query = this.inspectionGroupRepository
      .createQueryBuilder('inspectiongroup')
      .select([
        'inspectiongroup.id AS id',
        'inspectiongroup.code AS "code"',
        'inspectiongroup.name AS "name"',
        'inspectiongroup.qc_request_type_id AS "qcRequestTypeId"',
        'inspectiongroup.chemical_type AS "chemicalType"',
        'inspectiongroup.check_type AS "checkType"',
        'inspectiongroup.oqc_device_type AS "oqcDeviceType"',
        'inspectiongroup.description AS "description"',
        'inspectiongroup.status AS "status"',
        'inspectiongroup.created_by AS "createdBy"',
        'inspectiongroup.created_at AS "createdAt"',
        'inspectiongroup.updated_by AS "updatedBy"',
        'inspectiongroup.updated_at AS "updatedAt"',
      ])
      .leftJoin(
        'qc_request_types',
        'qrt',
        'inspectiongroup.qc_request_type_id = qrt.id',
      )
      .addSelect([
        `CASE WHEN COUNT(qrt.id) = 0 THEN '{}' ELSE
          (SELECT qrt.id as id,qrt.code as code, qrt.name as name, qrt.qc_type as qcType FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "qcRequestType"`,
      ])
      .andWhere('"inspectiongroup"."id" = :id', {
        id: id,
      })
      .groupBy('inspectiongroup.id')
      .addGroupBy('inspectiongroup.code')
      .addGroupBy('inspectiongroup.name')
      .addGroupBy('inspectiongroup.qc_request_type_id')
      .addGroupBy('inspectiongroup.chemical_type')
      .addGroupBy('inspectiongroup.check_type')
      .addGroupBy('inspectiongroup.oqc_device_type')
      .addGroupBy('inspectiongroup.description')
      .addGroupBy('inspectiongroup.status')
      .addGroupBy('inspectiongroup.created_by')
      .addGroupBy('inspectiongroup.created_at')
      .addGroupBy('inspectiongroup.updated_by')
      .addGroupBy('inspectiongroup.updated_at')
      .addGroupBy('qrt.id')
      .addGroupBy('qrt.code')
      .addGroupBy('qrt.qc_type')
      .addGroupBy('qrt.name');

    const data = await query.getRawOne();

    return {
      ...data,
      id: Number(data.id),
      qcRequestTypeId: Number(data.qcRequestTypeId),
      checkType: Number(data.checkType),
      qcRequestType: parseJSONValueField(data.qcRequestType),
    };
  }

  async active(request: UpdateStatusInspectionGroupRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.inspectionGroupRepository
      .createQueryBuilder()
      .update(InspectionGroupEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(request: UpdateStatusInspectionGroupRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.inspectionGroupRepository
      .createQueryBuilder()
      .update(InspectionGroupEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async getInspectionGroupByInspectionTypeCodes(
    inspectionTypeCodes: string[],
  ): Promise<any> {
    const query = this.inspectionGroupRepository
      .createQueryBuilder('inspectiongroup')
      .select([
        'inspectiongroup.id AS id',
        'inspectiongroup.code AS "code"',
        'inspectiongroup.name AS "name"',
        'inspectiongroup.qc_request_type_id AS "qcRequestTypeId"',
        'inspectiongroup.chemical_type AS "chemicalType"',
        'inspectiongroup.check_type AS "checkType"',
        'inspectiongroup.oqc_device_type AS "oqcDeviceType"',
        'inspectiongroup.description AS "description"',
        'inspectiongroup.status AS "status"',
        'inspectiongroup.created_by AS "createdBy"',
        'inspectiongroup.created_at AS "createdAt"',
        'inspectiongroup.updated_by AS "updatedBy"',
        'inspectiongroup.updated_at AS "updatedAt"',
      ])
      .innerJoin(
        'inspection_types',
        'its',
        'inspectiongroup.id = its.inspection_group_id',
      )
      .andWhere('"its"."code" IN (:...inspectionTypeCodes)', {
        inspectionTypeCodes,
      });

    const data = await query.getRawMany();

    return {
      data,
    };
  }
}
