import {
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsO<PERSON>al,
  IsString,
  Matches,
  MaxLength,
} from 'class-validator';

export class ImportInspectionGroupRequestDto {
  @IsNotEmpty()
  @MaxLength(50)
  @Matches(/^[a-zA-Z0-9_.-]+$/)
  code: string;

  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @IsNotEmpty()
  @IsString()
  qcRequestTypeCode: string;

  @IsNotEmpty()
  @IsNumber()
  checkType: number;

  @IsOptional()
  @IsInt()
  chemicalType: number;

  @IsOptional()
  @IsInt()
  oqcDeviceType: number;

  @IsOptional()
  @MaxLength(255)
  description?: string;
}
