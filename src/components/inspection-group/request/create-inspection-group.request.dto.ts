import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsOptional, MaxLength } from 'class-validator';

export class CreateInspectionGroupRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  code: string;

  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  qcRequestTypeId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  chemicalType: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  checkType: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  oqcDeviceType: number;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  description?: string;
}
