import { Column, Entity } from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';

@Entity({ name: 'inspection_groups' })
export class InspectionGroupEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    unique: true,
    nullable: false,
    length: 255,
  })
  code: string;

  @Column({
    type: 'nvarchar',
    nullable: false,
    length: 255,
  })
  name: string;

  @Column({
    type: 'int',
    nullable: true,
  })
  qcRequestTypeId: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  chemicalType: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  checkType: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  oqcDeviceType: number;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  description: string;

  @Column({
    type: 'tinyint',
  })
  status: number;
}
