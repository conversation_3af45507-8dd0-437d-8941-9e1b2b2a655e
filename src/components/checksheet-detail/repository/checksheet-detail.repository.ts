import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { parseJSONValueField } from '../../../utils/common';
import { ChecksheetDetailEntity } from '../entities/checksheet-detail.entity';

@Injectable()
export class ChecksheetDetailRepository extends BaseAbstractRepository<ChecksheetDetailEntity> {
  constructor(
    @InjectRepository(ChecksheetDetailEntity)
    private readonly checksheetDetailRepository: Repository<ChecksheetDetailEntity>,
  ) {
    super(checksheetDetailRepository);
  }

  async findAllByChecksheetIds(checksheetIds: number[]): Promise<any> {
    const query = this.checksheetDetailRepository
      .createQueryBuilder('checksheetdetail')
      .select([
        'checksheetdetail.id AS "checksheetDetailId"',
        'checksheetdetail.checksheet_id AS "checksheetId"',
        'checksheetdetail.check_type AS "checkType"',
        'checksheetdetail.spec AS "spec"',
        'checksheetdetail.plus_above AS "plusAbove"',
        'checksheetdetail.minus_below AS "minusBelow"',
        'checksheetdetail.is_scoring AS "isScoring"',
        'checksheetdetail.scoring_scale AS "scoringScale"',
        'checksheetdetail.category AS "category"',
      ])
      .leftJoin('inspections', 'i', 'checksheetdetail.inspection_id = i.id')
      .leftJoin(
        'inspection_types',
        'it',
        'checksheetdetail.inspection_type_id = it.id',
      )
      .leftJoin(
        'inspection_groups',
        'ig',
        'checksheetdetail.inspection_group_id = ig.id',
      )
      .leftJoin('qc_request_types', 'qrt', 'ig.qc_request_type_id = qrt.id')
      .leftJoin('units', 'u', 'checksheetdetail.unit_id = u.id')
      .leftJoin(
        'evaluation_standard_types',
        'est',
        'checksheetdetail.evaluation_standard_type_id = est.id',
      )
      .addSelect([
        `CASE WHEN COUNT(i.id) = 0 THEN '{}' ELSE 
        (SELECT i.id as id, i.code as code, i.name as name, i.management_scope as managementScope FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "inspection"`,
        `CASE WHEN COUNT(it.id) = 0 THEN '{}' ELSE
          (SELECT it.id as id, it.code as code,it.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "inspectionType"`,
        `CASE WHEN COUNT(ig.id) = 0 THEN '{}' ELSE
          (SELECT ig.id as id,ig.code as code,ig.name as name,ig.check_type as checkType,ig.oqc_device_type as oqcDeviceType FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "inspectionGroup"`,
        `CASE WHEN COUNT(u.id) = 0 THEN '{}' ELSE 
        (SELECT u.id as id,u.code as code,u.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "unit"`,
        `CASE WHEN COUNT(est.id) = 0 THEN '{}' ELSE 
        (SELECT est.id as id, est.code as code, est.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "evaluationStandardType"`,
      ])
      .where('checksheetdetail.checksheet_id IN (:...checksheetIds)', {
        checksheetIds,
      })
      .addGroupBy('checksheetdetail.id')
      .addGroupBy('checksheetdetail.checksheet_id')
      .addGroupBy('checksheetdetail.check_type')
      .addGroupBy('checksheetdetail.spec')
      .addGroupBy('checksheetdetail.plus_above')
      .addGroupBy('checksheetdetail.minus_below')
      .addGroupBy('checksheetdetail.is_scoring')
      .addGroupBy('checksheetdetail.scoring_scale')
      .addGroupBy('checksheetdetail.category')
      .addGroupBy('i.id')
      .addGroupBy('i.code')
      .addGroupBy('i.name')
      .addGroupBy('i.management_scope')
      .addGroupBy('it.id')
      .addGroupBy('it.code')
      .addGroupBy('it.name')
      .addGroupBy('ig.id')
      .addGroupBy('ig.code')
      .addGroupBy('ig.name')
      .addGroupBy('ig.check_type')
      .addGroupBy('ig.oqc_device_type')
      .addGroupBy('u.id')
      .addGroupBy('u.code')
      .addGroupBy('u.name')
      .addGroupBy('est.id')
      .addGroupBy('est.code')
      .addGroupBy('est.name');

    const result = await query.getRawMany();
    return result?.map((item) => {
      return {
        ...item,
        inspection: parseJSONValueField(item.inspection),
        inspectionType: parseJSONValueField(item.inspectionType),
        inspectionGroup: parseJSONValueField(item.inspectionGroup),
        unit: parseJSONValueField(item.unit),
        evaluationStandardType: parseJSONValueField(
          item.evaluationStandardType,
        ),
      };
    });
  }

  async findAllDataByIds(checksheetDetailIds: number[]): Promise<any> {
    const query = this.checksheetDetailRepository
      .createQueryBuilder('checksheetdetail')
      .select([
        'checksheetdetail.id AS "id"',
        'checksheetdetail.spec AS "spec"',
        'checksheetdetail.check_type AS "checkType"',
        'checksheetdetail.plus_above AS "plusAbove"',
        'checksheetdetail.minus_below AS "minusBelow"',
        'checksheetdetail.is_scoring AS "isScoring"',
        'checksheetdetail.scoring_scale AS "scoringScale"',
        'checksheetdetail.category AS "category"',
      ])
      .leftJoin('inspections', 'i', 'checksheetdetail.inspection_id = i.id')
      .leftJoin(
        'inspection_types',
        'it',
        'checksheetdetail.inspection_type_id = it.id',
      )
      .leftJoin(
        'inspection_groups',
        'ig',
        'checksheetdetail.inspection_group_id = ig.id',
      )
      .leftJoin('qc_request_types', 'qrt', 'ig.qc_request_type_id = qrt.id')
      .leftJoin('units', 'u', 'checksheetdetail.unit_id = u.id')
      .leftJoin(
        'evaluation_standard_types',
        'est',
        'checksheetdetail.evaluation_standard_type_id = est.id',
      )
      .addSelect([
        `CASE WHEN COUNT(i.id) = 0 THEN '{}' ELSE 
        (SELECT i.id as id, i.code as code, i.name as name, i.management_scope as managementScope FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "inspection"`,
        `CASE WHEN COUNT(it.id) = 0 THEN '{}' ELSE
          (SELECT it.id as id, it.code as code,it.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "inspectionType"`,
        `CASE WHEN COUNT(ig.id) = 0 THEN '{}' ELSE
          (SELECT ig.id as id,ig.code as code,ig.name as name,ig.check_type as checkType,ig.oqc_device_type as oqcDeviceType FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "inspectionGroup"`,
        `CASE WHEN COUNT(u.id) = 0 THEN '{}' ELSE 
        (SELECT u.id as id,u.code as code,u.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "unit"`,
        `CASE WHEN COUNT(est.id) = 0 THEN '{}' ELSE 
        (SELECT est.id as id, est.code as code, est.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "evaluationStandardType"`,
      ])
      .where('checksheetdetail.id IN (:...checksheetDetailIds)', {
        checksheetDetailIds,
      })
      .addGroupBy('checksheetdetail.id')
      .addGroupBy('checksheetdetail.spec')
      .addGroupBy('checksheetdetail.check_type')
      .addGroupBy('checksheetdetail.plus_above')
      .addGroupBy('checksheetdetail.minus_below')
      .addGroupBy('checksheetdetail.is_scoring')
      .addGroupBy('checksheetdetail.scoring_scale')
      .addGroupBy('checksheetdetail.category')
      .addGroupBy('i.id')
      .addGroupBy('i.code')
      .addGroupBy('i.name')
      .addGroupBy('i.management_scope')
      .addGroupBy('it.id')
      .addGroupBy('it.code')
      .addGroupBy('it.name')
      .addGroupBy('ig.id')
      .addGroupBy('ig.code')
      .addGroupBy('ig.name')
      .addGroupBy('ig.check_type')
      .addGroupBy('ig.oqc_device_type')
      .addGroupBy('u.id')
      .addGroupBy('u.code')
      .addGroupBy('u.name')
      .addGroupBy('est.id')
      .addGroupBy('est.code')
      .addGroupBy('est.name');

    const result = await query.getRawMany();
    return result?.map((item) => {
      return {
        ...item,
        inspection: parseJSONValueField(item.inspection),
        inspectionType: parseJSONValueField(item.inspectionType),
        inspectionGroup: parseJSONValueField(item.inspectionGroup),
        unit: parseJSONValueField(item.unit),
        evaluationStandardType: parseJSONValueField(
          item.evaluationStandardType,
        ),
      };
    });
  }

  async getChecksheetDetailErrors(ids: number[]): Promise<any> {
    const data = await this.entity
      .createQueryBuilder('cd')
      .innerJoinAndSelect('cd.errors', 'cde')
      .leftJoin('errors', 'e', 'cde.error_id = e.id')
      .select([
        'cd.id as checksheetDetailId',
        'cde.id as id',
        'cde.error_id as errorId',
        `CASE WHEN COUNT(e.id) = 0 THEN '{}' ELSE
        (SELECT e.id as id,e.code as code,e.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "error"`,
      ])
      .where('cd.id IN (:...ids)', { ids })
      .groupBy('cd.id, cde.id, cde.error_id, e.id, e.code, e.name')
      .getRawMany();

    return data?.map((item) => {
      return {
        ...item,
        error: parseJSONValueField(item.error),
      };
    });
  }

  async findAllChecksheetByErrorIds(errorIds: number[]): Promise<any> {
    const data = await this.entity
      .createQueryBuilder('cd')
      .innerJoinAndSelect('cd.errors', 'cde')
      .where('cde.error_id IN (:...errorIds)', { errorIds })
      .getRawMany();

    return data;
  }

  async getInspectionByChecksheetIdsAndCheckType(
    checksheetIds: number[],
    checkTypes: number[],
  ): Promise<any> {
    const query = this.checksheetDetailRepository
      .createQueryBuilder('checksheetdetail')
      .leftJoin('inspections', 'i', 'checksheetdetail.inspection_id = i.id')
      .select([
        `CASE WHEN COUNT(i.id) = 0 THEN '{}' ELSE 
        (SELECT i.id as id, i.code as code, i.name as name, i.management_scope as managementScope FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "inspection"`,
      ])
      .where(
        'checksheetdetail.checksheet_id IN (:...checksheetIds) AND checksheetdetail.check_type Not IN (:...checkTypes)',
        {
          checksheetIds,
          checkTypes,
        },
      )
      .groupBy('checksheetdetail.checksheet_id')
      .addGroupBy('checksheetdetail.inspection_id')
      .addGroupBy('checksheetdetail.check_type')
      .addGroupBy('i.id')
      .addGroupBy('i.code')
      .addGroupBy('i.name')
      .addGroupBy('i.management_scope');

    const result = await query.getRawMany();
    return result?.map((item) => {
      return {
        inspection: parseJSONValueField(item.inspection),
      };
    });
  }

  async getChecksheetDetailIdsByInspectionIds(
    inspectionIds: number[],
  ): Promise<number[]> {
    const query = this.checksheetDetailRepository
      .createQueryBuilder('checksheetdetail')
      .select(['checksheetdetail.id as checksheetDetailId']);

    if (inspectionIds?.length) {
      query.andWhere('checksheetdetail.inspection_id IN (:...inspectionIds)', {
        inspectionIds,
      });
    }

    const data = await query.getRawMany();
    return data.map((item) => item.checksheetDetailId);
  }
}
