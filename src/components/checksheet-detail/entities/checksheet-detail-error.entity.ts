import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ChecksheetDetailEntity } from './checksheet-detail.entity';

@Entity({ name: 'checksheet_detail_errors' })
export class ChecksheetDetailErrorEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  checksheetDetailId: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  errorId: number;

  @ManyToOne(() => ChecksheetDetailEntity, (s) => s.errors, {
    orphanedRowAction: 'delete',
  })
  @JoinColumn({ name: 'checksheet_detail_id', referencedColumnName: 'id' })
  checksheetDetail: ChecksheetDetailEntity;
}
