import {
  Column,
  <PERSON><PERSON><PERSON>,
  <PERSON>in<PERSON><PERSON>um<PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ChecksheetEntity } from '../../checksheet/entities/checksheet.entity';
import { ChecksheetDetailErrorEntity } from './checksheet-detail-error.entity';

@Entity({ name: 'checksheet_details' })
export class ChecksheetDetailEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  checksheetId: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  checkType: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  inspectionGroupId: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  inspectionTypeId: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  inspectionId: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  unitId: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  evaluationStandardTypeId: number;

  @Column({
    type: 'float',
    nullable: true,
  })
  spec: number;

  @Column({
    type: 'float',
    nullable: true,
  })
  plusAbove: number;

  @Column({
    type: 'float',
    nullable: true,
  })
  minusBelow: number;

  @Column({
    type: 'tinyint',
    default: 0,
  })
  isScoring: number;

  @Column({
    type: 'float',
    nullable: true,
  })
  scoringScale: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  category: number;

  @ManyToOne(() => ChecksheetEntity, (s) => s.dataDetails, {
    orphanedRowAction: 'delete',
  })
  @JoinColumn({ name: 'checksheet_id', referencedColumnName: 'id' })
  checksheet: ChecksheetEntity;

  @OneToMany(() => ChecksheetDetailErrorEntity, (s) => s.checksheetDetail, {
    cascade: ['insert', 'update'],
    eager: true,
  })
  @JoinColumn({ name: 'id', referencedColumnName: 'checksheet_detail_id' })
  errors: ChecksheetDetailErrorEntity[];
}
