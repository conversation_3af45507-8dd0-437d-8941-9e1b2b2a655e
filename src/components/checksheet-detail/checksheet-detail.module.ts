import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { ChecksheetDetailEntity } from './entities/checksheet-detail.entity';
import { ChecksheetDetailRepository } from './repository/checksheet-detail.repository';
import { ChecksheetDetailService } from './service/checksheet-detail.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([ChecksheetDetailEntity]),
    AnotherServiceModule,
  ],
  providers: [ChecksheetDetailService, ChecksheetDetailRepository],
  exports: [ChecksheetDetailService],
  controllers: [],
})
export class ChecksheetDetailModule {}
