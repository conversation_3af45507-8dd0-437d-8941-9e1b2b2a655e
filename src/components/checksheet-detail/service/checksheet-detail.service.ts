import { Injectable, Logger } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { In } from 'typeorm';

import { isEmpty } from 'lodash';
import { BaseService } from '../../../common/service/base.service';
import { CHECK_TYPE_ENUM } from '../../../constant/common';
import { UserService } from '../../another-service/services/user-service';
import { ChecksheetDetailEntity } from '../entities/checksheet-detail.entity';
import { ChecksheetDetailRepository } from '../repository/checksheet-detail.repository';

@Injectable()
export class ChecksheetDetailService extends BaseService {
  private readonly logger = new Logger(ChecksheetDetailService.name);

  constructor(
    private readonly checksheetDetailRepository: ChecksheetDetailRepository,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,
  ) {
    super(userService);
  }

  async deleteByChecksheetIds(checksheetIds: number[]): Promise<any> {
    const filterCondition = {
      checksheetId: In(checksheetIds),
    };
    return await this.checksheetDetailRepository.removeByCondition(
      filterCondition,
    );
  }

  async findAllByChecksheetIds(checksheetIds: number[]): Promise<any> {
    // Không chia batch cho findAllByChecksheetIds
    const checksheetDetail =
      await this.checksheetDetailRepository.findAllByChecksheetIds(
        checksheetIds,
      );

    if (isEmpty(checksheetDetail)) return [];

    const checksheetDetailIds: number[] = Array.from(
      new Set(checksheetDetail?.flatMap((item) => +item.checksheetDetailId)),
    );

    const BATCH_SIZE = 1000;
    const batches = [];
    for (let i = 0; i < checksheetDetailIds.length; i += BATCH_SIZE) {
      batches.push(checksheetDetailIds.slice(i, i + BATCH_SIZE));
    }

    const errorsPromises = batches.map((batch) =>
      this.checksheetDetailRepository.getChecksheetDetailErrors(batch),
    );
    const errorsResults = await Promise.all(errorsPromises);
    const errors = errorsResults.flat();
    const errorMap = errors?.reduce((acc, item) => {
      const key = `${item.checksheetDetailId}`;
      if (isEmpty(acc[key])) {
        acc[key] = { errors: [item] };
      } else {
        acc[key].errors.push(item);
      }
      return acc;
    }, {});

    return (
      checksheetDetail?.map((item) => {
        return {
          ...item,
          errors: errorMap[item.checksheetDetailId]?.errors || [],
        };
      }) || []
    );
  }

  async findAllByUnitIds(unitIds: number[]): Promise<any> {
    const filterCondition = {
      unitId: In(unitIds),
    };
    return await this.checksheetDetailRepository.findByCondition(
      filterCondition,
    );
  }

  async findAllByInspectionIds(inspectionIds: number[]): Promise<any> {
    const filterCondition = {
      inspectionId: In(inspectionIds),
    };
    return await this.checksheetDetailRepository.findByCondition(
      filterCondition,
    );
  }

  async findAllByIds(ids: number[]): Promise<ChecksheetDetailEntity[]> {
    return await this.checksheetDetailRepository.findAllByIds(ids);
  }

  async getDetailList(ids: number[]): Promise<any[]> {
    const checksheetDetail =
      await this.checksheetDetailRepository.findAllDataByIds(ids);

    if (isEmpty(checksheetDetail)) return [];

    const checksheetDetailIds: number[] = Array.from(
      new Set(checksheetDetail?.flatMap((item) => +item.id)),
    );

    const errors =
      await this.checksheetDetailRepository.getChecksheetDetailErrors(
        checksheetDetailIds,
      );

    const errorMap = errors?.reduce((acc, item) => {
      const key = `${item.checksheetDetailId}`;
      if (isEmpty(acc[key])) {
        acc[key] = { errors: [item] };
      } else {
        acc[key].errors.push(item);
      }
      return acc;
    }, {});

    return (
      checksheetDetail?.map((item) => {
        return {
          ...item,
          errors: errorMap[item.id]?.errors || [],
        };
      }) || []
    );
  }

  async getInspectionByChecksheetIdsAndCheckType(
    checksheetIds: number[],
  ): Promise<any> {
    const checkTypes = [
      CHECK_TYPE_ENUM.APPEARANCE,
      CHECK_TYPE_ENUM.ENVIRONMENTAL_TOXICITY,
    ];

    const data =
      await this.checksheetDetailRepository.getInspectionByChecksheetIdsAndCheckType(
        checksheetIds,
        checkTypes,
      );

    return data;
  }

  async getChecksheetDetailIdsByInspectionIds(
    inspectionIds: number[],
  ): Promise<number[]> {
    return await this.checksheetDetailRepository.getChecksheetDetailIdsByInspectionIds(
      inspectionIds,
    );
  }
}
