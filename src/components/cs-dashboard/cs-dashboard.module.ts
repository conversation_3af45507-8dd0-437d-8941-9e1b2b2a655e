import { Module } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { FinanceYearModule } from '../finance-year/finance-year.module';
import { CsDashboardController } from './controller/cs-dashboard.controller';
import { CsDashboardService } from './service/cs-dashboard.service';

@Module({
  imports: [AnotherServiceModule, FinanceYearModule, BaseProcessModule],
  providers: [CsDashboardService],
  exports: [CsDashboardService],
  controllers: [CsDashboardController],
})
export class CsDashboardModule {}
