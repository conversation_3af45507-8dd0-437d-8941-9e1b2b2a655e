import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import { VIEW_CS_DASHBOARD_PERMISSION } from '../../../utils/permissions/cs-dashboard.permission';
import { GetListCsDashboardRequestDto } from '../request/get-list-cs-dashboard.request.dto';
import { CsDashboardService } from '../service/cs-dashboard.service';

@Controller('cs-dashboards')
export class CsDashboardController {
  constructor(private readonly csDashboardService: CsDashboardService) {}

  @PermissionCode(VIEW_CS_DASHBOARD_PERMISSION.code)
  @Get('/get-exported-lot-quantity')
  @ApiOperation({
    tags: ['Cs-dashboards'],
    summary: '<PERSON><PERSON>y dữ liệu số LOT Xuất hàng',
    description: '<PERSON><PERSON>y dữ liệu số LOT Xuất hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getExportedLotQuantity(
    @Query() query: GetListCsDashboardRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.csDashboardService.getExportedLotQuantity(request);
  }

  @PermissionCode(VIEW_CS_DASHBOARD_PERMISSION.code)
  @Get('/get-voc-summary')
  @ApiOperation({
    tags: ['Cs-dashboards'],
    summary: 'Get VOC Summary',
    description: 'Get VOC Summary',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getVocSummary(
    @Query() query: GetListCsDashboardRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.csDashboardService.getVocSummary(request);
  }

  @PermissionCode(VIEW_CS_DASHBOARD_PERMISSION.code)
  @Get('/get-total-customer-claim-fee')
  @ApiOperation({
    tags: ['Cs-dashboards'],
    summary: 'Get Total Customer Claim Fee',
    description: 'Get Total Customer Claim Fee',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getTotalCustomerClaimFee(
    @Query() query: GetListCsDashboardRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.csDashboardService.getTotalCustomerClaimFee(request);
  }

  @PermissionCode(VIEW_CS_DASHBOARD_PERMISSION.code)
  @Get('/get-top-exported-lot-by-item-lines')
  @ApiOperation({
    tags: ['Cs-dashboards'],
    summary: 'Get Top Exported Lot By Item Lines',
    description: 'Get Top Exported Lot By Item Lines',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getTopExportedLotByItemLines(
    @Query() query: GetListCsDashboardRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return this.csDashboardService.getTopExportedLotByItemLines(request);
  }

  @PermissionCode(VIEW_CS_DASHBOARD_PERMISSION.code)
  @Get('/get-top-rate-ng-items')
  @ApiOperation({
    tags: ['Cs-dashboards'],
    summary: 'Get Top Rate NG Items',
    description: 'Get Top Rate NG Items',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getTopRateNgItems(
    @Query() query: GetListCsDashboardRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.csDashboardService.getTopRateNgItems(request);
  }

  @PermissionCode(VIEW_CS_DASHBOARD_PERMISSION.code)
  @Get('/get-top-rate-total-claim-fee-by-customers')
  @ApiOperation({
    tags: ['Cs-dashboards'],
    summary: 'Get Top Rate NG Items',
    description: 'Get Top Rate NG Items',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getTopRateTotalClaimFeeByCustomers(
    @Query() query: GetListCsDashboardRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.csDashboardService.getTopRateTotalClaimFeeByCustomers(request);
  }

  @PermissionCode(VIEW_CS_DASHBOARD_PERMISSION.code)
  @Get('/get-top-voc-by-item-lines')
  @ApiOperation({
    tags: ['Cs-dashboards'],
    summary: 'Get Top VOC By Item Lines',
    description: 'Get Top VOC By Item Lines',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getTopVocByItemLines(
    @Query() query: GetListCsDashboardRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return this.csDashboardService.getTopVocByItemLines(request);
  }

  @PermissionCode(VIEW_CS_DASHBOARD_PERMISSION.code)
  @Get('/get-top-worst-errors')
  @ApiOperation({
    tags: ['Cs-dashboards'],
    summary: 'Get Top Worst Errors',
    description: 'Get Top Worst Errors',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getTopWorstErrors(
    @Query() query: GetListCsDashboardRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.csDashboardService.getTopWorstErrors(request);
  }
}
