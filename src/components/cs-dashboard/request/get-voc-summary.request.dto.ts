import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { <PERSON>Array, IsNumber, IsOptional } from 'class-validator';

export class GetVocSummaryRequestDto {
  @ApiPropertyOptional({ type: [Number], description: 'List of Customer IDs' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  customerIds: number[];

  @ApiPropertyOptional({ type: [Number], description: 'List of Item Line IDs' })
  @IsOptional()
  @IsArray()
  @Type(() => Number)
  @IsNumber({}, { each: true })
  itemLineIds: number[];

  @ApiPropertyOptional()
  @IsOptional()
  startDate: Date;

  @ApiPropertyOptional()
  @IsOptional()
  endDate: Date;

  @ApiPropertyOptional()
  @IsOptional()
  skip: number;

  @ApiPropertyOptional()
  @IsOptional()
  take: number;

  @ApiPropertyOptional()
  @IsOptional()
  timePeriod?: string[];
}
