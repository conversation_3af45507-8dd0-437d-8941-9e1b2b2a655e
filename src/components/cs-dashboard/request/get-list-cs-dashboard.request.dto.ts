import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsArray, IsEnum, IsNumber, IsOptional } from 'class-validator';
import { PaginationQuery } from '../../../utils/dto/request/pagination.query';
import { PERIOD_ENUM } from '../../sales-quality-report/sales-quality-report.constant';

export class GetListCsDashboardRequestDto extends PaginationQuery {
  @ApiPropertyOptional({ type: [Number], description: 'List of Customer IDs' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  @Transform(({ value }) => (Array.isArray(value) ? value : [value]), {
    toClassOnly: true,
  })
  customerIds: number[];

  @ApiPropertyOptional()
  @IsOptional()
  year: number = new Date().getUTCFullYear();

  @ApiPropertyOptional({ enum: PERIOD_ENUM, type: Number })
  @IsOptional()
  @Type(() => Number)
  @IsEnum(PERIOD_ENUM)
  reportPeriod: PERIOD_ENUM;

  @ApiPropertyOptional()
  @IsOptional()
  timePoint: number;

  @ApiPropertyOptional({ type: [Number], description: 'List of Item Line IDs' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  @Transform(({ value }) => (Array.isArray(value) ? value : [value]), {
    toClassOnly: true,
  })
  itemLineIds: number[];
}
