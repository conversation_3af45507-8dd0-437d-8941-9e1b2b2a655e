import { Injectable, Logger } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';

import { GetListCsDashboardRequestDto } from './../request/get-list-cs-dashboard.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { BaseService } from '../../../common/service/base.service';
import { QmsxCustomerSupportService } from '../../another-service/services/qmsx-customer-support-service';
import { QmsxReportService } from '../../another-service/services/qmsx-report-service';
import { UserService } from '../../another-service/services/user-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { FinanceYearService } from '../../finance-year/service/finance-year.service';
import { PERIOD_ENUM } from '../../sales-quality-report/sales-quality-report.constant';
import { GetVocSummaryRequestDto } from '../request/get-voc-summary.request.dto';

@Injectable()
export class CsDashboardService extends BaseService {
  private readonly logger = new Logger(CsDashboardService.name);

  constructor(
    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly qmsxReportService: QmsxReportService,

    private readonly financeYearService: FinanceYearService,

    private readonly qmsxCustomerSupportService: QmsxCustomerSupportService,

    private readonly baseService: BaseProcessService,
  ) {
    super(userService);
  }

  async getExportedLotQuantity(
    request: GetListCsDashboardRequestDto,
  ): Promise<any> {
    const exportedLotQuantity =
      await this.qmsxReportService.getExportedLotQuantityForCsDashboard(
        request,
      );
    return new ResponseBuilder(exportedLotQuantity)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getVocSummary(request: GetListCsDashboardRequestDto): Promise<any> {
    const { customerIds, itemLineIds, year, timePoint, reportPeriod } = request;
    const { startDate, endDate } =
      await this.financeYearService.findTimeFrameByTimePoint(
        timePoint,
        year,
        reportPeriod,
      );
    const requestVocSummary: GetVocSummaryRequestDto = {
      customerIds: customerIds,
      itemLineIds: itemLineIds,
      startDate: startDate,
      endDate: endDate,
      take: request.take,
      skip: request.skip,
    };
    const vocSummary = await this.qmsxCustomerSupportService.getVocSummary(
      requestVocSummary,
    );
    return new ResponseBuilder(vocSummary)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getTotalCustomerClaimFee(
    request: GetListCsDashboardRequestDto,
  ): Promise<any> {
    const { customerIds, itemLineIds, year, timePoint, reportPeriod } = request;
    const { startDate, endDate, month } =
      await this.financeYearService.findTimeFrameByTimePoint(
        timePoint,
        year,
        reportPeriod,
      );
    const formatMonth = (num: number) => String(num).padStart(2, '0');

    const timePeriod =
      reportPeriod === PERIOD_ENUM.WEEK && timePoint != null
        ? [`${formatMonth(month)}/${year}`]
        : reportPeriod === PERIOD_ENUM.MONTH && timePoint != null
        ? [`${formatMonth(timePoint)}/${year}`]
        : Array.from({ length: 12 }, (_, i) => `${formatMonth(i + 1)}/${year}`);
    const requestVocSummary: GetVocSummaryRequestDto = {
      customerIds: customerIds,
      itemLineIds: itemLineIds,
      startDate: startDate,
      endDate: endDate,
      skip: request.skip,
      take: request.take,
      timePeriod: timePeriod,
    };
    const vocSummary =
      await this.qmsxCustomerSupportService.getTotalCustomerClaimFee(
        requestVocSummary,
      );
    return new ResponseBuilder(vocSummary)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getTopExportedLotByItemLines(
    request: GetListCsDashboardRequestDto,
  ): Promise<any> {
    const data = await this.qmsxReportService.getTopExportedLotByItemLines(
      request,
    );

    const dataMap = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder(dataMap)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getTopRateNgItems(request: GetListCsDashboardRequestDto): Promise<any> {
    const data = await this.qmsxReportService.getTopRateNgItems(request);

    const dataMap = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder(dataMap)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getTopRateTotalClaimFeeByCustomers(
    request: GetListCsDashboardRequestDto,
  ): Promise<any> {
    const { customerIds, itemLineIds, year, timePoint, reportPeriod } = request;
    const { startDate, endDate, month } =
      await this.financeYearService.findTimeFrameByTimePoint(
        timePoint,
        year,
        reportPeriod,
      );
    const formatMonth = (num: number) => String(num).padStart(2, '0');

    const timePeriod =
      reportPeriod === PERIOD_ENUM.WEEK && timePoint != null
        ? [`${formatMonth(month)}/${year}`]
        : reportPeriod === PERIOD_ENUM.MONTH && timePoint != null
        ? [`${formatMonth(timePoint)}/${year}`]
        : Array.from({ length: 12 }, (_, i) => `${formatMonth(i + 1)}/${year}`);
    const requestVocSummary: GetVocSummaryRequestDto = {
      customerIds: customerIds,
      itemLineIds: itemLineIds,
      startDate: startDate,
      endDate: endDate,
      take: request.take,
      skip: request.skip,
      timePeriod: timePeriod,
    };
    const data =
      await this.qmsxCustomerSupportService.getTopRateTotalClaimFeeByCustomers(
        requestVocSummary,
      );

    const dataMap = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder(dataMap)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getTopVocByItemLines(
    request: GetListCsDashboardRequestDto,
  ): Promise<any> {
    const { customerIds, itemLineIds, year, timePoint, reportPeriod } = request;
    const { startDate, endDate } =
      await this.financeYearService.findTimeFrameByTimePoint(
        timePoint,
        year,
        reportPeriod,
      );
    const requestTopVocByItemLines: GetVocSummaryRequestDto = {
      customerIds: customerIds,
      itemLineIds: itemLineIds,
      startDate: startDate,
      endDate: endDate,
      take: request.take,
      skip: request.skip,
    };
    const data = await this.qmsxCustomerSupportService.getTopVocByItemLines(
      requestTopVocByItemLines,
    );

    const dataMap = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder(dataMap)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getTopWorstErrors(request: GetListCsDashboardRequestDto): Promise<any> {
    const { customerIds, itemLineIds, year, timePoint, reportPeriod } = request;
    const { startDate, endDate } =
      await this.financeYearService.findTimeFrameByTimePoint(
        timePoint,
        year,
        reportPeriod,
      );
    const requestVocSummary: GetVocSummaryRequestDto = {
      customerIds: customerIds,
      itemLineIds: itemLineIds,
      startDate: startDate,
      endDate: endDate,
      take: request.take,
      skip: request.skip,
    };
    const data = await this.qmsxCustomerSupportService.getTopWorstErrors(
      requestVocSummary,
    );

    const dataMap = await this.baseService.mapMasterInfoToResponse(
      data.topWorstError,
    );

    return new ResponseBuilder({
      totalErrorQuantity: data.totalErrorQuantity,
      topWorstError: dataMap,
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
