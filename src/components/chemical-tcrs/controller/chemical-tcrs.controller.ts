import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';

import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';
import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  DELETE_CHEMICAL_TCRS_PERMISSION,
  EXPORT_CHEMICAL_TCRS_PERMISSION,
  LIST_CHEMICAL_TCRS_PERMISSION,
  UPDATE_CHEMICAL_TCRS_PERMISSION,
} from '../../../utils/permissions/chemical-tcrs.permission';
import { DeleteChemicalTcrsRequestDto } from '../request/delete-chemical-tcrs.request.dto';
import { GetDetailChemicalTcrsByKeyRequestDto } from '../request/get-detail-chemical-tcrs-by-key.request.dto';
import { GetListChemicalTcrsRequestDto } from '../request/get-list-chemical-tcrs.request.dto';
import { UpdateChemicalTcrsRequestDto } from '../request/update-chemical-tcrs.request.dto';
import { ChemicalTcrsService } from '../service/chemical-tcrs.service';

@Controller('chemical-tcrs')
export class ChemicalTcrsController {
  constructor(private readonly chemicalTcrsService: ChemicalTcrsService) {}

  @Post('/detail-by-key')
  @ApiOperation({
    tags: ['Chemical-Tcrs'],
    summary: 'Chi tiết Chemicals-Tcrs',
    description: 'Chi tiết Chemicals-Tcrs',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetailByKey(
    @Body() payload: GetDetailChemicalTcrsByKeyRequestDto,
  ): Promise<any> {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalTcrsService.getDetailByKey(request);
  }

  @PermissionCode(LIST_CHEMICAL_TCRS_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Chemical-Tcrs'],
    summary: 'Danh sách Chemical-Tcrs',
    description: 'Danh sách Chemical-Tcrs',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(@Query() query: GetListChemicalTcrsRequestDto): Promise<any> {
    const { request, responseError } = query;

    if (responseError && responseError?.statusCode && !isEmpty(responseError)) {
      return responseError;
    }

    return this.chemicalTcrsService.getList(request);
  }

  //@PermissionCode(CREATE_CHEMICAL_TCRS_PERMISSION.code)
  // @Post('/create')
  // @ApiOperation({
  //   tags: ['Chemical-Tcrs'],
  //   summary: 'Tạo Chemical-Tcrs mới',
  //   description: 'Tạo Chemical-Tcrs mới',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Thành công',
  // })
  // public create(@Body() payload: CreateChemicalTcrsRequestDto) {
  //   const { request, responseError } = payload;

  //   if (responseError && responseError?.statusCode && !isEmpty(responseError)) {
  //     return responseError;
  //   }

  //   return this.chemicalTcrsService.create(request);
  // }

  @PermissionCode(UPDATE_CHEMICAL_TCRS_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Chemical-Tcrs'],
    summary: 'Cập nhật Chemical-Tcrs',
    description: 'Cập nhật Chemical-Tcrs',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateChemicalTcrsRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && responseError?.statusCode && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalTcrsService.update(request);
  }

  @PermissionCode(EXPORT_CHEMICAL_TCRS_PERMISSION.code)
  @Get('/export')
  @ApiOperation({
    tags: ['Export-excel'],
    summary: 'Export in list screen',
    description: 'Export in list screen',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public export(@Query() query: GetListChemicalTcrsRequestDto): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return this.chemicalTcrsService.export(request);
  }

  @PermissionCode(DELETE_CHEMICAL_TCRS_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Chemical-Tcrs'],
    summary: 'Xóa Chemical-Tcrs',
    description: 'Xóa Chemical-Tcrs',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(@Param() param: DeleteChemicalTcrsRequestDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.chemicalTcrsService.delete(request);
  }
}
