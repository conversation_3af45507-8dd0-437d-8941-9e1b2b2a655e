import { Injectable, Logger } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { isEmpty } from 'lodash';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { exportExcel } from '../../../helper/export.helper';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';
import { addSevenHoursToAllItems } from '../../../utils/helper';
import { ResponseBuilder } from '../../../utils/response-builder';
import { QmsxTcrsService } from '../../another-service/services/qmsx-tcrs-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { ExportExcelColumnProperty } from '../../export-excel/dto/export-excel-column-property.dto';
import {
  chemicalTcrsColumnProperties,
  chemicalTcrsExportSheets,
} from '../../export-excel/template/export-chemical-tcrs-template';
import { ChemicalTcrsStatusEnum } from '../chemical-tcrs-status.enum';
import { CreateChemicalTcrsRequestDto } from '../request/create-chemical-tcrs.request.dto';
import { DeleteChemicalTcrsRequestDto } from '../request/delete-chemical-tcrs.request.dto';
import { GetDetailChemicalTcrsByKeyRequestDto } from '../request/get-detail-chemical-tcrs-by-key.request.dto';
import { GetListChemicalTcrsRequestDto } from '../request/get-list-chemical-tcrs.request.dto';
import { UpdateChemicalTcrsData } from '../request/update-chemical-tcrs-data';
import { UpdateChemicalTcrsRequestDto } from '../request/update-chemical-tcrs.request.dto';

@Injectable()
export class ChemicalTcrsService {
  private readonly logger = new Logger(ChemicalTcrsService.name);

  constructor(
    private readonly i18n: I18nService,
    private readonly baseService: BaseProcessService,
    protected readonly qmsxTcrsService: QmsxTcrsService,
  ) {}

  async create(request: CreateChemicalTcrsRequestDto): Promise<any> {
    request.status = ChemicalTcrsStatusEnum.PENDING;
    request.createdBy = request.userId;
    const response = await this.qmsxTcrsService.createChemicalTcrs(request);
    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }
    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage('success.SUCCESS')
      .build();
  }

  async update(request: UpdateChemicalTcrsRequestDto): Promise<any> {
    const chemicalTcrs = await this.qmsxTcrsService.getDetailChemicalTcrsById({
      id: request.id,
    });
    if (isEmpty(chemicalTcrs)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const date1 = new Date(request.resolvedDate);
    const date2 = new Date(chemicalTcrs.measurementDate);

    const day1 =
      date1.getUTCFullYear() +
      '-' +
      (date1.getUTCMonth() + 1).toString().padStart(2, '0') +
      '-' +
      date1.getUTCDate().toString().padStart(2, '0');
    const day2 =
      date2.getUTCFullYear() +
      '-' +
      (date2.getUTCMonth() + 1).toString().padStart(2, '0') +
      '-' +
      date2.getUTCDate().toString().padStart(2, '0');

    if (day1 < day2) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.INVALID_RESOLVED_DATE'))
        .build();
    }

    const updateRequest = new UpdateChemicalTcrsData();
    Object.assign(updateRequest, chemicalTcrs);
    updateRequest.updatedBy = request.userId;
    updateRequest.resolvedDate = request.resolvedDate;
    updateRequest.resolvedBy = request.resolvedBy;
    updateRequest.resolvedContent = request.resolvedContent;
    updateRequest.status = ChemicalTcrsStatusEnum.PROCESSED;

    const response = await this.qmsxTcrsService.updateChemicalTcrs(
      updateRequest,
    );
    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListChemicalTcrsRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.qmsxTcrsService.getListChemicalTcrs(
      request,
    );

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetailByKey(
    request: GetDetailChemicalTcrsByKeyRequestDto,
  ): Promise<any> {
    const chemical = await this.qmsxTcrsService.getDetailChemicalTcrsByKey(
      request,
    );

    return new ResponseBuilder(chemical)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteChemicalTcrsRequestDto): Promise<any> {
    const chemicalTcrs = await this.qmsxTcrsService.getDetailChemicalTcrsById({
      id: request.id,
    });
    if (isEmpty(chemicalTcrs)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const { result, messageError } =
      await this.qmsxTcrsService.deleteChemicalTcrs(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async export(request: GetListChemicalTcrsRequestDto): Promise<any> {
    const { data } = await this.getList(request);
    const dataMapping = addSevenHoursToAllItems(data);
    const mapData = new Map<string, any[]>();
    const mapColumn = new Map<string, ExportExcelColumnProperty[]>();
    mapColumn.set(chemicalTcrsExportSheets[0], chemicalTcrsColumnProperties);
    mapData.set(chemicalTcrsExportSheets[0], dataMapping.items);
    const fileName = await this.i18n.translate('export.chemicalTcrs.fileName');
    const buffer = await exportExcel(
      chemicalTcrsExportSheets,
      mapData,
      mapColumn,
      fileName,
      this.i18n,
    );
    return new ResponseBuilder(buffer)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
