import { Module } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { ChemicalTcrsController } from './controller/chemical-tcrs.controller';
import { ChemicalTcrsService } from './service/chemical-tcrs.service';

@Module({
  imports: [AnotherServiceModule, BaseProcessModule, ChemicalTcrsModule],
  providers: [ChemicalTcrsService],
  exports: [ChemicalTcrsService],
  controllers: [ChemicalTcrsController],
})
export class ChemicalTcrsModule {}
