import { BaseDto } from '../../../core/dto/base.dto';

export class UpdateChemicalTcrsData extends BaseDto {
  id?: number;

  updatedBy?: number;

  measurementDate: Date;

  workshift: number;

  processId: number;

  deviceTypeId: number;

  deviceId: number;

  managementCategoryName: string;

  chemicalId: number;

  scope: string;

  managementType: number;

  measurementIndex: number;

  measurementTime: string;

  measurementValue: number;

  adjustmentValue: string;

  pm: number;

  resolvedDate: Date;

  resolvedBy: number;

  resolvedContent: string;

  tcrsType: number;

  createdBy?: number;

  status: number;
}
