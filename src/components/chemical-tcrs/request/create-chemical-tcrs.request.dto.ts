import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsDateString,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { BaseDto } from '../../../core/dto/base.dto';

export class CreateChemicalTcrsRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsDateString()
  measurementDate: Date;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  workshift: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  processId: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  deviceTypeId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  deviceId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  managementCategoryName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  chemicalId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  processChemicalDetailId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  scope: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  managementType: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  measurementIndex: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  measurementTime: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  measurementValue: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  adjustmentValue: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  pm: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDateString()
  resolvedDate: Date;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  resolvedBy: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  resolvedContent: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  tcrsType: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  createdBy?: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  status: number;
}
