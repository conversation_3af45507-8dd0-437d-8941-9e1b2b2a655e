import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsInt, IsNotEmpty, IsOptional } from 'class-validator';
import { BaseDto } from 'src/core/dto/base.dto';

export class GetDetailChemicalTcrsByKeyRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsDateString()
  measurementDate: Date;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  processId: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  deviceTypeId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  deviceId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  workshift: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  chemicalId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  measurementIndex: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  processChemicalDetailId: number;
}
