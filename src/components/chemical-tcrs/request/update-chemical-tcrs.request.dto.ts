import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsDateString, IsInt, IsOptional, IsString } from 'class-validator';
import { BaseDto } from '../../../core/dto/base.dto';

export class UpdateChemicalTcrsRequestDto extends BaseDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDateString()
  resolvedDate: Date;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  resolvedBy: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  resolvedContent: string;
}
