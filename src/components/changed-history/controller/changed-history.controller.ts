import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';
import { GetListChangedHistoryRequestDto } from '../request/get-list-changed-history.request.dto';
import { ChangedHistoryService } from '../service/changed-history.service';

@Controller('changed-history')
export class ChangedHistoryController {
  constructor(private readonly changedHistoryService: ChangedHistoryService) {}

  @Get('/list')
  @ApiOperation({
    tags: ['changed-history'],
    summary: 'Danh sách changed-history',
    description: 'Danh sách changed-history',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListChangedHistoryRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return this.changedHistoryService.getList(request);
  }
}
