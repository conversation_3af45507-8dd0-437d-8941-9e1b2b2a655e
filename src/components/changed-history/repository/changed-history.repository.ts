import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { ChangedHistoryEntity } from '../entities/changed-history.entity';
import { GetDetailChangedHistoryRequestDto } from '../request/get-detail-changed-history.request.dto';
import { GetListChangedHistoryRequestDto } from '../request/get-list-changed-history.request.dto';
import { CreateChangedHistoryRequestDto } from './../request/create-changed-history.request.dto';

@Injectable()
export class ChangedHistoryRepository extends BaseAbstractRepository<ChangedHistoryEntity> {
  constructor(
    @InjectRepository(ChangedHistoryEntity)
    private readonly changedHistoryRepository: Repository<ChangedHistoryEntity>,
  ) {
    super(changedHistoryRepository);
  }

  createEntity(request: CreateChangedHistoryRequestDto): ChangedHistoryEntity {
    const changedHistoryEntity = new ChangedHistoryEntity();
    Object.assign(changedHistoryEntity, request);
    return changedHistoryEntity;
  }

  async getDetail(
    request: GetDetailChangedHistoryRequestDto,
  ): Promise<ChangedHistoryEntity> {
    const { id } = request;

    const data = await this.changedHistoryRepository.findOne({
      where: { id: id },
    });

    return data;
  }

  async getList(request: GetListChangedHistoryRequestDto): Promise<any> {
    const query =
      this.changedHistoryRepository.createQueryBuilder('changedHistory');
    query.where('changedHistory.screen = :screen', {
      screen: request.screen,
    });
    query.andWhere('changedHistory.record_id = :recordId', {
      recordId: request.recordId,
    });
    query.orderBy('changedHistory.id', 'DESC');

    const data = await query.getMany();

    return data;
  }
}
