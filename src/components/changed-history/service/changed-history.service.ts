import { Injectable, Logger } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';

import { CreateChangedHistoryRequestDto } from './../request/create-changed-history.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { plainToInstance } from 'class-transformer';
import { UserService } from '../../another-service/services/user-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { SCREEN_ENUM } from '../changed-history.constant';
import { ChangedHistoryRepository } from '../repository/changed-history.repository';
import { GetListChangedHistoryRequestDto } from '../request/get-list-changed-history.request.dto';

@Injectable()
export class ChangedHistoryService {
  private readonly logger = new Logger(ChangedHistoryService.name);

  constructor(
    private readonly changedHistoryRepository: ChangedHistoryRepository,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly baseSerivce: BaseProcessService,
  ) {}

  async createHistory(request: {
    screen: SCREEN_ENUM;
    recordId: number;
    oldData: any;
    newData?: any;
  }) {
    const requestCreate = plainToInstance(
      CreateChangedHistoryRequestDto,
      request,
    );
    return this.create(requestCreate);
  }

  async create(request: CreateChangedHistoryRequestDto): Promise<any> {
    const changedHistoryEntity =
      this.changedHistoryRepository.createEntity(request);
    const changedHistory = await this.changedHistoryRepository.create(
      changedHistoryEntity,
    );
    return new ResponseBuilder(changedHistory)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListChangedHistoryRequestDto): Promise<any> {
    const data = await this.changedHistoryRepository.getList(request);
    let count = data.length;
    const parseData = data.map((item) => ({
      historyId: item.id,
      count: count--,
      ...JSON.parse(item.oldData),
    }));
    const response = await this.baseSerivce.mapMasterInfoForAllOfKey(parseData);
    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
