import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsInt, IsNotEmpty } from 'class-validator';
import { BaseDto } from '../../../core/dto/base.dto';
import { SCREEN_ENUM } from '../changed-history.constant';
const screenEnums = Object.entries(SCREEN_ENUM)
  .filter(([, value]) => typeof value === 'number')
  .map(([key, value]) => `- ${value}: ${key}`)
  .join('\n');
export class GetListChangedHistoryRequestDto extends BaseDto {
  @ApiProperty({
    enum: SCREEN_ENUM,
    description: `List screen :\n${screenEnums}`,
  })
  @IsNotEmpty()
  @Type(() => Number)
  @IsEnum(SCREEN_ENUM)
  screen: SCREEN_ENUM;

  @ApiProperty({
    type: Number,
    description: 'Id of record which want to check history',
  })
  @Type(() => Number)
  @IsInt()
  recordId: number;
}
