import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity({ name: 'changed_history' })
export class ChangedHistoryEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  screen: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  recordId: number;

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: false,
  })
  oldData: string;

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  newData: string;

  @CreateDateColumn()
  createdAt: Date;
}
