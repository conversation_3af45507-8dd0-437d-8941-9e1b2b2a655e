import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { ChangedHistoryController } from './controller/changed-history.controller';
import { ChangedHistoryEntity } from './entities/changed-history.entity';
import { ChangedHistoryRepository } from './repository/changed-history.repository';
import { ChangedHistoryService } from './service/changed-history.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([ChangedHistoryEntity]),
    AnotherServiceModule,
    BaseProcessModule,
  ],
  providers: [ChangedHistoryService, ChangedHistoryRepository],
  exports: [ChangedHistoryService],
  controllers: [ChangedHistoryController],
})
export class ChangedHistoryModule {}
