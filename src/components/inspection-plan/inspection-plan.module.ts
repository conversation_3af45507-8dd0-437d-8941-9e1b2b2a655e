import { Modu<PERSON> } from '@nestjs/common';
import { FileService } from '../../core/components/file/file.service';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { ChecksheetModule } from '../checksheet/checksheet.module';
import { InspectionExpiredMaterialPlanController } from './controller/inspection-expired-material-plan.controller';
import { InspectionOtherPlanController } from './controller/inspection-other-plan.controller';
import { InspectionPlanController } from './controller/inspection-plan.controller';
import { InspectionShipmentPlanController } from './controller/inspection-shipment-plan.controller';
import { InspectionPlanService } from './service/inspection-plan.service';

@Module({
  imports: [AnotherServiceModule, BaseProcessModule, ChecksheetModule],
  providers: [
    InspectionPlanService,
    {
      provide: 'FileServiceInterface',
      useClass: FileService,
    },
  ],
  exports: [InspectionPlanService],
  controllers: [
    InspectionPlanController,
    InspectionShipmentPlanController,
    InspectionOtherPlanController,
    InspectionExpiredMaterialPlanController,
  ],
})
export class InspectionPlanModule {}
