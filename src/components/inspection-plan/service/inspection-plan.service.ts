import { Inject, Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { DeleteInspectionPlanRequestDto } from '../request/delete-inspection-plan.request.dto';
import { GetDetailInspectionPlanRequestDto } from '../request/get-detail-inspection-plan.request.dto';
import { GetListInspectionPlanRequestDto } from '../request/get-list-inspection-plan.request.dto';
import { UpdateInspectionPlanFormDto } from '../request/update-inspection-plan-form.request.dto';
import { UpdateStatusInspectionPlanRequestDto } from '../request/update-status-inspection-plan.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { FileResource } from '../../../core/components/file/constant/file-upload.constant';
import { FileService } from '../../../core/components/file/file.service';
import {
  FileDelete,
  FileUpload,
} from '../../../core/dto/request/file.request.dto';
import { QmsxPlanService } from '../../another-service/services/qmsx-plan-service';
import { QmsxTicketService } from '../../another-service/services/qmsx-ticket-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { GetDetailChecksheetRequestDto } from '../../checksheet/request/get-detail-checksheet.request.dto';
import { ChecksheetService } from '../../checksheet/service/checksheet.service';
import { FileRequestDto } from '../../common/request/file.request.dto';
import { CreateTicketDetailRequestDto } from '../../inspection-ticket/request/create-ticket-detail.request.dto';
import { CreateTicketRequestDto } from '../../inspection-ticket/request/create-ticket.request.dto';
import {
  INSPECTION_PLAN_STATUS_ENUM,
  IS_EXISTING_TICKET,
} from '../enums/inspection-plan-status.enum';
import { CreateInspectionPlanFormDto } from '../request/create-inspection-plan-form.request.dto';
import { CreateInspectionPlansDto } from '../request/create-inspection-plan.request.dto';
import { UpdateInspectionPlanDto } from '../request/update-inspection-plan.request.dto';

@Injectable()
export class InspectionPlanService {
  private readonly logger = new Logger(InspectionPlanService.name);

  constructor(
    private readonly i18n: I18nService,

    @Inject('FileServiceInterface')
    private readonly fileService: FileService,

    private readonly qmsxPlanService: QmsxPlanService,

    private readonly qmsxTicketService: QmsxTicketService,

    private readonly checksheetService: ChecksheetService,

    private readonly baseService: BaseProcessService,
  ) {}

  async validateInspectionPlan(data: any) {
    const keyDup = ['lotNo', 'itemId'];
    if (
      this.baseService.checkDuplicateByKey(data.inspectionPlanDetails, keyDup)
    ) {
      return {
        result: false,
        messageError: 'error.INSPECTION_PLAN_DETAIL_DUPLICATE_KEY',
      };
    }

    for (const detail of data.inspectionPlanDetails) {
      if (detail.rev && (detail.rev.length < 3 || detail.rev.length > 10)) {
        return {
          result: false,
          messageError: 'error.REV_LENGTH_INVALID',
        };
      }

      if (
        detail.productionWeek &&
        (detail.productionWeek.length < 3 || detail.productionWeek.length > 10)
      ) {
        return {
          result: false,
          messageError: 'error.PRODUCTION_WEEK_LENGTH_INVALID',
        };
      }
    }

    const { itemIds, checksheetIds } = data.inspectionPlanDetails.reduce(
      (result, item) => {
        if (item.itemId !== null && item.itemId !== undefined) {
          result.itemIds.add(item.itemId);
        }

        if (item.checksheetId !== null && item.checksheetId !== undefined) {
          result.checksheetIds.add(item.checksheetId);
        }
        return result;
      },
      {
        itemIds: new Set<number>(),
        checksheetIds: new Set<number>(),
      },
    );

    return await this.baseService.validateMaster({
      itemIds: Array.from(itemIds),
      checksheetIds: Array.from(checksheetIds),
    });
  }

  async handleFile(uploadFiles: FileUpload[], deleteFiles: FileDelete[]) {
    const savedFileResponse = await this.fileService.handleSaveFiles({
      resource: FileResource.QMSX_PLAN,
      deleteFiles: deleteFiles,
      uploadFiles: uploadFiles,
    });

    if (savedFileResponse.statusCode !== ResponseCodeEnum.SUCCESS) {
      return savedFileResponse;
    }

    const filesResponse = await this.fileService.getFilesByIds(
      savedFileResponse.data,
    );

    return filesResponse?.map((res) => {
      const fileRequestDto = new FileRequestDto();
      fileRequestDto.fileId = res.id;
      fileRequestDto.fileName = res.fileNameRaw;
      fileRequestDto.fileUrl = res.fileUrl;
      return fileRequestDto;
    });
  }

  async create(request: CreateInspectionPlanFormDto): Promise<any> {
    const { data } = request;

    const { result, messageError } = await this.validateInspectionPlan(data);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }
    const inspectionPlan = plainToInstance(CreateInspectionPlansDto, data);
    inspectionPlan.createdBy = request.userId;
    const response = await this.qmsxPlanService.createInspectionPlan(
      inspectionPlan,
    );

    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListInspectionPlanRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } =
      await this.qmsxPlanService.getListQmsxInspectionRequest(request);

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailInspectionPlanRequestDto): Promise<any> {
    const inspectionPlan =
      await this.qmsxPlanService.getDetailQmsxInspectionPlanById(request);

    if (isEmpty(inspectionPlan)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMapUser = await this.baseService.mapMasterInfoToResponse([
      inspectionPlan,
    ]);
    const details = await this.baseService.mapMasterInfoToResponse(
      inspectionPlan.inspectionPlanDetails,
    );
    dataMapUser.forEach((s) => {
      s.inspectionPlanDetails = details;
    });

    return new ResponseBuilder(dataMapUser[0] ? dataMapUser[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateInspectionPlanFormDto): Promise<any> {
    const { id, data } = request;

    const inspectionPlan =
      await this.qmsxPlanService.getDetailQmsxInspectionPlanById({
        id: id,
      });

    if (isEmpty(inspectionPlan)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const { result, messageError } = await this.validateInspectionPlan(data);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }
    const inspectionPlanUpdate = plainToInstance(UpdateInspectionPlanDto, data);
    inspectionPlanUpdate.id = id;
    inspectionPlanUpdate.updatedBy = request.userId;
    const response = await this.qmsxPlanService.updateInspectionPlan(
      inspectionPlanUpdate,
    );
    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }
    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteInspectionPlanRequestDto): Promise<any> {
    const inspectionPlan =
      await this.qmsxPlanService.getDetailQmsxInspectionPlanById(request);

    if (isEmpty(inspectionPlan)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const { result, messageError } =
      await this.qmsxPlanService.deleteInspection(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    await this.fileService.handleSaveFiles({
      resource: FileResource.QMSX_PLAN,
      deleteFiles: inspectionPlan.files,
      uploadFiles: [],
    });
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async updateStatus(
    request: UpdateStatusInspectionPlanRequestDto,
  ): Promise<any> {
    const { result, messageError } =
      await this.qmsxPlanService.updateStatusInspection(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async approve(request: GetDetailInspectionPlanRequestDto): Promise<any> {
    const { result, messageError } =
      await this.qmsxPlanService.approveInspection(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async reject(request: GetDetailInspectionPlanRequestDto): Promise<any> {
    const { result, messageError } =
      await this.qmsxPlanService.rejectInspection(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async cancel(request: GetDetailInspectionPlanRequestDto): Promise<any> {
    const { result, messageError } =
      await this.qmsxPlanService.cancelInspection(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }

    await this.qmsxTicketService.cancelInspectionTicketByPlan(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  private async mapPlanDataToTicketDetail(
    inspectionPlanDetail: any,
    inspectionPlan: any,
    request: any,
  ): Promise<any> {
    const getInspectionPlanRequest = new GetDetailChecksheetRequestDto();
    getInspectionPlanRequest.id = inspectionPlanDetail.checksheetId;
    const checksheetData = await this.checksheetService.getDetail(
      getInspectionPlanRequest,
    );

    if (
      isEmpty(checksheetData) ||
      isEmpty(checksheetData.data) ||
      isEmpty(checksheetData.data.dataDetails) ||
      checksheetData.statusCode != ResponseCodeEnum.SUCCESS
    ) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const checksheetDetailData = checksheetData?.data.dataDetails;

    const ticketDetails = checksheetDetailData.map((checksheetData) =>
      plainToInstance(CreateTicketDetailRequestDto, {
        checksheetDetailId: checksheetData.checksheetDetailId,
        checkType: checksheetData.inspectionGroup?.checkType,
      }),
    );

    return plainToInstance(CreateTicketRequestDto, {
      inspectionPlanId: inspectionPlanDetail.inspectionPlanId,
      inspectionPlanDetailId: inspectionPlanDetail.id,
      checksheetId: inspectionPlanDetail.checksheetId,
      inspectionPlanCode: inspectionPlan.code,
      lotNo: inspectionPlanDetail.lotNo,
      qcQuantity: inspectionPlanDetail.requestQuantity,
      ticketDetails: ticketDetails,
      createdBy: request.userId,
      itemId: inspectionPlanDetail.itemId,
      itemCode: inspectionPlanDetail?.item?.code,
      itemName: inspectionPlanDetail?.item?.name,
      customerId: inspectionPlanDetail.customerId,
      customerCode: inspectionPlanDetail?.customer?.code,
      customerName: inspectionPlanDetail?.customer?.name,
      qcRequestTypeId: inspectionPlan.qcRequestTypeId,
      category: inspectionPlan.category,
      requestedDate: inspectionPlan.requestedDate,
      returnResultDate: inspectionPlan.returnResultDate,
      layer: inspectionPlanDetail.layer,
      itemStatus: inspectionPlanDetail.itemStatus,
    });
  }

  async generateTicketByPlanData(
    request: GetDetailInspectionPlanRequestDto,
  ): Promise<any> {
    const inspectionPlan = await this.getDetail(request);

    if (
      isEmpty(inspectionPlan) ||
      isEmpty(inspectionPlan.data) ||
      isEmpty(inspectionPlan?.data.inspectionPlanDetails) ||
      inspectionPlan?.data?.status !== INSPECTION_PLAN_STATUS_ENUM.CONFIRMED ||
      inspectionPlan.statusCode != ResponseCodeEnum.SUCCESS
    ) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const isExistingTickets = !!inspectionPlan.data.inspectionPlanDetails.find(
      (i) => i.isQc === IS_EXISTING_TICKET.EXISTING,
    );

    if (isExistingTickets) {
      const inspectionPlanDetailData =
        await this.baseService.mapMasterInfoToResponse(
          inspectionPlan.data.inspectionPlanDetails,
        );

      const ticketData = await Promise.all(
        inspectionPlanDetailData
          .filter((i) => i.isQc === IS_EXISTING_TICKET.EXISTING)
          .map((detailData) =>
            this.mapPlanDataToTicketDetail(
              detailData,
              inspectionPlan.data,
              request,
            ),
          ),
      );

      const { result, messageError } =
        await this.qmsxTicketService.createInspectionTicket(ticketData);

      if (!result) {
        return new ResponseBuilder()
          .withCode(ResponseCodeEnum.BAD_REQUEST)
          .withMessage(messageError)
          .build();
      }

      return new ResponseBuilder(ticketData)
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(await this.i18n.translate('success.SUCCESS'))
        .build();
    }

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.BAD_REQUEST)
      .withMessage(await this.i18n.translate('error.NOT_FOUND'))
      .build();
  }
}
