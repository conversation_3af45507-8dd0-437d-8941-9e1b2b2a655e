import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';
import { CreateInspectionPlansDto } from './create-inspection-plan.request.dto';

export class CreateInspectionPlanFormDto extends BaseDto {
  @ApiProperty({})
  @ValidateNested({ each: true })
  @Type(() => CreateInspectionPlansDto)
  data: CreateInspectionPlansDto;
}
