import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsDateString,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { BaseDto } from '../../../core/dto/base.dto';
import { FileRequestDto } from '../../common/request/file.request.dto';

export class CreateInspectionPlanDetailDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  lotNo: string;

  @ApiProperty()
  @IsNotEmpty()
  itemId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  itemStatus: number;

  @ApiProperty()
  @IsOptional()
  layer: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  checksheetId: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  customerId: number;

  @ApiProperty()
  @IsOptional()
  rev: string;

  @ApiProperty()
  @IsOptional()
  productionWeek: string;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  expiredDate: Date;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  requestQuantity: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  isQc: number;

  @ApiProperty()
  @IsOptional()
  productionEquipment: string;
}

export class CreateInspectionPlansDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  qcRequestTypeId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  category: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  requestedBy?: number;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  requestedDate: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @MaxLength(255)
  productionTicket?: string;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  returnResultDate: Date;

  @ApiProperty({ type: [FileRequestDto] })
  @IsArray()
  @IsOptional()
  @Type(() => FileRequestDto)
  files: FileRequestDto[];

  @IsOptional()
  @IsInt()
  approvedBy?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(1000)
  description?: string;

  @ApiProperty({ type: [CreateInspectionPlanDetailDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateInspectionPlanDetailDto)
  inspectionPlanDetails: CreateInspectionPlanDetailDto[];

  @IsOptional()
  @IsInt()
  createdBy?: number;
}
