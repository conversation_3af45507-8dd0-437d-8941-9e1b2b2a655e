import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  APPROVE_INSPECTION_SHIPMENT_PLAN_PERMISSION,
  CANCEL_INSPECTION_SHIPMENT_PLAN_PERMISSION,
  CREATE_INSPECTION_SHIPMENT_PLAN_PERMISSION,
  DELETE_INSPECTION_SHIPMENT_PLAN_PERMISSION,
  DETAIL_INSPECTION_SHIPMENT_PLAN_PERMISSION,
  LIST_INSPECTION_SHIPMENT_PLAN_PERMISSION,
  REJECT_INSPECTION_SHIPMENT_PLAN_PERMISSION,
  UPDATE_INSPECTION_SHIPMENT_PLAN_PERMISSION,
} from '../../../utils/permissions/inspection-shipment-plan.permission';
import { CREATE_INSPECTION_SHIPMENT_TICKET_PERMISSION } from '../../../utils/permissions/inspection-shipment-ticket.permission';
import { CreateInspectionPlanFormDto } from '../request/create-inspection-plan-form.request.dto';
import { DeleteInspectionPlanRequestDto } from '../request/delete-inspection-plan.request.dto';
import { GetDetailInspectionPlanRequestDto } from '../request/get-detail-inspection-plan.request.dto';
import { GetListInspectionPlanRequestDto } from '../request/get-list-inspection-plan.request.dto';
import { UpdateInspectionPlanFormDto } from '../request/update-inspection-plan-form.request.dto';
import { InspectionPlanService } from '../service/inspection-plan.service';

@Controller('inspection-shipment-plans')
export class InspectionShipmentPlanController {
  constructor(private readonly inspectionPlanService: InspectionPlanService) {}

  @PermissionCode(DETAIL_INSPECTION_SHIPMENT_PLAN_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Inspection-shipment-plans'],
    summary: 'Chi tiết Inspection-shipment-plans',
    description: 'Chi tiết Inspection-shipment-plans',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailInspectionPlanRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionPlanService.getDetail(request);
  }

  @PermissionCode(LIST_INSPECTION_SHIPMENT_PLAN_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Inspection-shipment-plans'],
    summary: 'Danh sách Inspection-shipment-plans',
    description: 'Danh sách Inspection-shipment-plans',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListInspectionPlanRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionPlanService.getList(request);
  }

  @PermissionCode(CREATE_INSPECTION_SHIPMENT_PLAN_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Inspection-shipment-plans'],
    summary: 'Tạo Inspection-shipment-plans mới',
    description: 'Tạo Inspection-shipment-plans mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateInspectionPlanFormDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionPlanService.create(request);
  }

  @PermissionCode(UPDATE_INSPECTION_SHIPMENT_PLAN_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Inspection-shipment-plans'],
    summary: 'Cập nhật Inspection-shipment-plans',
    description: 'Cập nhật Inspection-shipment-plans',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(@Body() body: UpdateInspectionPlanFormDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionPlanService.update(request);
  }

  // @PermissionCode(UPDATE_STATUS_INSPECTION_SHIPMENT_PLAN_PERMISSION.code)
  // @Put('/updateStatus')
  // @ApiOperation({
  //   tags: ['Inspection-shipment-plans'],
  //   summary: 'Cập nhật Inspection-shipment-plans Status',
  //   description: 'Cập nhật Inspection-shipment-plans Status',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Thành công',
  // })
  // public async updateStatus(
  //   @Body() body: UpdateStatusInspectionPlanRequestDto,
  // ): Promise<any> {
  //   const { request, responseError } = body;

  //   if (responseError && !isEmpty(responseError)) {
  //     return responseError;
  //   }

  //   return await this.inspectionPlanService.updateStatus(request);
  // }

  @PermissionCode(APPROVE_INSPECTION_SHIPMENT_PLAN_PERMISSION.code)
  @Put('/approved')
  @ApiOperation({
    tags: ['Inspection-shipment-plans'],
    summary: 'Approved Inspection-shipment-plans',
    description: 'Approved Inspection-shipment-plans',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async approve(
    @Body() body: GetDetailInspectionPlanRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionPlanService.approve(request);
  }

  @PermissionCode(REJECT_INSPECTION_SHIPMENT_PLAN_PERMISSION.code)
  @Put('/reject')
  @ApiOperation({
    tags: ['Inspection-shipment-plans'],
    summary: 'Rejected Inspection-shipment-plans',
    description: 'Rejected Inspection-shipment-plans',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async reject(
    @Body() body: GetDetailInspectionPlanRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionPlanService.reject(request);
  }

  @PermissionCode(DELETE_INSPECTION_SHIPMENT_PLAN_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Inspection-shipment-plans'],
    summary: 'Xóa Inspection-shipment-plans',
    description: 'Xóa Inspection-shipment-plans',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(@Param() param: DeleteInspectionPlanRequestDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionPlanService.delete(request);
  }

  @PermissionCode(CANCEL_INSPECTION_SHIPMENT_PLAN_PERMISSION.code)
  @Put('/canceled')
  @ApiOperation({
    tags: ['Inspection-shipment-plans'],
    summary: 'Canceled Inspection-shipment-plans',
    description: 'Canceled Inspection-shipment-plans',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async cancel(
    @Body() body: GetDetailInspectionPlanRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionPlanService.cancel(request);
  }

  @PermissionCode(CREATE_INSPECTION_SHIPMENT_TICKET_PERMISSION.code)
  @Post('generate-ticket-by-plan-data')
  @ApiOperation({
    tags: ['Inspection-shipment-plans'],
    summary: 'Tạo Inspection-shipment-tickets',
    description: 'Tạo Inspection-shipment-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async generateTicketByPlanData(
    @Body() param: GetDetailInspectionPlanRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionPlanService.generateTicketByPlanData(request);
  }
}
