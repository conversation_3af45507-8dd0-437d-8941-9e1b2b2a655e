import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { ErrorTypeModule } from '../error-type/error-type.module';
import { InspectionErrorModule } from '../inspection-error/inspection-error.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { ErrorController } from './controller/error.controller';
import { ErrorEntity } from './entities/error.entity';
import { ErrorRepository } from './repository/error.repository';
import { ErrorService } from './service/error.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([ErrorEntity]),
    AnotherServiceModule,
    ErrorTypeModule,
    InspectionErrorModule,
    MasterDataReferenceModule,
  ],
  providers: [ErrorService, ErrorRepository],
  exports: [ErrorService],
  controllers: [ErrorController],
})
export class ErrorModule {}
