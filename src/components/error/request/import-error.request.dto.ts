import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>ber,
  IsO<PERSON>al,
  IsString,
  Matches,
  MaxLength,
} from 'class-validator';

export class ImportErrorRequestDto {
  @IsNotEmpty()
  @MaxLength(50)
  @Matches(/^[a-zA-Z0-9_.-]+$/)
  code: string;

  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @IsNotEmpty()
  @IsString()
  errorTypeCode: string;

  @IsOptional()
  @MaxLength(255)
  description: string;

  @IsOptional()
  @IsNumber()
  serverity: number;
}
