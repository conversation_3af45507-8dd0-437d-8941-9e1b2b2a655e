import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { isDateString } from 'class-validator';
import { Repository } from 'typeorm';

import { StatusEnum } from '../../../common/enums/status.enum';
import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import {
  escapeCharForSearch,
  parseJSONValueField,
} from '../../../utils/common';
import { ErrorEntity } from '../entities/error.entity';
import { GetDetailErrorRequestDto } from '../request/get-detail-error.request.dto';
import { GetListErrorRequestDto } from '../request/get-list-error.request.dto';
import { UpdateErrorRequestDto } from '../request/update-error.request.dto';
import { UpdateStatusErrorRequestDto } from '../request/update-status-error.request.dto';

@Injectable()
export class ErrorRepository extends BaseAbstractRepository<ErrorEntity> {
  constructor(
    @InjectRepository(ErrorEntity)
    private readonly errorRepository: Repository<ErrorEntity>,
  ) {
    super(errorRepository);
  }

  createEntity(request: any): ErrorEntity {
    const errorEntity = new ErrorEntity();
    errorEntity.code = request.code;
    errorEntity.name = request.name;
    errorEntity.errorTypeId = request.errorTypeId;
    errorEntity.description = request?.description;
    errorEntity.serverity = request?.serverity;
    errorEntity.status = StatusEnum.ACTIVE;
    errorEntity.createdBy = request.userId;
    errorEntity.updatedBy = request.userId;
    return errorEntity;
  }

  updateEntity(
    request: UpdateErrorRequestDto,
    entity: ErrorEntity,
  ): ErrorEntity {
    entity.name = request?.name;
    entity.errorTypeId = request?.errorTypeId;
    entity.description = request?.description;
    entity.serverity = request?.serverity;
    entity.updatedBy = request?.userId;
    return entity;
  }

  async getList(request: GetListErrorRequestDto): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.errorRepository
      .createQueryBuilder('error')
      .select([
        'error.id AS id',
        'error.code AS "code"',
        'error.name AS "name"',
        'error.error_type_id AS "errorTypeId"',
        'error.serverity AS "serverity"',
        'error.description AS "description"',
        'error.status AS "status"',
        'error.created_by AS "createdBy"',
        'error.created_at AS "createdAt"',
        'error.updated_by AS "updatedBy"',
        'error.updated_at AS "updatedAt"',
      ])
      .leftJoin('error_types', 'et', 'error.error_type_id = et.id')
      .leftJoin('error_groups', 'eg', 'et.error_group_id = eg.id')
      .addSelect([
        `CASE WHEN COUNT(et.id) = 0 THEN '{}' ELSE
          (SELECT et.id as id, et.code as code, et.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "errorType"`,
        `CASE WHEN COUNT(eg.id) = 0 THEN '{}' ELSE 
          (SELECT eg.id as id, eg.code as code, eg.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "errorGroup"`,
      ]);

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("error"."code") like lower(:code) escape '\\' OR
              lower("error"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('error.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'code':
            query.andWhere(
              `lower("error"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'name':
            query.andWhere(
              `lower("error"."name") like lower(:name) escape '\\'`,
              {
                name: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'description':
            query.andWhere(
              `lower("error"."description") like lower(:description) escape '\\'`,
              {
                description: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'serverity':
            query.andWhere('"error"."serverity" = :serverity', {
              serverity: Number(value),
            });
            break;
          case 'status':
            query.andWhere('"error"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'errorTypeId':
            query.andWhere('"error"."error_type_id" = :errorTypeId', {
              errorTypeId: Number(value),
            });
            break;
          case 'errorTypeIds':
            query.andWhere('"error"."error_type_id" IN (:...errorTypeIds)', {
              errorTypeIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'errorGroupId':
            query.andWhere('"eg"."id" = :errorGroupId', {
              errorGroupId: Number(value),
            });
            break;
          case 'errorGroupIds':
            query.andWhere('"eg"."id" IN (:...errorGroupIds)', {
              errorGroupIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'createdById':
            query.andWhere('"error"."created_by" = :createdById', {
              createdById: Number(value),
            });
            break;
          case 'createdByIds':
            query.andWhere('"error"."created_by" IN (:...createdByIds)', {
              createdByIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'createdAt':
            const createFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const createTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"error"."created_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: createFrom,
                dateTo: createTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"error"."updated_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: updateFrom,
                dateTo: upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'name':
            query.addOrderBy('"error"."name"', order);
            break;
          case 'code':
            query.addOrderBy('"error"."code"', order);
            break;
          case 'errorType':
            query.addOrderBy('"et"."id"', order);
            break;
          case 'errorGroup':
            query.addOrderBy('"eg"."id"', order);
            break;
          case 'status':
            query.addOrderBy('"error"."status"', order);
            break;
          case 'description':
            query.addOrderBy('"error"."description"', order);
            break;
          case 'serverity':
            query.addOrderBy('"error"."serverity"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"error"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"error"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"error"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('error.id', 'DESC');
    }

    query
      .groupBy('error.id')
      .addGroupBy('error.code')
      .addGroupBy('error.name')
      .addGroupBy('error.error_type_id')
      .addGroupBy('error.description')
      .addGroupBy('error.serverity')
      .addGroupBy('error.status')
      .addGroupBy('error.created_by')
      .addGroupBy('error.created_at')
      .addGroupBy('error.updated_by')
      .addGroupBy('error.updated_at')
      .addGroupBy('et.id')
      .addGroupBy('et.code')
      .addGroupBy('et.name')
      .addGroupBy('eg.id')
      .addGroupBy('eg.code')
      .addGroupBy('eg.name');

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const data = await query.getRawMany();
    const count = await query.getCount();

    return {
      data: data?.map((item) => {
        return {
          ...item,
          errorType: parseJSONValueField(item.errorType),
          errorGroup: parseJSONValueField(item.errorGroup),
        };
      }),
      count: count,
    };
  }

  async getListFromInspection(request: GetListErrorRequestDto): Promise<any> {
    const { skip, take, filter, queryIds, isGetAll, keyword } = request;

    let query = this.errorRepository
      .createQueryBuilder('error')
      .select([
        'error.id AS id',
        'error.code AS "code"',
        'error.name AS "name"',
        'error.error_type_id AS "errorTypeId"',
        'error.serverity AS "serverity"',
        'error.description AS "description"',
        'error.status AS "status"',
        'error.created_by AS "createdBy"',
        'error.created_at AS "createdAt"',
        'error.updated_by AS "updatedBy"',
        'error.updated_at AS "updatedAt"',
      ])
      .leftJoin('error_types', 'et', 'error.error_type_id = et.id')
      .leftJoin('error_groups', 'eg', 'et.error_group_id = eg.id')
      .innerJoin('inspection_ref_errors', 'ire', 'ire.error_id = error.id')
      .addSelect([
        `CASE WHEN COUNT(et.id) = 0 THEN '{}' ELSE
          (SELECT et.id as id, et.code as code, et.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "errorType"`,
        `CASE WHEN COUNT(eg.id) = 0 THEN '{}' ELSE 
          (SELECT eg.id as id, eg.code as code, eg.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "errorGroup"`,
      ]);

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
                lower("error"."code") like lower(:code) escape '\\' OR
                lower("error"."name") like lower(:name) escape '\\'
            )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('error.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'status':
            query.andWhere('"error"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'inspectionIds':
            query.andWhere('"ire"."inspection_id" IN (:...inspectionIds)', {
              inspectionIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'inspectionId':
            query.andWhere('ire.inspection_id = :inspectionId ', {
              inspectionId: Number(value),
            });
            break;
          default:
            break;
        }
      });
    }

    query = query.orderBy('error.id', 'DESC');

    query
      .groupBy('error.id')
      .addGroupBy('error.code')
      .addGroupBy('error.name')
      .addGroupBy('error.error_type_id')
      .addGroupBy('error.description')
      .addGroupBy('error.serverity')
      .addGroupBy('error.status')
      .addGroupBy('error.created_by')
      .addGroupBy('error.created_at')
      .addGroupBy('error.updated_by')
      .addGroupBy('error.updated_at')
      .addGroupBy('et.id')
      .addGroupBy('et.code')
      .addGroupBy('et.name')
      .addGroupBy('eg.id')
      .addGroupBy('eg.code')
      .addGroupBy('eg.name');

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const data = await query.getRawMany();
    const count = await query.getCount();

    return {
      data: data?.map((item) => {
        return {
          ...item,
          errorType: parseJSONValueField(item.errorType),
          errorGroup: parseJSONValueField(item.errorGroup),
        };
      }),
      count: count,
    };
  }

  async getDetail(request: GetDetailErrorRequestDto): Promise<ErrorEntity> {
    const { id } = request;

    const data = await this.errorRepository.findOne({
      where: { id: id },
    });

    return data;
  }

  async active(request: UpdateStatusErrorRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.errorRepository
      .createQueryBuilder()
      .update(ErrorEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(request: UpdateStatusErrorRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.errorRepository
      .createQueryBuilder()
      .update(ErrorEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }
}
