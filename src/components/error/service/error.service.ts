import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { isEmpty, keyBy } from 'lodash';
import { I18nService } from 'nestjs-i18n';
import { DataSource, In } from 'typeorm';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { CreateErrorRequestDto } from './../request/create-error.request.dto';
import { DeleteErrorRequestDto } from './../request/delete-error.request.dto';
import { GetDetailErrorRequestDto } from './../request/get-detail-error.request.dto';
import { GetListErrorRequestDto } from './../request/get-list-error.request.dto';
import { UpdateErrorRequestDto } from './../request/update-error.request.dto';
import { UpdateStatusErrorRequestDto } from './../request/update-status-error.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { StatusEnum } from '../../../common/enums/status.enum';
import { BaseService } from '../../../common/service/base.service';
import { UserService } from '../../another-service/services/user-service';
import { ErrorTypeService } from '../../error-type/service/error-type.service';
import { InspectionErrorService } from '../../inspection-error/service/inspection-error.service';
import { MasterDataReferenceService } from '../../master-data-reference/service/master-data-reference.service';
import { ErrorEntity } from '../entities/error.entity';
import { ErrorRepository } from '../repository/error.repository';

@Injectable()
export class ErrorService extends BaseService {
  private readonly logger = new Logger(ErrorService.name);

  constructor(
    private readonly errorRepository: ErrorRepository,

    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly errorTypeService: ErrorTypeService,

    private readonly inspectionErrorService: InspectionErrorService,

    private readonly masterReferenceService: MasterDataReferenceService,
  ) {
    super(userService);
  }

  async create(request: CreateErrorRequestDto): Promise<any> {
    const existCode = await this.errorRepository.findOneByCode(request.code);

    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate exist error-types
    const errorType = await this.errorTypeService.findOneById(
      request?.errorTypeId,
    );
    if (isEmpty(errorType) || errorType.status === StatusEnum.IN_ACTIVE) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.ERROR_TYPE_IS_NOT_EXISTS'),
        )
        .build();
    }

    const errorEntity = this.errorRepository.createEntity(request);
    const error = await this.errorRepository.create(errorEntity);

    const dataMap = await this.mapErrorTypeAndErrorGroupToResponse([error]);
    const response = await this.mapUserInfoToResponse(dataMap);

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListErrorRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.errorRepository.getList(request);

    const response = await this.mapUserInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getListFromInspections(request: GetListErrorRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.errorRepository.getListFromInspection(
      request,
    );

    const response = await this.mapUserInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailErrorRequestDto): Promise<any> {
    const error = await this.errorRepository.getDetail(request);

    if (isEmpty(error)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMap = await this.mapErrorTypeAndErrorGroupToResponse([error]);
    const response = await this.mapUserInfoToResponse(dataMap);

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateErrorRequestDto): Promise<any> {
    const { id } = request;
    const error = await this.errorRepository.findOneById(id);

    if (isEmpty(error)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate error in used
    const usedList = await this.validateErrorInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.ERROR_IN_USED'))
        .build();
    }

    // Validate Code unique
    const existCode = await this.errorRepository.findOneByCode(request.code);

    if (!isEmpty(existCode) && existCode.id !== error.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate exist error-types
    const errorType = await this.errorTypeService.findOneById(
      request?.errorTypeId,
    );
    if (isEmpty(errorType) || errorType.status === StatusEnum.IN_ACTIVE) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.ERROR_TYPE_IS_NOT_EXISTS'),
        )
        .build();
    }

    const dataUpdate = this.errorRepository.updateEntity(request, error);
    const data = await this.errorRepository.update(dataUpdate);

    const dataMap = await this.mapErrorTypeAndErrorGroupToResponse([data]);
    const response = await this.mapUserInfoToResponse(dataMap);

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteErrorRequestDto): Promise<any> {
    const { id } = request;
    const error = await this.errorRepository.findOneById(id);

    if (isEmpty(error)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate error in used
    const usedList = await this.validateErrorInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.ERROR_IN_USED'))
        .build();
    }

    await this.errorRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(request: UpdateStatusErrorRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.errorRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.errorRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(request: UpdateStatusErrorRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.errorRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.errorRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async mapErrorTypeAndErrorGroupToResponse(data: ErrorEntity[]) {
    const ids: number[] = Array.from(
      new Set(
        data
          .flatMap((item) => item.errorTypeId)
          .filter((id) => id !== null && id !== undefined),
      ),
    );

    const errorType = await this.errorTypeService.getErrorTypeDetailByIds(ids);
    if (isEmpty(errorType)) return data;

    const errorTypeMap = keyBy(errorType, 'id');
    return (
      data?.map((item) => {
        return {
          ...item,
          errorType: {
            id: errorTypeMap[item.errorTypeId]?.id,
            code: errorTypeMap[item.errorTypeId]?.code,
            name: errorTypeMap[item.errorTypeId]?.name,
          },
          errorGroup: {
            id: errorTypeMap[item.errorTypeId]?.errorGroup?.id,
            code: errorTypeMap[item.errorTypeId]?.errorGroup?.code,
            name: errorTypeMap[item.errorTypeId]?.errorGroup?.name,
          },
        };
      }) || []
    );
  }

  async validateErrorInUsed(id: number): Promise<boolean> {
    // Validate used in inspection_ref_errors
    const errorList = await this.inspectionErrorService.findAllByErrorIds([id]);
    if (!isEmpty(errorList)) {
      return true;
    }

    // Validate used in checksheet_detail_errors
    const csList =
      await this.masterReferenceService.findAllChecksheetByErrorIds([id]);
    if (!isEmpty(csList)) {
      return true;
    }

    // Validate used in another service
    const isUse =
      await this.masterReferenceService.validateDataMasterUseAnotherService(
        new ValidateMasterRequestDto('error_id', id),
      );
    if (isUse) {
      return true;
    }

    return false;
  }

  async findAllByErrorTypeIds(ids: number[]): Promise<ErrorEntity[]> {
    const filterCondition: any = {
      errorTypeId: In(ids),
    };
    return await this.errorRepository.findByCondition(filterCondition);
  }

  async findOneById(id: number): Promise<ErrorEntity> {
    return await this.errorRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<ErrorEntity[]> {
    return await this.errorRepository.findAllByIds(ids);
  }

  async findAllDataByIds(ids: number[]): Promise<ErrorEntity[]> {
    const data = await this.errorRepository.findAllByIds(ids);
    if (!data || data.length === 0) {
      return [];
    }
    const dataMap = await this.mapErrorTypeAndErrorGroupToResponse(data);
    return dataMap;
  }
}
