import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  ACTIVE_ERROR_PERMISSION,
  CREATE_ERROR_PERMISSION,
  DELETE_ERROR_PERMISSION,
  DETAIL_ERROR_PERMISSION,
  INACTIVE_ERROR_PERMISSION,
  LIST_ERROR_PERMISSION,
  UPDATE_ERROR_PERMISSION,
} from '../../../utils/permissions/error.permission';
import { CreateErrorRequestDto } from '../request/create-error.request.dto';
import { DeleteErrorRequestDto } from '../request/delete-error.request.dto';
import { GetDetailErrorRequestDto } from '../request/get-detail-error.request.dto';
import { GetListErrorRequestDto } from '../request/get-list-error.request.dto';
import { UpdateErrorRequestDto } from '../request/update-error.request.dto';
import { UpdateStatusErrorRequestDto } from '../request/update-status-error.request.dto';
import { ErrorService } from '../service/error.service';

@Controller('errors')
export class ErrorController {
  constructor(private readonly errorService: ErrorService) {}

  @PermissionCode(DETAIL_ERROR_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Errors'],
    summary: 'Chi tiết Errors',
    description: 'Chi tiết Errors',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailErrorRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorService.getDetail(request);
  }

  @PermissionCode(LIST_ERROR_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Errors'],
    summary: 'Danh sách Errors',
    description: 'Danh sách Errors',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(@Query() query: GetListErrorRequestDto): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.errorService.getList(request);
  }

  @PermissionCode(LIST_ERROR_PERMISSION.code)
  @Get('/list-from-inspections')
  @ApiOperation({
    tags: ['Errors'],
    summary: 'Danh sách Errors',
    description: 'Danh sách Errors',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getListFromInspections(
    @Query() query: GetListErrorRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.errorService.getListFromInspections(request);
  }

  @PermissionCode(CREATE_ERROR_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Errors'],
    summary: 'Tạo Errors mới',
    description: 'Tạo Errors mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateErrorRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.errorService.create(request);
  }

  @PermissionCode(UPDATE_ERROR_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Errors'],
    summary: 'Cập nhật Errors',
    description: 'Cập nhật Errors',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(@Body() body: UpdateErrorRequestDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorService.update(request);
  }

  @PermissionCode(ACTIVE_ERROR_PERMISSION.code)
  @Put('/active')
  @ApiOperation({
    tags: ['Errors'],
    summary: 'Cập nhật Errors Status Active',
    description: 'Cập nhật Errors Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async active(@Body() body: UpdateStatusErrorRequestDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorService.active(request);
  }

  @PermissionCode(INACTIVE_ERROR_PERMISSION.code)
  @Put('/inactive')
  @ApiOperation({
    tags: ['Errors'],
    summary: 'Cập nhật Errors Status Inactive',
    description: 'Cập nhật Errors Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async inactive(
    @Body() body: UpdateStatusErrorRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorService.inactive(request);
  }

  @PermissionCode(DELETE_ERROR_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Errors'],
    summary: 'Xóa Errors',
    description: 'Xóa Errors',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(@Param() param: DeleteErrorRequestDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.errorService.delete(request);
  }
}
