import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateInspectionTcrsRequestDto } from './create-inspection-tcrs.request.dto';

export class UpdateInspectionTcrsDto extends CreateInspectionTcrsRequestDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;

  @IsOptional()
  @IsInt()
  updatedBy?: number;
}
