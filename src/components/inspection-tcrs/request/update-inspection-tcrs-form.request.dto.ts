import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateInspectionTcrsFormDto } from './create-inspection-tcrs-form.request.dto';
export class UpdateInspectionTcrsFormRequestDto extends CreateInspectionTcrsFormDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;

  @Transform(({ value }) => Number(value))
  @IsOptional()
  @IsInt()
  updatedBy?: number;
}
