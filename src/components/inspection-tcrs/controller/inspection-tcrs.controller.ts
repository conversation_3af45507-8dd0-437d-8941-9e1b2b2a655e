import { Body, Controller, Get, Param, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  APPROVE_INSPECTION_TCRS_PERMISSION,
  DETAIL_INSPECTION_TCRS_PERMISSION,
  LIST_INSPECTION_TCRS_PERMISSION,
  REJECT_INSPECTION_TCRS_PERMISSION,
  UPDATE_INSPECTION_TCRS_PERMISSION,
} from '../../../utils/permissions/inspection-tcrs.permission';
import { GetDetailInspectionTcrsRequestDto } from '../request/get-detail-inspection-tcrs.request.dto';
import { GetListInspectionTcrsRequestDto } from '../request/get-list-inspection-tcrs.request.dto';
import { UpdateInspectionTcrsFormRequestDto } from '../request/update-inspection-tcrs-form.request.dto';
import { InspectionTcrsService } from '../service/inspection-tcrs.service';

@Controller('inspection-tcrs')
export class InspectionTcrsController {
  constructor(private readonly inspectionTcrsService: InspectionTcrsService) {}

  @PermissionCode(DETAIL_INSPECTION_TCRS_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Inspection-tcrs'],
    summary: 'Chi tiết Inspection-tcrs mạ đồng',
    description: 'Chi tiết Inspection-tcrs mạ đồng',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailInspectionTcrsRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionTcrsService.getDetail(request);
  }

  @PermissionCode(LIST_INSPECTION_TCRS_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Inspection-tcrs'],
    summary: 'Danh sách Inspection-tcrs mạ đồng',
    description: 'Danh sách Inspection-tcrs mạ đồng',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListInspectionTcrsRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionTcrsService.getList(request);
  }

  @PermissionCode(UPDATE_INSPECTION_TCRS_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Inspection-tcrs'],
    summary: 'Cập nhật Inspection-tcrs mạ đồng',
    description: 'Cập nhật Inspection-tcrs mạ đồng',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateInspectionTcrsFormRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionTcrsService.update(request);
  }

  @PermissionCode(APPROVE_INSPECTION_TCRS_PERMISSION.code)
  @Put('/approved')
  @ApiOperation({
    tags: ['Inspection-tcrs'],
    summary: 'Approved Inspection-tcrs mạ đồng',
    description: 'Approved Inspection-tcrs mạ đồng',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async approve(
    @Body() body: GetDetailInspectionTcrsRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionTcrsService.approve(request);
  }

  @PermissionCode(UPDATE_INSPECTION_TCRS_PERMISSION.code)
  @Put('/submit')
  @ApiOperation({
    tags: ['Inspection-tcrs'],
    summary: 'submit Inspection-tcrs mạ đồng',
    description: 'submit Inspection-tcrs mạ đồng',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async submit(
    @Body() body: GetDetailInspectionTcrsRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionTcrsService.submit(request);
  }

  @PermissionCode(REJECT_INSPECTION_TCRS_PERMISSION.code)
  @Put('/reject')
  @ApiOperation({
    tags: ['Inspection-tcrs'],
    summary: 'Rejected Inspection-tcrs mạ đồng',
    description: 'Rejected Inspection-tcrs mạ đồng',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async reject(
    @Body() body: GetDetailInspectionTcrsRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionTcrsService.reject(request);
  }
}
