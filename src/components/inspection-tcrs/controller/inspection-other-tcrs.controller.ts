import { Body, Controller, Get, Param, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  APPROVE_INSPECTION_OTHER_TCRS_PERMISSION,
  DETAIL_INSPECTION_OTHER_TCRS_PERMISSION,
  LIST_INSPECTION_OTHER_TCRS_PERMISSION,
  REJECT_INSPECTION_OTHER_TCRS_PERMISSION,
  UPDATE_INSPECTION_OTHER_TCRS_PERMISSION,
} from '../../../utils/permissions/inspection-other-tcrs.permission';
import { GetDetailInspectionTcrsRequestDto } from '../request/get-detail-inspection-tcrs.request.dto';
import { GetListInspectionTcrsRequestDto } from '../request/get-list-inspection-tcrs.request.dto';
import { UpdateInspectionTcrsFormRequestDto } from '../request/update-inspection-tcrs-form.request.dto';
import { InspectionTcrsService } from '../service/inspection-tcrs.service';

@Controller('inspection-other-tcrs')
export class InspectionOtherTcrsController {
  constructor(private readonly inspectionTcrsService: InspectionTcrsService) {}

  @PermissionCode(DETAIL_INSPECTION_OTHER_TCRS_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Inspection-other-tcrs'],
    summary: 'Chi tiết Inspection-other-tcrs',
    description: 'Chi tiết Inspection-other-tcrs',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailInspectionTcrsRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionTcrsService.getDetail(request);
  }

  @PermissionCode(LIST_INSPECTION_OTHER_TCRS_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Inspection-other-tcrs'],
    summary: 'Danh sách Inspection-other-tcrs',
    description: 'Danh sách Inspection-other-tcrs',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListInspectionTcrsRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionTcrsService.getList(request);
  }

  @PermissionCode(UPDATE_INSPECTION_OTHER_TCRS_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Inspection-other-tcrs'],
    summary: 'Cập nhật Inspection-other-tcrs',
    description: 'Cập nhật Inspection-other-tcrs',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateInspectionTcrsFormRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionTcrsService.update(request);
  }

  @PermissionCode(APPROVE_INSPECTION_OTHER_TCRS_PERMISSION.code)
  @Put('/approved')
  @ApiOperation({
    tags: ['Inspection-other-tcrs'],
    summary: 'Approved Inspection-other-tcrs',
    description: 'Approved Inspection-other-tcrs',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async approve(
    @Body() body: GetDetailInspectionTcrsRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionTcrsService.approve(request);
  }

  @PermissionCode(REJECT_INSPECTION_OTHER_TCRS_PERMISSION.code)
  @Put('/reject')
  @ApiOperation({
    tags: ['Inspection-other-tcrs'],
    summary: 'Rejected Inspection-other-tcrs',
    description: 'Rejected Inspection-other-tcrs',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async reject(
    @Body() body: GetDetailInspectionTcrsRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionTcrsService.reject(request);
  }
}
