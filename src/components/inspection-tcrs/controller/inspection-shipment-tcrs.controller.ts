import { Body, Controller, Get, Param, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  APPROVE_INSPECTION_SHIPMENT_TCRS_PERMISSION,
  DETAIL_INSPECTION_SHIPMENT_TCRS_PERMISSION,
  LIST_INSPECTION_SHIPMENT_TCRS_PERMISSION,
  REJECT_INSPECTION_SHIPMENT_TCRS_PERMISSION,
  UPDATE_INSPECTION_SHIPMENT_TCRS_PERMISSION,
} from '../../../utils/permissions/inspection-shipment-tcrs.permission';
import { GetDetailInspectionTcrsRequestDto } from '../request/get-detail-inspection-tcrs.request.dto';
import { GetListInspectionTcrsRequestDto } from '../request/get-list-inspection-tcrs.request.dto';
import { UpdateInspectionTcrsFormRequestDto } from '../request/update-inspection-tcrs-form.request.dto';
import { InspectionTcrsService } from '../service/inspection-tcrs.service';

@Controller('inspection-shipment-tcrs')
export class InspectionShipmentTcrsController {
  constructor(private readonly inspectionTcrsService: InspectionTcrsService) {}

  @PermissionCode(DETAIL_INSPECTION_SHIPMENT_TCRS_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Inspection-shipment-tcrs'],
    summary: 'Chi tiết Inspection-shipment-tcrs',
    description: 'Chi tiết Inspection-shipment-tcrs',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailInspectionTcrsRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionTcrsService.getDetail(request);
  }

  @PermissionCode(LIST_INSPECTION_SHIPMENT_TCRS_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Inspection-shipment-tcrs'],
    summary: 'Danh sách Inspection-shipment-tcrs',
    description: 'Danh sách Inspection-shipment-tcrs',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListInspectionTcrsRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionTcrsService.getList(request);
  }

  @PermissionCode(UPDATE_INSPECTION_SHIPMENT_TCRS_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Inspection-shipment-tcrs'],
    summary: 'Cập nhật Inspection-shipment-tcrs',
    description: 'Cập nhật Inspection-shipment-tcrs',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateInspectionTcrsFormRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionTcrsService.update(request);
  }

  @PermissionCode(APPROVE_INSPECTION_SHIPMENT_TCRS_PERMISSION.code)
  @Put('/approved')
  @ApiOperation({
    tags: ['Inspection-shipment-tcrs'],
    summary: 'Approved Inspection-shipment-tcrs',
    description: 'Approved Inspection-shipment-tcrs',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async approve(
    @Body() body: GetDetailInspectionTcrsRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionTcrsService.approve(request);
  }

  @PermissionCode(REJECT_INSPECTION_SHIPMENT_TCRS_PERMISSION.code)
  @Put('/reject')
  @ApiOperation({
    tags: ['Inspection-shipment-tcrs'],
    summary: 'Rejected Inspection-shipment-tcrs',
    description: 'Rejected Inspection-shipment-tcrs',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async reject(
    @Body() body: GetDetailInspectionTcrsRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionTcrsService.reject(request);
  }
}
