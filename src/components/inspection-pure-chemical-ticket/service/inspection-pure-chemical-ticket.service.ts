import { Injectable, Logger } from '@nestjs/common';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';

import { GetDetailInspectionPureChemicalTicketRequestDto } from '../request/get-detail-inspection-pure-chemical-ticket.request.dto';
import { GetListInspectionPureChemicalTicketRequestDto } from '../request/get-list-inspection-pure-chemical-ticket.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';

import { PaginationResponse } from '../../../utils/dto/response/pagination.response';
import { QmsxTicketService } from '../../another-service/services/qmsx-ticket-service';
import { UserService } from '../../another-service/services/user-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { INSPECTION_PURE_CHEMICAL_TYPE_ENUM } from '../enums/inspection-pure-chemical-ticket-status.enum';
import { CreateInspectionPureChemicalTicketRequestDto } from '../request/create-inspection-pure-chemical-ticket.request.dto';
import { DeleteInspectionPureChemicalTicketRequestDto } from '../request/delete-inspection-pure-chemical-ticket.request.dto';
import { UpdateInspectionPureChemicalTicketDto } from '../request/update-inspection-pure-chemical-ticket.dto';

@Injectable()
export class InspectionPureChemicalTicketService {
  private readonly logger = new Logger(
    InspectionPureChemicalTicketService.name,
  );

  constructor(
    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly qmsxTicketService: QmsxTicketService,

    private readonly baseService: BaseProcessService,
  ) {}

  async getList(
    request: GetListInspectionPureChemicalTicketRequestDto,
  ): Promise<any> {
    const { page } = request;
    const { data, count } =
      await this.qmsxTicketService.getListInspectionPureChemicalTicket(request);

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(
    request: GetDetailInspectionPureChemicalTicketRequestDto,
  ): Promise<any> {
    const inspectionPureChemicalTicket =
      await this.qmsxTicketService.getDetailInspectionPureChemicalTicketById(
        request,
      );

    if (isEmpty(inspectionPureChemicalTicket)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const dataMapUser = await this.baseService.mapMasterInfoToResponse([
      inspectionPureChemicalTicket,
    ]);
    const details = await this.baseService.mapMasterInfoToResponse(
      inspectionPureChemicalTicket.inspectionPureChemicalTicketDetails,
    );
    dataMapUser.forEach((s) => {
      s.inspectionPureChemicalTicketDetails = details;
    });
    return new ResponseBuilder(dataMapUser[0] ? dataMapUser[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateInspectionPureChemicalTicketDto): Promise<any> {
    const { id } = request;

    const inspectionPureChemicalTicket =
      await this.qmsxTicketService.getDetailInspectionPureChemicalTicketById({
        id: id,
      });

    if (isEmpty(inspectionPureChemicalTicket)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const { result, messageError } = await this.validateInspectionPlan(request);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }

    request.updatedBy = request.userId;
    const response =
      await this.qmsxTicketService.updateInspectionPureChemicalTicket(request);
    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }
    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async validateInspectionPlan(data: any) {
    const keyDup =
      data.type === INSPECTION_PURE_CHEMICAL_TYPE_ENUM.IN_USE
        ? ['processId', 'equipmentId', 'chemicalId']
        : ['lotNo', 'chemicalId'];
    if (this.baseService.checkDuplicateByKey(data.ticketDetails, keyDup)) {
      return {
        result: false,
        messageError:
          'error.INSPECTION_PURE_CHEMICAL_TICKET_DETAIL_DUPLICATE_KEY',
      };
    }
    if (
      data.type === INSPECTION_PURE_CHEMICAL_TYPE_ENUM.NEW &&
      (!data.vendorId ||
        (data.newType !== 0 && data.newType !== 1) ||
        data.ticketDetails.some((s) => !s.lotNo))
    ) {
      return {
        result: false,
        messageError: 'error.SOME_FIELD_CAN_NOT_EMPTY',
      };
    }
    if (
      data.type === INSPECTION_PURE_CHEMICAL_TYPE_ENUM.IN_USE &&
      data.ticketDetails.some((s) => !s.processId || !s.equipmentId)
    ) {
      return {
        result: false,
        messageError: 'error.SOME_FIELD_CAN_NOT_EMPTY',
      };
    }
    const { processIds, chemicalIds, equipmentIds } = data.ticketDetails.reduce(
      (result, item) => {
        if (item.processId !== null && item.processId !== undefined) {
          result.processIds.add(item.processId);
        }
        if (item.equipmentId !== null && item.equipmentId !== undefined) {
          result.equipmentIds.add(item.equipmentId);
        }
        if (item.chemicalId !== null && item.chemicalId !== undefined) {
          result.chemicalIds.add(item.chemicalId);
        }
        return result;
      },
      {
        processIds: new Set<number>(),
        chemicalIds: new Set<number>(),
        equipmentIds: new Set<number>(),
      },
    );

    const { departmentId, vendorId } = data;

    return await this.baseService.validateMaster({
      departmentIds: [departmentId],
      chemicalIds: Array.from(chemicalIds),
      ...(data.type === INSPECTION_PURE_CHEMICAL_TYPE_ENUM.NEW
        ? {
            vendorIds: [vendorId],
          }
        : {
            processIds: Array.from(processIds),
            equipmentIds: Array.from(equipmentIds),
          }),
    });
  }

  async create(
    request: CreateInspectionPureChemicalTicketRequestDto,
  ): Promise<any> {
    const { result, messageError } = await this.validateInspectionPlan(request);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }
    request.createdBy = request.userId;
    const response =
      await this.qmsxTicketService.createInspectionPureChemicalTicket(request);

    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async approve(
    request: GetDetailInspectionPureChemicalTicketRequestDto,
  ): Promise<any> {
    const { result, messageError } =
      await this.qmsxTicketService.approveInspectionPureChemicalTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async cancel(
    request: GetDetailInspectionPureChemicalTicketRequestDto,
  ): Promise<any> {
    const { result, messageError } =
      await this.qmsxTicketService.cancelInspectionPureChemicalTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(
    request: DeleteInspectionPureChemicalTicketRequestDto,
  ): Promise<any> {
    const { result, messageError } =
      await this.qmsxTicketService.deleteInspectionPureChemicalTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
