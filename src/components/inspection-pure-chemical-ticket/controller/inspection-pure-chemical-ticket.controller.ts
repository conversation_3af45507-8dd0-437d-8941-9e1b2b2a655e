import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  APPROVE_INSPECTION_PURE_CHEMICAL_TICKET_PERMISSION,
  CANCEL_INSPECTION_PURE_CHEMICAL_TICKET_PERMISSION,
  CREATE_INSPECTION_PURE_CHEMICAL_TICKET_PERMISSION,
  DELETE_INSPECTION_PURE_CHEMICAL_TICKET_PERMISSION,
  DETAIL_INSPECTION_PURE_CHEMICAL_TICKET_PERMISSION,
  LIST_INSPECTION_PURE_CHEMICAL_TICKET_PERMISSION,
  UPDATE_INSPECTION_PURE_CHEMICAL_TICKET_PERMISSION,
} from '../../../utils/permissions/inspection-pure-chemical-ticket.permission';
import { CreateInspectionPureChemicalTicketRequestDto } from '../request/create-inspection-pure-chemical-ticket.request.dto';
import { DeleteInspectionPureChemicalTicketRequestDto } from '../request/delete-inspection-pure-chemical-ticket.request.dto';
import { GetDetailInspectionPureChemicalTicketRequestDto } from '../request/get-detail-inspection-pure-chemical-ticket.request.dto';
import { GetListInspectionPureChemicalTicketRequestDto } from '../request/get-list-inspection-pure-chemical-ticket.request.dto';
import { UpdateInspectionPureChemicalTicketDto } from '../request/update-inspection-pure-chemical-ticket.dto';
import { InspectionPureChemicalTicketService } from '../service/inspection-pure-chemical-ticket.service';

@Controller('inspection-pure-chemical-tickets')
export class InspectionPureChemicalTicketController {
  constructor(
    private readonly inspectionPureChemicalTicketService: InspectionPureChemicalTicketService,
  ) {}

  @PermissionCode(DETAIL_INSPECTION_PURE_CHEMICAL_TICKET_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Inspection-pure-chemical-tickets'],
    summary: 'Chi tiết Inspection-pure-chemical-tickets',
    description: 'Chi tiết Inspection-pure-chemical-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailInspectionPureChemicalTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionPureChemicalTicketService.getDetail(request);
  }

  @PermissionCode(LIST_INSPECTION_PURE_CHEMICAL_TICKET_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Inspection-pure-chemical-tickets'],
    summary: 'Danh sách Inspection-pure-chemical-tickets',
    description: 'Danh sách Inspection-pure-chemical-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListInspectionPureChemicalTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionPureChemicalTicketService.getList(request);
  }

  @PermissionCode(UPDATE_INSPECTION_PURE_CHEMICAL_TICKET_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Inspection-pure-chemical-tickets'],
    summary: 'Cập nhật Inspection-pure-chemical-tickets',
    description: 'Cập nhật Inspection-pure-chemical-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateInspectionPureChemicalTicketDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionPureChemicalTicketService.update(request);
  }

  @PermissionCode(CREATE_INSPECTION_PURE_CHEMICAL_TICKET_PERMISSION.code)
  @Post('create-ticket')
  @ApiOperation({
    tags: ['Inspection-pure-chemical-tickets'],
    summary: 'Chi tiết Inspection-pure-chemical-tickets',
    description: 'Chi tiết Inspection-pure-chemical-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async create(
    @Body() param: CreateInspectionPureChemicalTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionPureChemicalTicketService.create(request);
  }

  @PermissionCode(APPROVE_INSPECTION_PURE_CHEMICAL_TICKET_PERMISSION.code)
  @Put('/approved')
  @ApiOperation({
    tags: ['Inspection-pure-chemical-tickets'],
    summary: 'Approved Inspection-pure-chemical-tickets',
    description: 'Approved Inspection-pure-chemical-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async approve(
    @Body() body: GetDetailInspectionPureChemicalTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionPureChemicalTicketService.approve(request);
  }

  @PermissionCode(CANCEL_INSPECTION_PURE_CHEMICAL_TICKET_PERMISSION.code)
  @Put('/canceled')
  @ApiOperation({
    tags: ['Inspection-pure-chemical-tickets'],
    summary: 'Canceled Inspection-pure-chemical-tickets',
    description: 'Canceled Inspection-pure-chemical-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async canceled(
    @Body() body: GetDetailInspectionPureChemicalTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionPureChemicalTicketService.cancel(request);
  }

  @PermissionCode(DELETE_INSPECTION_PURE_CHEMICAL_TICKET_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Inspection-pure-chemical-tickets'],
    summary: 'Xóa Inspection-pure-chemical-tickets',
    description: 'Xóa Inspection-pure-chemical-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(
    @Param() param: DeleteInspectionPureChemicalTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionPureChemicalTicketService.delete(request);
  }
}
