import { Module } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { InspectionPureChemicalTicketController } from './controller/inspection-pure-chemical-ticket.controller';
import { InspectionPureChemicalTicketService } from './service/inspection-pure-chemical-ticket.service';

@Module({
  imports: [AnotherServiceModule, BaseProcessModule],
  providers: [InspectionPureChemicalTicketService],
  exports: [InspectionPureChemicalTicketService],
  controllers: [InspectionPureChemicalTicketController],
})
export class InspectionPureChemicalTicketModule {}
