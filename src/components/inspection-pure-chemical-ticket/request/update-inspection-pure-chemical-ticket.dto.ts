import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateInspectionPureChemicalTicketRequestDto } from './create-inspection-pure-chemical-ticket.request.dto';

export class UpdateInspectionPureChemicalTicketDto extends CreateInspectionPureChemicalTicketRequestDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  updatedBy?: number;
}
