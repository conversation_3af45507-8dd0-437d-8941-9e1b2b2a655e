import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { FileRequestDto } from '../../common/request/file.request.dto';

export class CreatePureChemicalTicketDetailRequestDto {
  @IsNumber()
  @IsOptional()
  @ApiProperty()
  processId: number;

  @IsNumber()
  @IsOptional()
  @ApiProperty()
  equipmentId: number;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty()
  chemicalId: number;

  @IsOptional()
  @ApiProperty()
  scope: string;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty()
  externalInspection: number;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty()
  concentration: number;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty()
  testCLResult: number;

  @IsNumber()
  @IsOptional()
  @ApiProperty()
  result: number;

  @IsOptional()
  @ApiProperty()
  lotNo: string;

  @ApiProperty({ type: [FileRequestDto] })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FileRequestDto)
  files: FileRequestDto[];
}
