import { Type } from 'class-transformer';
import {
  Is<PERSON>rray,
  IsDateString,
  <PERSON><PERSON><PERSON>,
  IsInt,
  IsNotEmpty,
  <PERSON>N<PERSON>ber,
  IsOptional,
} from 'class-validator';
import { FileRequestDto } from '../../common/request/file.request.dto';
import {
  INSPECTION_PURE_CHEMICAL_TYPE_ENUM,
  NEW_INSPECTION_PURE_CHEMICAL_TYPE_ENUM,
} from '../enums/inspection-pure-chemical-ticket-status.enum';
import { CreatePureChemicalTicketDetailRequestDto } from './create-pure-chemical-ticket-detail.request.dto';

export class CreateTicketRequestDto {
  @IsNumber()
  @IsOptional()
  departmentId: number;

  @IsOptional()
  @IsDateString()
  inspectionDate: Date;

  @IsNumber()
  @IsOptional()
  inspectionBy: number;

  @IsNumber()
  @IsOptional()
  status: number;

  @IsEnum(INSPECTION_PURE_CHEMICAL_TYPE_ENUM)
  @IsOptional()
  type: INSPECTION_PURE_CHEMICAL_TYPE_ENUM;

  @IsEnum(NEW_INSPECTION_PURE_CHEMICAL_TYPE_ENUM)
  @IsOptional()
  newType: NEW_INSPECTION_PURE_CHEMICAL_TYPE_ENUM;

  @IsNumber()
  @IsOptional()
  vendorId: number;

  @IsArray()
  @IsOptional()
  @Type(() => FileRequestDto)
  files: FileRequestDto[];

  @IsArray()
  @Type(() => CreatePureChemicalTicketDetailRequestDto)
  ticketDetails: CreatePureChemicalTicketDetailRequestDto[];

  @IsNotEmpty()
  @IsInt()
  createdBy: number;
}
