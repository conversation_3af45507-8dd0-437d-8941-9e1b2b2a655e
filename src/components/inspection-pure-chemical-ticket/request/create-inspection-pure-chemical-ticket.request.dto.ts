import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsDateString,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { FileRequestDto } from '../../common/request/file.request.dto';
import {
  INSPECTION_PURE_CHEMICAL_TYPE_ENUM,
  NEW_INSPECTION_PURE_CHEMICAL_TYPE_ENUM,
} from '../enums/inspection-pure-chemical-ticket-status.enum';
import { CreatePureChemicalTicketDetailRequestDto } from './create-pure-chemical-ticket-detail.request.dto';

export class CreateInspectionPureChemicalTicketRequestDto extends BaseDto {
  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  departmentId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsDateString()
  inspectionDate: Date;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  inspectionBy: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  status: number;

  @ApiPropertyOptional({
    enum: [0, 1],
  })
  @IsEnum(INSPECTION_PURE_CHEMICAL_TYPE_ENUM)
  @IsOptional()
  type: INSPECTION_PURE_CHEMICAL_TYPE_ENUM;

  @ApiPropertyOptional({
    enum: [0, 1],
  })
  @IsEnum(NEW_INSPECTION_PURE_CHEMICAL_TYPE_ENUM)
  @IsOptional()
  newType: NEW_INSPECTION_PURE_CHEMICAL_TYPE_ENUM;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  vendorId: number;

  @ApiProperty({ type: [FileRequestDto] })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FileRequestDto)
  files: FileRequestDto[];

  @ApiProperty()
  @IsOptional()
  @IsInt()
  createdBy: number;

  @ApiProperty({ type: [CreatePureChemicalTicketDetailRequestDto] })
  @IsArray()
  @Type(() => CreatePureChemicalTicketDetailRequestDto)
  @ValidateNested({ each: true })
  ticketDetails: CreatePureChemicalTicketDetailRequestDto[];
}
