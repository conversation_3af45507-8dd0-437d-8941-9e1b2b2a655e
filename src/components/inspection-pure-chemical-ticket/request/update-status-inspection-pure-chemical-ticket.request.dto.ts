import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional } from 'class-validator';
import { CreateInspectionPureChemicalTicketRequestDto } from './create-inspection-pure-chemical-ticket.request.dto';

export class UpdateStatusInspectionPureChemicalTicketRequestDto extends CreateInspectionPureChemicalTicketRequestDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  @IsInt()
  @IsOptional()
  updatedBy?: number;
}
