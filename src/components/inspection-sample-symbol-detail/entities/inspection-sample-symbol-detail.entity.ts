import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { InspectionSampleSymbolDetailStandardEntity } from '../../inspection-sample-symbol-detail-standard/entities/inspection-sample-symbol-detail-standard.entity';
import { InspectionSampleSymbolEntity } from '../../inspection-sample-symbol/entities/inspection-sample-symbol.entity';

@Entity({ name: 'inspection_sample_symbol_details' })
export class InspectionSampleSymbolDetailEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  inspectionSampleSymbolId: number;

  @Column({
    type: 'float',
    nullable: false,
  })
  lotSizeFrom: number;

  @Column({
    type: 'float',
    nullable: false,
  })
  lotSizeTo: number;

  @OneToMany(
    () => InspectionSampleSymbolDetailStandardEntity,
    (detailStandard) => detailStandard.inspectionSampleSymbolDetail,
    {
      cascade: ['insert', 'update', 'remove'],
      onDelete: 'CASCADE',
      eager: true,
    },
  )
  detailStandards: InspectionSampleSymbolDetailStandardEntity[];

  @ManyToOne(
    () => InspectionSampleSymbolEntity,
    (inspectionSampleSymbol) => inspectionSampleSymbol.dataDetails,
    {
      orphanedRowAction: 'delete',
    },
  )
  @JoinColumn({
    name: 'inspection_sample_symbol_id',
    referencedColumnName: 'id',
  })
  inspectionSampleSymbol: InspectionSampleSymbolEntity;
}
