import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InspectionSampleSymbolDetailEntity } from './entities/inspection-sample-symbol-detail.entity';
import { InspectionSampleSymbolDetailRepository } from './repository/inspection-sample-symbol-detail.repository';
import { InspectionSampleSymbolDetailService } from './service/inspection-sample-symbol-detail.service';

@Module({
  imports: [TypeOrmModule.forFeature([InspectionSampleSymbolDetailEntity])],
  providers: [
    InspectionSampleSymbolDetailService,
    InspectionSampleSymbolDetailRepository,
  ],
  exports: [InspectionSampleSymbolDetailService],
  controllers: [],
})
export class InspectionSampleSymbolDetailModule {}
