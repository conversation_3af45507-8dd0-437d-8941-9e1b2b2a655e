import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { InspectionSampleSymbolDetailEntity } from '../entities/inspection-sample-symbol-detail.entity';
import { GetInspectionQuantityRequestDto } from '../request/get-inspection-quantity.request.dto';
import { GetInspectionQuantityResponseDto } from '../response/get-inspection-quantity.response.dto';

@Injectable()
export class InspectionSampleSymbolDetailRepository extends BaseAbstractRepository<InspectionSampleSymbolDetailEntity> {
  constructor(
    @InjectRepository(InspectionSampleSymbolDetailEntity)
    private readonly detailRepository: Repository<InspectionSampleSymbolDetailEntity>,
  ) {
    super(detailRepository);
  }

  async getDataDetailsByIssIds(inspecionSampleSymbolIds: number[]) {
    const data = await this.detailRepository
      .createQueryBuilder('issd')
      .innerJoinAndSelect('issd.detailStandards', 'ds')
      .select([
        'issd.id as id',
        'issd.inspection_sample_symbol_id as inspectionSampleSymbolId',
        'issd.lot_size_from as lotSizeFrom',
        'issd.lot_size_to as lotSizeTo',
        'ds.id as detailStandardId',
        'ds.inspection_sample_symbol_detail_id as inspectionSampleSymbolDetailId',
        'ds.inspection_standard_id as inspection_standard_id',
        'ds.inspection_sample_quantity_id as inspectionSampleQuantityId',
      ])
      .where(
        'issd.inspection_sample_symbol_id IN (:...inspecionSampleSymbolIds)',
        {
          inspecionSampleSymbolIds,
        },
      )
      .getRawMany();

    return data;
  }

  async getInspectionQuantity(
    request: GetInspectionQuantityRequestDto,
  ): Promise<any> {
    const {
      productQuantity,
      inspectionStandardId,
      samplingRateId,
      aqlSampleLimitId,
    } = request;

    const findInspectionQuantity = await this.detailRepository
      .createQueryBuilder('issd')
      .select([
        'COALESCE(isq.inspection_sample_quantity, NULL) AS inspectionQuantity',
      ])
      .leftJoin(
        'inspection_sample_symbol_detail_standards',
        'issds',
        'issds.inspection_sample_symbol_detail_id = issd.id',
      )
      .leftJoin(
        'inspection_sample_quantities',
        'isq',
        'issds.inspection_sample_quantity_id = isq.id',
      )
      .innerJoin(
        'aql_sample_limits',
        'asl',
        'issd.inspection_sample_symbol_id = asl.inspection_sample_symbol_id',
      )
      .where('issd.lot_size_from <= :productQuantity', { productQuantity })
      .andWhere('issd.lot_size_to >= :productQuantity', { productQuantity })
      .andWhere('issds.inspection_standard_id = :inspectionStandardId', {
        inspectionStandardId,
      })
      .andWhere('asl.id = :aqlSampleLimitId', {
        aqlSampleLimitId,
      })
      .getRawOne();

    const findMaxAllowedNgQuantity = await this.detailRepository
      .createQueryBuilder('issd')
      .select(['COALESCE(isqd.accept_quantity, NULL) AS maxAllowedNgQuantity'])
      .leftJoin(
        'inspection_sample_symbol_detail_standards',
        'issds',
        'issds.inspection_sample_symbol_detail_id = issd.id',
      )
      .leftJoin(
        'inspection_sample_quantities',
        'isq',
        'issds.inspection_sample_quantity_id = isq.id',
      )
      .leftJoin(
        'inspection_sample_quantity_details',
        'isqd',
        'isqd.inspection_sample_quantity_id = isq.id',
      )
      .innerJoin(
        'aql_sample_limits',
        'asl',
        'issd.inspection_sample_symbol_id = asl.inspection_sample_symbol_id',
      )
      .where('issd.lot_size_from <= :productQuantity', { productQuantity })
      .andWhere('issd.lot_size_to >= :productQuantity', { productQuantity })
      .andWhere('issds.inspection_standard_id = :inspectionStandardId', {
        inspectionStandardId,
      })
      .andWhere('isqd.sampling_rate_id = :samplingRateId', { samplingRateId })
      .andWhere('asl.id = :aqlSampleLimitId', {
        aqlSampleLimitId,
      })
      .getRawOne();

    if (findInspectionQuantity?.inspectionQuantity) {
      const response = new GetInspectionQuantityResponseDto();
      response.inspectionQuantity =
        findInspectionQuantity?.inspectionQuantity ?? null;
      response.maxAllowedNgQuantity =
        findMaxAllowedNgQuantity?.maxAllowedNgQuantity ?? 0;
      return { result: true, response: response };
    }

    return { result: false, response: null };
  }
}
