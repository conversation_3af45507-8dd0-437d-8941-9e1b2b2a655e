import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { BaseDto } from 'src/core/dto/base.dto';

export class GetInspectionQuantityRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  inspectionStandardId: number;

  @ApiProperty()
  @IsNotEmpty()
  samplingRateId: number;

  @ApiProperty()
  @IsNotEmpty()
  productQuantity: number;

  @ApiProperty()
  @IsNotEmpty()
  aqlSampleLimitId: number;
}
