import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { InspectionSampleSymbolDetailStandardEntity } from '../../inspection-sample-symbol-detail-standard/entities/inspection-sample-symbol-detail-standard.entity';
import { CreateInspectionSampleSymbolDetailStandardRequestDto } from '../../inspection-sample-symbol-detail-standard/request/create-inspection-sample-symbol-detail-standard.request.dto';

export class CreateInspectionSampleSymbolDetailRequestDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  id: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  inspectionSampleSymbolId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  lotSizeFrom: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  lotSizeTo: number;

  @ApiProperty({ type: [CreateInspectionSampleSymbolDetailStandardRequestDto] })
  @IsArray()
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateInspectionSampleSymbolDetailStandardRequestDto)
  detailStandards: InspectionSampleSymbolDetailStandardEntity[];
}
