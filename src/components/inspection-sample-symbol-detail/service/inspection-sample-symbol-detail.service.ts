import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { DataSource } from 'typeorm';

import { InspectionSampleSymbolDetailRepository } from '../repository/inspection-sample-symbol-detail.repository';

@Injectable()
export class InspectionSampleSymbolDetailService {
  private readonly logger = new Logger(
    InspectionSampleSymbolDetailService.name,
  );

  constructor(
    private readonly inspectionSampleSymbolDetailRepository: InspectionSampleSymbolDetailRepository,

    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,
  ) {}

  async getDataDetailsByIssIds(inspecionSampleSymbolIds: number[]) {
    const data =
      await this.inspectionSampleSymbolDetailRepository.getDataDetailsByIssIds(
        inspecionSampleSymbolIds,
      );

    return data;
  }
}
