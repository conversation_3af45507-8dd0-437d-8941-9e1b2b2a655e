import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { InspectionSampleQuantityDetailModule } from '../inspection-sample-quantity-detail/inspection-sample-quantity-detail.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { SamplingRateModule } from '../sampling-rate/sampling-rate.module';
import { InspectionSampleQuantityController } from './controller/inspection-sample-quantity.controller';
import { InspectionSampleQuantityEntity } from './entities/inspection-sample-quantity.entity';
import { InspectionSampleQuantityRepository } from './repository/inspection-sample-quantity.repository';
import { InspectionSampleQuantityService } from './service/inspection-sample-quantity.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([InspectionSampleQuantityEntity]),
    AnotherServiceModule,
    InspectionSampleQuantityDetailModule,
    SamplingRateModule,
    MasterDataReferenceModule,
  ],
  providers: [
    InspectionSampleQuantityService,
    InspectionSampleQuantityRepository,
  ],
  exports: [InspectionSampleQuantityService],
  controllers: [InspectionSampleQuantityController],
})
export class InspectionSampleQuantityModule {}
