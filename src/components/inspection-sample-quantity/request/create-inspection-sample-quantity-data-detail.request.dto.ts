import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  MaxLength,
} from 'class-validator';

export class CreateInspectionSampleQuantityDataDetailRequestDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  id: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  inspectionSampleQuantityId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  samplingRateId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  acceptQuantity: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  rejectQuantity: number;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  description?: string;
}
