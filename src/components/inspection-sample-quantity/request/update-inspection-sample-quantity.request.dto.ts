import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt } from 'class-validator';
import { CreateInspectionSampleQuantityRequestDto } from './create-inspection-sample-quantity.request.dto';

export class UpdateInspectionSampleQuantityRequestDto extends CreateInspectionSampleQuantityRequestDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;
}
