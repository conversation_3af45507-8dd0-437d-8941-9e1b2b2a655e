import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { InspectionSampleQuantityDetailEntity } from '../../inspection-sample-quantity-detail/entities/inspection-sample-quantity-detail.entity';
import { CreateInspectionSampleQuantityDataDetailRequestDto } from './create-inspection-sample-quantity-data-detail.request.dto';

export class CreateInspectionSampleQuantityRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  code: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  inspectionSampleQuantity: number;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  description?: string;

  @ApiProperty({ type: [CreateInspectionSampleQuantityDataDetailRequestDto] })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested()
  @Type(() => CreateInspectionSampleQuantityDataDetailRequestDto)
  dataDetails: InspectionSampleQuantityDetailEntity[];
}
