import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ptional,
  IsString,
  Matches,
  MaxLength,
} from 'class-validator';

export class ImportInspectionSampleQuantityDetailDto {
  @IsNotEmpty()
  @IsString()
  keyMapping: string;

  @IsNotEmpty()
  @IsString()
  samplingRateCode: string;

  @IsNotEmpty()
  @IsNumber()
  acceptQuantity: number;

  @IsNotEmpty()
  @IsNumber()
  rejectQuantity: number;

  @IsOptional()
  @MaxLength(255)
  description?: string;
}

export class ImportInspectionSampleQuantityDto {
  @IsNotEmpty()
  @IsString()
  keyMapping: string;

  @IsNotEmpty()
  @MaxLength(50)
  @Matches(/^[a-zA-Z0-9_.-]+$/)
  code: string;

  @IsNotEmpty()
  @IsNumber()
  inspectionSampleQuantity: number;

  @IsOptional()
  @MaxLength(255)
  description?: string;
}
