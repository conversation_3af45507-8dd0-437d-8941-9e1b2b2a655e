import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { isDateString } from 'class-validator';
import { StatusEnum } from '../../../common/enums/status.enum';
import connectionOptions from '../../../config/database.config';
import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { escapeCharForSearch } from '../../../utils/common';
import { InspectionSampleQuantityEntity } from '../entities/inspection-sample-quantity.entity';
import { GetDetailInspectionSampleQuantityRequestDto } from '../request/get-detail-inspection-sample-quantity.request.dto';
import { GetListInspectionSampleQuantityRequestDto } from '../request/get-list-inspection-sample-quantity.request.dto';
import { UpdateInspectionSampleQuantityRequestDto } from '../request/update-inspection-sample-quantity.request.dto';
import { UpdateStatusInspectionSampleQuantityRequestDto } from '../request/update-status-inspection-sample-quantity.request.dto';
import { CreateInspectionSampleQuantityRequestDto } from './../request/create-inspection-sample-quantity.request.dto';

@Injectable()
export class InspectionSampleQuantityRepository extends BaseAbstractRepository<InspectionSampleQuantityEntity> {
  constructor(
    @InjectRepository(InspectionSampleQuantityEntity)
    private readonly inspectionSampleQuantityRepository: Repository<InspectionSampleQuantityEntity>,
  ) {
    super(inspectionSampleQuantityRepository);
  }

  createEntity(
    request: CreateInspectionSampleQuantityRequestDto,
  ): InspectionSampleQuantityEntity {
    const inspectionSampleQuantityEntity = new InspectionSampleQuantityEntity();
    inspectionSampleQuantityEntity.code = request.code;
    inspectionSampleQuantityEntity.inspectionSampleQuantity =
      request.inspectionSampleQuantity;
    inspectionSampleQuantityEntity.description = request.description;
    inspectionSampleQuantityEntity.dataDetails = request.dataDetails;
    inspectionSampleQuantityEntity.status = StatusEnum.ACTIVE;
    inspectionSampleQuantityEntity.createdBy = request.userId;
    inspectionSampleQuantityEntity.updatedBy = request.userId;

    return inspectionSampleQuantityEntity;
  }

  updateEntity(
    request: UpdateInspectionSampleQuantityRequestDto,
    entity: InspectionSampleQuantityEntity,
  ): InspectionSampleQuantityEntity {
    entity.inspectionSampleQuantity = request?.inspectionSampleQuantity;
    entity.description = request?.description;
    entity.dataDetails = request.dataDetails;
    entity.updatedBy = request?.userId;

    return entity;
  }

  async getList(
    request: GetListInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.inspectionSampleQuantityRepository.createQueryBuilder(
      'inspectionsamplequantity',
    );

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("inspectionsamplequantity"."code") like lower(:code) escape '\\'
          )`,
        {
          code: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('inspectionsamplequantity.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'code':
            query.andWhere(
              `lower("inspectionsamplequantity"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'inspectionSampleQuantity':
            query.andWhere(
              '"inspectionsamplequantity"."inspection_sample_quantity" = :inspectionSampleQuantity',
              {
                inspectionSampleQuantity: Number(value),
              },
            );
            break;
          case 'inspectionSampleQuantityFrom':
            query.andWhere(
              '"inspectionsamplequantity"."inspection_sample_quantity" >= :inspectionSampleQuantityFrom',
              {
                inspectionSampleQuantityFrom: Number(value),
              },
            );
            break;
          case 'inspectionSampleQuantityTo':
            query.andWhere(
              '"inspectionsamplequantity"."inspection_sample_quantity" <= :inspectionSampleQuantityTo',
              {
                inspectionSampleQuantityTo: Number(value),
              },
            );
            break;
          case 'description':
            query.andWhere(
              `lower("inspectionsamplequantity"."description") like lower(:description) escape '\\'`,
              {
                description: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'status':
            query.andWhere('"inspectionsamplequantity"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'createdById':
            query.andWhere(
              '"inspectionsamplequantity"."created_by" = :createdById',
              {
                createdById: Number(value),
              },
            );
            break;
          case 'createdByIds':
            query.andWhere(
              '"inspectionsamplequantity"."created_by" IN (:...createdByIds)',
              {
                createdByIds: value.split(',').map((id) => Number(id)),
              },
            );
            break;
          case 'createdAt':
            const createFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const createTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"inspectionsamplequantity"."created_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: createFrom,
                dateTo: createTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"inspectionsamplequantity"."updated_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: updateFrom,
                dateTo: upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'code':
            query.addOrderBy('"inspectionsamplequantity"."code"', order);
            break;
          case 'status':
            query.addOrderBy('"inspectionsamplequantity"."status"', order);
            break;
          case 'inspectionSampleQuantity':
            query.addOrderBy(
              '"inspectionsamplequantity"."inspection_sample_quantity"',
              order,
            );
            break;
          case 'description':
            query.addOrderBy('"inspectionsamplequantity"."description"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"inspectionsamplequantity"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"inspectionsamplequantity"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"inspectionsamplequantity"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('inspectionsamplequantity.id', 'DESC');
    }

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const [data, count] = await query.getManyAndCount();

    return { data, count };
  }

  async getListByInspectionSampleSymbolId(
    request: GetListInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { queryIds } = request;
    const query = connectionOptions
      .createQueryBuilder()
      .select([
        'DISTINCT(isq.id) as id',
        'isq.code as code',
        'isq.inspection_sample_quantity as inspectionSampleQuantity',
      ])
      .from('inspection_sample_symbols', 'iss')
      .innerJoin(
        'inspection_sample_symbol_details',
        'issd',
        'iss.id = issd.inspection_sample_symbol_id',
      )
      .innerJoin(
        'inspection_sample_symbol_detail_standards',
        'issds',
        'issds.inspection_sample_symbol_detail_id = issd.id',
      )
      .innerJoin(
        'inspection_sample_quantities',
        'isq',
        'issds.inspection_sample_quantity_id = isq.id',
      )
      .innerJoin(
        'inspection_sample_quantity_details',
        'isqd',
        'isq.id = isqd.inspection_sample_quantity_id',
      )
      .orderBy('isq.id', 'DESC');
    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('iss.id IN (:...ids)', {
        ids,
      });
    }
    const data = await query.getRawMany();
    return data;
  }

  async getDetail(
    request: GetDetailInspectionSampleQuantityRequestDto,
  ): Promise<InspectionSampleQuantityEntity> {
    const { id } = request;

    const data = await this.inspectionSampleQuantityRepository.findOne({
      where: { id: id },
    });

    return data;
  }

  async active(
    request: UpdateStatusInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { ids } = request;
    const data = await this.inspectionSampleQuantityRepository
      .createQueryBuilder()
      .update(InspectionSampleQuantityEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(
    request: UpdateStatusInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { ids } = request;
    const data = await this.inspectionSampleQuantityRepository
      .createQueryBuilder()
      .update(InspectionSampleQuantityEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }
}
