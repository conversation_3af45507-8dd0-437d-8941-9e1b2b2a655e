import { Column, Entity, OneToMany } from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { InspectionSampleQuantityDetailEntity } from '../../inspection-sample-quantity-detail/entities/inspection-sample-quantity-detail.entity';

@Entity({ name: 'inspection_sample_quantities' })
export class InspectionSampleQuantityEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    unique: true,
    nullable: false,
    length: 255,
  })
  code: string;

  @Column({
    type: 'float',
  })
  inspectionSampleQuantity: number;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  description: string;

  @Column({
    type: 'tinyint',
  })
  status: number;

  @OneToMany(
    () => InspectionSampleQuantityDetailEntity,
    (detail) => detail.inspectionSampleQuantity,
    {
      cascade: ['insert', 'update', 'remove'],
      onDelete: 'CASCADE',
      eager: true,
    },
  )
  dataDetails: InspectionSampleQuantityDetailEntity[];
}
