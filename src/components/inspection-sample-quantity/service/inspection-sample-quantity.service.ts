import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { InspectionSampleQuantityEntity } from '../entities/inspection-sample-quantity.entity';
import { DeleteInspectionSampleQuantityRequestDto } from './../request/delete-inspection-sample-quantity.request.dto';
import { GetDetailInspectionSampleQuantityRequestDto } from './../request/get-detail-inspection-sample-quantity.request.dto';
import { GetListInspectionSampleQuantityRequestDto } from './../request/get-list-inspection-sample-quantity.request.dto';
import { UpdateInspectionSampleQuantityRequestDto } from './../request/update-inspection-sample-quantity.request.dto';
import { UpdateStatusInspectionSampleQuantityRequestDto } from './../request/update-status-inspection-sample-quantity.request.dto';

import { GetDetailInspectionSampleQuantityResponseDto } from './../response/get-detail-inspection-sample-quantity.response.dto';
import { GetListInspectionSampleQuantityResponseDto } from './../response/get-list-inspection-sample-quantity.response.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { ValidateResultCommonDto } from '../../../common/dtos/validate.result.common.dto';
import { StatusEnum } from '../../../common/enums/status.enum';
import { BaseService } from '../../../common/service/base.service';
import { UserService } from '../../another-service/services/user-service';
import { InspectionSampleQuantityDetailService } from '../../inspection-sample-quantity-detail/service/inspection-sample-quantity-detail.service';
import { MasterDataReferenceService } from '../../master-data-reference/service/master-data-reference.service';
import { SamplingRateService } from '../../sampling-rate/service/sampling-rate.service';
import { InspectionSampleQuantityRepository } from '../repository/inspection-sample-quantity.repository';
import { CreateInspectionSampleQuantityRequestDto } from '../request/create-inspection-sample-quantity.request.dto';

@Injectable()
export class InspectionSampleQuantityService extends BaseService {
  private readonly logger = new Logger(InspectionSampleQuantityService.name);

  constructor(
    private readonly inspectionSampleQuantityRepository: InspectionSampleQuantityRepository,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly samplingRateService: SamplingRateService,

    private readonly inspectionSampleQuantityDetailService: InspectionSampleQuantityDetailService,

    private readonly masterDataReferenceService: MasterDataReferenceService,
  ) {
    super(userService);
  }

  async create(
    request: CreateInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const existCode =
      await this.inspectionSampleQuantityRepository.findOneByCode(request.code);
    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate inspection-sample-quantity
    const resultValidate = await this.validateSaveInspectionSampleQuantity(
      request,
    );
    if (resultValidate?.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(resultValidate.messageError))
        .build();
    }

    const inspectionSampleQuantityEntity =
      this.inspectionSampleQuantityRepository.createEntity(request);
    const inspectionSampleQuantity =
      await this.inspectionSampleQuantityRepository.create(
        inspectionSampleQuantityEntity,
      );

    return new ResponseBuilder(inspectionSampleQuantity)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(
    request: GetListInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { page } = request;
    const { data, count } =
      await this.inspectionSampleQuantityRepository.getList(request);

    const dataMap = await this.mapDataDetailToResponse(data);
    const dataMapUser = await this.mapUserInfoToResponse(dataMap);
    const response = plainToInstance(
      GetListInspectionSampleQuantityResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getListByInspectionSampleSymbolId(
    request: GetListInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { page, skip, take } = request;
    const data =
      await this.inspectionSampleQuantityRepository.getListByInspectionSampleSymbolId(
        request,
      );
    const dataPagination = data.slice(skip, skip + take);

    const dataMap = await this.mapDataDetailToResponse(dataPagination);
    const dataMapUser = await this.mapUserInfoToResponse(dataMap);
    const response = plainToInstance(
      GetListInspectionSampleQuantityResponseDto,
      dataMapUser,
    );
    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: data.length, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(
    request: GetDetailInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const inspectionSampleQuantity =
      await this.inspectionSampleQuantityRepository.getDetail(request);
    if (isEmpty(inspectionSampleQuantity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMap = await this.mapDataDetailToResponse([
      inspectionSampleQuantity,
    ]);
    const dataMapUser = await this.mapUserInfoToResponse(dataMap);
    const response = plainToInstance(
      GetDetailInspectionSampleQuantityResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(
    request: UpdateInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { id } = request;
    const inspectionSampleQuantity =
      await this.inspectionSampleQuantityRepository.findOneById(id);

    if (isEmpty(inspectionSampleQuantity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate inspection-sample-quantities in used
    const usedList = await this.validateInspectionSampleQuantityInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.INSPECTION_SAMPLE_QUANTITY_IN_USED'),
        )
        .build();
    }

    // Validate Code unique
    const existCode =
      await this.inspectionSampleQuantityRepository.findOneByCode(request.code);
    if (!isEmpty(existCode) && existCode.id !== inspectionSampleQuantity.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate inspection-sample-quantity
    const resultValidate = await this.validateSaveInspectionSampleQuantity(
      request,
    );
    if (resultValidate?.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(resultValidate.messageError))
        .build();
    }

    const dataUpdate = this.inspectionSampleQuantityRepository.updateEntity(
      request,
      inspectionSampleQuantity,
    );
    const data = await this.inspectionSampleQuantityRepository.update(
      dataUpdate,
    );

    return new ResponseBuilder(data)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(
    request: DeleteInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { id } = request;
    const inspectionSampleQuantity =
      await this.inspectionSampleQuantityRepository.findOneById(id);

    if (isEmpty(inspectionSampleQuantity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate inspection-sample-quantities in used
    const usedList = await this.validateInspectionSampleQuantityInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.INSPECTION_SAMPLE_QUANTITY_IN_USED'),
        )
        .build();
    }

    await this.inspectionSampleQuantityRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(
    request: UpdateStatusInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { ids } = request;

    const listExitsInDB =
      await this.inspectionSampleQuantityRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.inspectionSampleQuantityRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(
    request: UpdateStatusInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { ids } = request;

    const listExitsInDB =
      await this.inspectionSampleQuantityRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.inspectionSampleQuantityRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async findOneById(id: number): Promise<InspectionSampleQuantityEntity> {
    return await this.inspectionSampleQuantityRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<InspectionSampleQuantityEntity[]> {
    return await this.inspectionSampleQuantityRepository.findAllByIds(ids);
  }

  async validateSaveInspectionSampleQuantity(
    request:
      | CreateInspectionSampleQuantityRequestDto
      | UpdateInspectionSampleQuantityRequestDto,
  ): Promise<ValidateResultCommonDto> {
    // Validate exist sampling-rates
    const samplingRatesIds: number[] = Array.from(
      new Set(
        request?.dataDetails
          ?.flatMap((item) => item?.samplingRateId)
          .filter((id) => id !== null && id !== undefined),
      ),
    );

    if (samplingRatesIds.length !== request?.dataDetails.length) {
      return {
        result: true,
        messageError:
          'error.INSPECTION_SAMPLE_QUANTITY_SAMPLING_RATE_HAS_DUPLICATE',
      };
    }

    const existSamplingRates = await this.samplingRateService.findAllByIds(
      samplingRatesIds,
    );
    const inactiveSamplingRates = existSamplingRates?.filter(
      (unit) => unit.status === StatusEnum.IN_ACTIVE,
    );
    if (
      isEmpty(samplingRatesIds) ||
      isEmpty(existSamplingRates) ||
      !isEmpty(inactiveSamplingRates) ||
      samplingRatesIds?.length !== existSamplingRates?.length
    ) {
      return {
        result: true,
        messageError: 'error.SAMPLING_RATE_IS_NOT_EXISTS',
      };
    }

    let detailErrorMsg: string;
    request?.dataDetails?.map((detail) => {
      if (detail?.rejectQuantity > request?.inspectionSampleQuantity) {
        detailErrorMsg =
          'error.INSPECTION_SAMPLE_QUANTITY_LESS_THAN_REJECT_QUANTITY';
        return detailErrorMsg;
      } else if (detail?.acceptQuantity > detail?.rejectQuantity) {
        detailErrorMsg = 'error.REJECT_QUANTITY_LESS_THAN_ACCEPT_QUANTITY';
      }
    });

    if (detailErrorMsg) {
      return {
        result: true,
        messageError: detailErrorMsg,
      };
    }

    return { result: false, messageError: '' };
  }

  async mapDataDetailToResponse(data: any[]) {
    const inspectionSampleQuantityIds: number[] = Array.from(
      new Set(data?.flatMap((item) => item.id)),
    );
    if (isEmpty(inspectionSampleQuantityIds)) return data;

    const dataDetails =
      await this.inspectionSampleQuantityDetailService.findAllByInspectionSampleQuantityIds(
        inspectionSampleQuantityIds,
      );
    if (isEmpty(dataDetails)) return data;

    const dataDetailMap = dataDetails?.reduce((acc, item) => {
      const key = `${item.inspectionSampleQuantityId}`;
      if (isEmpty(acc[key])) {
        acc[key] = { dataDetails: [item] };
      } else {
        acc[key].dataDetails.push(item);
      }
      return acc;
    }, {});

    return (
      data?.map((item) => {
        return {
          ...item,
          dataDetails: dataDetailMap[item.id]?.dataDetails,
        };
      }) || []
    );
  }

  async validateInspectionSampleQuantityInUsed(id: number): Promise<boolean> {
    // Validate used in aql_sample_limit_details
    const aqlDetails =
      await this.masterDataReferenceService.findAllAqlByInspectionSampleQuantityIds(
        [id],
      );

    if (!isEmpty(aqlDetails)) {
      return true;
    }

    // Validate used in inspection_sample_symbol_detail_standards
    const issDetailStandards =
      await this.masterDataReferenceService.findAllIssByInspectionSampleQuantityIds(
        [id],
      );

    if (!isEmpty(issDetailStandards)) {
      return true;
    }

    // Validate used in another service
    const isUse =
      await this.masterDataReferenceService.validateDataMasterUseAnotherService(
        new ValidateMasterRequestDto('inspection_sample_quantity_id', id),
      );
    if (isUse) {
      return true;
    }

    return false;
  }

  async getIsqDataDetail(ids: number[]) {
    const dataDetails =
      await this.inspectionSampleQuantityDetailService.findAllByInspectionSampleQuantityIds(
        ids,
      );
    if (isEmpty(dataDetails)) return null;

    const dataDetailMap = dataDetails?.reduce((acc, item) => {
      const key = `${item.inspectionSampleQuantityId}`;
      if (isEmpty(acc[key])) {
        acc[key] = { dataDetails: [item] };
      } else {
        acc[key].dataDetails.push(item);
      }
      return acc;
    }, {});

    return dataDetailMap;
  }
}
