import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { BaseCommonResponseDto } from '../../../common/dtos/response/base.common.dto';
import { GetDetailInspectionSampleQuantityDataResponseDto } from './get-detail-inspection-sample-quantity-data.request.dto';

export class GetDetailInspectionSampleQuantityResponseDto extends BaseCommonResponseDto {
  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  inspectionSampleQuantity: number;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty()
  @Expose()
  status: number;

  @ApiProperty({ type: GetDetailInspectionSampleQuantityDataResponseDto })
  @Expose()
  @Type(() => GetDetailInspectionSampleQuantityDataResponseDto)
  dataDetails: GetDetailInspectionSampleQuantityDataResponseDto[];
}
