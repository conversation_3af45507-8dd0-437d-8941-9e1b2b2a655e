import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { SamplingRateResponseDto } from '../../../common/dtos/response/sampling-rate.common.dto';

export class GetDetailInspectionSampleQuantityDataResponseDto {
  @ApiProperty({ type: SamplingRateResponseDto })
  @Expose()
  @Type(() => SamplingRateResponseDto)
  samplingRate: SamplingRateResponseDto;

  @ApiProperty()
  @Expose()
  acceptQuantity: number;

  @ApiProperty()
  @Expose()
  rejectQuantity: number;

  @ApiProperty()
  @Expose()
  description?: string;
}
