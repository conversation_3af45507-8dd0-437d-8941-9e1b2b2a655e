import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  ACTIVE_INSPECTION_SAMPLE_QUANTITY_PERMISSION,
  CREATE_INSPECTION_SAMPLE_QUANTITY_PERMISSION,
  DELETE_INSPECTION_SAMPLE_QUANTITY_PERMISSION,
  DETAIL_INSPECTION_SAMPLE_QUANTITY_PERMISSION,
  INACTIVE_INSPECTION_SAMPLE_QUANTITY_PERMISSION,
  LIST_INSPECTION_SAMPLE_QUANTITY_PERMISSION,
  UPDATE_INSPECTION_SAMPLE_QUANTITY_PERMISSION,
} from '../../../utils/permissions/inspection-sample-quantity.permission';
import { CreateInspectionSampleQuantityRequestDto } from '../request/create-inspection-sample-quantity.request.dto';
import { DeleteInspectionSampleQuantityRequestDto } from '../request/delete-inspection-sample-quantity.request.dto';
import { GetDetailInspectionSampleQuantityRequestDto } from '../request/get-detail-inspection-sample-quantity.request.dto';
import { GetListInspectionSampleQuantityRequestDto } from '../request/get-list-inspection-sample-quantity.request.dto';
import { UpdateInspectionSampleQuantityRequestDto } from '../request/update-inspection-sample-quantity.request.dto';
import { UpdateStatusInspectionSampleQuantityRequestDto } from '../request/update-status-inspection-sample-quantity.request.dto';
import { InspectionSampleQuantityService } from '../service/inspection-sample-quantity.service';

@Controller('inspection-sample-quantities')
export class InspectionSampleQuantityController {
  constructor(
    private readonly inspectionSampleQuantityService: InspectionSampleQuantityService,
  ) {}

  @PermissionCode(DETAIL_INSPECTION_SAMPLE_QUANTITY_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Inspection-sample-quantities'],
    summary: 'Chi tiết Inspection-sample-quantities',
    description: 'Chi tiết Inspection-sample-quantities',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionSampleQuantityService.getDetail(request);
  }

  @PermissionCode(LIST_INSPECTION_SAMPLE_QUANTITY_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Inspection-sample-quantities'],
    summary: 'Danh sách Inspection-sample-quantities',
    description: 'Danh sách Inspection-sample-quantities',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionSampleQuantityService.getList(request);
  }

  @PermissionCode(LIST_INSPECTION_SAMPLE_QUANTITY_PERMISSION.code)
  @Get('/list-by-inspection-sample-symbol')
  @ApiOperation({
    tags: ['Inspection-sample-quantities'],
    summary:
      'Danh sách Inspection-sample-quantities by inspection sample symbol',
    description:
      'Danh sách Inspection-sample-quantities  by inspection sample symbol',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getListByInspectionSampleSymbol(
    @Query() query: GetListInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionSampleQuantityService.getListByInspectionSampleSymbolId(
      request,
    );
  }

  @PermissionCode(CREATE_INSPECTION_SAMPLE_QUANTITY_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Inspection-sample-quantities'],
    summary: 'Tạo Inspection-sample-quantities mới',
    description: 'Tạo Inspection-sample-quantities mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateInspectionSampleQuantityRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionSampleQuantityService.create(request);
  }

  @PermissionCode(UPDATE_INSPECTION_SAMPLE_QUANTITY_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Inspection-sample-quantities'],
    summary: 'Cập nhật Inspection-sample-quantities',
    description: 'Cập nhật Inspection-sample-quantities',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionSampleQuantityService.update(request);
  }

  @PermissionCode(ACTIVE_INSPECTION_SAMPLE_QUANTITY_PERMISSION.code)
  @Put('/active')
  @ApiOperation({
    tags: ['Inspection-sample-quantities'],
    summary: 'Cập nhật Inspection-sample-quantities Status Active',
    description: 'Cập nhật Inspection-sample-quantities Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async active(
    @Body() body: UpdateStatusInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionSampleQuantityService.active(request);
  }

  @PermissionCode(INACTIVE_INSPECTION_SAMPLE_QUANTITY_PERMISSION.code)
  @Put('/inactive')
  @ApiOperation({
    tags: ['Inspection-sample-quantities'],
    summary: 'Cập nhật Inspection-sample-quantities Status Inactive',
    description: 'Cập nhật Inspection-sample-quantities Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async inactive(
    @Body() body: UpdateStatusInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionSampleQuantityService.inactive(request);
  }

  @PermissionCode(DELETE_INSPECTION_SAMPLE_QUANTITY_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Inspection-sample-quantities'],
    summary: 'Xóa Inspection-sample-quantities',
    description: 'Xóa Inspection-sample-quantities',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(
    @Param() param: DeleteInspectionSampleQuantityRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionSampleQuantityService.delete(request);
  }
}
