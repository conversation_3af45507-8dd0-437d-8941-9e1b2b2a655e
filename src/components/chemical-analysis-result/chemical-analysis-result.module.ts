import { Modu<PERSON> } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { ChemicalAnalysisResultController } from './controller/chemical-analysis-result.controller';
import { ChemicalAnalysisResultService } from './service/chemical-analysis-result.service';

@Module({
  imports: [AnotherServiceModule, BaseProcessModule],
  providers: [ChemicalAnalysisResultService],
  exports: [ChemicalAnalysisResultService],
  controllers: [ChemicalAnalysisResultController],
})
export class ChemicalAnalysisResultModule {}
