import { Controller, Get, Query } from '@nestjs/common';

import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';
import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  EXPORT_CHEMICAL_ANALYSIS_RESULT_PERMISSION,
  LIST_CHEMICAL_ANALYSIS_RESULT_PERMISSION,
} from '../../../utils/permissions/chemical-analysis-result.permission';
import { GetListChemicalAnalysisResultRequestDto } from '../request/get-list-chemical-analysis-result.request.dto';
import { ChemicalAnalysisResultService } from '../service/chemical-analysis-result.service';

@Controller('chemical-analysis-results')
export class ChemicalAnalysisResultController {
  constructor(
    private readonly chemicalAnalysisResultService: ChemicalAnalysisResultService,
  ) {}

  @PermissionCode(LIST_CHEMICAL_ANALYSIS_RESULT_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Chemical-analysis-Result'],
    summary: 'Danh sách Chemical-analysis-Result',
    description: 'Danh sách Chemical-analysis-Result',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListChemicalAnalysisResultRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && responseError?.statusCode && !isEmpty(responseError)) {
      return responseError;
    }

    return this.chemicalAnalysisResultService.getList(request);
  }

  @PermissionCode(EXPORT_CHEMICAL_ANALYSIS_RESULT_PERMISSION.code)
  @Get('/export')
  @ApiOperation({
    tags: ['Export-excel'],
    summary: 'Export in list screen',
    description: 'Export in list screen',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public export(
    @Query() query: GetListChemicalAnalysisResultRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return this.chemicalAnalysisResultService.export(request);
  }
}
