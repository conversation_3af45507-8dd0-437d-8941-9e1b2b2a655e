import { Injectable, Logger } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { ResponseCodeEnum } from '../../../constant/response-code.enum';

import { exportExcel } from '../../../helper/export.helper';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';
import { addSevenHoursToAllItems } from '../../../utils/helper';
import { ResponseBuilder } from '../../../utils/response-builder';
import { QmsxTicketService } from '../../another-service/services/qmsx-ticket-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { ExportExcelColumnProperty } from '../../export-excel/dto/export-excel-column-property.dto';
import {
  chemicalAnalysisResultColumnProperties,
  chemicalAnalysisResultExportSheets,
} from '../../export-excel/template/export-chemical-analysis-result-template';
import { GetListChemicalAnalysisResultRequestDto } from '../request/get-list-chemical-analysis-result.request.dto';

@Injectable()
export class ChemicalAnalysisResultService {
  private readonly logger = new Logger(ChemicalAnalysisResultService.name);

  constructor(
    private readonly i18n: I18nService,
    private readonly baseService: BaseProcessService,

    protected readonly qmsxTicketService: QmsxTicketService,
  ) {}

  async getList(
    request: GetListChemicalAnalysisResultRequestDto,
  ): Promise<any> {
    const { page } = request;
    const { data, count } =
      await this.qmsxTicketService.getListChemicalAnalysisResult(request);

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('error.SUCCESS'))
      .build();
  }

  async export(request: GetListChemicalAnalysisResultRequestDto): Promise<any> {
    const { data } = await this.getList(request);
    const dataMapping = addSevenHoursToAllItems(data);
    const dates = new Set<string>();

    const transformedData = dataMapping.items.map((item) => {
      const transformedItem = { ...item };
      item.results.forEach((result) => {
        const date = new Date(result.measurementDate);

        const year = date.getUTCFullYear();
        const month = String(date.getUTCMonth() + 1).padStart(2, '0');
        const day = String(date.getUTCDate()).padStart(2, '0');

        const formattedDate = `${day}/${month}/${year}`;
        dates.add(formattedDate);
        const measurementDateField = `measurementDate${formattedDate}`;
        const adjustmentValueField = `adjustmentValue${formattedDate}`;
        const pmField = `pm${formattedDate}`;
        transformedItem[measurementDateField] = result.measurementValue;
        transformedItem[adjustmentValueField] = result.adjustmentValue;
        transformedItem[pmField] =
          result.pm === 0 ? 'N' : result.pm === 1 ? 'Y' : '';
      });
      return transformedItem;
    });
    const sortedDates = [...dates].sort((a, b) => {
      const [dayA, monthA, yearA] = a.split('/');
      const [dayB, monthB, yearB] = b.split('/');

      const dateA = new Date(Number(yearA), Number(monthA) - 1, Number(dayA));
      const dateB = new Date(Number(yearB), Number(monthB) - 1, Number(dayB));

      return dateA.getTime() - dateB.getTime();
    });
    const mapData = new Map<string, any[]>();
    const mapColumn = new Map<string, ExportExcelColumnProperty[]>();
    const colomnProperties = [...chemicalAnalysisResultColumnProperties];
    sortedDates.forEach((s) => {
      colomnProperties.push(
        new ExportExcelColumnProperty({
          header: s,
          key: 'measurementDate' + s,
          width: 20,
          styleColumn: {
            alignment: {
              horizontal: 'right',
            },
          },
        }),
      );

      colomnProperties.push(
        new ExportExcelColumnProperty({
          header: 'export.chemicalAnalysisResult.adjustmentValue',
          key: 'adjustmentValue' + s,
          width: 20,
        }),
      );

      colomnProperties.push(
        new ExportExcelColumnProperty({
          header: 'export.chemicalAnalysisResult.pm',
          key: 'pm' + s,
          width: 20,
          styleColumn: {
            alignment: {
              horizontal: 'center',
            },
          },
        }),
      );
    });
    mapColumn.set(chemicalAnalysisResultExportSheets[0], colomnProperties);
    mapData.set(chemicalAnalysisResultExportSheets[0], transformedData);
    const fileName = await this.i18n.translate(
      'export.chemicalAnalysisResult.fileName',
    );
    const buffer = await exportExcel(
      chemicalAnalysisResultExportSheets,
      mapData,
      mapColumn,
      fileName,
      this.i18n,
    );
    return new ResponseBuilder(buffer)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
