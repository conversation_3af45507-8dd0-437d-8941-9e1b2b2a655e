import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { GET_ALL_ENUM } from '../../../constant/common';
import { PaginationQuery } from '../../../utils/dto/request/pagination.query';

export class GetListChemicalAnalysisResultRequestDto extends PaginationQuery {
  @ApiPropertyOptional({
    enum: [0, 1],
  })
  @IsEnum(GET_ALL_ENUM)
  @IsOptional()
  isGetAll: GET_ALL_ENUM;
}
