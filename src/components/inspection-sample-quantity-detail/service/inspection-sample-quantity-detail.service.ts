import { Injectable, Logger } from '@nestjs/common';
import { In } from 'typeorm';

import { InspectionSampleQuantityDetailRepository } from '../repository/inspection-sample-quantity-detail.repository';

@Injectable()
export class InspectionSampleQuantityDetailService {
  private readonly logger = new Logger(
    InspectionSampleQuantityDetailService.name,
  );

  constructor(
    private readonly inspectionSampleQuantityDetailRepository: InspectionSampleQuantityDetailRepository,
  ) {}

  async findAllByInspectionSampleQuantityIds(
    inspectionSampleQuantityIds: number[],
  ): Promise<any> {
    return await this.inspectionSampleQuantityDetailRepository.findAllByInspectionSampleQuantityIds(
      inspectionSampleQuantityIds,
    );
  }

  async findAllBySamplingRateIds(samplingRateIds: number[]): Promise<any> {
    const filterCondition = {
      samplingRateId: In(samplingRateIds),
    };
    return await this.inspectionSampleQuantityDetailRepository.findByCondition(
      filterCondition,
    );
  }
}
