import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { InspectionSampleQuantityEntity } from '../../inspection-sample-quantity/entities/inspection-sample-quantity.entity';

@Entity({ name: 'inspection_sample_quantity_details' })
export class InspectionSampleQuantityDetailEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  inspectionSampleQuantityId: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  samplingRateId: number;

  @Column({
    type: 'float',
    nullable: false,
  })
  acceptQuantity: number;

  @Column({
    type: 'float',
    nullable: false,
  })
  rejectQuantity: number;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  description: string;

  @ManyToOne(
    () => InspectionSampleQuantityEntity,
    (entity) => entity.dataDetails,
    {
      orphanedRowAction: 'delete',
    },
  )
  @JoinColumn({
    name: 'inspection_sample_quantity_id',
    referencedColumnName: 'id',
  })
  inspectionSampleQuantity: InspectionSampleQuantityEntity;
}
