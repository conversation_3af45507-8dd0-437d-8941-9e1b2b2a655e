import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InspectionSampleQuantityDetailEntity } from './entities/inspection-sample-quantity-detail.entity';
import { InspectionSampleQuantityDetailRepository } from './repository/inspection-sample-quantity-detail.repository';
import { InspectionSampleQuantityDetailService } from './service/inspection-sample-quantity-detail.service';

@Module({
  imports: [TypeOrmModule.forFeature([InspectionSampleQuantityDetailEntity])],
  providers: [
    InspectionSampleQuantityDetailService,
    InspectionSampleQuantityDetailRepository,
  ],
  exports: [InspectionSampleQuantityDetailService],
  controllers: [],
})
export class InspectionSampleQuantityDetailModule {}
