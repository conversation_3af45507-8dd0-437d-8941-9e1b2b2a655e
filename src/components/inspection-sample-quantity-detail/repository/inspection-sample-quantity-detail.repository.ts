import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { parseJ<PERSON><PERSON><PERSON><PERSON>ueField } from '../../../utils/common';
import { InspectionSampleQuantityDetailEntity } from '../entities/inspection-sample-quantity-detail.entity';

@Injectable()
export class InspectionSampleQuantityDetailRepository extends BaseAbstractRepository<InspectionSampleQuantityDetailEntity> {
  constructor(
    @InjectRepository(InspectionSampleQuantityDetailEntity)
    private readonly inspectionSampleQuantityDetailRepository: Repository<InspectionSampleQuantityDetailEntity>,
  ) {
    super(inspectionSampleQuantityDetailRepository);
  }

  async findAllByInspectionSampleQuantityIds(
    inspectionSampleQuantityIds: number[],
  ) {
    const query = this.inspectionSampleQuantityDetailRepository
      .createQueryBuilder('detail')
      .select([
        'detail.inspection_sample_quantity_id AS "inspectionSampleQuantityId"',
        'detail.accept_quantity AS "acceptQuantity"',
        'detail.reject_quantity AS "rejectQuantity"',
        'detail.description AS "description"',
      ])
      .leftJoin('sampling_rates', 'sr', 'detail.sampling_rate_id = sr.id')
      .addSelect([
        `CASE WHEN COUNT(sr.id) = 0 THEN '{}' ELSE 
        (SELECT sr.id as id,sr.code as code,sr.sampling_rate as samplingRate FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "samplingRate"`,
      ])
      .where(
        'detail.inspection_sample_quantity_id IN (:...inspectionSampleQuantityIds)',
        {
          inspectionSampleQuantityIds,
        },
      )
      .groupBy('detail.inspection_sample_quantity_id')
      .addGroupBy('detail.acceptQuantity')
      .addGroupBy('detail.rejectQuantity')
      .addGroupBy('detail.description')
      .addGroupBy('sr.id')
      .addGroupBy('sr.code')
      .addGroupBy('sr.sampling_rate');

    const result = await query.getRawMany();
    return result?.map((item) => {
      return {
        ...item,
        samplingRate: parseJSONValueField(item.samplingRate),
      };
    });
  }
}
