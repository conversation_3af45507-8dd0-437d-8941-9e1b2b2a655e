import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt } from 'class-validator';
import { CreateCustomerClaimFeeRequestDto } from './create-customer-claim-fee.request.dto';

export class UpdateCustomerClaimFeeRequestDto extends CreateCustomerClaimFeeRequestDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;
}
