import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsDateString,
  IsInt,
  IsNotEmpty,
  IsOptional,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { FileRequestDto } from '../../common/request/file.request.dto';
import { CreateCustomerClaimFeeDetailRequestDto } from './create-customer-claim-fee-detail.request.dto';

export class CreateCustomerClaimFeeRequestDto extends BaseDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  requestDepartmentId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  customerId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsDateString()
  fromDate: Date;

  @ApiProperty()
  @IsNotEmpty()
  @IsDateString()
  toDate: Date;

  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(7)
  claimPeriod: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  createdBy?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  updatedBy?: number;

  @ApiPropertyOptional({ type: [CreateCustomerClaimFeeDetailRequestDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateCustomerClaimFeeDetailRequestDto)
  customerClaimFeeDetail?: CreateCustomerClaimFeeDetailRequestDto[];

  @ApiPropertyOptional({ type: [FileRequestDto] })
  @IsArray()
  @IsOptional()
  @Type(() => FileRequestDto)
  files: FileRequestDto[];
}
