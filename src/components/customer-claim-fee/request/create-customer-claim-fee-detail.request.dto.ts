import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsInt,
  IsNotEmpty,
  IsOptional,
  MaxLength,
} from 'class-validator';
import { FileRequestDto } from '../../common/request/file.request.dto';

export class CreateCustomerClaimFeeDetailRequestDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  vocHandlingTicketId?: number;

  @ApiProperty()
  @IsNotEmpty()
  itemId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  itemLineId: number;

  @ApiProperty()
  @IsNotEmpty()
  claimQuantity: number;

  @ApiProperty()
  @IsNotEmpty()
  estimatedUnitPrice: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  errorId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  passQuantity: number;

  @ApiProperty()
  @IsNotEmpty()
  billingUnitPrice: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  ngBillingQuantity: number;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  description?: string;

  @ApiPropertyOptional({ type: [FileRequestDto] })
  @IsArray()
  @IsOptional()
  @Type(() => FileRequestDto)
  files: FileRequestDto[];
}
