import { Module } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { CustomerClaimFeeController } from './controller/customer-claim-fee.controller';
import { CustomerClaimFeeService } from './service/customer-claim-fee.service';

@Module({
  imports: [AnotherServiceModule, BaseProcessModule],
  providers: [CustomerClaimFeeService],
  exports: [CustomerClaimFeeService],
  controllers: [CustomerClaimFeeController],
})
export class CustomerClaimFeeModule {}
