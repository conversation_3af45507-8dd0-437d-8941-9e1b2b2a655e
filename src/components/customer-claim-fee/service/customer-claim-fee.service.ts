import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import * as fs from 'fs';
import path from 'path';
import { BaseService } from '../../../common/service/base.service';
import { IdParamDto } from '../../../core/dto/request/id-param.request.dto';
import { writeDataToExcel } from '../../../helper/export.helper';
import { SRC_DIR } from '../../../main';
import { IdParamSqlDto } from '../../../utils/dto/request/param-id-sql.request.dto';
import { formatDateWithOffset } from '../../../utils/helper';
import { QmsxCustomerSupportService } from '../../another-service/services/qmsx-customer-support-service';
import { UserService } from '../../another-service/services/user-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { CUSTOMER_CLAIM_FEE_BY_CUSTOMER_TEMPLATE } from '../../export-excel/constants/export-excel.constants';
import {
  ExportCellNumberDto,
  ExportCellStringDto,
} from '../../export-excel/dto/export-cell.dto';
import { ExportClaimFeeTicketByCustomerDto } from '../../export-excel/dto/export-claim-fee-ticket-by-customer.dto';
import { CreateCustomerClaimFeeRequestDto } from '../request/create-customer-claim-fee.request.dto';
import { GetListCustomerClaimFeeRequestDto } from '../request/get-list-customer-claim-fee.request.dto';
import { UpdateCustomerClaimFeeRequestDto } from '../request/update-customer-claim-fee.request.dto';

@Injectable()
export class CustomerClaimFeeService extends BaseService {
  private readonly logger = new Logger(CustomerClaimFeeService.name);

  constructor(
    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly qmsxCustomerService: QmsxCustomerSupportService,

    private readonly baseService: BaseProcessService,
  ) {
    super(userService);
  }

  async create(request: CreateCustomerClaimFeeRequestDto): Promise<any> {
    const { result, messageError } = await this.validateCreateUpdate(request);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }
    request.createdBy = request.userId;
    request.updatedBy = request.userId;
    const entity = await this.qmsxCustomerService.createCustomerClaimFee(
      request,
    );
    if (isEmpty(entity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    return new ResponseBuilder(entity)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async validateCreateUpdate(
    request:
      | CreateCustomerClaimFeeRequestDto
      | UpdateCustomerClaimFeeRequestDto,
  ) {
    const keyDup = ['itemId', 'vocHandlingTicketId'];
    if (
      this.baseService.checkDuplicateByKey(
        request.customerClaimFeeDetail,
        keyDup,
      )
    ) {
      return {
        result: false,
        messageError: 'error.CUSTOMER_CLAIM_FEE_HAS_DUPPLICATE_KEY',
      };
    }
    const { itemIds, errorIds, vocHandlingTicketIds } =
      request.customerClaimFeeDetail.reduce(
        (result, item) => {
          if (item.itemId !== null && item.itemId !== undefined) {
            result.itemIds.add(item.itemId);
          }
          if (item.errorId !== null && item.errorId !== undefined) {
            result.errorIds.add(item.errorId);
          }
          if (
            item.vocHandlingTicketId !== null &&
            item.vocHandlingTicketId !== undefined
          ) {
            result.vocHandlingTicketIds.add(item.vocHandlingTicketId);
          }
          return result;
        },
        {
          errorIds: new Set<number>(),
          itemIds: new Set<number>(),
          vocHandlingTicketIds: new Set<number>(),
        },
      );
    if (vocHandlingTicketIds.size > 0) {
      const vocHandlingTicets =
        await this.qmsxCustomerService.getAllVocHandlingTicketByIds({
          ids: Array.from(vocHandlingTicketIds),
        });
      if (vocHandlingTicets.length !== vocHandlingTicketIds.size) {
        return {
          result: false,
          messageError: 'error.VOC_HANDLING_TICKET_NOT_FOUND',
        };
      }
    }
    return await this.baseService.validateMaster({
      itemIds: Array.from(itemIds),
      errorIds: Array.from(errorIds),
      departmentIds:
        request.requestDepartmentId != null
          ? [request.requestDepartmentId]
          : null,
      customerIds: [+request.customerId],
    });
  }

  async update(request: UpdateCustomerClaimFeeRequestDto): Promise<any> {
    const entity = await this.qmsxCustomerService.getDetailCustomerClaimFeeById(
      {
        id: request.id,
      },
    );
    if (isEmpty(entity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const updateRequest = await this.handleRequestUpdate(request);
    const responseEntity =
      await this.qmsxCustomerService.updateCustomerClaimFee(updateRequest);
    if (isEmpty(responseEntity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }
    return new ResponseBuilder(responseEntity)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: IdParamDto): Promise<any> {
    const entity = await this.qmsxCustomerService.getDetailCustomerClaimFeeById(
      request,
    );
    if (isEmpty(entity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const vocHandlingTicketIds: number[] = Array.from(
      new Set(
        entity.customerClaimFeeDetail
          .map((item) => item.vocHandlingTicketId)
          .filter((id): id is number => typeof id === 'number'),
      ),
    );
    const vocHandlingTickets =
      await this.qmsxCustomerService.getAllVocHandlingTicketByIds({
        ids: vocHandlingTicketIds,
      });
    const vocHandlingTicketMap = new Map<number, any>();
    vocHandlingTickets.forEach((ticket) => {
      vocHandlingTicketMap.set(ticket.id, ticket);
    });
    entity.customerClaimFeeDetail.forEach((item) => {
      if (
        item.vocHandlingTicketId !== null &&
        item.vocHandlingTicketId !== undefined
      ) {
        if (vocHandlingTicketMap.has(item.vocHandlingTicketId)) {
          item.vocHandlingTicket = vocHandlingTicketMap.get(
            item.vocHandlingTicketId,
          );
        }
      }
    });
    const dataMapUser = await this.baseService.mapMasterInfoToResponse([
      entity,
    ]);
    const details = await this.baseService.mapMasterInfoToResponse(
      entity.customerClaimFeeDetail,
    );
    dataMapUser.forEach((s) => {
      s.customerClaimFeeDetail = details;
    });
    return new ResponseBuilder(dataMapUser[0] ? dataMapUser[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async handleRequestUpdate(request: UpdateCustomerClaimFeeRequestDto) {
    const updateRequest = plainToInstance(
      UpdateCustomerClaimFeeRequestDto,
      request,
    );
    updateRequest.updatedBy = request.userId;
    updateRequest.id = request.id;
    return updateRequest;
  }

  async getList(request: GetListCustomerClaimFeeRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } =
      await this.qmsxCustomerService.getListCustomerClaimFee(request);

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async approve(request: IdParamDto): Promise<any> {
    const { result, messageError } =
      await this.qmsxCustomerService.approveCustomerClaimFee(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async reject(request: IdParamDto): Promise<any> {
    const { result, messageError } =
      await this.qmsxCustomerService.rejectCustomerClaimFee(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: IdParamDto): Promise<any> {
    const { result, messageError } =
      await this.qmsxCustomerService.deleteCustomerClaimFee(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async exportDetail(request: IdParamSqlDto): Promise<any> {
    const templatePath = this.pathFileName(
      CUSTOMER_CLAIM_FEE_BY_CUSTOMER_TEMPLATE.name,
    );
    if (!fs.existsSync(templatePath)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.FILE_NOT_FOUND'))
        .build();
    }
    const { data } = await this.getDetail(request);

    if (isEmpty(data)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.NOT_FOUND)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const mapReplace: Map<string, string> = new Map<string, string>();

    const mapData: Map<number, ExportClaimFeeTicketByCustomerDto[]> = new Map<
      number,
      ExportClaimFeeTicketByCustomerDto[]
    >();
    const formattedFromDate = formatDateWithOffset(new Date(data.fromDate), 7);
    const formattedToDate = formatDateWithOffset(new Date(data.toDate), 7);

    mapReplace.set('customerName', data.customer.name);
    mapReplace.set('claimPeriod', data.claimPeriod);
    mapReplace.set('datePeriod', `${formattedFromDate} - ${formattedToDate}`);
    mapReplace.set('totalEstimatedAmount', data.totalEstimatedAmount);
    mapReplace.set('totalBillingAmount', data.totalBillingAmount);

    const listExportDto: ExportClaimFeeTicketByCustomerDto[] =
      data.customerClaimFeeDetail.map((ticket) => {
        const exportDto = new ExportClaimFeeTicketByCustomerDto();
        exportDto.itemCode = new ExportCellStringDto({
          value: ticket.item.code,
        });
        exportDto.itemName = new ExportCellStringDto({
          value: ticket.item.name,
        });
        exportDto.itemLineName = new ExportCellStringDto({
          value: ticket.itemLine.name,
        });
        exportDto.goodsTypeName = new ExportCellStringDto({
          value: ticket.item.goodsType.name ?? '',
        });
        exportDto.unitName = new ExportCellStringDto({
          value: ticket.item.itemUnits[0].unitName,
        });
        exportDto.claimQuantity = new ExportCellNumberDto({
          value: ticket.claimQuantity,
          style: { alignment: { horizontal: 'right' } },
        });
        exportDto.estimatedUnitPrice = new ExportCellNumberDto({
          value: ticket.estimatedUnitPrice,
          style: { alignment: { horizontal: 'right' } },
        });
        exportDto.estimatedAmount = new ExportCellNumberDto({
          value: ticket.claimQuantity * ticket.estimatedUnitPrice,
          style: { alignment: { horizontal: 'right' } },
        });
        exportDto.errorName = new ExportCellStringDto({
          value: ticket.error.name,
        });
        exportDto.passQuantity = new ExportCellNumberDto({
          value: ticket.passQuantity,
          style: { alignment: { horizontal: 'right' } },
        });
        exportDto.ngBillingQuantity = new ExportCellNumberDto({
          value: ticket.ngBillingQuantity,
          style: { alignment: { horizontal: 'right' } },
        });
        exportDto.billingUnitPrice = new ExportCellNumberDto({
          value: ticket.billingUnitPrice,
          style: { alignment: { horizontal: 'right' } },
        });
        exportDto.billingAmount = new ExportCellNumberDto({
          value: ticket.ngBillingQuantity * ticket.billingUnitPrice,
          style: { alignment: { horizontal: 'right' } },
        });
        return exportDto;
      });

    mapData.set(
      CUSTOMER_CLAIM_FEE_BY_CUSTOMER_TEMPLATE.indexRow,
      listExportDto,
    );

    const buffer = await writeDataToExcel({
      templatePath: templatePath,
      mapData: mapData,
      mapReplace: mapReplace,
    });
    return new ResponseBuilder(buffer)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async report(request: GetListCustomerClaimFeeRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } =
      await this.qmsxCustomerService.getReportSummaryCustomerClaimFee(request);

    const dataMap = await this.baseService.mapMasterInfoForAllOfKey(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMap,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  private pathFileName(fileName: string) {
    return path.join(SRC_DIR, '..', 'export-template', fileName);
  }
}
