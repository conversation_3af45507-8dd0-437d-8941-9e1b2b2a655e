import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import { IdParamDto } from '../../../core/dto/request/id-param.request.dto';
import { IdParamSqlDto } from '../../../utils/dto/request/param-id-sql.request.dto';
import {
  APPROVE_CUSTOMER_CLAIM_FEE_PERMISSION,
  CREATE_CUSTOMER_CLAIM_FEE_PERMISSION,
  DELETE_CUSTOMER_CLAIM_FEE_PERMISSION,
  DETAIL_CUSTOMER_CLAIM_FEE_PERMISSION,
  EXPORT_CUSTOMER_CLAIM_FEE_PERMISSION,
  LIST_CUSTOMER_CLAIM_FEE_PERMISSION,
  REJECT_CUSTOMER_CLAIM_FEE_PERMISSION,
  UPDATE_CUSTOMER_CLAIM_FEE_PERMISSION,
} from '../../../utils/permissions/customer-claim-fee.permission';
import { LIST_REPORT_CLAIM_CUSTOMER_PERMISSION } from '../../../utils/permissions/report-claim-customer.permission';
import { CreateCustomerClaimFeeRequestDto } from '../request/create-customer-claim-fee.request.dto';
import { DeleteCustomerClaimFeeRequestDto } from '../request/delete-customer-claim-fee.request.dto';
import { GetDetailCustomerClaimFeeRequestDto } from '../request/get-detail-customer-claim-fee.request.dto';
import { GetListCustomerClaimFeeRequestDto } from '../request/get-list-customer-claim-fee.request.dto';
import { UpdateCustomerClaimFeeRequestDto } from '../request/update-customer-claim-fee.request.dto';
import { CustomerClaimFeeService } from '../service/customer-claim-fee.service';

@Controller('customer-claim-fees')
export class CustomerClaimFeeController {
  constructor(
    private readonly customerClaimFeeService: CustomerClaimFeeService,
  ) {}

  @PermissionCode(DETAIL_CUSTOMER_CLAIM_FEE_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Customer-claim-fees'],
    summary: 'Chi tiết Customer-claim-fees',
    description: 'Chi tiết Customer-claim-fees',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailCustomerClaimFeeRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.customerClaimFeeService.getDetail(request);
  }

  @PermissionCode(LIST_CUSTOMER_CLAIM_FEE_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Customer-claim-fees'],
    summary: 'Danh sách Customer-claim-fees',
    description: 'Danh sách Customer-claim-fees',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListCustomerClaimFeeRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.customerClaimFeeService.getList(request);
  }

  @PermissionCode(CREATE_CUSTOMER_CLAIM_FEE_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Customer-claim-fees'],
    summary: 'Tạo Customer-claim-fees mới',
    description: 'Tạo Customer-claim-fees mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateCustomerClaimFeeRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.customerClaimFeeService.create(request);
  }

  @PermissionCode(UPDATE_CUSTOMER_CLAIM_FEE_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Customer-claim-fees'],
    summary: 'Cập nhật Customer-claim-fees',
    description: 'Cập nhật Customer-claim-fees',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateCustomerClaimFeeRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.customerClaimFeeService.update(request);
  }

  @PermissionCode(APPROVE_CUSTOMER_CLAIM_FEE_PERMISSION.code)
  @Put('/:id/approve')
  @ApiOperation({
    tags: ['Customer-claim-fees'],
    summary: 'Approve Customer-claim-fees',
    description: 'Approve Customer-claim-fees',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async approve(@Param() param: IdParamDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.customerClaimFeeService.approve(request);
  }

  @PermissionCode(REJECT_CUSTOMER_CLAIM_FEE_PERMISSION.code)
  @Put('/:id/reject')
  @ApiOperation({
    tags: ['Customer-claim-fees'],
    summary: 'Reject Customer-claim-fees',
    description: 'Reject Customer-claim-fees',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async reject(@Param() body: IdParamDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.customerClaimFeeService.reject(request);
  }

  @PermissionCode(DELETE_CUSTOMER_CLAIM_FEE_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Customer-claim-fees'],
    summary: 'Xóa Customer-claim-fees',
    description: 'Xóa Customer-claim-fees',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(
    @Param() param: DeleteCustomerClaimFeeRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.customerClaimFeeService.delete(request);
  }

  @PermissionCode(EXPORT_CUSTOMER_CLAIM_FEE_PERMISSION.code)
  @Get('/export-detail')
  @ApiOperation({
    tags: ['Export-excel'],
    summary: 'Export in detail screen',
    description: 'Export in detail screen',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public exportDetail(@Query() query: IdParamSqlDto): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return this.customerClaimFeeService.exportDetail(request);
  }

  @PermissionCode(LIST_REPORT_CLAIM_CUSTOMER_PERMISSION.code)
  @Get('/report')
  @ApiOperation({
    tags: ['Customer-claim-fees'],
    summary: 'Danh sách Customer-claim-fees',
    description: 'Danh sách Customer-claim-fees',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public report(
    @Query() query: GetListCustomerClaimFeeRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.customerClaimFeeService.report(request);
  }
}
