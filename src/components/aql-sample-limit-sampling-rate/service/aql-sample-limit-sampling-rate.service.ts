import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { DataSource, In } from 'typeorm';

import { AqlSampleLimitSamplingRateEntity } from '../entities/aql-sample-limit-sampling-rate.entity';
import { AqlSampleLimitSamplingRateRepository } from '../repository/aql-sample-limit-sampling-rate.repository';

@Injectable()
export class AqlSampleLimitSamplingRateService {
  private readonly logger = new Logger(AqlSampleLimitSamplingRateService.name);

  constructor(
    private readonly aqlSampleLimitSamplingRateRepository: AqlSampleLimitSamplingRateRepository,

    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,
  ) {}

  async findAllByAqlSampleLimitIds(aqlSampleLimitIds: number[]): Promise<any> {
    return await this.aqlSampleLimitSamplingRateRepository.findAllByAqlSampleLimitIds(
      aqlSampleLimitIds,
    );
  }

  async findAllBySamplingRateIds(
    samplingRateIds: number[],
  ): Promise<AqlSampleLimitSamplingRateEntity[]> {
    const filterCondition = {
      samplingRateId: In(samplingRateIds),
    };
    return await this.aqlSampleLimitSamplingRateRepository.findByCondition(
      filterCondition,
    );
  }
}
