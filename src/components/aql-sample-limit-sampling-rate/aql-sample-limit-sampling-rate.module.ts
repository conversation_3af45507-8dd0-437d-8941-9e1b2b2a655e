import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AqlSampleLimitSamplingRateEntity } from './entities/aql-sample-limit-sampling-rate.entity';
import { AqlSampleLimitSamplingRateRepository } from './repository/aql-sample-limit-sampling-rate.repository';
import { AqlSampleLimitSamplingRateService } from './service/aql-sample-limit-sampling-rate.service';

@Module({
  imports: [TypeOrmModule.forFeature([AqlSampleLimitSamplingRateEntity])],
  providers: [
    AqlSampleLimitSamplingRateService,
    AqlSampleLimitSamplingRateRepository,
  ],
  exports: [AqlSampleLimitSamplingRateService],
  controllers: [],
})
export class AqlSampleLimitSamplingRateModule {}
