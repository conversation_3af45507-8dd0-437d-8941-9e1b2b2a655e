import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional } from 'class-validator';

export class CreateAqlSampleLimitSamplingRateRequestDto {
  @ApiPropertyOptional()
  @IsOptional()
  id: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  aqlSampleLimitId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  samplingRateId: number;
}
