import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { parseJ<PERSON>NV<PERSON>ueField } from '../../../utils/common';
import { AqlSampleLimitSamplingRateEntity } from '../entities/aql-sample-limit-sampling-rate.entity';

@Injectable()
export class AqlSampleLimitSamplingRateRepository extends BaseAbstractRepository<AqlSampleLimitSamplingRateEntity> {
  constructor(
    @InjectRepository(AqlSampleLimitSamplingRateEntity)
    private readonly aqlSampleLimitSamplingRateRepository: Repository<AqlSampleLimitSamplingRateEntity>,
  ) {
    super(aqlSampleLimitSamplingRateRepository);
  }

  async findAllByAqlSampleLimitIds(aqlSampleLimitIds: number[]): Promise<any> {
    const query = this.aqlSampleLimitSamplingRateRepository
      .createQueryBuilder('aqlsamplelimitsamplingrate')
      .select([
        'aqlsamplelimitsamplingrate.id AS "id"',
        'aqlsamplelimitsamplingrate.aql_sample_limit_id AS "aqlSampleLimitId"',
        'aqlsamplelimitsamplingrate.sampling_rate_id AS "samplingRateId"',
      ])
      .leftJoin(
        'sampling_rates',
        'sr',
        'aqlsamplelimitsamplingrate.sampling_rate_id = sr.id',
      )
      .addSelect([
        `CASE WHEN COUNT(sr.id) = 0 THEN '{}' ELSE 
        (SELECT sr.id as id, sr.code as code,sr.sampling_rate as samplingRate FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "samplingRate"`,
      ])
      .where(
        'aqlsamplelimitsamplingrate.aql_sample_limit_id IN (:...aqlSampleLimitIds)',
        {
          aqlSampleLimitIds,
        },
      )
      .addGroupBy('aqlsamplelimitsamplingrate.id')
      .addGroupBy('aqlsamplelimitsamplingrate.aql_sample_limit_id')
      .addGroupBy('aqlsamplelimitsamplingrate.sampling_rate_id')
      .addGroupBy('sr.id')
      .addGroupBy('sr.code')
      .addGroupBy('sr.sampling_rate');

    const result = await query.getRawMany();
    return result?.map((item) => {
      return {
        ...item,
        samplingRate: parseJSONValueField(item.samplingRate),
      };
    });
  }
}
