import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { isEmpty, keyBy } from 'lodash';
import { I18nService } from 'nestjs-i18n';
import { DataSource, In } from 'typeorm';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { CreateErrorTypeRequestDto } from './../request/create-error-type.request.dto';
import { DeleteErrorTypeRequestDto } from './../request/delete-error-type.request.dto';
import { GetDetailErrorTypeRequestDto } from './../request/get-detail-error-type.request.dto';
import { GetListErrorTypeRequestDto } from './../request/get-list-error-type.request.dto';
import { UpdateErrorTypeRequestDto } from './../request/update-error-type.request.dto';
import { UpdateStatusErrorTypeRequestDto } from './../request/update-status-error-type.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { StatusEnum } from '../../../common/enums/status.enum';
import { BaseService } from '../../../common/service/base.service';
import { UserService } from '../../another-service/services/user-service';
import { ErrorGroupService } from '../../error-group/service/error-group.service';
import { MasterDataReferenceService } from '../../master-data-reference/service/master-data-reference.service';
import { ErrorTypeEntity } from '../entities/error-type.entity';
import { ErrorTypeRepository } from '../repository/error-type.repository';

@Injectable()
export class ErrorTypeService extends BaseService {
  private readonly logger = new Logger(ErrorTypeService.name);

  constructor(
    private readonly errorTypeRepository: ErrorTypeRepository,

    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly errorGroupService: ErrorGroupService,

    private readonly masterDataReferenceService: MasterDataReferenceService,
  ) {
    super(userService);
  }

  async create(request: CreateErrorTypeRequestDto): Promise<any> {
    const existCode = await this.errorTypeRepository.findOneByCode(
      request.code,
    );

    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate exist error-groups
    const existErrorGroup = await this.errorGroupService.findOneById(
      request?.errorGroupId,
    );
    if (
      isEmpty(existErrorGroup) ||
      existErrorGroup.status === StatusEnum.IN_ACTIVE
    ) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.ERROR_GROUP_IS_NOT_EXISTS'),
        )
        .build();
    }

    const errorTypeEntity = this.errorTypeRepository.createEntity(request);
    const errorType = await this.errorTypeRepository.create(errorTypeEntity);

    const dataMapErrorGroup = await this.mapErrorGroupToResponse([errorType]);
    const response = await this.mapUserInfoToResponse(dataMapErrorGroup);

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListErrorTypeRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.errorTypeRepository.getList(request);

    const response = await this.mapUserInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailErrorTypeRequestDto): Promise<any> {
    const errorType = await this.errorTypeRepository.getDetail(request);

    if (isEmpty(errorType)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMapErrorGroup = await this.mapErrorGroupToResponse([errorType]);
    const response = await this.mapUserInfoToResponse(dataMapErrorGroup);

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateErrorTypeRequestDto): Promise<any> {
    const { id } = request;
    const errorType = await this.errorTypeRepository.findOneById(id);

    if (isEmpty(errorType)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate error-types in used
    const usedList = await this.validateErrorTypeInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.ERROR_TYPE_IN_USED'))
        .build();
    }

    // Validate Code unique
    const existCode = await this.errorTypeRepository.findOneByCode(
      request.code,
    );

    if (!isEmpty(existCode) && existCode.id !== errorType.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate exist error-groups
    const existErrorGroup = await this.errorGroupService.findOneById(
      request?.errorGroupId,
    );
    if (
      isEmpty(existErrorGroup) ||
      existErrorGroup.status === StatusEnum.IN_ACTIVE
    ) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.ERROR_GROUP_IS_NOT_EXISTS'),
        )
        .build();
    }

    const dataUpdate = this.errorTypeRepository.updateEntity(
      request,
      errorType,
    );
    const data = await this.errorTypeRepository.update(dataUpdate);

    const dataMapErrorGroup = await this.mapErrorGroupToResponse([data]);
    const response = await this.mapUserInfoToResponse(dataMapErrorGroup);

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteErrorTypeRequestDto): Promise<any> {
    const { id } = request;
    const errorType = await this.errorTypeRepository.findOneById(id);

    if (isEmpty(errorType)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate error-types in used
    const usedList = await this.validateErrorTypeInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.ERROR_TYPE_IN_USED'))
        .build();
    }

    await this.errorTypeRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(request: UpdateStatusErrorTypeRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.errorTypeRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.errorTypeRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(request: UpdateStatusErrorTypeRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.errorTypeRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.errorTypeRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async validateErrorTypeInUsed(id: number): Promise<boolean> {
    // Validate used in errors
    const errorList =
      await this.masterDataReferenceService.findAllErrorByErrorTypeIds([id]);
    if (!isEmpty(errorList)) {
      return true;
    }

    // Validate used in another service
    const isUse =
      await this.masterDataReferenceService.validateDataMasterUseAnotherService(
        new ValidateMasterRequestDto('error_type_id', id),
      );
    if (isUse) {
      return true;
    }

    return false;
  }

  async mapErrorGroupToResponse(errorType: ErrorTypeEntity[]) {
    const ids: number[] = Array.from(
      new Set(
        errorType
          .flatMap((item) => item.errorGroupId)
          .filter((id) => id !== null && id !== undefined),
      ),
    );

    const errorGroups = await this.errorGroupService.findAllByIds(ids);
    if (isEmpty(errorGroups)) return errorType;

    const errorGroupMap = keyBy(errorGroups, 'id');
    return (
      errorType?.map((item) => {
        return {
          ...item,
          errorGroup: {
            id: errorGroupMap[item.errorGroupId]?.id,
            code: errorGroupMap[item.errorGroupId]?.code,
            name: errorGroupMap[item.errorGroupId]?.name,
          },
        };
      }) || []
    );
  }

  async findAllByErrorGroupIds(ids: number[]): Promise<ErrorTypeEntity[]> {
    const filterCondition: any = {
      errorGroupId: In(ids),
    };
    return await this.errorTypeRepository.findByCondition(filterCondition);
  }

  async findOneById(id: number): Promise<ErrorTypeEntity> {
    return await this.errorTypeRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<ErrorTypeEntity[]> {
    return await this.errorTypeRepository.findAllByIds(ids);
  }

  async getErrorTypeDetailByIds(ids: number[]): Promise<any> {
    const list = await this.errorTypeRepository.findAllByIds(ids);
    if (isEmpty(list)) return null;
    return await this.mapErrorGroupToResponse(list);
  }
}
