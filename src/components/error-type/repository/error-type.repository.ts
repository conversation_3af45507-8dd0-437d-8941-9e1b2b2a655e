import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { isDateString } from 'class-validator';
import { Repository } from 'typeorm';
import { StatusEnum } from '../../../common/enums/status.enum';

import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import {
  escapeCharForSearch,
  parseJSONValueField,
} from '../../../utils/common';
import { ErrorTypeEntity } from '../entities/error-type.entity';
import { GetDetailErrorTypeRequestDto } from '../request/get-detail-error-type.request.dto';
import { GetListErrorTypeRequestDto } from '../request/get-list-error-type.request.dto';
import { UpdateErrorTypeRequestDto } from '../request/update-error-type.request.dto';
import { UpdateStatusErrorTypeRequestDto } from '../request/update-status-error-type.request.dto';

@Injectable()
export class ErrorTypeRepository extends BaseAbstractRepository<ErrorTypeEntity> {
  constructor(
    @InjectRepository(ErrorTypeEntity)
    private readonly errorTypeRepository: Repository<ErrorTypeEntity>,
  ) {
    super(errorTypeRepository);
  }

  createEntity(request: any): ErrorTypeEntity {
    const errorTypeEntity = new ErrorTypeEntity();
    errorTypeEntity.code = request.code;
    errorTypeEntity.name = request.name;
    errorTypeEntity.errorGroupId = request.errorGroupId;
    errorTypeEntity.description = request.description;
    errorTypeEntity.status = StatusEnum.ACTIVE;
    errorTypeEntity.createdBy = request.userId;
    errorTypeEntity.updatedBy = request.userId;

    return errorTypeEntity;
  }

  updateEntity(
    request: UpdateErrorTypeRequestDto,
    entity: ErrorTypeEntity,
  ): ErrorTypeEntity {
    entity.name = request?.name;
    entity.errorGroupId = request?.errorGroupId;
    entity.description = request?.description;
    entity.updatedBy = request?.userId;

    return entity;
  }

  async getList(request: GetListErrorTypeRequestDto): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.errorTypeRepository
      .createQueryBuilder('errortype')
      .select([
        'errortype.id AS id',
        'errortype.code AS "code"',
        'errortype.name AS "name"',
        'errortype.error_group_id AS "errorGroupId"',
        'errortype.description AS "description"',
        'errortype.status AS "status"',
        'errortype.created_by AS "createdBy"',
        'errortype.created_at AS "createdAt"',
        'errortype.updated_by AS "updatedBy"',
        'errortype.updated_at AS "updatedAt"',
      ])
      .leftJoin('error_groups', 'eg', 'errortype.error_group_id = eg.id')
      .addSelect([
        `CASE WHEN COUNT(eg.id) = 0 THEN '{}' ELSE
          (SELECT eg.id as id, eg.code as code, eg.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "errorGroup"`,
      ]);

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("errortype"."code") like lower(:code) escape '\\' OR
              lower("errortype"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('errortype.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'code':
            query.andWhere(
              `lower("errortype"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'name':
            query.andWhere(
              `lower("errortype"."name") like lower(:name) escape '\\'`,
              {
                name: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'description':
            query.andWhere(
              `lower("errortype"."description") like lower(:description) escape '\\'`,
              {
                description: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'status':
            query.andWhere('"errortype"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'errorGroupId':
            query.andWhere('"errortype"."error_group_id" = :errorGroupId', {
              errorGroupId: Number(value),
            });
            break;
          case 'errorGroupIds':
            query.andWhere(
              '"errortype"."error_group_id" IN (:...errorGroupIds)',
              {
                errorGroupIds: value.split(',').map((id) => Number(id)),
              },
            );
            break;
          case 'createdById':
            query.andWhere('"errortype"."created_by" = :createdById', {
              createdById: Number(value),
            });
            break;
          case 'createdByIds':
            query.andWhere('"errortype"."created_by" IN (:...createdByIds)', {
              createdByIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'createdAt':
            const createFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const createTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"errortype"."created_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: createFrom,
                dateTo: createTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"errortype"."updated_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: updateFrom,
                dateTo: upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'name':
            query.addOrderBy('"errortype"."name"', order);
            break;
          case 'code':
            query.addOrderBy('"errortype"."code"', order);
            break;
          case 'errorGroup':
            query.addOrderBy('"eg"."id"', order);
            break;
          case 'status':
            query.addOrderBy('"errortype"."status"', order);
            break;
          case 'description':
            query.addOrderBy('"errortype"."description"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"errortype"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"errortype"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"errortype"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('errortype.id', 'DESC');
    }

    query
      .groupBy('errortype.id')
      .addGroupBy('errortype.code')
      .addGroupBy('errortype.name')
      .addGroupBy('errortype.error_group_id')
      .addGroupBy('errortype.description')
      .addGroupBy('errortype.status')
      .addGroupBy('errortype.created_by')
      .addGroupBy('errortype.created_at')
      .addGroupBy('errortype.updated_by')
      .addGroupBy('errortype.updated_at')
      .addGroupBy('eg.id')
      .addGroupBy('eg.code')
      .addGroupBy('eg.name');

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const data = await query.getRawMany();
    const count = await query.getCount();

    return {
      data: data?.map((item) => {
        return {
          ...item,
          errorGroup: parseJSONValueField(item.errorGroup),
        };
      }),
      count: count,
    };
  }

  async getDetail(
    request: GetDetailErrorTypeRequestDto,
  ): Promise<ErrorTypeEntity> {
    const { id } = request;

    const data = await this.errorTypeRepository.findOne({
      where: { id: id },
    });

    return data;
  }

  async active(request: UpdateStatusErrorTypeRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.errorTypeRepository
      .createQueryBuilder()
      .update(ErrorTypeEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(request: UpdateStatusErrorTypeRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.errorTypeRepository
      .createQueryBuilder()
      .update(ErrorTypeEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }
}
