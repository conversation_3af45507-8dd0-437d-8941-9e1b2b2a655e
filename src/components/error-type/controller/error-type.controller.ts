import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  ACTIVE_ERROR_TYPE_PERMISSION,
  CREATE_ERROR_TYPE_PERMISSION,
  DELETE_ERROR_TYPE_PERMISSION,
  DETAIL_ERROR_TYPE_PERMISSION,
  INACTIVE_ERROR_TYPE_PERMISSION,
  LIST_ERROR_TYPE_PERMISSION,
  UPDATE_ERROR_TYPE_PERMISSION,
} from '../../../utils/permissions/error-type.permission';
import { CreateErrorTypeRequestDto } from '../request/create-error-type.request.dto';
import { DeleteErrorTypeRequestDto } from '../request/delete-error-type.request.dto';
import { GetDetailErrorTypeRequestDto } from '../request/get-detail-error-type.request.dto';
import { GetListErrorTypeRequestDto } from '../request/get-list-error-type.request.dto';
import { UpdateErrorTypeRequestDto } from '../request/update-error-type.request.dto';
import { UpdateStatusErrorTypeRequestDto } from '../request/update-status-error-type.request.dto';
import { ErrorTypeService } from '../service/error-type.service';

@Controller('error-types')
export class ErrorTypeController {
  constructor(private readonly errorTypeService: ErrorTypeService) {}

  @PermissionCode(DETAIL_ERROR_TYPE_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Error-types'],
    summary: 'Chi tiết Error-types',
    description: 'Chi tiết Error-types',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailErrorTypeRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorTypeService.getDetail(request);
  }

  @PermissionCode(LIST_ERROR_TYPE_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Error-types'],
    summary: 'Danh sách Error-types',
    description: 'Danh sách Error-types',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(@Query() query: GetListErrorTypeRequestDto): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.errorTypeService.getList(request);
  }

  @PermissionCode(CREATE_ERROR_TYPE_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Error-types'],
    summary: 'Tạo Error-types mới',
    description: 'Tạo Error-types mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateErrorTypeRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.errorTypeService.create(request);
  }

  @PermissionCode(UPDATE_ERROR_TYPE_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Error-types'],
    summary: 'Cập nhật Error-types',
    description: 'Cập nhật Error-types',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(@Body() body: UpdateErrorTypeRequestDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorTypeService.update(request);
  }

  @PermissionCode(ACTIVE_ERROR_TYPE_PERMISSION.code)
  @Put('/active')
  @ApiOperation({
    tags: ['Error-types'],
    summary: 'Cập nhật Error-types Status Active',
    description: 'Cập nhật Error-types Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async active(
    @Body() body: UpdateStatusErrorTypeRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorTypeService.active(request);
  }

  @PermissionCode(INACTIVE_ERROR_TYPE_PERMISSION.code)
  @Put('/inactive')
  @ApiOperation({
    tags: ['Error-types'],
    summary: 'Cập nhật Error-types Status Inactive',
    description: 'Cập nhật Error-types Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async inactive(
    @Body() body: UpdateStatusErrorTypeRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorTypeService.inactive(request);
  }

  @PermissionCode(DELETE_ERROR_TYPE_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Error-types'],
    summary: 'Xóa Error-types',
    description: 'Xóa Error-types',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(@Param() param: DeleteErrorTypeRequestDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.errorTypeService.delete(request);
  }
}
