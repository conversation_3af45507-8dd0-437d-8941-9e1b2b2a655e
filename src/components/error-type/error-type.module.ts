import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { ErrorGroupModule } from '../error-group/error-group.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { ErrorTypeController } from './controller/error-type.controller';
import { ErrorTypeEntity } from './entities/error-type.entity';
import { ErrorTypeRepository } from './repository/error-type.repository';
import { ErrorTypeService } from './service/error-type.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([ErrorTypeEntity]),
    AnotherServiceModule,
    ErrorGroupModule,
    MasterDataReferenceModule,
  ],
  providers: [ErrorTypeService, ErrorTypeRepository],
  exports: [ErrorTypeService],
  controllers: [ErrorTypeController],
})
export class ErrorTypeModule {}
