import { Module } from '@nestjs/common';
import { MmsDeviceService } from './services/mms-device-service';
import { QmsxCustomerSupportService } from './services/qmsx-customer-support-service';
import { QmsxPlanService } from './services/qmsx-plan-service';
import { QmsxReportService } from './services/qmsx-report-service';
import { QmsxRequestService } from './services/qmsx-request-service';
import { QmsxTcrsService } from './services/qmsx-tcrs-service';
import { QmsxTicketService } from './services/qmsx-ticket-service';
import { UserService } from './services/user-service';
import { VendorService } from './services/vendor-service';

@Module({
  providers: [
    UserService,
    QmsxRequestService,
    VendorService,
    QmsxPlanService,
    QmsxTicketService,
    QmsxTcrsService,
    QmsxReportService,
    QmsxCustomerSupportService,
    MmsDeviceService,
  ],
  exports: [
    UserService,
    QmsxRequestService,
    VendorService,
    QmsxPlanService,
    QmsxTicketService,
    QmsxTcrsService,
    QmsxReportService,
    QmsxCustomerSupportService,
    MmsDeviceService,
  ],
})
export class AnotherServiceModule {}
