import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateClaimFeeTicketDetailRequest {
  @ApiPropertyOptional()
  @IsOptional()
  id: number;

  @ApiProperty()
  @IsNotEmpty()
  itemId: number;

  @ApiPropertyOptional()
  @IsOptional()
  processId: number;

  @ApiProperty()
  @IsNotEmpty()
  unitId: number;

  @ApiPropertyOptional()
  @IsOptional()
  lotNo: string;

  @ApiProperty()
  @IsNotEmpty()
  billingQuantity: number;

  @ApiProperty()
  @IsNotEmpty()
  unitPrice: number;

  @ApiPropertyOptional()
  @IsOptional()
  note: string;
}
