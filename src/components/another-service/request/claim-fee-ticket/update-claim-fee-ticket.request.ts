import { ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateClaimFeeTicketRequest } from './create-claim-fee-ticket.request';

export class UpdateClaimFeeTicketRequest extends PartialType(
  CreateClaimFeeTicketRequest,
) {
  @ApiPropertyOptional({ example: 1 })
  @IsInt()
  @IsOptional()
  @Transform(({ value }) => +value)
  id: number;
}
