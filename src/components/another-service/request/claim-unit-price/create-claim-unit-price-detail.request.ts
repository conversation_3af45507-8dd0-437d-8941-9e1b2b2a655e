import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateClaimUnitPriceDetailRequest {
  @ApiPropertyOptional()
  @IsOptional()
  id: number;

  @ApiProperty()
  @IsNotEmpty()
  itemId: number;

  @ApiPropertyOptional()
  @IsOptional()
  lotNo: string;

  @ApiPropertyOptional()
  @IsOptional()
  processId: number;

  @ApiPropertyOptional()
  @IsOptional()
  unitId: number;

  @ApiProperty()
  @IsNotEmpty()
  unitPrice: number;

  @ApiPropertyOptional()
  @IsOptional()
  note: string;
}
