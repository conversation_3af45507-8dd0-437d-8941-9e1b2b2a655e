import { ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateClaimUnitPriceRequest } from './create-claim-unit-price.request';

export class UpdateClaimUnitPriceRequest extends PartialType(
  CreateClaimUnitPriceRequest,
) {
  @ApiPropertyOptional({ example: 1 })
  @IsInt()
  @IsOptional()
  @Transform(({ value }) => +value)
  id: number;
}
