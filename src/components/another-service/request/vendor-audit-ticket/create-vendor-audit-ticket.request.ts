import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { FileDto } from '../../../../utils/dto/request/file.request.dto';
import { VENDOR_AUDIT_TYPE } from '../../../vendor-audit-plan/vendor-audit-plan.constant';
import { CreateVendorAuditTicketDetailRequest } from './create-vendot-audit-ticket-detail.request';

export class CreateVendorAuditTicketRequest extends BaseDto {
  @ApiPropertyOptional()
  @IsOptional()
  vendorAuditPlanId: number;

  @ApiPropertyOptional()
  @IsOptional()
  vendorAuditPlanCode: string;

  @ApiProperty({ example: '08/2024' })
  @IsNotEmpty()
  @MaxLength(7)
  auditPeriod: string;

  @ApiPropertyOptional()
  @IsOptional()
  vendorAuditPlanDetailId: number;

  @ApiProperty()
  @IsNotEmpty()
  vendorId: number;

  @ApiProperty()
  @IsNotEmpty()
  processId: number;

  @ApiPropertyOptional()
  @IsOptional()
  goodsTypeId: number;

  @ApiProperty()
  @IsNotEmpty()
  checksheetId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(VENDOR_AUDIT_TYPE)
  auditType: VENDOR_AUDIT_TYPE;

  @ApiProperty()
  @IsNotEmpty()
  targetPoint: number;

  @ApiPropertyOptional()
  @IsOptional()
  auditorId: number;

  @ApiPropertyOptional()
  @IsOptional()
  auditDate: Date;

  @ApiPropertyOptional()
  @IsOptional()
  score: number;

  @ApiPropertyOptional()
  @IsOptional()
  rank: string;

  @ApiPropertyOptional()
  @IsOptional()
  createdBy: number;

  @ApiPropertyOptional()
  @IsOptional()
  updatedBy: number;

  @ApiPropertyOptional({ type: [FileDto] })
  @IsArray()
  @IsOptional()
  @Type(() => FileDto)
  files: FileDto[];

  @ApiProperty({ type: [CreateVendorAuditTicketDetailRequest] })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CreateVendorAuditTicketDetailRequest)
  vendorAuditTicketDetail: CreateVendorAuditTicketDetailRequest[];

  @ApiProperty({ default: false })
  @IsNotEmpty()
  isSubmittedForApproval: boolean = false;
}
