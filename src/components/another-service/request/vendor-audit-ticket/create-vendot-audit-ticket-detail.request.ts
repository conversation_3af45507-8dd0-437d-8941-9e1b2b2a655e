import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';
import { FileDto } from '../../../../utils/dto/request/file.request.dto';

export class CreateVendorAuditTicketDetailRequest {
  @ApiPropertyOptional()
  @IsOptional()
  id: number;

  @ApiProperty()
  @IsNotEmpty()
  checksheetDetailId: number;

  @ApiPropertyOptional()
  @IsOptional()
  assessmentScore: number;

  @ApiPropertyOptional()
  @IsOptional()
  result: number;

  @ApiPropertyOptional()
  @IsOptional()
  errorId: number;

  @ApiPropertyOptional()
  @IsOptional()
  description: string;

  @ApiPropertyOptional({ type: [FileDto] })
  @IsArray()
  @IsOptional()
  @Type(() => FileDto)
  files: FileDto[];
}
