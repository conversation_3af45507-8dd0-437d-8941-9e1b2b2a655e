import { ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateVendorAuditTicketRequest } from './create-vendor-audit-ticket.request';

export class UpdateVendorAuditTicketRequest extends PartialType(
  CreateVendorAuditTicketRequest,
) {
  @ApiPropertyOptional({ example: 1 })
  @IsInt()
  @IsOptional()
  @Transform(({ value }) => +value)
  id: number;
}
