import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { CreateVendorAuditPlanAuditRequestDto } from '../../../vendor-audit-plan/request/create-vendor-audit-plan-auditor.request.dto';
import { VENDOR_AUDIT_TYPE } from '../../../vendor-audit-plan/vendor-audit-plan.constant';
import { VENDOR_TYPE_ENUM } from '../../../vendor-evaluation-plan/vendor-evaluation-plan.constant';

export class CreateVendorAuditPlanDetailRequest {
  @ApiPropertyOptional()
  @IsOptional()
  id: number;

  @ApiProperty()
  @IsNotEmpty()
  vendorId: number;

  @ApiProperty()
  @IsEnum(VENDOR_TYPE_ENUM)
  @IsNotEmpty()
  vendorType: VENDOR_TYPE_ENUM;

  @ApiProperty()
  @IsNotEmpty()
  processId: number;

  @ApiPropertyOptional()
  @IsOptional()
  goodsTypeId: number;

  @ApiProperty()
  @IsNotEmpty()
  checksheetId: number;

  @ApiPropertyOptional({
    example: VENDOR_AUDIT_TYPE.AUDIT_VENDOR,
  })
  @IsOptional()
  @IsEnum(VENDOR_AUDIT_TYPE)
  auditType: VENDOR_AUDIT_TYPE;

  @ApiProperty()
  @IsNotEmpty()
  targetPoint: number;

  @ApiPropertyOptional({ type: [CreateVendorAuditPlanAuditRequestDto] })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateVendorAuditPlanAuditRequestDto)
  auditor: CreateVendorAuditPlanAuditRequestDto[];
}
