import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsNotEmpty,
  IsOptional,
  Max<PERSON>ength,
  ValidateNested,
} from 'class-validator';
import { BaseDto } from '../../../../core/dto/base.dto';
import { FileDto } from '../../../../utils/dto/request/file.request.dto';
import { CreateVendorAuditPlanDetailRequestDto } from '../../../vendor-audit-plan/request/create-vendor-audit-plan-detail.request.dto';

export class CreateVendorAuditPlanRequest extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(7)
  auditPeriod: string;

  @ApiProperty()
  @IsNotEmpty()
  fromDate: Date;

  @ApiProperty()
  @IsNotEmpty()
  toDate: Date;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  description?: string;

  @ApiPropertyOptional({ type: [FileDto] })
  @IsArray()
  @IsOptional()
  @Type(() => FileDto)
  files: FileDto[];

  @ApiProperty({ type: [CreateVendorAuditPlanDetailRequestDto] })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CreateVendorAuditPlanDetailRequestDto)
  vendorAuditPlanDetail: CreateVendorAuditPlanDetailRequestDto[];

  @ApiPropertyOptional()
  @IsOptional()
  createdBy: number;

  @ApiPropertyOptional()
  @IsOptional()
  updatedBy: number;
}
