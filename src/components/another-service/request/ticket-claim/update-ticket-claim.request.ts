import { ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateTicketClaimRequest } from './create-ticket-claim.request';

export class UpdateTicketClaimRequest extends PartialType(
  CreateTicketClaimRequest,
) {
  @ApiPropertyOptional({ example: 1 })
  @IsInt()
  @IsOptional()
  @Transform(({ value }) => +value)
  id: number;
}
