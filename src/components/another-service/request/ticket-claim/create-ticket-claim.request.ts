import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { BaseDto } from '../../../../core/dto/base.dto';
import { FileDto } from '../../../../utils/dto/request/file.request.dto';
import { CLAIM_TYPE } from '../../../ticket-claim/ticket-claim.constant';
import { CreateTicketClaimDetailRequest } from './create-ticket-claim-detail.request';

export class CreateTicketClaimRequest extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  requestDepartmentId: number;

  @ApiProperty()
  @IsNotEmpty()
  vendorId: number;

  @ApiProperty({ example: '2024-06-07T00:00:00' })
  @IsNotEmpty()
  @IsDateString()
  claimDate: Date;

  @ApiProperty({
    example: CLAIM_TYPE.VENDOR_CLAIM_IN_IQC,
  })
  @IsNotEmpty()
  @IsEnum(CLAIM_TYPE)
  claimType: CLAIM_TYPE;

  @ApiPropertyOptional()
  @IsOptional()
  iqcRequestCode: string;

  @ApiPropertyOptional()
  @IsOptional()
  iqcRequestId: number;

  @ApiPropertyOptional()
  @IsOptional()
  ngHandlingCode: string;

  @ApiPropertyOptional()
  @IsOptional()
  ngHandlingId: number;

  @ApiPropertyOptional()
  @IsOptional()
  description: string;

  @ApiPropertyOptional({ type: [FileDto] })
  @IsArray()
  @IsOptional()
  @Type(() => FileDto)
  files: FileDto[];

  @ApiProperty({ type: [CreateTicketClaimDetailRequest] })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CreateTicketClaimDetailRequest)
  ticketClaimDetail: CreateTicketClaimDetailRequest[];

  @ApiProperty()
  @IsNotEmpty()
  createdBy: number;

  @ApiProperty()
  @IsNotEmpty()
  updatedBy: number;
}
