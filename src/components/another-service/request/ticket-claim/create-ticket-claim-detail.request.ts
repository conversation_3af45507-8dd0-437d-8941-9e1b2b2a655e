import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateTicketClaimDetailRequest {
  @ApiPropertyOptional()
  @IsOptional()
  id: number;

  @ApiProperty()
  @IsNotEmpty()
  itemId: number;

  @ApiPropertyOptional()
  @IsOptional()
  lotNo: string;

  @ApiPropertyOptional()
  @IsOptional()
  processId: number;

  @ApiPropertyOptional()
  @IsOptional()
  unitId: number;

  @ApiPropertyOptional()
  @IsOptional()
  claimQuantity: number;

  @ApiPropertyOptional()
  @IsOptional()
  billingQuantity: number;

  @ApiPropertyOptional()
  @IsOptional()
  lotQuantity: number;

  @ApiPropertyOptional()
  @IsOptional()
  reason: string;
}
