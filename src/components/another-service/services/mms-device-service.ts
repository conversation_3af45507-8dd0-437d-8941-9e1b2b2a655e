import { Injectable } from '@nestjs/common';
import { natsConfig } from '../../../config/nats.config';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { NatsClientService } from '../../../core/transporter/nats-transporter/nats-client.service';

@Injectable()
export class MmsDeviceService {
  constructor(private readonly natClientService: NatsClientService) {}

  async getDeviceById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.mmsDevice.path.getDeviceById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, response };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getListDevice(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.mmsDevice.path.getListDevice,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, response };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getDeviceMaintenanceTemplateByRoomIds(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.mmsDevice.path
          .getDeviceMaintenanceTemplateByRoomIds,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, response };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getListDeviceByPositionIds(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.mmsDevice.path.getListDeviceByPositionIds,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, response };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateStatusInternal(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.mmsDevice.path.updateStatusInternal,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, response };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async confirmRepairRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.mmsDevice.path.confirmRepairRequest,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, response };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getDetailInternal(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.mmsDevice.path.getDetailInternal,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, response };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getDeviceByIdsInternal(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.mmsDevice.path.getDeviceByIdsInternal,
        request,
      );
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getDeviceByCodes(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.mmsDevice.path.getDeviceByCodes,
        request,
      );
      return response?.data?.items || [];
    } catch (err) {
      return [];
    }
  }

  async updateLastCalibration(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.mmsDevice.path.updateLastCalibration,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, response };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getDeviceGroupsByIds(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.mmsDevice.path.getDeviceGroupsByIds,
        request,
      );
      return response?.data;
    } catch (err) {
      return [];
    }
  }
}
