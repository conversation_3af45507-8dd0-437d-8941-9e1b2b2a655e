import { Injectable } from '@nestjs/common';
import { natsConfig } from '../../../config/nats.config';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { NatsClientService } from '../../../core/transporter/nats-transporter/nats-client.service';
import { GetListDepartmentRequestDto } from '../../p-code/request/get-list-department.request.dto';
import { GetListUsersRequestDto } from '../request/get-list-user-request.dto';

@Injectable()
export class UserService {
  constructor(private readonly natClientService: NatsClientService) {}

  async getListUserByIds(request: GetListUsersRequestDto): Promise<any[]> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.users.path.getUsersByIds,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListUserByCodes(request: any): Promise<any[]> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.users.path.getUsersByCodes,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListDepartmentSettingsByIds(ids: number[]): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.users.path.getDepartmentSsettingByIds,
        {
          departmentSettingIds: ids,
        },
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListRoomByIds(ids: number[]): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.users.path.getListRoom,
        {
          queryIds: ids.join(','),
        },
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];

      return response?.data.items;
    } catch (err) {
      return [];
    }
  }

  async getListRoomByCodes(request: GetListDepartmentRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.users.path.getListRoom,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];

      return response?.data.items;
    } catch (err) {
      return [];
    }
  }

  async getListManufacturingPlanByIds(ids: number[]): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.users.path.getListManufacturingPlant,
        {
          isGetAll: '1',
          queryIds: ids.join(','),
        },
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];

      return response?.data.items;
    } catch (err) {
      return [];
    }
  }
}
