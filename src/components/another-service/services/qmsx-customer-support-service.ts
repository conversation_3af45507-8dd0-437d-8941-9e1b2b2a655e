import { Injectable } from '@nestjs/common';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { natsConfig } from '../../../config/nats.config';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { IdParamDto } from '../../../core/dto/request/id-param.request.dto';
import { NatsClientService } from '../../../core/transporter/nats-transporter/nats-client.service';
import { GetVocSummaryRequestDto } from '../../cs-dashboard/request/get-voc-summary.request.dto';
import { CreateCustomerClaimFeeRequestDto } from '../../customer-claim-fee/request/create-customer-claim-fee.request.dto';
import { GetListCustomerClaimFeeRequestDto } from '../../customer-claim-fee/request/get-list-customer-claim-fee.request.dto';
import { UpdateCustomerClaimFeeRequestDto } from '../../customer-claim-fee/request/update-customer-claim-fee.request.dto';
import { CreateNgSortingTicketRequestDto } from '../../ng-sorting-ticket/request/create-ng-sorting-ticket.request.dto';
import { GetListNgSortingTicketRequestDto } from '../../ng-sorting-ticket/request/get-list-ng-sorting-ticket.request.dto';
import { UpdateNgSortingTicketRequestDto } from '../../ng-sorting-ticket/request/update-ng-sorting-ticket.request.dto';
import { UpdateStatusNgSortingTicketRequestDto } from '../../ng-sorting-ticket/request/update-status-ng-sorting-ticket.request.dto';
import { CreateShipmentTrackingTicketRequestDto } from '../../shipment-tracking-ticket/request/create-shipment-tracking-ticket.request.dto';
import { GetListShipmentTrackingTicketRequestDto } from '../../shipment-tracking-ticket/request/get-list-shipment-tracking-ticket.request.dto';
import { UpdateShipmentTrackingTicketRequestDto } from '../../shipment-tracking-ticket/request/update-shipment-tracking-ticket.request.dto';
import { CreateVocHandlingTicketRequestDto } from '../../voc-handling-ticket/request/create-voc-handling-ticket.request.dto';
import { GetDataForCreateCustomerClaimFeeRequestDto } from '../../voc-handling-ticket/request/get-data-for-create-customer-claim.request.dto';
import { GetListVocHandlingTicketRequestDto } from '../../voc-handling-ticket/request/get-list-voc-handling-ticket.request.dto';
import { UpdateStatusVocHandlingTicketRequestDto } from '../../voc-handling-ticket/request/update-status-voc-handling-ticket.request.dto';
import { UpdateVocHandlingTicketRequestDto } from '../../voc-handling-ticket/request/update-voc-handling-ticket.request.dto';
import { CreateVocRequestDto } from '../../voc/request/create-voc.request.dto';
import { GetListVocRequestDto } from '../../voc/request/get-list-voc.request.dto';
import { UpdateStatusVocRequestDto } from '../../voc/request/update-status-voc.request.dto';
import { UpdateVocRequestDto } from '../../voc/request/update-voc.request.dto';

@Injectable()
export class QmsxCustomerSupportService {
  constructor(private readonly natClientService: NatsClientService) {}

  async createVoc(request: CreateVocRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.createVoc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListVocTicket(request: GetListVocRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.getListVoc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailVocTicketById(request: { id: number }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.getDetailVocById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateVocTicket(request: UpdateVocRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.updateVoc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getAllVocByShipmentTrackingTicketId(request: {
    id: number;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .getAllVocByShipmentTrackingTicketId,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getVocByKeyShipmentTrackingTicket(request: {
    listKey: string[];
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .getByKeyShipmentTrackingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async deleteVocTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.deleteVoc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateStatusVoc(request: UpdateStatusVocRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.updateStatusVoc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveVoc(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.approveVoc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectVoc(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.rejectVoc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return {
          result: false,
          messageError: response.message,
        };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getListVocHandlingTicket(
    request: GetListVocHandlingTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.getListVocHandlingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getByCustomerAndVocDateRange(
    request: GetDataForCreateCustomerClaimFeeRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .getByCustomerIdAndVocDateRange,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async createManyVocHandlingTicket(
    request: CreateVocHandlingTicketRequestDto[],
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .createManyVocHandlingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async createVocHandlingTicket(
    request: CreateVocHandlingTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.createVocHandlingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async approveVocHandlingTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.approveVocHandlingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectVocHandlingTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.rejectVocHandlingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateVocHandlingTicket(
    request: UpdateVocHandlingTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.updateVocHandlingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailVocHandlingTicketById(request: { id: number }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .getDetailVocHandlingTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateStatusVocHandlingTicket(
    request: UpdateStatusVocHandlingTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .updateStatusVocHandlingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getAllVocHandlingTicketByVocId(request: { id: number }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .getAllVocHandlingTicketByVocId,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getAllVocHandlingTicketByVocIds(request: {
    ids: number[];
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .getAllVocHandlingTicketByVocIds,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getAllVocHandlingTicketByIds(request: { ids: number[] }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .getAllVocHandlingTicketByIds,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createShipmentTrackingTicket(
    request: CreateShipmentTrackingTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .createShipmentTrackingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListShipmentTrackingTicket(
    request: GetListShipmentTrackingTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .getListShipmentTrackingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getAllShipmentTrackingTicketByIds(request: {
    ids: number[];
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .getAllShipmentTrackingTicketByIds,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getDetailShipmentTrackingTicketById(request: {
    id: number;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .getDetailShipmentTrackingTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateShipmentTrackingTicket(
    request: UpdateShipmentTrackingTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .updateShipmentTrackingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async deleteShipmentTrackingTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .deleteShipmentTrackingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveShipmentTrackingTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .approveShipmentTrackingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectShipmentTrackingTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .rejectShipmentTrackingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return {
          result: false,
          messageError: response.message,
        };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getListNgSortingTicket(
    request: GetListNgSortingTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.getListNgSortingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createManyNgSortingTicket(
    request: CreateNgSortingTicketRequestDto[],
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.createManyNgSortingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async createNgSortingTicket(
    request: CreateNgSortingTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.createNgSortingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async approveNgSortingTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.approveNgSortingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectNgSortingTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.rejectNgSortingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateNgSortingTicket(
    request: UpdateNgSortingTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.updateNgSortingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailNgSortingTicketById(request: { id: number }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .getDetailNgSortingTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateStatusNgSortingTicket(
    request: UpdateStatusNgSortingTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .updateStatusNgSortingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getAllNgSortingTicketByVocId(request: { id: number }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .getAllNgSortingTicketByVocId,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getAllNgSortingTicketByVocIds(request: {
    ids: number[];
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .getAllNgSortingTicketByVocIds,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getAllNgSortingTicketByIds(request: { ids: number[] }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.getAllNgSortingTicketByIds,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createCustomerClaimFee(
    request: CreateCustomerClaimFeeRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.createCustomerClaimFee,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListCustomerClaimFee(
    request: GetListCustomerClaimFeeRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.getListCustomerClaimFee,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getReportSummaryCustomerClaimFee(
    request: GetListCustomerClaimFeeRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .customerClaimFeeSummaryReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailCustomerClaimFeeById(request: { id: number }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .getDetailCustomerClaimFeeById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateCustomerClaimFee(
    request: UpdateCustomerClaimFeeRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.updateCustomerClaimFee,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async deleteCustomerClaimFee(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.deleteCustomerClaimFee,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveCustomerClaimFee(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.approveCustomerClaimFee,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectCustomerClaimFee(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.rejectCustomerClaimFee,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return {
          result: false,
          messageError: response.message,
        };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }
  async validateMaster(request: ValidateMasterRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.validateMaster,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return false;
      return true;
    } catch (err) {
      return false;
    }
  }

  async getVocSummary(request: GetVocSummaryRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.getVocSummary,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getTotalCustomerClaimFee(
    request: GetVocSummaryRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.getTotalCustomerClaimFee,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getTopRateTotalClaimFeeByCustomers(
    request: GetVocSummaryRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path
          .getTopRateTotalClaimFeeByCustomers,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getTopVocByItemLines(request: GetVocSummaryRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.getTopVocByItemLines,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getTopWorstErrors(request: GetVocSummaryRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxCustomerSupport.path.getTopWorstErrors,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }
}
