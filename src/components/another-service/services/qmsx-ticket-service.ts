import { Injectable } from '@nestjs/common';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { natsConfig } from '../../../config/nats.config';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { IdParamDto } from '../../../core/dto/request/id-param.request.dto';
import { NatsClientService } from '../../../core/transporter/nats-transporter/nats-client.service';
import { CreateAuditHandlingTicketRequestDto } from '../../audit-handling-ticket/request/create-audit-handling-ticket.request.dto';
import { GetListAuditHandlingTicketRequestDto } from '../../audit-handling-ticket/request/get-list-audit-handling-ticket.request.dto';
import { UpdateAuditHandlingTicketRequestDto } from '../../audit-handling-ticket/request/update-audit-handling-ticket.request.dto';
import { NoteEquipmentCalibrationTicketRequestDto } from '../../equipment-calibration-ticket/request/note-equipment-calibration-ticket.request.dto';
import { VENDOR_AUDIT_TICKET_STATUS } from '../../vendor-audit-ticket/vendor-audit-ticket.constant';
import { CreateVendorEvaluationTicketRequestDto } from '../../vendor-evaluation-ticket/request/create-vendor-evaluation-ticket.request.dto';
import { GetListVendorEvaluationTicketRequestDto } from '../../vendor-evaluation-ticket/request/get-list-vendor-evaluation-ticket.request.dto';
import { UpdateVendorEvaluationTicketRequestDto } from '../../vendor-evaluation-ticket/request/update-vendor-evaluation-ticket.request.dto';
import { VENDOR_EVALUATION_TICKET_STATUS } from '../../vendor-evaluation-ticket/vendor-evaluation-ticket.constant';
import { CreateVendorAuditTicketRequest } from '../request/vendor-audit-ticket/create-vendor-audit-ticket.request';

@Injectable()
export class QmsxTicketService {
  constructor(private readonly natClientService: NatsClientService) {}

  async getDetailQmsxTicketById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getDetailIqcById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListQmsxTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createQmsxTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createQmsxTickets(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createIqcs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateQmsxTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getAllIqcTicketByIds(request: { ids: number[] }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getAllIqcTicketByIds,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async updateStatusIqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateStatusIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateStatusByIqcRequestId(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateStatusByIqcRequestId,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveIqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.approveIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async submitIqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.submitIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectIqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.rejectIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getDetailOqcTicketById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getDetailOqcTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailInspectionTicketById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getDetailInspectionTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async cancelInspectionTicketByPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.cancelInspectionTicketByPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async checkInspectionTicketStatusByPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.checkInspectionTicketStatusByPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListOqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListOqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListOqcTicketForSpcReport(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListOqcTicketForSpcReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListOqcTicketForSpcChart(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListOqcTicketForSpcChart,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListOqcTicketForBarChart(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListOqcTicketForBarChart,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async countMeasurementsOqcTicketForBarChart(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .countMeasurementsOqcTicketForBarChart,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListIqcTicketForSpcReport(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListIqcTicketForSpcReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListIqcTicketForSpcChart(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListIqcTicketForSpcChart,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListIqcTicketForBarChart(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListIqcTicketForBarChart,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async countMeasurementsIqcTicketForBarChart(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .countMeasurementsIqcTicketForBarChart,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListIpqcTicketForSpcReport(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListIpqcTicketForSpcReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListIpqcTicketForSpcChart(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListIpqcTicketForSpcChart,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListIpqcTicketForBarChart(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListIpqcTicketForBarChart,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async countMeasurementsIpqcTicketForBarChart(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .countMeasurementsIpqcTicketForBarChart,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListOqcTicketByKey(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListOqcTicketByKey,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListInspectionTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListInspectionTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createOqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createOqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createOqcTickets(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createOqcTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createInspectionTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createInspectionTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateOqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateOqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateStatusInspection(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateStatusInspectionTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateInspectionTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateInspectionTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async approveOqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.approveOqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveInspection(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.approveInspectionTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectOqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.rejectOqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async submitOqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.submitOqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelOqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.cancelOqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return {
          cancelOqcTicketResult: false,
          cancelOqcTicketMessageError: response.message,
        };
      return { cancelOqcTicketResult: true };
    } catch (err) {
      return { cancelOqcTicketResult: false, cancelOqcTicketMessageError: err };
    }
  }

  async updateStatusOqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateStatusOqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectInspection(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.rejectInspectionTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getDetailVendorAuditTicketById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getDetailVendorAuditTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListInspectionTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListInspectionTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListVendorAuditTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListVendorAuditTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListVendorAuditTicketByIds(request: {
    ids: number[];
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListVendorAuditTicketByIds,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createManyVendorAuditTicket(
    request: CreateVendorAuditTicketRequest[],
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createManyVendorAuditTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createVendorAuditTicket(
    request: CreateVendorAuditTicketRequest,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createVendorAuditTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailInspectionTcrsById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getDetailInspectionTcrsById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateVendorAuditTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateVendorAuditTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateInspectionTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateInspectionTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async approveVendorAuditTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.approveVendorAuditTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectVendorAuditTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.rejectVendorAuditTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateStatusVendorAuditTicket(request: {
    ids: number[];
    status: VENDOR_AUDIT_TICKET_STATUS;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateStatusVendorAuditTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getAllAuditTicketByVendorAuditPlanId(request: {
    id: number;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .getAllAuditTicketByVendorAuditPlanId,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createInspectionTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createInspectionTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveInspectionTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.approveInspectionTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectInspectionTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.rejectInspectionTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async createIpqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createIpqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createMultiIpqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createMultiIpqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateIpqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateIpqcicket,
        request,
      );

      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return null;
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateStatusIpqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateStatusIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getListIpqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListIpqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListIpqcTicketByKey(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListIpqcTicketByKey,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getIpqcTicketById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getIpqcTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return null;
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async approveIpqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.approveIpqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectIpqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.rejectIpqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelIpqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.cancelIpqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelIpqcTicketByRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.cancelIpqcTicketByRequest,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelIpqcTicketByPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.cancelIpqcTicketByPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async createEquipmentCalibrationTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createEquipmentCalibrationTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async cancelEquipmentCalibrationTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.cancelEquipmentCalibrationTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelEquipmentCalibrationTicketByPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .cancelEquipmentCalibrationTicketByPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveEquipmentCalibrationTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.approveEquipmentCalibrationTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async checkEquipmentCalibrationTicketStatusByPlan(
    request: any,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .checkEquipmentCalibrationTicketStatusByPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListQmsxEquipmentCalibrationRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .getListQmsxEquipmentCalibrationRequest,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailQmsxEquipmentCalibrationTicketById(
    request: any,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .getDetailQmsxEquipmentCalibrationTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateEquipmentCalibrationTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateEquipmentCalibrationTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async noteEquipmentCalibrationTicket(
    request: NoteEquipmentCalibrationTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.noteEquipmentCalibrationTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async deleteEquipmentCalibration(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.deleteEquipmentCalibration,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateStatusEquipmentCalibration(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateStatusEquipmentCalibration,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async checkEquipmentCalibrationOverlap(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.checkEquipmentCalibrationOverlap,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListAuditHandlingTicket(
    request: GetListAuditHandlingTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListAuditHandlingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createAuditHandlingTicket(
    request: CreateAuditHandlingTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createAuditHandlingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async approveAuditHandlingTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.approveAuditHandlingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectAuditHandlingTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.rejectAuditHandlingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateAuditHandlingTicket(
    request: UpdateAuditHandlingTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateAuditHandlingTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailAuditHandlingTicketById(request: {
    id: number;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getDetailAuditHandlingTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getAuditHandlingTicketByVendorAuditTicketId(request: {
    id: number;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .getAuditHandlingTicketByVendorAuditTicketId,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }
  //iqc-tcrs
  async getDetailQmsxIqcByIdTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getDetailIqcByIdTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListQmsxIqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListIqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListIqcTicketByKey(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListIqcTicketByKey,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createQmsxIqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createIqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createQmsxIqcTcrses(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createIqcTcrses,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return {
          result: false,
          messageError: response.message,
        };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateQmsxIqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateIqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateStatusIqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateStatusIqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateStatusByIqcRequestIdTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateStatusByIqcRequestIdTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateStatusInspectionTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateStatusInspectionTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveIqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.approveIqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectIqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.rejectIqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async unResponsiveIqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.unResponsiveIqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async submitIqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.submitIqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getListInspectionPureChemicalTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListInspectionPureChemicalTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailInspectionPureChemicalTicketById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .getDetailInspectionPureChemicalTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }
  async updateInspectionPureChemicalTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateInspectionPureChemicalTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createInspectionPureChemicalTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createInspectionPureChemicalTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async approveInspectionPureChemicalTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .approveInspectionPureChemicalTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelInspectionPureChemicalTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.cancelInspectionPureChemicalTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async deleteInspectionPureChemicalTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.deleteInspectionPureChemicalTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async createPqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createPqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListPqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListPqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailPqcTicketById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getDetailPqcTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updatePqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updatePqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async deletePqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.deletePqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateStatusPqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateStatusPqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async submitPqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.submitPqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approvePqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.approvePqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectPqcTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.rejectPqcTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async createPqcTickets(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createPqcTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelPqcTicketFromPqcPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.cancelPqcTicketFromPqcPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getPqcTicketByKey(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getPqcTicketByKey,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getPqcTicketByDeviceId(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getPqcTicketByDeviceId,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListVendorEvaluationTicket(
    request: GetListVendorEvaluationTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListVendorEvaluationTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async updateStatusVendorEvaluationTicket(request: {
    ids: number[];
    status: VENDOR_EVALUATION_TICKET_STATUS;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateStatusVendorEvaluationTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getAllTicketByVendorEvaluationPlanId(request: {
    id: number;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .getAllTicketByVendorEvaluationPlanId,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createManyVendorEvaluationTicket(
    request: CreateVendorEvaluationTicketRequestDto[],
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createManyVendorEvaluationTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async createVendorEvaluationTicket(
    request: CreateVendorEvaluationTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createVendorEvaluationTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async approveVendorEvaluationTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.approveVendorEvaluationTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectVendorEvaluationTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.rejectVendorEvaluationTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateVendorEvaluationTicket(
    request: UpdateVendorEvaluationTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateVendorEvaluationTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailVendorEvaluationTicketById(request: {
    id: number;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getDetailVendorEvaluationTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListIqcByVendorIdsWithinPeriod(request: {
    vendorIds: number[];
    fromDate: Date;
    toDate: Date;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListIqcByVendorIdsWithinPeriod,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListIqcTcrsByVendorIdsWithinPeriod(request: {
    vendorIds: number[];
    fromDate: Date;
    toDate: Date;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .getListIqcTcrsByVendorIdsWithinPeriod,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListAuditHandlingByVendorIdsWithinPeriod(request: {
    listKey: string[];
    evaluationPeriod: string;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .getListAuditHandlingByVendorIdsWithinPeriod,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListVendorAuditTicketByKeyAndEvaluationPeriod(request: {
    listKey: string[];
    evaluationPeriod: string;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .getListVendorAuditTicketByKeyAndEvaluationPeriod,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  // chemical import ticket
  async getListChemicalImportTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListChemicalImportTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailChemicalImportTicketById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getDetailChemicalImportTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }
  async updateChemicalImportTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateChemicalImportTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createChemicalImportTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createChemicalImportTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async approveChemicalImportTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.approveChemicalImportTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelChemicalImportTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.cancelChemicalImportTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async deleteChemicalImportTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.deleteChemicalImportTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  // chemical export ticket
  async getListChemicalExportTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListChemicalExportTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailChemicalExportTicketById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getDetailChemicalExportTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getInventoryQuantity(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getInventoryQuantity,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateChemicalExportTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateChemicalExportTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createChemicalExportTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createChemicalExportTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async approveChemicalExportTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.approveChemicalExportTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelChemicalExportTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.cancelChemicalExportTickets,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async deleteChemicalExportTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.deleteChemicalExportTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getDetailChemicalAnalysisTicketById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getDetailChemicalAnalysisTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailChemicalAnalysisTicketByKey(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path
          .getDetailChemicalAnalysisTicketByKey,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListChemicalAnalysisTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListChemicalAnalysisTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createChemicalAnalysisTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createChemicalAnalysisTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateChemicalAnalysisTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateChemicalAnalysisTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async approveChemicalAnalysisTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.approveChemicalAnalysisTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async inprogressChemicalAnalysisTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.inprogressChemicalAnalysisTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelChemicalAnalysisTicket(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.cancelChemicalAnalysisTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getMeasurementDetailByKey(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getMeasurementDetailByKey,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createMeasurementDetail(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createMeasurementDetail,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createManyMeasurementDetail(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createManyMeasurementDetail,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateMeasurementDetail(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateMeasurementDetail,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async deleteMeasurementDetail(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.deleteMeasurementDetail,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getDetailByMeasurementDetailId(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getDetailByMeasurementDetailId,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  //pqc-tcrs
  async getDetailPqcTcrsById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getDetailPqcByIdTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListPqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListPqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createPqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createPqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async createPqcTcrses(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createPqcTcrses,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updatePqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updatePqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async reviewPqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.reviewPqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateStatusPqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.updateStatusPqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async submitPqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.submitPqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approvePqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.approvePqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectPqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.rejectPqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async deletePqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.deletePqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getListChemicalAnalysisResult(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListChemicalAnalysisResult,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getChemicalSpcChart(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getChemicalSpcChart,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async validateMaster(request: ValidateMasterRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.validateMaster,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return false;
      return true;
    } catch (err) {
      return false;
    }
  }

  async getListChemicalInventory(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListChemicalInventory,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getOqcDashboardSummary(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getOqcDashboardSummary,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getOqcDashboardSetting(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getListOqcDashboardSetting,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getAllOqcDashboardSetting(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getAllOqcDashboardSetting,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createOqcDashboardSetting(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.createOqcDashboardSetting,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getWeeklyRejectRate(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getWeeklyRejectRate,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getRejectRateByPeriod(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getRejectRateByPeriod,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getRejectRateByCustomer(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getRejectRateByCustomer,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getTop10WorstDefects(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTicket.path.getTop10WorstDefects,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }
}
