import { Injectable } from '@nestjs/common';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { natsConfig } from '../../../config/nats.config';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { NatsClientService } from '../../../core/transporter/nats-transporter/nats-client.service';
import { IqcRequestResponseDto } from '../response/iqc-request/get-detail-iqc-request.response.dto';

@Injectable()
export class QmsxRequestService {
  constructor(private readonly natClientService: NatsClientService) {}

  async getDetailQmsxRequestById(request: any): Promise<IqcRequestResponseDto> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.getDetailIqcById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return new IqcRequestResponseDto();
      return response?.data;
    } catch (err) {
      return new IqcRequestResponseDto();
    }
  }

  async getListQmsxRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.getListIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createQmsxRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.createIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateQmsxRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.updateIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateStatusIqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.updateStatusIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveIqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.approveIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectIqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.rejectIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelIqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.cancelIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async deleteIqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.deleteIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async validateGrNumberAndItem(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.validateItemAndGrNumberIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async createIpqcRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.createIpqc,
        request,
      );

      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }
  async updateStatusInspection(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.updateStatusInspection,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveInspection(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.approveInspection,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectInspection(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.rejectInspection,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async deleteInspection(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.deleteInspection,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getDetailQmsxInspectionRequestById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.getDetailInspectionById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListQmsxInspectionRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.getListInspectionRequests,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListIpqcRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.getListIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];

      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListIpqcRequestDetail(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.getListIpqcDetail,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];

      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListIpqcRequestByIds(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.getListIpqcByIds,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];

      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createInspectionRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.createInspection,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailIpqcRequestById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.getDetailIpqcById,
        request,
      );

      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return null;
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateInspectionRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.updateInspection,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateIpqcRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.updateIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateIpqcRequestFromPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.updateIpqcFromPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async deleteIpqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.deleteIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveIpqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.approveIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectIpqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.rejectIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelIpqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.cancelIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async inprogressIpqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.inprogressIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async completeIpqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.completeIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateStatusIpqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.updateStatusIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async validateMaster(request: ValidateMasterRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.validateMaster,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return false;
      return true;
    } catch (err) {
      return false;
    }
  }

  async getAllIqcRequestByIds(request: { ids: number[] }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxRequest.path.getAllIqcRequestByIds,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }
}
