import { Injectable } from '@nestjs/common';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { natsConfig } from '../../../config/nats.config';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { IdParamDto } from '../../../core/dto/request/id-param.request.dto';
import { NatsClientService } from '../../../core/transporter/nats-transporter/nats-client.service';
import { GetListPqcPlanByDeviceIdDto } from '../../pqc-plan/request/get-list-pqc-plan-by-device-id.request.dto';
import { GetListPqcPlanRequestDto } from '../../pqc-plan/request/get-list-pqc-plan.request.dto';
import { UpdatePqcPlanFromPqcTcrsRequestDto } from '../../pqc-plan/request/update-pqc-plan-from-pqc-tcrs.request.dto';
import { UpdatePqcPlanRequestDto } from '../../pqc-plan/request/update-pqc-plan.request.dto';
import { GetListVendorAuditPlanRequestDto } from '../../vendor-audit-plan/request/get-list-vendor-audit-plan.request.dto';
import { VENDOR_AUDIT_PLAN_STATUS } from '../../vendor-audit-plan/vendor-audit-plan.constant';
import { CreateVendorEvaluationPlanRequestDto } from '../../vendor-evaluation-plan/request/create-vendor-evaluation-plan.request.dto';
import { GetListVendorEvaluationPlanRequestDto } from '../../vendor-evaluation-plan/request/get-list-vendor-evaluation-plan.request.dto';
import { UpdateVendorEvaluationPlanRequestDto } from '../../vendor-evaluation-plan/request/update-vendor-evaluation-plan.request.dto';
import { VENDOR_EVALUATION_PLAN_STATUS } from '../../vendor-evaluation-plan/vendor-evaluation-plan.constant';
import { CreateVendorAuditPlanRequest } from '../request/vendor-audit-plan/create-vendor-audit-plan.request';
import { UpdateVendorAuditPlanRequest } from '../request/vendor-audit-plan/update-vendor-audit-plan.request.dto';

@Injectable()
export class QmsxPlanService {
  constructor(private readonly natClientService: NatsClientService) {}
  async getDetailQmsxPlanByIqcRequestId(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getDetailIqcByIqcRequestId,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailQmsxPlanById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getDetailIqcById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListQmsxPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getListIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createQmsxPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.createIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateQmsxPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateStatusIqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateStatusIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response?.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateStatusByIqcRequestId(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateStatusByIqcRequestId,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response?.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveIqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.approveIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectIqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.rejectIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelIqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.cancelIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async deleteIqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.deleteIqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async createVendorAuditPlan(
    request: CreateVendorAuditPlanRequest,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.createVendorAuditPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateStatusInspection(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateStatusInspection,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveInspection(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.approveInspection,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelInspection(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.cancelInspection,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectInspection(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.rejectInspection,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async deleteInspection(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.deleteInspection,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getDetailQmsxInspectionPlanById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getDetailInspectionById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateVendorAuditPlan(
    request: UpdateVendorAuditPlanRequest,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateVendorAuditPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListQmsxInspectionRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getListInspectionPlans,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createInspectionRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.createInspection,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async listVendorAuditPlan(
    request: GetListVendorAuditPlanRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getListVendorAuditPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async approveVendorAuditPlan(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.approveVendorAuditPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateStatusVendorAuditPlan(request: {
    ids: number[];
    status: VENDOR_AUDIT_PLAN_STATUS;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateStatusVendorAuditPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectVendorAuditPlan(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.rejectVendorAuditPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async deleteVendorAuditPlan(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.deleteVendorAuditPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getDetailVendorAuditPlanById(request: { id: number }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getDetailVendorAuditPlanById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getExistVendorAuditPlanByKey(request: {
    listKey: string[];
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getExistVendorAuditPlanByKey,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createInspectionPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.createInspection,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateInspectionPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateInspection,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailOqcPlanById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getDetailOqcById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListOqcPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getListOqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createOqcPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.createOqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateOqcPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateOqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateStatusOqcPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateStatusOqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveOqcPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.approveOqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectOqcPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.rejectOqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelOqcPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.cancelOqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { cancelResult: false, cancelMessageError: response.message };
      return { cancelResult: true };
    } catch (err) {
      return { cancelResult: false, cancelMessageError: err };
    }
  }

  async deleteOqcPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.deleteOqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async createEquipmentCalibrationPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.createEquipmentCalibrationPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListQmsxEquipmentCalibrationRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path
          .getListQmsxEquipmentCalibrationRequest,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailQmsxEquipmentCalibrationPlanById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path
          .getDetailQmsxEquipmentCalibrationPlanById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailQmsxEquipmentCalibrationPlanByCode(
    request: any,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path
          .getDetailQmsxEquipmentCalibrationPlanByCode,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async checkCalibrationOverlap(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.checkCalibrationOverlap,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateEquipmentCalibrationPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateEquipmentCalibrationPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async deleteEquipmentCalibration(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.deleteEquipmentCalibration,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateStatusEquipmentCalibration(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateStatusEquipmentCalibration,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveEquipmentCalibration(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.approveEquipmentCalibration,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectEquipmentCalibration(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.rejectEquipmentCalibration,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getListVendorEvaluationPlan(
    request: GetListVendorEvaluationPlanRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getListVendorEvaluationPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateStatusVendorEvaluationPlan(request: {
    ids: number[];
    status: VENDOR_EVALUATION_PLAN_STATUS;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateStatusVendorEvaluationPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveVendorEvaluationPlan(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.approveVendorEvaluationPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectVendorEvaluationPlan(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.rejectVendorEvaluationPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async deleteVendorEvaluationPlan(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.deleteVendorEvaluationPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getDetailVendorEvaluationPlanById(request: {
    id: number;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getDetailVendorEvaluationPlanById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getVendorEvaluationByEvaluationPeriod(request: {
    evaluationPeriod: string;
    keyDupInDb: string[];
    vendorType: number;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path
          .getVendorEvaluationPlanByEvaluationPeriod,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createVendorEvaluationPlan(
    request: CreateVendorEvaluationPlanRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.createVendorEvaluationPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateVendorEvaluationPlan(
    request: UpdateVendorEvaluationPlanRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateVendorEvaluationPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createPqcPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.createPqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListPqcPlan(request: GetListPqcPlanRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getListPqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getDetailPqcPlanById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getDetailPqcById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updatePqcPlan(request: UpdatePqcPlanRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updatePqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async deletePqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.deletePqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateStatusPqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateStatusPqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response?.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelPqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.cancelPqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response?.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approvePqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.approvePqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectPqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.rejectPqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getPqcPlanByDeviceId(
    request: GetListPqcPlanByDeviceIdDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getPqcPlanByDeviceId,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateFromPqcTcrs(
    request: UpdatePqcPlanFromPqcTcrsRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updatePqcPlanFormTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async cancelEquipmentCalibrationPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.cancelEquipmentCalibrationPlan,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  // ipqc-plan
  async createIpqcPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.createIpqc,
        request,
      );

      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListIpqcPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getListIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];

      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListIpqcPlanDetail(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getListIpqcDetail,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];

      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getDetailIpqcPlanById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getDetailIpqcById,
        request,
      );

      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return null;
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getIpqcPlanByIpqcRequestId(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.getIpqcPlanByIpqcRequestId,
        request,
      );

      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return null;
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateIpqcPlan(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async deleteIpqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.deleteIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async approveIpqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.approveIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectIpqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.rejectIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelIpqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.cancelIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async cancelIpqcPlanByRequest(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.cancelIpqcPlanByRequest,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async inprogressIpqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.inprogressIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async completeIpqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.completeIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateStatusIpqc(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.updateStatusIpqc,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async validateMaster(request: ValidateMasterRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxPlan.path.validateMaster,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return false;
      return true;
    } catch (err) {
      return false;
    }
  }
}
