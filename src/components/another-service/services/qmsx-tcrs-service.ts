import { Injectable } from '@nestjs/common';
import { natsConfig } from '../../../config/nats.config';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { NatsClientService } from '../../../core/transporter/nats-transporter/nats-client.service';

@Injectable()
export class QmsxTcrsService {
  constructor(private readonly natClientService: NatsClientService) {}

  async getDetailOqcTcrsById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTcrs.path.getDetailOqcTcrsById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListOqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTcrs.path.getListOqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createOqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTcrs.path.createOqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateOqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTcrs.path.updateOqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async approveOqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTcrs.path.approveOqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectOqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTcrs.path.rejectOqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async submitOqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTcrs.path.submitOqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async updateStatusOqcTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTcrs.path.updateStatusOqcTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getDetailChemicalTcrsById(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTcrs.path.getDetailChemicalTcrsById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailChemicalTcrsByKey(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTcrs.path.getDetailChemicalTcrsByKey,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListChemicalTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTcrs.path.getListChemicalTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createChemicalTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTcrs.path.createChemicalTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createManyChemicalTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTcrs.path.createManyChemicalTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateChemicalTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTcrs.path.updateChemicalTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async deleteChemicalTcrs(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxTcrs.path.deleteChemicalTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }
}
