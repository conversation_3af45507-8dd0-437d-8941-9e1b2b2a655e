import { Injectable } from '@nestjs/common';
import { natsConfig } from '../../../config/nats.config';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { IdParamDto } from '../../../core/dto/request/id-param.request.dto';
import { NatsClientService } from '../../../core/transporter/nats-transporter/nats-client.service';
import { CreateAuditVendorResultRequestDto } from '../../audit-vendor-result/request/create-audit-vendor-result.request.dto';
import { GetListAuditVendorResultRequestDto } from '../../audit-vendor-result/request/get-list-audit-vendor-result.request.dto';
import { CreateClaimVendorReportRequestDto } from '../../claim-vendor-report/request/create-claim-vendor-report.request.dto';
import { GetListClaimVendorReportRequestDto } from '../../claim-vendor-report/request/get-list-claim-vendor-report.request.dto';
import { GetListCsDashboardRequestDto } from '../../cs-dashboard/request/get-list-cs-dashboard.request.dto';
import { CreatePqcDeviceDetailReportRequestDto } from '../../pqc-report/request/create-pqc-device-detail-report.request.dto';
import { CreatePqcDeviceErrorReportRequestDto } from '../../pqc-report/request/create-pqc-device-error-report.request.dto';
import { CreatePqcDeviceReportRequestDto } from '../../pqc-report/request/create-pqc-device-report.request.dto';
import { CreatePqcErrorReportRequestDto } from '../../pqc-report/request/create-pqc-error-report.request.dto';
import { CreatePqcReportRequestDto } from '../../pqc-report/request/create-pqc-report.request.dto';
import { GetListPqcReportRequestDto } from '../../pqc-report/request/get-list-pqc-report.request.dto';
import { UpdatePqcDeviceReportFromTcrsRequestDto } from '../../pqc-report/request/update-pqc-device-report-from-tcrs.request.dto';
import { UpdatePqcReportFromTcrsRequestDto } from '../../pqc-report/request/update-pqc-report-from-tcrs.request.dto';
import { CreateReportConfigRequestDto } from '../../report-config/request/create-report-config.request.dto';
import { UpdateReportConfigRequestDto } from '../../report-config/request/update-report-config.request.dto';
import { CreateSalesQualityReportRequestDto } from '../../sales-quality-report/request/create-sales-quality-report.request.dto';
import { GetListSalesQualityReportRequestDto } from '../../sales-quality-report/request/get-list-sales-quality-report.request.dto';
import { CreateVendorEvaluationReportRequestDto } from '../../vendor-evaluation-report/request/create-vendor-evaluation-report.request.dto';
import { GetListVendorEvaluationReportRequestDto } from '../../vendor-evaluation-report/request/get-list-vendor-evaluation-report.request.dto';

@Injectable()
export class QmsxReportService {
  constructor(private readonly natClientService: NatsClientService) {}

  async getListOqcReport(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getListOqcReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createOqcReport(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.createOqcReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateOqcReport(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.updateOqcReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailOqcReportByCustomerId(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getDetailOqcReportByCustomerId,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListOqcTcrsReport(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getListOqcTcrsReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createOqcTcrsReport(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.createOqcTcrsReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListOqcErrorReport(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getListOqcErrorReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createOqcErrorReport(request: any): Promise<boolean> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.createOqcErrorReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return false;
      return true;
    } catch (err) {
      return false;
    }
  }

  async createClaimVendorReport(
    request: CreateClaimVendorReportRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.createClaimVendorReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getClaimVendorReport(
    request: GetListClaimVendorReportRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getVendorClaimReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async rollBackClaimVendorReport(request: { id: number }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.rollBackClaimVendorReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async createAuditVendorResult(
    request: CreateAuditVendorResultRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.createAuditVendorResult,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getDetailAuditVendorResult(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getDetailAuditVendorResult,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getAuditResultByVendorAuditTicketId(request: {
    id: number;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getByVendorAuditTicketId,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListAuditVendorResult(
    request: GetListAuditVendorResultRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getListAuditVendorResult,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getReportAuditVendor(
    request: GetListAuditVendorResultRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getReportAuditVendor,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async rollBackAuditVendorReport(request: { id: number }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.rollBackAuditVendorReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getListContractVendorEvaluationReportDetail(
    request: GetListVendorEvaluationReportRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path
          .getListContractVendorEvaluationReportDetail,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListMaterialVendorEvaluationReport(
    request: GetListVendorEvaluationReportRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path
          .getListMaterialVendorEvaluationReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListContractVendorEvaluationReport(
    request: GetListVendorEvaluationReportRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path
          .getListContractVendorEvaluationReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createVendorEvaluationReport(
    request: CreateVendorEvaluationReportRequestDto[],
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.createVendorEvaluationReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rollBackVendorEvaluationReport(request: {
    ids: number[];
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.rollBackVendorEvaluationReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getVendorEvaluationReportByVendorEvaluationTicketDetailIds(request: {
    ids: number[];
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path
          .getVendorEvaluationReportByVendorEvaluationTicketDetailIds,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  // pqc-report
  async createPqcReport(request: CreatePqcReportRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.createPqcReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createPqcErrorReport(
    request: CreatePqcErrorReportRequestDto[],
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.createPqcErrorReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createPqcDeviceReport(
    request: CreatePqcDeviceReportRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.createPqcDeviceReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createPqcDeviceDetailReport(
    request: CreatePqcDeviceDetailReportRequestDto[],
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.createPqcDeviceDetailReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createPqcDeviceErrorReport(
    request: CreatePqcDeviceErrorReportRequestDto[],
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.createPqcDeviceErrorReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updatePqcReportFromTcrs(
    request: UpdatePqcReportFromTcrsRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.updatePqcReportFromTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updatePqcDeviceReportFromTcrs(
    request: UpdatePqcDeviceReportFromTcrsRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.updatePqcDeviceReportFromTcrs,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getPqcReport(request: GetListPqcReportRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getPqcReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getPqcCompareReport(request: GetListPqcReportRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getPqcCompareReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getPqcErrorAnalysisReport(
    request: GetListPqcReportRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getPqcErrorAnalysisReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getPqcSummaryReportByProcess(
    request: GetListPqcReportRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getPqcSummaryReportByProcess,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getPqcDeviceReport(request: GetListPqcReportRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getPqcDeviceReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getPqcDeviceResultReport(
    request: GetListPqcReportRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getPqcDeviceResultReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getPqcDeviceResultReportByDevice(
    request: GetListPqcReportRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getPqcDeviceResultReportByDevice,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async rollBackPqcReport(request: { id: number }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.rollBackPqcReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async createReportConfig(
    request: CreateReportConfigRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.createReportConfig,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateReportConfig(
    request: UpdateReportConfigRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.updateReportConfig,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailReportConfig(request: { id: number }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getDetailReportConfig,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getReportConfigByVendorType(request: {
    vendorType: number;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getReportConfigByVendorType,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListSalesQualityReport(
    request: GetListSalesQualityReportRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getListSalesQualityReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async rollBackSalesQualityReport(request: { ids: number[] }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.rollBackSalesQualityReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rollBackReportByShipmentTrackingTicketId(request: {
    id: number;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path
          .rollBackReportByShipmentTrackingTicketId,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async createSalesQualityReport(
    request: CreateSalesQualityReportRequestDto[],
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.createSalesQualityReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getExportedLotQuantityForCsDashboard(
    request: GetListCsDashboardRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getExportedLotQuantity,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getTopExportedLotByItemLines(
    request: GetListCsDashboardRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getTopExportedLotByItemLines,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getTopRateNgItems(request: GetListCsDashboardRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getTopRateNgItems,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getErrorRateByStageIpqcReport(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getErrorRateByStageIpqcReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getWorstErrorIpqcReport(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.getWorstErrorIpqcReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createIpqcReport(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.createIpqcReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async deleteIpqcReport(request: any): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.qmsxReport.path.deleteIpqcReport,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }
}
