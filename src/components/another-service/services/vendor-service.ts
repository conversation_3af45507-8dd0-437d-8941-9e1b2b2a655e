import { Injectable } from '@nestjs/common';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { natsConfig } from '../../../config/nats.config';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { IdParamDto } from '../../../core/dto/request/id-param.request.dto';
import { NatsClientService } from '../../../core/transporter/nats-transporter/nats-client.service';
import { DateRangeDto } from '../../../utils/dto/request/date-range.request.dto';
import { CLAIM_FEE_TICKET_STATUS } from '../../claim-fee-ticket/claim-fee-ticket.constant';
import { GetDataForCreateClaimFeeTicketRequestDto } from '../../claim-fee-ticket/request/get-data-for-create-claim-fee-ticket.request.dto';
import { GetListClaimFeeTicketRequestDto } from '../../claim-fee-ticket/request/get-list-claim-fee-ticket.request.dto';
import { UpdateStatusClaimFeeTicketRequestDto } from '../../claim-fee-ticket/request/update-status-claim-fee-ticket.request.dto';
import { GetListClaimUnitPriceRequestDto } from '../../claim-unit-price/request/get-list-claim-unit-price.request.dto';
import { ImportVendorRequestDto } from '../../import-excel/request/import-vendor.request.dto';
import {
  CLAIM_TYPE,
  TICKET_CLAIM_STATUS,
} from '../../ticket-claim/ticket-claim.constant';
import { CreateClaimFeeTicketRequest } from '../request/claim-fee-ticket/create-claim-fee-ticket.request';
import { UpdateClaimFeeTicketRequest } from '../request/claim-fee-ticket/update-claim-fee-ticket.request';
import { CreateClaimUnitPriceRequest } from '../request/claim-unit-price/create-claim-unit-price.request';
import { UpdateClaimUnitPriceRequest } from '../request/claim-unit-price/update-claim-unit-price.request';
import { CreateTicketClaimRequest } from '../request/ticket-claim/create-ticket-claim.request';
import { GetListTicketClaimRequest } from '../request/ticket-claim/get-list-ticket-claim.request';
import { UpdateTicketClaimRequest } from '../request/ticket-claim/update-ticket-claim.request';

@Injectable()
export class VendorService {
  constructor(private readonly natClientService: NatsClientService) {}

  async getVendorById(request: { id: number }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getVendorById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getTicketClaimDetailById(request: { id: number }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getTicketClaimById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async createTicketClaim(request: CreateTicketClaimRequest): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.createTicketClaim,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateTicketClaim(request: UpdateTicketClaimRequest): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.updateTicketClaim,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async listTicketClaim(request: GetListTicketClaimRequest): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.listTicketClaim,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListTicketClaimExistByKey(request: {
    listKey: string[];
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getListTicketClaimExistByKey,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async getListTicketClaimByVendorIdsInPeriods(request: {
    vendorIds: number[];
    fromDate: Date;
    toDate: Date;
    statuses: TICKET_CLAIM_STATUS[];
    claimTypes?: CLAIM_TYPE[];
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getListTicketClaimByVendorIdsInPeriods,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getListClaimFeeTicketByVendorIdsInPeriods(request: {
    vendorIds: number[];
    fromDate: Date;
    toDate: Date;
    statuses: CLAIM_FEE_TICKET_STATUS[];
    claimTypes?: CLAIM_TYPE[];
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path
          .getListClaimFeeTicketByVendorIdsInPeriods,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async approveTicketClaim(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.approveTicketClaim,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectTicketClaim(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.rejectTicketClaim,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async deleteTicketClaim(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.deleteTicketClaim,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async findAllVendorByIds(ids: number[]): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getAllVendorByIds,
        ids,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createClaimUnitPrice(
    request: CreateClaimUnitPriceRequest,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.createClaimUnitPrice,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailClaimUnitPriceById(request: { id: number }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getDetailClaimUnitPriceById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateClaimUnitPrice(
    request: UpdateClaimUnitPriceRequest,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.updateClaimUnitPrice,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getClaimUnitPriceByClaimPeriod(request: {
    claimPeriod: string;
    status?: number;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getClaimUnitPriceByClaimPeriod,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getClaimUnitPriceByDateRange(request: {
    fromDate: Date;
    toDate: Date;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getClaimUnitPriceByDateRange,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async listClaimUnitPrice(
    request: GetListClaimUnitPriceRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.listClaimUnitPrice,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async approveClaimUnitPrice(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.approveClaimUnitPrice,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectClaimUnitPrice(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.rejectClaimUnitPrice,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async deleteClaimUnitPrice(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.deleteClaimUnitPrice,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getApprovedClaimUnitPriceInDate(request: {
    claimDate: Date;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getApprovedClaimUnitPriceInDate,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDataForClaimUnitPrice(request: DateRangeDto): Promise<any[]> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getDataForClaimUnitPrice,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async createClaimTicketFee(
    request: CreateClaimFeeTicketRequest,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.createClaimFeeTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getDetailClaimFeeTicketById(request: { id: number }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getDetailClaimFeeTicketById,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getClaimFeeTicketByVendorIdInPeriod(request: {
    vendorId: number;
    fromDate: Date;
    toDate: Date;
    status?: number;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getClaimFeeTicketByVendorIdInPeriod,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return [];
      return response?.data;
    } catch (err) {
      return [];
    }
  }

  async updateClaimFeeTicket(
    request: UpdateClaimFeeTicketRequest,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.updateClaimFeeTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async listClaimFeeTicket(
    request: GetListClaimFeeTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.listClaimFeeTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async approveClaimFeeTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.approveClaimFeeTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true, data: response.data };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async rejectClaimFeeTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.rejectClaimFeeTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async deleteClaimFeeTicket(request: IdParamDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.deleteClaimFeeTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async getDataForCreateClaimFeeTicket(
    request: GetDataForCreateClaimFeeTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getDataForCreateClaimFeeTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async updateStatusClaimFeeTicket(
    request: UpdateStatusClaimFeeTicketRequestDto,
  ): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.updateStatusClaimFeeTicket,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS)
        return { result: false, messageError: response.message };
      return { result: true };
    } catch (err) {
      return { result: false, messageError: err };
    }
  }

  async findAllCriteriaByIds(ids: number[]): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getAllCriteriaTypeByIds,
        ids,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async findAllScoreBoardByIds(ids: number[]): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getAllScoreBoardByIds,
        ids,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async findAllVendorByCodes(codes): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.getAllVendorByCodes,
        codes,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async importVendors(request: ImportVendorRequestDto[]): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.importVendors,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async getScoreboardByVendorTypeAndScoreboardType(request: {
    vendorType: number;
    scoreboardType: number;
  }): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path
          .getScoreboardByVendorTypeAndScoreboardType,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return {};
      return response?.data;
    } catch (err) {
      return {};
    }
  }

  async validateMaster(request: ValidateMasterRequestDto): Promise<any> {
    try {
      const response = await this.natClientService.send(
        natsConfig.services.vendor.path.validateMaster,
        request,
      );
      if (response.statusCode !== ResponseCodeEnum.SUCCESS) return false;
      return true;
    } catch (err) {
      return false;
    }
  }
}
