import { FileDto } from '../../../../utils/dto/request/file.request.dto';

export class IqcRequestDetailResponseDto {
  id: number;

  iqcRequestId: number;

  lotNo: string;

  itemId: number;

  processId: number;

  grQuantity: number;

  itemUnitId: number;

  itemUnitQcId: number;

  numberOfTimeQc: number;

  iqcTicketCodeBefore: string;

  iqcTicketIdBefore: number;
}

export class IqcRequestResponseDto {
  id: number;

  code: string;

  qcRequestTypeId: number;

  requestDate: Date;

  purchaseOrder: string;

  grDate: Date;

  grNumber: string;

  returnResultDate: Date;

  vendorId: number;

  approvedBy: number;

  approvedAt: Date;

  description: string;

  status: number;

  iqcRequestDetails: IqcRequestDetailResponseDto[];

  files: FileDto[];
}
