import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Matches,
  MaxLength,
} from 'class-validator';

export class ImportCustomerRequestDto {
  @IsNotEmpty()
  @MaxLength(50)
  @Matches(/^[a-zA-Z0-9_.-]+$/)
  code: string;

  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @IsNotEmpty()
  @IsNumber()
  country: number;

  @IsOptional()
  @MaxLength(255)
  address: string;

  @IsOptional()
  @MaxLength(255)
  representative?: string;

  @IsOptional()
  @MaxLength(255)
  tel?: string;

  @IsOptional()
  @MaxLength(255)
  email?: string;

  @IsOptional()
  @MaxLength(255)
  note?: string;
}
