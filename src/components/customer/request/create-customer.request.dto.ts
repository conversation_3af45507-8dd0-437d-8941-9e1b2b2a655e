import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, MaxLength } from 'class-validator';

export class CreateCustomerRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  code: string;

  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  country: number;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  address: string;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  representative?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  tel?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  email?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  note?: string;
}
