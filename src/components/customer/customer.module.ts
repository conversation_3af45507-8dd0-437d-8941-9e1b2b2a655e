import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { CustomerController } from './controller/customer.controller';
import { CustomerEntity } from './entities/customer.entity';
import { CustomerRepository } from './repository/customer.repository';
import { CustomerService } from './service/customer.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([CustomerEntity]),
    AnotherServiceModule,
    MasterDataReferenceModule,
  ],
  providers: [CustomerService, CustomerRepository],
  exports: [CustomerService],
  controllers: [CustomerController],
})
export class CustomerModule {}
