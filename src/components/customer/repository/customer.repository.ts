import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { isDateString } from 'class-validator';
import { StatusEnum } from '../../../common/enums/status.enum';
import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { escapeCharForSearch } from '../../../utils/common';
import { CustomerEntity } from '../entities/customer.entity';
import { GetDetailCustomerRequestDto } from '../request/get-detail-customer.request.dto';
import { GetListCustomerRequestDto } from '../request/get-list-customer.request.dto';
import { UpdateCustomerRequestDto } from '../request/update-customer.request.dto';
import { UpdateStatusCustomerRequestDto } from '../request/update-status-customer.request.dto';
import { CreateCustomerRequestDto } from './../request/create-customer.request.dto';

@Injectable()
export class CustomerRepository extends BaseAbstractRepository<CustomerEntity> {
  constructor(
    @InjectRepository(CustomerEntity)
    private readonly customerRepository: Repository<CustomerEntity>,
  ) {
    super(customerRepository);
  }

  createEntity(request: CreateCustomerRequestDto): CustomerEntity {
    const customerEntity = new CustomerEntity();
    customerEntity.code = request.code;
    customerEntity.name = request.name;
    customerEntity.country = request.country;
    customerEntity.address = request.address;
    customerEntity.representative = request.representative;
    customerEntity.tel = request.tel;
    customerEntity.email = request.email;
    customerEntity.note = request.note;
    customerEntity.status = StatusEnum.ACTIVE;
    customerEntity.createdBy = request.userId;
    customerEntity.updatedBy = request.userId;

    return customerEntity;
  }

  updateEntity(
    request: UpdateCustomerRequestDto,
    entity: CustomerEntity,
  ): CustomerEntity {
    entity.name = request?.name;
    entity.country = request.country;
    entity.address = request.address;
    entity.representative = request.representative;
    entity.tel = request.tel;
    entity.email = request.email;
    entity.note = request.note;
    entity.updatedBy = request?.userId;

    return entity;
  }

  async getList(request: GetListCustomerRequestDto): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.customerRepository.createQueryBuilder('customer');

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("customer"."code") like lower(:code) escape '\\' OR
              lower("customer"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('customer.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'code':
            query.andWhere(
              `lower("customer"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'name':
            query.andWhere(
              `lower("customer"."name") like lower(:name) escape '\\'`,
              {
                name: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'country':
            query.andWhere('"customer"."country" = :country', {
              country: Number(value),
            });
            break;
          case 'countries':
            query.andWhere('"customer"."country" IN (:...countries)', {
              countries: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'address':
            query.andWhere(
              `lower("customer"."address") like lower(:address) escape '\\'`,
              {
                address: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'representative':
            query.andWhere(
              `lower("customer"."representative") like lower(:representative) escape '\\'`,
              {
                representative: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'tel':
            query.andWhere(
              `lower("customer"."tel") like lower(:tel) escape '\\'`,
              {
                tel: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'email':
            query.andWhere(
              `lower("customer"."email") like lower(:email) escape '\\'`,
              {
                email: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'note':
            query.andWhere(
              `lower("customer"."note") like lower(:note) escape '\\'`,
              {
                note: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'status':
            query.andWhere('"customer"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'createdById':
            query.andWhere('"customer"."created_by" = :createdById', {
              createdById: Number(value),
            });
            break;
          case 'createdByIds':
            query.andWhere('"customer"."created_by" IN (:...createdByIds)', {
              createdByIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'createdAt':
            const createFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const createTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"customer"."created_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: createFrom,
                dateTo: createTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"customer"."updated_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: updateFrom,
                dateTo: upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'name':
            query.addOrderBy('"customer"."name"', order);
            break;
          case 'code':
            query.addOrderBy('"customer"."code"', order);
            break;
          case 'country':
            query.addOrderBy('"customer"."country"', order);
            break;
          case 'address':
            query.addOrderBy('"customer"."address"', order);
            break;
          case 'representative':
            query.addOrderBy('"customer"."representative"', order);
            break;
          case 'tel':
            query.addOrderBy('"customer"."tel"', order);
            break;
          case 'email':
            query.addOrderBy('"customer"."email"', order);
            break;
          case 'note':
            query.addOrderBy('"customer"."note"', order);
            break;
          case 'status':
            query.addOrderBy('"customer"."status"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"customer"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"customer"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"customer"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('customer.id', 'DESC');
    }

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const [data, count] = await query.getManyAndCount();

    return { data, count };
  }

  async getDetail(
    request: GetDetailCustomerRequestDto,
  ): Promise<CustomerEntity> {
    const { id } = request;

    const data = await this.customerRepository.findOne({
      where: { id: id },
    });

    return data;
  }

  async active(request: UpdateStatusCustomerRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.customerRepository
      .createQueryBuilder()
      .update(CustomerEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(request: UpdateStatusCustomerRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.customerRepository
      .createQueryBuilder()
      .update(CustomerEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }
}
