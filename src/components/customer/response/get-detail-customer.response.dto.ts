import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseCommonResponseDto } from '../../../common/dtos/response/base.common.dto';

export class GetDetailCustomerResponseDto extends BaseCommonResponseDto {
  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  country: number;

  @ApiProperty()
  @Expose()
  address: string;

  @ApiProperty()
  @Expose()
  representative: string;

  @ApiProperty()
  @Expose()
  tel: string;

  @ApiProperty()
  @Expose()
  email: string;

  @ApiProperty()
  @Expose()
  note: string;

  @ApiProperty()
  @Expose()
  status: number;
}
