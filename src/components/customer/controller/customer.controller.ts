import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  ACTIVE_CUSTOMER_PERMISSION,
  CREATE_CUSTOMER_PERMISSION,
  DELETE_CUSTOMER_PERMISSION,
  DETAIL_CUSTOMER_PERMISSION,
  INACTIVE_CUSTOMER_PERMISSION,
  LIST_CUSTOMER_PERMISSION,
  UPDATE_CUSTOMER_PERMISSION,
} from '../../../utils/permissions/customer.permission';
import { CreateCustomerRequestDto } from '../request/create-customer.request.dto';
import { DeleteCustomerRequestDto } from '../request/delete-customer.request.dto';
import { GetDetailCustomerRequestDto } from '../request/get-detail-customer.request.dto';
import { GetListCustomerRequestDto } from '../request/get-list-customer.request.dto';
import { UpdateCustomerRequestDto } from '../request/update-customer.request.dto';
import { UpdateStatusCustomerRequestDto } from '../request/update-status-customer.request.dto';
import { CustomerService } from '../service/customer.service';

@Controller('customers')
export class CustomerController {
  constructor(private readonly customerService: CustomerService) {}

  @PermissionCode(DETAIL_CUSTOMER_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Customers'],
    summary: 'Chi tiết Customers',
    description: 'Chi tiết Customers',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailCustomerRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.customerService.getDetail(request);
  }

  @PermissionCode(LIST_CUSTOMER_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Customers'],
    summary: 'Danh sách Customers',
    description: 'Danh sách Customers',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(@Query() query: GetListCustomerRequestDto): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.customerService.getList(request);
  }

  @PermissionCode(CREATE_CUSTOMER_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Customers'],
    summary: 'Tạo Customers mới',
    description: 'Tạo Customers mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateCustomerRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.customerService.create(request);
  }

  @PermissionCode(UPDATE_CUSTOMER_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Customers'],
    summary: 'Cập nhật Customers',
    description: 'Cập nhật Customers',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(@Body() body: UpdateCustomerRequestDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.customerService.update(request);
  }

  @PermissionCode(ACTIVE_CUSTOMER_PERMISSION.code)
  @Put('/active')
  @ApiOperation({
    tags: ['Customers'],
    summary: 'Cập nhật Customers Status Active',
    description: 'Cập nhật Customers Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async active(
    @Body() body: UpdateStatusCustomerRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.customerService.active(request);
  }

  @PermissionCode(INACTIVE_CUSTOMER_PERMISSION.code)
  @Put('/inactive')
  @ApiOperation({
    tags: ['Customers'],
    summary: 'Cập nhật Customers Status Inactive',
    description: 'Cập nhật Customers Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async inactive(
    @Body() body: UpdateStatusCustomerRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.customerService.inactive(request);
  }

  @PermissionCode(DELETE_CUSTOMER_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Customers'],
    summary: 'Xóa Customers',
    description: 'Xóa Customers',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(@Param() param: DeleteCustomerRequestDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.customerService.delete(request);
  }
}
