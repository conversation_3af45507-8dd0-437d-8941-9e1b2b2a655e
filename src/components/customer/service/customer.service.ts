import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { CustomerEntity } from '../entities/customer.entity';
import { CreateCustomerRequestDto } from './../request/create-customer.request.dto';
import { DeleteCustomerRequestDto } from './../request/delete-customer.request.dto';
import { GetDetailCustomerRequestDto } from './../request/get-detail-customer.request.dto';
import { GetListCustomerRequestDto } from './../request/get-list-customer.request.dto';
import { UpdateCustomerRequestDto } from './../request/update-customer.request.dto';
import { UpdateStatusCustomerRequestDto } from './../request/update-status-customer.request.dto';

import { GetDetailCustomerResponseDto } from './../response/get-detail-customer.response.dto';
import { GetListCustomerResponseDto } from './../response/get-list-customer.response.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { BaseService } from '../../../common/service/base.service';
import { UserService } from '../../another-service/services/user-service';
import { MasterDataReferenceService } from '../../master-data-reference/service/master-data-reference.service';
import { CustomerRepository } from '../repository/customer.repository';

@Injectable()
export class CustomerService extends BaseService {
  private readonly logger = new Logger(CustomerService.name);

  constructor(
    private readonly customerRepository: CustomerRepository,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly masterReferenceService: MasterDataReferenceService,
  ) {
    super(userService);
  }

  async create(request: CreateCustomerRequestDto): Promise<any> {
    const existCode = await this.customerRepository.findOneByCode(request.code);
    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    const customerEntity = this.customerRepository.createEntity(request);
    const customer = await this.customerRepository.create(customerEntity);

    const response = plainToInstance(GetDetailCustomerResponseDto, customer);

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListCustomerRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.customerRepository.getList(request);

    const dataMapUser = await this.mapUserInfoToResponse(data);
    const response = plainToInstance(GetListCustomerResponseDto, dataMapUser);

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailCustomerRequestDto): Promise<any> {
    const customer = await this.customerRepository.getDetail(request);
    if (isEmpty(customer)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMapUser = await this.mapUserInfoToResponse([customer]);
    const response = plainToInstance(GetDetailCustomerResponseDto, dataMapUser);

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateCustomerRequestDto): Promise<any> {
    const { id } = request;
    const customer = await this.customerRepository.findOneById(id);

    if (isEmpty(customer)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate item in used
    const usedList = await this.validateCustomerInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.CUSTOMER_IN_USED'))
        .build();
    }

    // Validate Code unique
    const existCode = await this.customerRepository.findOneByCode(request.code);
    if (!isEmpty(existCode) && existCode.id !== customer.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    const dataUpdate = this.customerRepository.updateEntity(request, customer);
    const data = await this.customerRepository.update(dataUpdate);

    const response = plainToInstance(GetDetailCustomerResponseDto, data);

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteCustomerRequestDto): Promise<any> {
    const { id } = request;
    const customer = await this.customerRepository.findOneById(id);

    if (isEmpty(customer)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate item in used
    const usedList = await this.validateCustomerInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.CUSTOMER_IN_USED'))
        .build();
    }

    await this.customerRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(request: UpdateStatusCustomerRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.customerRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.customerRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(request: UpdateStatusCustomerRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.customerRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.customerRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async findOneById(id: number): Promise<CustomerEntity> {
    return await this.customerRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<CustomerEntity[]> {
    return await this.customerRepository.findAllByIds(ids);
  }

  async validateCustomerInUsed(id: number): Promise<boolean> {
    // Validate used in item-customers
    const itemList = await this.masterReferenceService.findAllItemByCustomerIds(
      [id],
    );
    if (!isEmpty(itemList)) {
      return true;
    }

    // Validate used in another service
    const isUse =
      await this.masterReferenceService.validateDataMasterUseAnotherService(
        new ValidateMasterRequestDto('customer_id', id),
      );
    if (isUse) {
      return true;
    }

    return false;
  }
}
