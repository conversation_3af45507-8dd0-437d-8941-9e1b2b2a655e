import { Column, Entity } from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';

@Entity({ name: 'customers' })
export class CustomerEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    unique: true,
    nullable: false,
    length: 255,
  })
  code: string;

  @Column({
    type: 'nvarchar',
    nullable: false,
    length: 255,
  })
  name: string;

  @Column({
    type: 'int',
    nullable: false,
  })
  country: number;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  address: string;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  representative: string;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  tel: string;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  email: string;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  note: string;

  @Column({
    type: 'tinyint',
  })
  status: number;
}
