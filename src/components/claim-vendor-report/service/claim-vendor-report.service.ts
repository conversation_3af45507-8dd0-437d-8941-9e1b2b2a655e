import { Injectable, Logger } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';

import { CreateClaimVendorReportRequestDto } from './../request/create-claim-vendor-report.request.dto';
import { GetListClaimVendorReportRequestDto } from './../request/get-list-claim-vendor-report.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { BaseService } from '../../../common/service/base.service';
import { QmsxReportService } from '../../another-service/services/qmsx-report-service';
import { UserService } from '../../another-service/services/user-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';

@Injectable()
export class ClaimVendorReportService extends BaseService {
  private readonly logger = new Logger(ClaimVendorReportService.name);

  constructor(
    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly qmsxReportService: QmsxReportService,

    private readonly baseService: BaseProcessService,
  ) {
    super(userService);
  }

  async create(request: CreateClaimVendorReportRequestDto): Promise<any> {
    const claimVendorReport =
      await this.qmsxReportService.createClaimVendorReport(request);

    return new ResponseBuilder(claimVendorReport)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getClaimVendorReport(
    request: GetListClaimVendorReportRequestDto,
  ): Promise<any> {
    const data = await this.qmsxReportService.getClaimVendorReport(request);

    const dataMap = await this.baseService.mapMasterInfoToResponse(
      data.claimVendorReport,
    );

    return new ResponseBuilder(dataMap)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
