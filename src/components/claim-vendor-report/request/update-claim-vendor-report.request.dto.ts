import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt } from 'class-validator';
import { CreateClaimVendorReportRequestDto } from './create-claim-vendor-report.request.dto';

export class UpdateClaimVendorReportRequestDto extends CreateClaimVendorReportRequestDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;
}
