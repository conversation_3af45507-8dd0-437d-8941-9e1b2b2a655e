import { Module } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { ClaimVendorReportController } from './controller/claim-vendor-report.controller';
import { ClaimVendorReportService } from './service/claim-vendor-report.service';

@Module({
  imports: [BaseProcessModule, AnotherServiceModule],
  providers: [ClaimVendorReportService],
  exports: [ClaimVendorReportService],
  controllers: [ClaimVendorReportController],
})
export class ClaimVendorReportModule {}
