import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import { REPORT_CLAIM_VENDOR_REPORT_PERMISSION } from '../../../utils/permissions/claim-vendor-report.permission';
import { GetListClaimVendorReportRequestDto } from '../request/get-list-claim-vendor-report.request.dto';
import { ClaimVendorReportService } from '../service/claim-vendor-report.service';

@Controller('claim-vendor-reports')
export class ClaimVendorReportController {
  constructor(
    private readonly claimVendorReportService: ClaimVendorReportService,
  ) {}

  @PermissionCode(REPORT_CLAIM_VENDOR_REPORT_PERMISSION.code)
  @Get('/report')
  @ApiOperation({
    tags: ['Claim-vendor-reports'],
    summary: 'Báo cáo Claim-vendor-reports',
    description: 'Báo cáo Claim-vendor-reports',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getClaimVendorReport(
    @Query() query: GetListClaimVendorReportRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.claimVendorReportService.getClaimVendorReport(request);
  }
}
