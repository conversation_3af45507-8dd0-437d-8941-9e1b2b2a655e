import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { InspectionEntity } from '../../inspection/entities/inspection.entity';

@Entity({ name: 'inspection_ref_errors' })
export class InspectionErrorEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  inspectionId: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  errorId: number;

  @ManyToOne(() => InspectionEntity, (s) => s.inspectionErrors, {
    orphanedRowAction: 'delete',
  })
  @JoinColumn({ name: 'inspection_id', referencedColumnName: 'id' })
  inspection: InspectionEntity;
}
