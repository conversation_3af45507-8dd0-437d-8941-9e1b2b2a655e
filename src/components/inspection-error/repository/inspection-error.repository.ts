import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { InspectionErrorEntity } from '../entities/inspection-error.entity';
import { CreateInspectionErrorRequestDto } from './../request/create-inspection-error.request.dto';

@Injectable()
export class InspectionErrorRepository extends BaseAbstractRepository<InspectionErrorEntity> {
  constructor(
    @InjectRepository(InspectionErrorEntity)
    private readonly inspectionErrorRepository: Repository<InspectionErrorEntity>,
  ) {
    super(inspectionErrorRepository);
  }

  createEntity(
    request: CreateInspectionErrorRequestDto,
  ): InspectionErrorEntity {
    const inspectionErrorEntity = new InspectionErrorEntity();
    inspectionErrorEntity.inspectionId = request.inspectionId;
    inspectionErrorEntity.errorId = request.errorId;

    return inspectionErrorEntity;
  }

  async findAllByInspectionIds(inspectionIds: number[]) {
    const query = this.inspectionErrorRepository
      .createQueryBuilder('inspectionreferror')
      .select([
        'inspectionreferror.inspection_id AS "inspectionId"',
        'error.id AS "errorId"',
        'error.code AS "errorCode"',
        'error.name AS "errorName"',
      ])
      .innerJoin('errors', 'error', 'inspectionreferror.error_id = error.id')
      .where('inspectionreferror.inspection_id IN (:...inspectionIds)', {
        inspectionIds,
      });

    const result = await query.getRawMany();
    return result;
  }
}
