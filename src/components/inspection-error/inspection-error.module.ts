import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InspectionErrorEntity } from './entities/inspection-error.entity';
import { InspectionErrorRepository } from './repository/inspection-error.repository';
import { InspectionErrorService } from './service/inspection-error.service';

@Module({
  imports: [TypeOrmModule.forFeature([InspectionErrorEntity])],
  providers: [InspectionErrorService, InspectionErrorRepository],
  exports: [InspectionErrorService],
  controllers: [],
})
export class InspectionErrorModule {}
