import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { DataSource, In } from 'typeorm';

import { CreateInspectionErrorRequestDto } from './../request/create-inspection-error.request.dto';

import { InspectionErrorRepository } from '../repository/inspection-error.repository';

@Injectable()
export class InspectionErrorService {
  private readonly logger = new Logger(InspectionErrorService.name);

  constructor(
    private readonly inspectionErrorRepository: InspectionErrorRepository,

    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,
  ) {}

  async create(request: CreateInspectionErrorRequestDto): Promise<any> {
    const inspectionErrorEntity =
      this.inspectionErrorRepository.createEntity(request);
    return await this.inspectionErrorRepository.create(inspectionErrorEntity);
  }

  async delete(inspectionIds: number[]): Promise<any> {
    const filterCondition = {
      inspectionId: In(inspectionIds),
    };
    return await this.inspectionErrorRepository.removeByCondition(
      filterCondition,
    );
  }

  async findErrorByInspectionIds(inspectionIds: number[]): Promise<any> {
    return await this.inspectionErrorRepository.findAllByInspectionIds(
      inspectionIds,
    );
  }

  async findAllByErrorIds(errorIds: number[]): Promise<any> {
    const filterCondition = {
      errorId: In(errorIds),
    };
    return await this.inspectionErrorRepository.findByCondition(
      filterCondition,
    );
  }
}
