import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { parseJSONV<PERSON>ueField } from '../../../utils/common';
import { AqlSampleLimitDetailEntity } from '../entities/aql-sample-limit-detail.entity';

@Injectable()
export class AqlSampleLimitDetailRepository extends BaseAbstractRepository<AqlSampleLimitDetailEntity> {
  constructor(
    @InjectRepository(AqlSampleLimitDetailEntity)
    private readonly aqlSampleLimitDetailRepository: Repository<AqlSampleLimitDetailEntity>,
  ) {
    super(aqlSampleLimitDetailRepository);
  }

  async findAllByAqlSampleLimitIds(aqlSampleLimitIds: number[]): Promise<any> {
    const query = this.aqlSampleLimitDetailRepository
      .createQueryBuilder('aqlsamplelimitdetail')
      .select([
        'DISTINCT aqlsamplelimitdetail.inspection_sample_quantity_id AS "inspectionSampleQuantityId"',
      ])
      .leftJoin(
        'inspection_sample_quantities',
        'isq',
        'aqlsamplelimitdetail.inspection_sample_quantity_id = isq.id',
      )
      .addSelect([
        `CASE WHEN COUNT(isq.id) = 0 THEN '{}' ELSE 
        (SELECT isq.id as id, isq.code as code , isq.inspection_sample_quantity as inspectionSampleQuantity FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "inspectionSampleQuantity"`,
      ])
      .where(
        'aqlsamplelimitdetail.aql_sample_limit_id IN (:...aqlSampleLimitIds)',
        {
          aqlSampleLimitIds,
        },
      )
      .groupBy('aqlsamplelimitdetail.inspection_sample_quantity_id')
      .addGroupBy('isq.id')
      .addGroupBy('isq.code')
      .addGroupBy('isq.inspection_sample_quantity');

    const result = await query.getRawMany();
    return result?.map((item) => {
      return {
        ...item,
        id: Number(item.id),
        aqlSampleLimitId: Number(item.aqlSampleLimitId),
        inspectionSampleQuantityId: Number(item.inspectionSampleQuantityId),
        inspectionSampleQuantity: parseJSONValueField(
          item.inspectionSampleQuantity,
        ),
      };
    });
  }
}
