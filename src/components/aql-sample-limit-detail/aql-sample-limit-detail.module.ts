import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AqlSampleLimitDetailEntity } from './entities/aql-sample-limit-detail.entity';
import { AqlSampleLimitDetailRepository } from './repository/aql-sample-limit-detail.repository';
import { AqlSampleLimitDetailService } from './service/aql-sample-limit-detail.service';

@Module({
  imports: [TypeOrmModule.forFeature([AqlSampleLimitDetailEntity])],
  providers: [AqlSampleLimitDetailService, AqlSampleLimitDetailRepository],
  exports: [AqlSampleLimitDetailService],
  controllers: [],
})
export class AqlSampleLimitDetailModule {}
