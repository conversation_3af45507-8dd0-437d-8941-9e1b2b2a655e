import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AqlSampleLimitEntity } from '../../aql-sample-limit/entities/aql-sample-limit.entity';

@Entity({ name: 'aql_sample_limit_details' })
export class AqlSampleLimitDetailEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  aqlSampleLimitId: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  inspectionSampleQuantityId: number;

  @ManyToOne(
    () => AqlSampleLimitEntity,
    (aqlSampleLimit) => aqlSampleLimit.dataDetails,
    {
      orphanedRowAction: 'delete',
    },
  )
  @JoinColumn({ name: 'aql_sample_limit_id', referencedColumnName: 'id' })
  aqlSampleLimit: AqlSampleLimitEntity;
}
