import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { DataSource, In } from 'typeorm';

import { AqlSampleLimitDetailEntity } from '../entities/aql-sample-limit-detail.entity';

import { AqlSampleLimitDetailRepository } from '../repository/aql-sample-limit-detail.repository';

@Injectable()
export class AqlSampleLimitDetailService {
  private readonly logger = new Logger(AqlSampleLimitDetailService.name);

  constructor(
    private readonly aqlSampleLimitDetailRepository: AqlSampleLimitDetailRepository,

    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,
  ) {}

  async findAllByAqlSampleLimitIds(aqlSampleLimitIds: number[]): Promise<any> {
    return await this.aqlSampleLimitDetailRepository.findAllByAqlSampleLimitIds(
      aqlSampleLimitIds,
    );
  }

  async findAllByInspectionSampleQuantityIds(
    inspectionSampleQuantityIds: number[],
  ): Promise<AqlSampleLimitDetailEntity[]> {
    const filterCondition = {
      inspectionSampleQuantityId: In(inspectionSampleQuantityIds),
    };
    return await this.aqlSampleLimitDetailRepository.findByCondition(
      filterCondition,
    );
  }
}
