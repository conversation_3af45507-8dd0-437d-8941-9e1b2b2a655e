import { Modu<PERSON> } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { ItemModule } from '../item/item.module';
import { ClaimUnitPriceController } from './controller/claim-unit-price.controller';
import { ClaimUnitPriceService } from './service/claim-unit-price.service';

@Module({
  imports: [AnotherServiceModule, ItemModule, BaseProcessModule],
  providers: [ClaimUnitPriceService],
  exports: [ClaimUnitPriceService],
  controllers: [ClaimUnitPriceController],
})
export class ClaimUnitPriceModule {}
