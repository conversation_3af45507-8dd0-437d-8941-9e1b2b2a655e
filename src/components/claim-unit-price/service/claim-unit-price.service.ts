import { Injectable } from '@nestjs/common';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ModuleRef } from '@nestjs/core';
import { StatusEnum } from '../../../common/enums/status.enum';
import { ErrorData } from '../../../common/errors/base.error';
import { IdParamDto } from '../../../core/dto/request/id-param.request.dto';
import { DateRangeDto } from '../../../utils/dto/request/date-range.request.dto';
import { CreateClaimUnitPriceRequest } from '../../another-service/request/claim-unit-price/create-claim-unit-price.request';
import { UpdateClaimUnitPriceRequest } from '../../another-service/request/claim-unit-price/update-claim-unit-price.request';
import { VendorService } from '../../another-service/services/vendor-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { ItemService } from '../../item/service/item.service';
import { CreateClaimUnitPriceRequestDto } from '../request/create-claim-unit-price.request.dto';
import { GetListClaimUnitPriceRequestDto } from '../request/get-list-claim-unit-price.request.dto';
import { UpdateClaimUnitPriceRequestDto } from '../request/update-claim-unit-price.request.dto';

@Injectable()
export class ClaimUnitPriceService {
  constructor(
    private readonly i18n: I18nService,

    protected readonly moduleRef: ModuleRef,

    private readonly vendorService: VendorService,

    private readonly baseService: BaseProcessService,

    private readonly itemService: ItemService,
  ) {}

  async create(request: CreateClaimUnitPriceRequestDto): Promise<any> {
    const { result, messageError } = await this.validateCreateUpdate(request);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }
    const requestCreate = await this.buildCreateClaimUnitPriceRequest(request);
    const claimUnitPrice = await this.vendorService.createClaimUnitPrice(
      requestCreate,
    );
    if (isEmpty(claimUnitPrice)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    return new ResponseBuilder(claimUnitPrice)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateClaimUnitPriceRequestDto): Promise<any> {
    const { result, messageError } = await this.validateCreateUpdate(request);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }
    const entity = await this.vendorService.getDetailClaimUnitPriceById({
      id: request.id,
    });
    if (isEmpty(entity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const updateRequest = await this.buildUpdateClaimUnitPriceRequest(request);
    const responseEntity = await this.vendorService.updateClaimUnitPrice(
      updateRequest,
    );
    if (isEmpty(responseEntity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }
    return new ResponseBuilder(responseEntity)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: IdParamDto): Promise<any> {
    const entity = await this.vendorService.getDetailClaimUnitPriceById(
      request,
    );
    if (isEmpty(entity)) {
      throw ErrorData.Validate.claimUnitPriceDoesNotExist(this.i18n);
    }
    const dataMapUser = await this.baseService.mapMasterInfoToResponse([
      entity,
    ]);
    const details = await this.baseService.mapMasterInfoToResponse(
      entity.claimUnitPriceDetail,
    );
    dataMapUser.forEach((s) => {
      s.claimUnitPriceDetail = details;
    });
    return new ResponseBuilder(dataMapUser[0] ? dataMapUser[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async approve(request: IdParamDto): Promise<any> {
    const { result, messageError } =
      await this.vendorService.approveClaimUnitPrice(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async reject(request: IdParamDto): Promise<any> {
    const { result, messageError } =
      await this.vendorService.rejectClaimUnitPrice(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: IdParamDto): Promise<any> {
    const { result, messageError } =
      await this.vendorService.deleteClaimUnitPrice(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async validateExistItemsByItemIds(listRequestItemId: number[]) {
    const listItemExist = await this.itemService.findAllByIds(
      listRequestItemId,
    );
    const listItemExistAndActive = listItemExist.map(
      (item) => item.status == StatusEnum.ACTIVE,
    );
    if (listItemExistAndActive.length != listRequestItemId.length) {
      throw ErrorData.Validate.itemDoesNotExist(this.i18n);
    }
  }

  async validateCreateUpdate(
    request: UpdateClaimUnitPriceRequestDto | CreateClaimUnitPriceRequestDto,
    id?: number,
  ) {
    const keyDup = ['lotNo', 'itemId', 'unitId', 'processId'];

    if (
      this.baseService.checkDuplicateByKey(request.claimUnitPriceDetail, keyDup)
    ) {
      return {
        result: false,
        messageError: 'error.ClAIM_UNIT_PRICE_HAS_DUPLICATE_KEY',
      };
    }
    const existByDateRange =
      await this.vendorService.getClaimUnitPriceByDateRange({
        fromDate: request.effectiveFromDate,
        toDate: request.effectiveToDate,
      });

    if (!id && !isEmpty(existByDateRange)) {
      throw ErrorData.Validate.claimUnitPriceExistByDateRange(
        this.i18n,
        request.effectiveFromDate,
        request.effectiveToDate,
      );
    }
    if (id && !isEmpty(existByDateRange) && id != existByDateRange.id) {
      throw ErrorData.Validate.claimUnitPriceExistByDateRange(
        this.i18n,
        request.effectiveFromDate,
        request.effectiveToDate,
      );
    }
    const { itemIds, unitIds, processIds } =
      request.claimUnitPriceDetail.reduce(
        (result, item) => {
          if (item.itemId !== null && item.itemId !== undefined) {
            result.itemIds.add(item.itemId);
          }
          if (item.processId !== null && item.processId !== undefined) {
            result.processIds.add(item.processId);
          }
          if (item.unitId !== null && item.unitId !== undefined) {
            result.unitIds.add(item.unitId);
          }
          return result;
        },
        {
          unitIds: new Set<number>(),
          itemIds: new Set<number>(),
          processIds: new Set<number>(),
        },
      );
    const departmentIds = new Set<number>();
    if (request.requestDepartmentId) {
      departmentIds.add(+request.requestDepartmentId);
    }

    if (request.priceProviderDepartmentId) {
      departmentIds.add(+request.priceProviderDepartmentId);
    }
    return await this.baseService.validateMaster({
      itemIds: Array.from(itemIds),
      unitIds: Array.from(unitIds),
      processIds: Array.from(processIds),
      departmentIds: Array.from(departmentIds),
    });
  }

  async buildCreateClaimUnitPriceRequest(
    request: CreateClaimUnitPriceRequestDto,
  ): Promise<CreateClaimUnitPriceRequest> {
    const createClaimUnitPriceRequest = new CreateClaimUnitPriceRequest();
    Object.assign(createClaimUnitPriceRequest, request);
    createClaimUnitPriceRequest.createdBy = request.userId;
    createClaimUnitPriceRequest.updatedBy = request.userId;
    return createClaimUnitPriceRequest;
  }

  async buildUpdateClaimUnitPriceRequest(
    request: UpdateClaimUnitPriceRequestDto,
  ): Promise<UpdateClaimUnitPriceRequest> {
    const updateEntity = new UpdateClaimUnitPriceRequest();
    Object.assign(updateEntity, request);
    updateEntity.id = request.id;
    updateEntity.updatedBy = request.userId;
    return updateEntity;
  }

  async getList(request: GetListClaimUnitPriceRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.vendorService.listClaimUnitPrice(
      request,
    );

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDataForClaimUnitPrice(request: DateRangeDto): Promise<any> {
    const data = await this.vendorService.getDataForClaimUnitPrice(request);
    if (isEmpty(data)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(await this.i18n.translate('success.SUCCESS'))
        .build();
    }
    const dataMapping = await this.baseService.mapMasterInfoToResponse(data);
    return new ResponseBuilder(dataMapping)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
