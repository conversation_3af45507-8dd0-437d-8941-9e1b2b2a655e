import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import { IdParamDto } from '../../../core/dto/request/id-param.request.dto';
import { DateRangeDto } from '../../../utils/dto/request/date-range.request.dto';
import {
  APPROVE_CLAIM_UNIT_PRICE_PERMISSION,
  CREATE_CLAIM_UNIT_PRICE_PERMISSION,
  DELETE_CLAIM_UNIT_PRICE_PERMISSION,
  DETAIL_CLAIM_UNIT_PRICE_PERMISSION,
  LIST_CLAIM_UNIT_PRICE_PERMISSION,
  REJECT_CLAIM_UNIT_PRICE_PERMISSION,
  UPDATE_CLAIM_UNIT_PRICE_PERMISSION,
} from '../../../utils/permissions/claim-unit-price.permission';
import { CreateClaimUnitPriceRequestDto } from '../request/create-claim-unit-price.request.dto';
import { GetListClaimUnitPriceRequestDto } from '../request/get-list-claim-unit-price.request.dto';
import { UpdateClaimUnitPriceRequestDto } from '../request/update-claim-unit-price.request.dto';
import { ClaimUnitPriceService } from '../service/claim-unit-price.service';

@Controller('claim-unit-prices')
export class ClaimUnitPriceController {
  constructor(private readonly claimUnitPriceService: ClaimUnitPriceService) {}

  @PermissionCode(CREATE_CLAIM_UNIT_PRICE_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Claim-unit-prices'],
    summary: 'Tạo Claim-unit-prices mới',
    description: 'Tạo Claim-unit-prices mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateClaimUnitPriceRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.claimUnitPriceService.create(request);
  }

  @PermissionCode(DETAIL_CLAIM_UNIT_PRICE_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Claim-unit-prices'],
    summary: 'Get detail',
    description: 'get detail',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(@Param() param: IdParamDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.claimUnitPriceService.getDetail(request);
  }

  @PermissionCode(UPDATE_CLAIM_UNIT_PRICE_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Claim-unit-prices'],
    summary: 'Update',
    description: 'Update',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public update(@Body() payload: UpdateClaimUnitPriceRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.claimUnitPriceService.update(request);
  }

  @PermissionCode(LIST_CLAIM_UNIT_PRICE_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Claim-unit-prices'],
    summary: 'List claim unit price',
    description: 'List claim unit price',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListClaimUnitPriceRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.claimUnitPriceService.getList(request);
  }

  @PermissionCode(APPROVE_CLAIM_UNIT_PRICE_PERMISSION.code)
  @Put('/:id/approve')
  @ApiOperation({
    tags: ['Claim-unit-prices'],
    summary: 'Xác nhận claim-unit-price',
    description: 'Xác nhận claim-unit-price',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async approve(@Param() param: IdParamDto): Promise<any> {
    const { request, responseError } = param;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.claimUnitPriceService.approve(request);
  }

  @PermissionCode(REJECT_CLAIM_UNIT_PRICE_PERMISSION.code)
  @Put('/:id/reject')
  @ApiOperation({
    tags: ['Claim-unit-prices'],
    summary: 'Từ chối Claim-unit-prices',
    description: 'Từ chối Claim-unit-prices',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async reject(@Param() param: IdParamDto): Promise<any> {
    const { request, responseError } = param;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.claimUnitPriceService.reject(request);
  }

  @PermissionCode(DELETE_CLAIM_UNIT_PRICE_PERMISSION.code)
  @Delete(':id')
  @ApiOperation({
    tags: ['Claim-unit-prices'],
    summary: 'Xoá Claim-unit-prices',
    description: 'Xoá Claim-unit-prices',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async delete(@Param() param: IdParamDto): Promise<any> {
    const { request, responseError } = param;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.claimUnitPriceService.delete(request);
  }

  @PermissionCode(CREATE_CLAIM_UNIT_PRICE_PERMISSION.code)
  @Post('/get-data-for-claim-unit-price')
  @ApiOperation({
    tags: ['Claim-unit-prices'],
    summary: 'Get List Ticket Claim Detail For Claim Unit Price',
    description: 'Get List Ticket Claim Detail For Claim Unit Price',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getDataForClaimUnitPrice(@Body() payload: DateRangeDto): Promise<any> {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.claimUnitPriceService.getDataForClaimUnitPrice(request);
  }
}
