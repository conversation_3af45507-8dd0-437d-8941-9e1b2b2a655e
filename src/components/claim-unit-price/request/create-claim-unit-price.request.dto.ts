import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsDateString,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { FileDto } from '../../../utils/dto/request/file.request.dto';
import { CreateClaimUnitPriceDetailRequestDto } from './create-claim-unit-price-detail.request.dto';

export class CreateClaimUnitPriceRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  requestDepartmentId: number;

  @ApiProperty()
  @IsOptional()
  priceProviderDepartmentId: number;

  @ApiProperty({ example: '2024-10-15' })
  @IsNotEmpty()
  @IsDateString()
  aggregationFromDate: Date;

  @ApiProperty({ example: '2024-11-16' })
  @IsNotEmpty()
  @IsDateString()
  aggregationToDate: Date;

  @ApiProperty({ example: '2024-10-15' })
  @IsNotEmpty()
  @IsDateString()
  effectiveFromDate: Date;

  @ApiProperty({ example: '2024-11-16' })
  @IsNotEmpty()
  @IsDateString()
  effectiveToDate: Date;

  @ApiPropertyOptional()
  @IsOptional()
  description: string;

  @ApiPropertyOptional({ type: [FileDto] })
  @IsArray()
  @IsOptional()
  @Type(() => FileDto)
  files: FileDto[];

  @ApiProperty({ type: [CreateClaimUnitPriceDetailRequestDto] })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CreateClaimUnitPriceDetailRequestDto)
  claimUnitPriceDetail: CreateClaimUnitPriceDetailRequestDto[];
}
