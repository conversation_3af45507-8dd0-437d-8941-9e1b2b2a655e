import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateClaimUnitPriceRequestDto } from './create-claim-unit-price.request.dto';

export class UpdateClaimUnitPriceRequestDto extends CreateClaimUnitPriceRequestDto {
  @ApiPropertyOptional({ example: 1 })
  @IsInt()
  @IsOptional()
  @Transform(({ value }) => +value)
  id: number;
}
