import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  APPROVE_CHEMICAL_EXPORT_TICKET_PERMISSION,
  CANCEL_CHEMICAL_EXPORT_TICKET_PERMISSION,
  CREATE_CHEMICAL_EXPORT_TICKET_PERMISSION,
  DELETE_CHEMICAL_EXPORT_TICKET_PERMISSION,
  DETAIL_CHEMICAL_EXPORT_TICKET_PERMISSION,
  LIST_CHEMICAL_EXPORT_TICKET_PERMISSION,
  UPDATE_CHEMICAL_EXPORT_TICKET_PERMISSION,
} from '../../../utils/permissions/chemical-export-ticket.permission';
import { CreateChemicalExportTicketFormDto } from '../request/create-chemical-export-ticket-form.request.dto';
import { DeleteChemicalExportTicketRequestDto } from '../request/delete-chemical-export-ticket.request.dto';
import { GetDetailChemicalExportTicketRequestDto } from '../request/get-detail-chemical-export-ticket.request.dto';
import { GetListChemicalExportTicketRequestDto } from '../request/get-list-chemical-export-ticket.request.dto';
import { UpdateChemicalExportTicketFormRequestDto } from '../request/update-chemical-export-ticket-form.request.dto';
import { ChemicalExportTicketService } from '../service/chemical-export-ticket.service';

@Controller('chemical-export-tickets')
export class ChemicalExportTicketController {
  constructor(
    private readonly chemicalExportTicketService: ChemicalExportTicketService,
  ) {}

  @PermissionCode(DETAIL_CHEMICAL_EXPORT_TICKET_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['chemical-export-tickets'],
    summary: 'Chi tiết chemical-export-tickets',
    description: 'Chi tiết chemical-export-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailChemicalExportTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalExportTicketService.getDetail(request);
  }

  @PermissionCode(LIST_CHEMICAL_EXPORT_TICKET_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['chemical-export-tickets'],
    summary: 'Danh sách chemical-export-tickets',
    description: 'Danh sách chemical-export-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListChemicalExportTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.chemicalExportTicketService.getList(request);
  }

  @PermissionCode(UPDATE_CHEMICAL_EXPORT_TICKET_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['chemical-export-tickets'],
    summary: 'Cập nhật chemical-export-tickets',
    description: 'Cập nhật chemical-export-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateChemicalExportTicketFormRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalExportTicketService.update(request);
  }

  @PermissionCode(CREATE_CHEMICAL_EXPORT_TICKET_PERMISSION.code)
  @Post('create-ticket')
  @ApiOperation({
    tags: ['chemical-export-tickets'],
    summary: 'Chi tiết chemical-export-tickets',
    description: 'Chi tiết chemical-export-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async create(
    @Body() param: CreateChemicalExportTicketFormDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalExportTicketService.create(request);
  }

  @PermissionCode(APPROVE_CHEMICAL_EXPORT_TICKET_PERMISSION.code)
  @Put('/approved')
  @ApiOperation({
    tags: ['chemical-export-tickets'],
    summary: 'Approved chemical-export-tickets',
    description: 'Approved chemical-export-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async approve(
    @Body() body: GetDetailChemicalExportTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalExportTicketService.approve(request);
  }

  @PermissionCode(CANCEL_CHEMICAL_EXPORT_TICKET_PERMISSION.code)
  @Put('/canceled')
  @ApiOperation({
    tags: ['chemical-export-tickets'],
    summary: 'Canceled chemical-export-tickets',
    description: 'Canceled chemical-export-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async canceled(
    @Body() body: GetDetailChemicalExportTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalExportTicketService.cancel(request);
  }

  @PermissionCode(DELETE_CHEMICAL_EXPORT_TICKET_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['chemical-export-tickets'],
    summary: 'Xóa chemical-export-tickets',
    description: 'Xóa chemical-export-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(
    @Param() param: DeleteChemicalExportTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.chemicalExportTicketService.delete(request);
  }
}
