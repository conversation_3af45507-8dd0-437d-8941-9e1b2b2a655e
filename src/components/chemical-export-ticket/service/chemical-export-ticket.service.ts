import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';

import { GetDetailChemicalExportTicketRequestDto } from '../request/get-detail-chemical-export-ticket.request.dto';
import { GetListChemicalExportTicketRequestDto } from '../request/get-list-chemical-export-ticket.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';

import { PaginationResponse } from '../../../utils/dto/response/pagination.response';
import { QmsxTicketService } from '../../another-service/services/qmsx-ticket-service';
import { UserService } from '../../another-service/services/user-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { CreateChemicalExportTicketFormDto } from '../request/create-chemical-export-ticket-form.request.dto';
import { CreateChemicalExportTicketRequestDto } from '../request/create-chemical-export-ticket.request.dto';
import { DeleteChemicalExportTicketRequestDto } from '../request/delete-chemical-export-ticket.request.dto';
import { GetChemicalInventoryQuantityRequestDto } from '../request/get-chemical-inventory-quantity.request.dto';
import { UpdateChemicalExportTicketFormRequestDto } from '../request/update-chemical-export-ticket-form.request.dto';
import { UpdateChemicalExportTicketDto } from '../request/update-chemical-export-ticket.dto';

@Injectable()
export class ChemicalExportTicketService {
  private readonly logger = new Logger(ChemicalExportTicketService.name);

  constructor(
    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly qmsxTicketService: QmsxTicketService,

    private readonly baseService: BaseProcessService,
  ) {}

  async getList(request: GetListChemicalExportTicketRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } =
      await this.qmsxTicketService.getListChemicalExportTicket(request);

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(
    request: GetDetailChemicalExportTicketRequestDto,
  ): Promise<any> {
    const chemicalExportTicket =
      await this.qmsxTicketService.getDetailChemicalExportTicketById(request);

    if (isEmpty(chemicalExportTicket)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const dataMapUser = await this.baseService.mapMasterInfoToResponse([
      chemicalExportTicket,
    ]);
    const details = await this.baseService.mapMasterInfoToResponse(
      chemicalExportTicket.chemicalExportTicketDetails,
    );
    dataMapUser.forEach((s) => {
      s.chemicalExportTicketDetails = details;
    });
    return new ResponseBuilder(dataMapUser[0] ? dataMapUser[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(
    request: UpdateChemicalExportTicketFormRequestDto,
  ): Promise<any> {
    const { id, data } = request;

    const chemicalExportTicket =
      await this.qmsxTicketService.getDetailChemicalExportTicketById({
        id: id,
      });

    if (isEmpty(chemicalExportTicket)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const { result, messageError } = await this.validateExportTicket(data);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }

    const chemicalExportTicketUpdate = plainToInstance(
      UpdateChemicalExportTicketDto,
      data,
    );
    chemicalExportTicketUpdate.id = id;
    request.updatedBy = request.userId;
    request.data = chemicalExportTicketUpdate;

    const response = await this.qmsxTicketService.updateChemicalExportTicket(
      request,
    );

    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }
    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async validateExportTicket(data: any) {
    if (
      this.baseService.checkDuplicateByKey(data.ticketDetails, ['chemicalId'])
    ) {
      return {
        result: false,
        messageError: 'error.CHEMICAL_EXPORT_TICKET_DETAIL_DUPLICATE_KEY',
      };
    }

    const { chemicalIds } = data.ticketDetails.reduce(
      (result, item) => {
        if (item.chemicalId !== null && item.chemicalId !== undefined) {
          result.chemicalIds.add(item.chemicalId);
        }
        return result;
      },
      {
        chemicalIds: new Set<number>(),
      },
    );

    return await this.baseService.validateMaster({
      chemicalIds: Array.from(chemicalIds),
    });
  }

  async create(request: CreateChemicalExportTicketFormDto): Promise<any> {
    const { data } = request;

    const { result, messageError } = await this.validateExportTicket(data);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }
    const chemicalExportTicket = plainToInstance(
      CreateChemicalExportTicketRequestDto,
      data,
    );
    chemicalExportTicket.createdBy = request.userId;
    const response = await this.qmsxTicketService.createChemicalExportTicket(
      chemicalExportTicket,
    );

    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async approve(
    request: GetDetailChemicalExportTicketRequestDto,
  ): Promise<any> {
    // check SL xuất kho <= Tồn cuối kỳ
    // todo

    const chemicalExportTicket =
      await this.qmsxTicketService.getDetailChemicalExportTicketById(request);

    if (isEmpty(chemicalExportTicket)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const chemicalIds = chemicalExportTicket.chemicalExportTicketDetails.map(
      (ticket) => ticket.chemicalId,
    );

    const requestNew = new GetChemicalInventoryQuantityRequestDto();
    requestNew.chemicalIds = chemicalIds;
    const inventoryQuantitys =
      await this.qmsxTicketService.getInventoryQuantity(requestNew);

    const errorArray = [];
    for (const chemicalExportTicketDetail of chemicalExportTicket.chemicalExportTicketDetails) {
      const item = inventoryQuantitys.find(
        (item) => item.chemicalId === chemicalExportTicketDetail.chemicalId,
      );
      if (
        item &&
        chemicalExportTicketDetail.exportQuantity > item.inventoryQuantity
      ) {
        const response = await this.baseService.mapMasterInfoToResponse([
          {
            chemicalId: chemicalExportTicketDetail.chemicalId,
          },
        ]);
        errorArray.push(
          await this.i18n.translate(
            'error.ERR_EXPORT_QUANTITY_EXCEEDS_INVENTORY',
            {
              args: {
                chemicalName: response[0].chemical.name,
                quantity: item.inventoryQuantity,
                unit: response[0].chemical.unit.name,
              },
            },
          ),
        );
      }
    }
    if (errorArray.length > 0) {
      return new ResponseBuilder(errorArray)
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .build();
    }

    const { result, messageError } =
      await this.qmsxTicketService.approveChemicalExportTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async cancel(request: GetDetailChemicalExportTicketRequestDto): Promise<any> {
    const { result, messageError } =
      await this.qmsxTicketService.cancelChemicalExportTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteChemicalExportTicketRequestDto): Promise<any> {
    const { result, messageError } =
      await this.qmsxTicketService.deleteChemicalExportTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
