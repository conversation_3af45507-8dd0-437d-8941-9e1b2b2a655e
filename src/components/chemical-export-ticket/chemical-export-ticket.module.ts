import { Module } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { ChemicalExportTicketController } from './controller/chemical-export-ticket.controller';
import { ChemicalExportTicketService } from './service/chemical-export-ticket.service';

@Module({
  imports: [AnotherServiceModule, BaseProcessModule],
  providers: [ChemicalExportTicketService],
  exports: [ChemicalExportTicketService],
  controllers: [ChemicalExportTicketController],
})
export class ChemicalExportTicketModule {}
