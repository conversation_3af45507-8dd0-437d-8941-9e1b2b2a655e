import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';

export class CreateChemicalExportTicketDetailRequestDto {
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  chemicalExportTicketId: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  chemicalId: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  exportQuantity: number;

  @ApiProperty()
  @IsOptional()
  note: string;
}
