import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsDateString,
  IsInt,
  IsNumber,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { FileRequestDto } from '../../common/request/file.request.dto';
import { CreateChemicalExportTicketDetailRequestDto } from './create-chemical-export-ticket-detail.request.dto';

export class CreateChemicalExportTicketRequestDto extends BaseDto {
  @ApiProperty()
  @IsOptional()
  @IsDateString()
  exportedDate: Date;

  @IsNumber()
  @ApiProperty()
  @IsOptional()
  exportedBy: number;

  @ApiProperty()
  @IsOptional()
  note: string;

  @IsNumber()
  @ApiProperty()
  @IsOptional()
  status: number;

  @ApiProperty({ type: [FileRequestDto] })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FileRequestDto)
  files: FileRequestDto[];

  @IsOptional()
  @IsInt()
  createdBy: number;

  @ApiProperty({ type: [CreateChemicalExportTicketDetailRequestDto] })
  @IsArray()
  @Type(() => CreateChemicalExportTicketDetailRequestDto)
  @ValidateNested({ each: true })
  ticketDetails: CreateChemicalExportTicketDetailRequestDto[];
}
