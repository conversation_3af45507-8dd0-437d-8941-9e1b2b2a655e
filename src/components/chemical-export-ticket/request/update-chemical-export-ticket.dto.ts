import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateChemicalExportTicketRequestDto } from './create-chemical-export-ticket.request.dto';

export class UpdateChemicalExportTicketDto extends CreateChemicalExportTicketRequestDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  updatedBy?: number;
}
