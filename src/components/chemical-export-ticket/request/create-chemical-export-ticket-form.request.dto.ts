import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';
import { CreateChemicalExportTicketRequestDto } from './create-chemical-export-ticket.request.dto';

export class CreateChemicalExportTicketFormDto extends BaseDto {
  @ApiProperty({})
  @ValidateNested({ each: true })
  @Type(() => CreateChemicalExportTicketRequestDto)
  data: CreateChemicalExportTicketRequestDto;
}
