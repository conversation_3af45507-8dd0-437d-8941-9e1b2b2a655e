import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateChemicalExportTicketFormDto } from './create-chemical-export-ticket-form.request.dto';
export class UpdateChemicalExportTicketFormRequestDto extends CreateChemicalExportTicketFormDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;

  @Transform(({ value }) => Number(value))
  @IsInt()
  @IsOptional()
  updatedBy?: number;
}
