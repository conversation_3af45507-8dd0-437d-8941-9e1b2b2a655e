import { Body, Controller, Get, Param, Post, Put, Query } from '@nestjs/common';

import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';
import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  APPROVE_CHEMICAL_ANALYSIS_TICKET_PERMISSION,
  CANCEL_CHEMICAL_ANALYSIS_TICKET_PERMISSION,
  CREATE_CHEMICAL_ANALYSIS_TICKET_PERMISSION,
  DETAIL_CHEMICAL_ANALYSIS_TICKET_PERMISSION,
  LIST_CHEMICAL_ANALYSIS_TICKET_PERMISSION,
  UPDATE_CHEMICAL_ANALYSIS_TICKET_PERMISSION,
} from '../../../utils/permissions/chemical-analysis-ticket.permission';
import { CreateChemicalAnalysisTicketRequestDto } from '../request/create-chemical-analysis-ticket.request.dto';
import { CreateManyMeasurementDetailRequestDto } from '../request/create-many-measurement-detail.request.dto';
import { GetDetailChemicalAnalysisTicketRequestDto } from '../request/get-detail-chemical-analysis-ticket.request.dto';
import { GetListChemicalAnalysisTicketRequestDto } from '../request/get-list-chemical-analysis-ticket.request.dto';
import { UpdateMeasurementDetailRequestDto } from '../request/update-measurement-detail.request.dto';
import { UpdateStatusChemicalAnalysisTicketRequestDto } from '../request/update-status-chemical-analysis-ticket.request.dto';
import { ChemicalAnalysisTicketService } from '../service/chemical-analysis-ticket.service';

@Controller('chemical-analysis-tickets')
export class ChemicalAnalysisTicketController {
  constructor(
    private readonly chemicalAnalysisTicketService: ChemicalAnalysisTicketService,
  ) {}

  @PermissionCode(DETAIL_CHEMICAL_ANALYSIS_TICKET_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Chemical-Analysis-Ticket'],
    summary: 'Chi tiết Chemical-Analysis-Ticket',
    description: 'Chi tiết Chemical-Analysis-Ticket',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailChemicalAnalysisTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && responseError?.statusCode && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalAnalysisTicketService.getDetail(request);
  }

  @PermissionCode(LIST_CHEMICAL_ANALYSIS_TICKET_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Chemical-Analysis-Ticket'],
    summary: 'Danh sách Chemical-Analysis-Ticket',
    description: 'Danh sách Chemical-Analysis-Ticket',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListChemicalAnalysisTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && responseError?.statusCode && !isEmpty(responseError)) {
      return responseError;
    }

    return this.chemicalAnalysisTicketService.getList(request);
  }

  @PermissionCode(CREATE_CHEMICAL_ANALYSIS_TICKET_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Chemical-Analysis-Ticket'],
    summary: 'Tạo Chemical-Analysis-Ticket mới',
    description: 'Tạo Chemical-Analysis-Ticket mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateChemicalAnalysisTicketRequestDto) {
    const { request, responseError } = payload;

    if (responseError && responseError?.statusCode && !isEmpty(responseError)) {
      return responseError;
    }

    return this.chemicalAnalysisTicketService.create(request);
  }

  //@PermissionCode(UPDATE_CHEMICAL_ANALYSIS_TICKET_PERMISSION.code)
  // @Put()
  // @ApiOperation({
  //   tags: ['Chemical-Analysis-Ticket'],
  //   summary: 'Cập nhật Chemical-Analysis-Ticket',
  //   description: 'Cập nhật Chemical-Analysis-Ticket',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Thành công',
  // })
  // public async update(
  //   @Body() body: UpdateChemicalAnalysisTicketRequestDto,
  // ): Promise<any> {
  //   const { request, responseError } = body;

  //   if (responseError && responseError?.statusCode && !isEmpty(responseError)) {
  //     return responseError;
  //   }

  //   return await this.chemicalAnalysisTicketService.update(request);
  // }

  @PermissionCode(UPDATE_CHEMICAL_ANALYSIS_TICKET_PERMISSION.code)
  @Post('/update-measurement-detail')
  @ApiOperation({
    tags: ['Chemical-Analysis-Ticket'],
    summary: 'update Measurement detail',
    description: 'update Measurement detail',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public updateMeasurementDetail(
    @Body() payload: UpdateMeasurementDetailRequestDto,
  ) {
    const { request, responseError } = payload;

    if (responseError && responseError?.statusCode && !isEmpty(responseError)) {
      return responseError;
    }

    return this.chemicalAnalysisTicketService.updateMeasurementDetail(request);
  }

  @PermissionCode(CREATE_CHEMICAL_ANALYSIS_TICKET_PERMISSION.code)
  @Post('/create-many-measurement-detail')
  @ApiOperation({
    tags: ['Chemical-Analysis-Ticket'],
    summary: 'create many Measurement detail',
    description: 'create many Measurement detail',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public createManyMeasurementDetail(
    @Body() payload: CreateManyMeasurementDetailRequestDto,
  ) {
    const { request, responseError } = payload;

    if (responseError && responseError?.statusCode && !isEmpty(responseError)) {
      return responseError;
    }
    return this.chemicalAnalysisTicketService.createManyMeasurementDetail(
      request,
    );
  }

  @PermissionCode(APPROVE_CHEMICAL_ANALYSIS_TICKET_PERMISSION.code)
  @Put('/approved')
  @ApiOperation({
    tags: ['Chemical-Analysis-Ticket'],
    summary: 'Approved Chemical-Analysis-Ticket',
    description: 'Approved Chemical-Analysis-Ticket',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async approve(
    @Body() body: UpdateStatusChemicalAnalysisTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && responseError?.statusCode && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalAnalysisTicketService.approve(request);
  }

  @PermissionCode(CANCEL_CHEMICAL_ANALYSIS_TICKET_PERMISSION.code)
  @Put('/cancel')
  @ApiOperation({
    tags: ['Chemical-Analysis-Ticket'],
    summary: 'cancel Chemical-Analysis-Ticket',
    description: 'cancel Chemical-Analysis-Ticket',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async cancel(
    @Body() body: UpdateStatusChemicalAnalysisTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && responseError?.statusCode && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalAnalysisTicketService.cancel(request);
  }

  // @Put('/generate')
  // @ApiOperation({
  //   tags: ['Chemical-Analysis-Ticket'],
  //   summary: 'Generate Chemical-tcrs',
  //   description: 'Generate Chemical-tcrs',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Thành công',
  // })
  // public async generateChemicalTcrs(
  //   @Body() body: GenerateChemicalTcrsRequestDto,
  // ): Promise<any> {
  //   const { request, responseError } = body;

  //   if (responseError && !isEmpty(responseError)) {
  //     return responseError;
  //   }

  //   return await this.chemicalAnalysisTicketService.generateChemicalTcrs(
  //     request,
  //   );
  // }
}
