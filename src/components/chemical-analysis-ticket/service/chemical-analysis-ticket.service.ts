import { Injectable, Logger } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { isEmpty } from 'lodash';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';
import { ResponseBuilder } from '../../../utils/response-builder';
import { QmsxTcrsService } from '../../another-service/services/qmsx-tcrs-service';
import { QmsxTicketService } from '../../another-service/services/qmsx-ticket-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { ChemicalTcrsStatusEnum } from '../../chemical-tcrs/chemical-tcrs-status.enum';
import { CreateChemicalTcrsRequestDto } from '../../chemical-tcrs/request/create-chemical-tcrs.request.dto';
import { MasterDataReferenceService } from '../../master-data-reference/service/master-data-reference.service';
import { StatusEnum, TcrsType } from '../chemical-analysis-ticket-status.enum';
import { CreateChemicalAnalysisTicketRequestDto } from '../request/create-chemical-analysis-ticket.request.dto';
import { CreateManyMeasurementDetailRequestDto } from '../request/create-many-measurement-detail.request.dto';
import { GetDetailChemicalAnalysisTicketRequestDto } from '../request/get-detail-chemical-analysis-ticket.request.dto';
import { GetListChemicalAnalysisTicketRequestDto } from '../request/get-list-chemical-analysis-ticket.request.dto';
import { UpdateChemicalAnalysisTicketRequestDto } from '../request/update-chemical-analysis-ticket.request.dto';
import { UpdateMeasurementDetailRequestDto } from '../request/update-measurement-detail.request.dto';
import { UpdateStatusChemicalAnalysisTicketRequestDto } from '../request/update-status-chemical-analysis-ticket.request.dto';

@Injectable()
export class ChemicalAnalysisTicketService {
  private readonly logger = new Logger(ChemicalAnalysisTicketService.name);

  constructor(
    private readonly i18n: I18nService,
    private readonly baseService: BaseProcessService,
    protected readonly qmsxTicketService: QmsxTicketService,
    protected readonly qmsxTcrsService: QmsxTcrsService,
    private readonly masterDataReferenceService: MasterDataReferenceService,
  ) {}

  async create(request: CreateChemicalAnalysisTicketRequestDto): Promise<any> {
    const { processId, deviceTypeId, deviceId, workshift, measurementDate } =
      request;
    const chemicalAnalysisTicket =
      await this.qmsxTicketService.getDetailChemicalAnalysisTicketByKey({
        processId,
        deviceTypeId,
        deviceId,
        workshift,
        measurementDate,
      });
    if (!isEmpty(chemicalAnalysisTicket)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate(
            'error.CHEMICAL_ANALYSIS_TICKET_EXISTS_IN_DATA_BASE',
          ),
        )
        .build();
    }

    request.status = StatusEnum.IN_PROGRESS;
    request.createdBy = request.userId;
    const response = await this.qmsxTicketService.createChemicalAnalysisTicket(
      request,
    );
    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }
    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('error.SUCCESS'))
      .build();
  }

  async updateMeasurementDetail(
    request: UpdateMeasurementDetailRequestDto,
  ): Promise<any> {
    const {
      id,
      chemicalAnalysisTicketDetailId,
      index,
      measurementValue,
      processChemicalDetailId,
    } = request;
    const measurementDetail =
      await this.qmsxTicketService.getMeasurementDetailByKey({
        chemicalAnalysisTicketDetailId,
        index,
      });
    if (!measurementDetail || measurementDetail.id !== id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }
    const chemicalAnalysisTicket =
      await this.qmsxTicketService.getDetailByMeasurementDetailId({
        measurementDetailId: measurementDetail.id,
      });
    if (isEmpty(chemicalAnalysisTicket)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    // check and create chemical tcrs
    let chemicalTcrsId;
    const processChemical =
      await this.masterDataReferenceService.findProcessChemicalByKey(
        chemicalAnalysisTicket.processId,
        chemicalAnalysisTicket.deviceTypeId,
        chemicalAnalysisTicket.deviceId,
      );
    if (isEmpty(processChemical)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const processChemicalDetail = processChemical.processChemicalDetails.find(
      (item) => item.id === processChemicalDetailId,
    );

    // delete chemical tcrs exist
    const chemicalTcrs = await this.qmsxTcrsService.getDetailChemicalTcrsByKey({
      measurementDate: request.measurementDate,
      processId: request.processId,
      deviceTypeId: request.deviceTypeId,
      deviceId: request.deviceId,
      workshift: request.workshift,
      chemicalId: processChemicalDetail.chemicalId,
      measurementIndex: request.index,
      processChemicalDetailId: processChemicalDetailId,
    });

    if (!isEmpty(chemicalTcrs)) {
      // delete tcrs
      const { result, messageError } =
        await this.qmsxTcrsService.deleteChemicalTcrs({
          id: chemicalTcrs.id,
        });
      if (!result) {
        return new ResponseBuilder()
          .withCode(ResponseCodeEnum.BAD_REQUEST)
          .withMessage(messageError)
          .build();
      }
    }

    const { standardValue, toLsl, toUsl } = processChemicalDetail;
    const LSL = standardValue - toLsl;
    const USL = standardValue + toUsl;

    if (measurementValue > USL || measurementValue < LSL) {
      // NG Case
      const chemicalTcrsNew = new CreateChemicalTcrsRequestDto();
      chemicalTcrsNew.tcrsType = TcrsType.NG;
      chemicalTcrsNew.measurementDate = request.measurementDate;
      chemicalTcrsNew.workshift = request.workshift;
      chemicalTcrsNew.processId = request.processId;
      chemicalTcrsNew.deviceTypeId = request.deviceTypeId;
      chemicalTcrsNew.deviceId = request.deviceId;
      chemicalTcrsNew.managementCategoryName =
        processChemicalDetail.managementCategoryName;
      chemicalTcrsNew.chemicalId = processChemicalDetail.chemicalId;
      chemicalTcrsNew.scope = processChemicalDetail.scope;
      chemicalTcrsNew.managementType = processChemicalDetail.managementType;
      chemicalTcrsNew.measurementTime = request.measurementTime;
      chemicalTcrsNew.measurementValue = measurementValue;
      chemicalTcrsNew.adjustmentValue = request.adjustmentValue;
      chemicalTcrsNew.pm = request.pm;
      chemicalTcrsNew.measurementIndex = index;
      chemicalTcrsNew.createdBy = request.userId;
      chemicalTcrsNew.status = ChemicalTcrsStatusEnum.PENDING;
      chemicalTcrsNew.processChemicalDetailId = processChemicalDetailId;
      const response = await this.qmsxTcrsService.createChemicalTcrs(
        chemicalTcrsNew,
      );
      if (isEmpty(response)) {
        return new ResponseBuilder()
          .withCode(ResponseCodeEnum.BAD_REQUEST)
          .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
          .build();
      }
      chemicalTcrsId = response.id;
    }

    // update
    const updateRequest = new UpdateMeasurementDetailRequestDto();
    Object.assign(updateRequest, request);
    updateRequest.id = measurementDetail.id;
    updateRequest.updatedBy = request.userId;
    const response = await this.qmsxTicketService.updateMeasurementDetail(
      updateRequest,
    );
    if (isEmpty(response)) {
      await this.qmsxTcrsService.deleteChemicalTcrs({
        id: chemicalTcrsId,
      });
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }
    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('error.SUCCESS'))
      .build();
  }

  async createManyMeasurementDetail(
    request: CreateManyMeasurementDetailRequestDto,
  ): Promise<any> {
    const measurementDetails =
      await this.qmsxTicketService.createManyMeasurementDetail(request);
    if (isEmpty(measurementDetails)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    for (const itemRequest of request.measurementDetails) {
      const processChemical =
        await this.masterDataReferenceService.findProcessChemicalByKey(
          itemRequest.processId,
          itemRequest.deviceTypeId,
          itemRequest.deviceId,
        );

      if (isEmpty(processChemical)) {
        return new ResponseBuilder()
          .withCode(ResponseCodeEnum.BAD_REQUEST)
          .withMessage(await this.i18n.translate('error.NOT_FOUND'))
          .build();
      }

      const processChemicalDetail = processChemical.processChemicalDetails.find(
        (item) => item.id === itemRequest.processChemicalDetailId,
      );

      const { standardValue, toLsl, toUsl } = processChemicalDetail;
      const LSL = standardValue - toLsl;
      const USL = standardValue + toUsl;

      if (
        itemRequest.measurementValue > USL ||
        itemRequest.measurementValue < LSL
      ) {
        // NG Case
        const chemicalTcrsNew = new CreateChemicalTcrsRequestDto();
        chemicalTcrsNew.tcrsType = TcrsType.NG;
        chemicalTcrsNew.measurementDate = itemRequest.measurementDate;
        chemicalTcrsNew.workshift = itemRequest.workshift;
        chemicalTcrsNew.processId = itemRequest.processId;
        chemicalTcrsNew.deviceTypeId = itemRequest.deviceTypeId;
        chemicalTcrsNew.deviceId = itemRequest.deviceId;
        chemicalTcrsNew.managementCategoryName =
          processChemicalDetail.managementCategoryName;
        chemicalTcrsNew.chemicalId = processChemicalDetail.chemicalId;
        chemicalTcrsNew.scope = processChemicalDetail.scope;
        chemicalTcrsNew.managementType = processChemicalDetail.managementType;
        chemicalTcrsNew.measurementTime = itemRequest.measurementTime;
        chemicalTcrsNew.measurementValue = itemRequest.measurementValue;
        chemicalTcrsNew.adjustmentValue = itemRequest.adjustmentValue;
        chemicalTcrsNew.pm = itemRequest.pm;
        chemicalTcrsNew.measurementIndex = itemRequest.index;
        chemicalTcrsNew.createdBy = request.userId;
        chemicalTcrsNew.status = ChemicalTcrsStatusEnum.PENDING;
        chemicalTcrsNew.processChemicalDetailId =
          itemRequest.processChemicalDetailId;
        const response = await this.qmsxTcrsService.createChemicalTcrs(
          chemicalTcrsNew,
        );
        if (isEmpty(response)) {
          // delete mesurement
          for (const measurementDetail of measurementDetails) {
            await this.qmsxTicketService.deleteMeasurementDetail({
              id: measurementDetail.id,
            });
          }
          return new ResponseBuilder()
            .withCode(ResponseCodeEnum.BAD_REQUEST)
            .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
            .build();
        }
      }
    }

    return new ResponseBuilder(measurementDetails)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('error.SUCCESS'))
      .build();
  }

  async update(request: UpdateChemicalAnalysisTicketRequestDto): Promise<any> {
    const chemicalAnalysisTicket =
      await this.qmsxTicketService.getDetailChemicalAnalysisTicketById({
        id: request.id,
      });
    if (isEmpty(chemicalAnalysisTicket)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    request.updatedBy = request.userId;
    request.status = StatusEnum.IN_PROGRESS;
    const response = await this.qmsxTicketService.updateChemicalAnalysisTicket(
      request,
    );
    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(
    request: GetListChemicalAnalysisTicketRequestDto,
  ): Promise<any> {
    const { page } = request;
    const { data, count } =
      await this.qmsxTicketService.getListChemicalAnalysisTicket(request);

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(
    request: GetDetailChemicalAnalysisTicketRequestDto,
  ): Promise<any> {
    const chemicalAnalysisTicket =
      await this.qmsxTicketService.getDetailChemicalAnalysisTicketById(request);

    if (!chemicalAnalysisTicket) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    chemicalAnalysisTicket.chemicalAnalysisTicketDetails.forEach((detail) => {
      detail.measurementDetails.sort((a, b) => a.index - b.index);
    });

    const dataMapUser = await this.baseService.mapMasterInfoToResponse([
      chemicalAnalysisTicket,
    ]);
    const chemicalAnalysisTicketDetails =
      await this.baseService.mapMasterInfoToResponse(
        chemicalAnalysisTicket.chemicalAnalysisTicketDetails,
      );

    dataMapUser.forEach((s) => {
      s.chemicalAnalysisTicketDetails = chemicalAnalysisTicketDetails;
    });
    return new ResponseBuilder(dataMapUser[0] ? dataMapUser[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
  async approve(
    request: UpdateStatusChemicalAnalysisTicketRequestDto,
  ): Promise<any> {
    const chemicalAnalysisTicket =
      await this.qmsxTicketService.getDetailChemicalAnalysisTicketById(request);

    if (
      !chemicalAnalysisTicket ||
      chemicalAnalysisTicket.status !== StatusEnum.IN_PROGRESS
    ) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const { result, messageError } =
      await this.qmsxTicketService.approveChemicalAnalysisTicket(request);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async cancel(
    request: UpdateStatusChemicalAnalysisTicketRequestDto,
  ): Promise<any> {
    const chemicalAnalysisTicket =
      await this.qmsxTicketService.getDetailChemicalAnalysisTicketById(request);

    if (
      !chemicalAnalysisTicket ||
      chemicalAnalysisTicket.status !== StatusEnum.IN_PROGRESS
    ) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const { result, messageError } =
      await this.qmsxTicketService.cancelChemicalAnalysisTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
