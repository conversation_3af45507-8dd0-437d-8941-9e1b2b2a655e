import { Modu<PERSON> } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { ProcessChemicalModule } from '../process-chemical/process-chemical.module';
import { ChemicalAnalysisTicketController } from './controller/chemical-analysis-ticket.controller';
import { ChemicalAnalysisTicketService } from './service/chemical-analysis-ticket.service';

@Module({
  imports: [
    AnotherServiceModule,
    BaseProcessModule,
    ChemicalAnalysisTicketModule,
    ProcessChemicalModule,
    MasterDataReferenceModule,
  ],
  providers: [ChemicalAnalysisTicketService],
  exports: [ChemicalAnalysisTicketService],
  controllers: [ChemicalAnalysisTicketController],
})
export class ChemicalAnalysisTicketModule {}
