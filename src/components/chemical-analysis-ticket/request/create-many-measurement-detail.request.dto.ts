import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, ValidateNested } from 'class-validator';
import { BaseDto } from '../../../core/dto/base.dto';
import { CreateMeasurementDetailRequestDto } from './create-measurement-detail.request.dto';

export class CreateManyMeasurementDetailRequestDto extends BaseDto {
  @ApiProperty({ type: [CreateMeasurementDetailRequestDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateMeasurementDetailRequestDto)
  measurementDetails: CreateMeasurementDetailRequestDto[];

  chemicalAnalysisTicketId: number;
}
