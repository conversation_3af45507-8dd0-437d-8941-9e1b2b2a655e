import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt } from 'class-validator';
import { CreateMeasurementDetailRequestDto } from './create-measurement-detail.request.dto';

export class UpdateMeasurementDetailRequestDto extends CreateMeasurementDetailRequestDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;

  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  updatedBy?: number;
}
