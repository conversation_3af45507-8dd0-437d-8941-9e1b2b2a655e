import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsDateString,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { BaseDto } from '../../../core/dto/base.dto';

export class ChemicalAnalysisTicketDetailDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  id?: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  processChemicalDetailId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  managementCategoryName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  chemicalId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  scope: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  managementType: number;
}

export class CreateChemicalAnalysisTicketRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsDateString()
  measurementDate: Date;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  workshift: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  processId: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  deviceTypeId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  deviceId: number;

  @ApiProperty({ type: [ChemicalAnalysisTicketDetailDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChemicalAnalysisTicketDetailDto)
  chemicalAnalysisTicketDetails: ChemicalAnalysisTicketDetailDto[];

  @ApiProperty()
  @IsOptional()
  @IsInt()
  createdBy?: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  status: number;
}
