import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateChemicalAnalysisTicketRequestDto } from './create-chemical-analysis-ticket.request.dto';

export class UpdateChemicalAnalysisTicketRequestDto extends CreateChemicalAnalysisTicketRequestDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  updatedBy?: number;
}
