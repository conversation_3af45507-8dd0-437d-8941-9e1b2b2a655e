import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsDateString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { BaseDto } from '../../../core/dto/base.dto';

export class CreateMeasurementDetailRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  chemicalAnalysisTicketDetailId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  measurementTime: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  measurementValue: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  adjustmentValue: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  pm: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  index: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsDateString()
  measurementDate: Date;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  workshift: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  processId: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  deviceTypeId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  deviceId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  processChemicalDetailId: number;
}
