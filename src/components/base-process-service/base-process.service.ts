import { Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { ValidateMasterRequestDto } from '../../common/dtos/request/validate-master.request.dto';
import { StatusEnum } from '../../common/enums/status.enum';
import { BaseService } from '../../common/service/base.service';
import { UserService } from '../../components/another-service/services/user-service';
import { VendorService } from '../../components/another-service/services/vendor-service';
import { ChecksheetService } from '../../components/checksheet/service/checksheet.service';
import { CustomerService } from '../../components/customer/service/customer.service';
import { InspectionStandardService } from '../../components/inspection-standard/service/inspection-standard.service';
import { ItemUnitService } from '../../components/item-unit/service/item-unit.service';
import { ItemService } from '../../components/item/service/item.service';
import { ProcessService } from '../../components/process/service/process.service';
import { ProductionProcessService } from '../../components/production-process/service/production-process.service';
import { QcRequestTypeService } from '../../components/qc-request-type/service/qc-request-type.service';
import { SamplingRateService } from '../../components/sampling-rate/service/sampling-rate.service';
import { Filter } from '../../utils/dto/request/pagination.query';
import { DEVICE_STATUS_ENUM } from '../another-service/constant/device.constant';
import { MmsDeviceService } from '../another-service/services/mms-device-service';
import { QmsxCustomerSupportService } from '../another-service/services/qmsx-customer-support-service';
import { QmsxPlanService } from '../another-service/services/qmsx-plan-service';
import { QmsxRequestService } from '../another-service/services/qmsx-request-service';
import { QmsxTicketService } from '../another-service/services/qmsx-ticket-service';
import { ChecksheetDetailService } from '../checksheet-detail/service/checksheet-detail.service';
import { ChemicalEntity } from '../chemical/entities/chemical.entity';
import { GetDetailChemicalRequestDto } from '../chemical/request/get-detail-chemical.request.dto';
import { ErrorHandlingStatusService } from '../error-handling-status/service/error-handling-status.service';
import { ErrorTypeService } from '../error-type/service/error-type.service';
import { ErrorService } from '../error/service/error.service';
import { GoodsTypeService } from '../goods-type/service/goods-type.service';
import { TABLE_NAME } from '../import-excel/constants/import-excel.constants';
import { InspectionGroupService } from '../inspection-group/service/inspection-group.service';
import { InspectionTypeService } from '../inspection-type/service/inspection-type.service';
import { InspectionService } from '../inspection/service/inspection.service';
import { ItemLineService } from '../item-line/service/item-line.service';
import { ItemTypeService } from '../item-type/service/item-type.service';
import { MasterDataReferenceService } from '../master-data-reference/service/master-data-reference.service';
import { GetListDepartmentRequestDto } from '../p-code/request/get-list-department.request.dto';
import { UnitService } from '../unit/service/unit.service';
import { BaseProcessRepositoy } from './base-process.repository';

@Injectable()
export class BaseProcessService extends BaseService {
  constructor(
    private readonly itemService: ItemService,
    private readonly qcRequestTypeService: QcRequestTypeService,
    private readonly itemUnitService: ItemUnitService,
    private readonly processService: ProcessService,
    private readonly vendorService: VendorService,
    protected readonly userService: UserService,
    private readonly productionProcessService: ProductionProcessService,
    private readonly checksheetService: ChecksheetService,
    private readonly inspectionStandardService: InspectionStandardService,
    private readonly samplingRateService: SamplingRateService,
    private readonly goodsTypeService: GoodsTypeService,
    private readonly customerService: CustomerService,
    private readonly inspectionService: InspectionService,
    private readonly checksheetDetailService: ChecksheetDetailService,
    private readonly masterDataReferenceService: MasterDataReferenceService,
    private readonly errorService: ErrorService,
    private readonly errorTypeService: ErrorTypeService,
    private readonly errorHandlingService: ErrorHandlingStatusService,
    private readonly itemTypeService: ItemTypeService,
    private readonly unitService: UnitService,
    private readonly itemLineService: ItemLineService,
    private readonly mmsDeviceService: MmsDeviceService,
    private readonly inspectionGroupService: InspectionGroupService,
    private readonly inspectionTypeService: InspectionTypeService,
    private readonly baseProcessRepositoy: BaseProcessRepositoy,
    private readonly qmsxRequestService: QmsxRequestService,
    private readonly qmsxTicketService: QmsxTicketService,
    private readonly qmsxPlanService: QmsxPlanService,
    private readonly qmsxCustomerSupportService: QmsxCustomerSupportService,
  ) {
    super(userService);
  }

  extractUniqueIds(data: any[], keys: string[]): number[] {
    return Array.from(
      new Set(
        data
          .flatMap((item) => {
            const arr = [];
            keys.forEach((key) =>
              isNaN(+item[key]) ? [] : arr.push(+item[key]),
            );
            return arr;
          })
          .filter((id) => id !== null && id !== undefined),
      ),
    );
  }

  getUniqueCodesMap(data: any[], keys: string[]): Record<string, string[]> {
    const uniqueCodesMap: Record<string, string[]> = {};

    keys.forEach((key) => {
      const uniqueCodes = Array.from(
        new Set(
          data
            .flatMap((item) => {
              const value = item[key]?.split(',');
              return value ? value : [];
            })
            .filter((code) => code !== null && code !== undefined),
        ),
      );

      if (uniqueCodes.length > 0) {
        if (key === 'itemUnitQcCode') {
          key = 'itemUnitCode';
        }
        uniqueCodesMap[key] = uniqueCodes;
      }
    });

    return uniqueCodesMap;
  }

  getUniqueKeysMap(datas: any[], keys: string[]): string[] {
    const uniqueKeysSet = new Set<string>();
    datas.forEach((item) => {
      const keyCombination = keys.map((key) => item[key]).join('|');
      uniqueKeysSet.add(keyCombination);
    });
    return Array.from(uniqueKeysSet);
  }

  getUniqueIdsMap(data: any[], keys: string[]): Record<string, number[]> {
    const uniqueIdsMap: Record<string, number[]> = {};

    keys.forEach((key) => {
      const uniqueIds = Array.from(
        new Set(
          data
            .flatMap((item) => {
              const value = item[key];
              return !isNaN(+value) ? [+value] : [];
            })
            .filter((id) => id !== null && id !== undefined),
        ),
      );

      if (uniqueIds.length > 0) {
        uniqueIdsMap[key] = uniqueIds;
      }
    });

    return uniqueIdsMap;
  }

  async keyByCustom(array: any[], key: string) {
    return array.reduce((result, item) => {
      const keyValue = item[key];
      result[keyValue] = item;
      return result;
    }, {});
  }

  async keysByCustom(array: any[], keys: string[]) {
    return array.reduce((result, item) => {
      const keyValue = keys.map((key) => item[key]).join('-');
      result[keyValue] = item;
      return result;
    }, {});
  }

  async keysByCustomMap(array: any[], keys: string[]) {
    return array.reduce((result, item) => {
      const keyValue = keys.map((key) => item[key]).join('-');

      if (!result.has(keyValue)) {
        result.set(keyValue, []);
      }

      result.get(keyValue).push(item);
      return result;
    }, new Map());
  }

  mergeUniqueIds(
    keys: string[],
    uniqueIdsMap: Record<string, number[]>,
  ): number[] {
    const mergedIdsSet = new Set<number>();

    keys.forEach((key) => {
      if (uniqueIdsMap[key]) {
        uniqueIdsMap[key].forEach((id) => mergedIdsSet.add(id));
      }
    });

    return Array.from(mergedIdsSet);
  }

  mergeUniqueCodes(
    keys: string[],
    uniqueCodesMap: Record<string, string[]>,
  ): string[] {
    const mergedCodesSet = new Set<string>();

    keys.forEach((key) => {
      if (uniqueCodesMap[key]) {
        uniqueCodesMap[key].forEach((code) => mergedCodesSet.add(code));
      }
    });

    return Array.from(mergedCodesSet);
  }

  replaceLastCharacter(str: string, newValue: string): string {
    if (str.length === 0) return str;
    if (str.endsWith('ses')) {
      return str.slice(0, str.length - 2) + newValue;
    }
    if (str.endsWith('ies')) {
      return str.slice(0, str.length - 3) + 'y' + newValue;
    }
    if (str.endsWith('Code')) {
      return str;
    }
    return str.slice(0, str.length - 1) + newValue;
  }

  toCamelCase(str: string): string {
    return str
      .replace(/_([a-z])/g, (match, letter) => letter.toUpperCase())
      .replace(/^([a-z])/, (match, letter) => letter.toLowerCase());
  }

  async buildMapDataMaster(
    data: any[],
    tables: string[],
    tableMain: string,
  ): Promise<Map<string, any[]>> {
    const mapData = new Map<string, any[]>();
    if (isEmpty(data)) {
      return mapData;
    }
    const uniqueCodesMap = this.getUniqueCodesMap(data, [
      'itemCode',
      'qcRequestTypeCode',
      'detectionProcessCode',
      'processCode',
      'errorProcessCode',
      'itemUnitQcCode',
      'itemUnitCode',
      'vendorCode',
      'checksheetCode',
      'samplingRateCode',
      'inspectionStandardCode',
      'goodsTypeCode',
      'customerCode',
      'inspectionCode',
      'departmentCode',
      'plantCode',
      'pCode',
      'errorCode',
      'errorTypeCode',
      'checksheetDetailCode',
      'errorHandlingStatusCode',
      'itemTypeCode',
      'criteriaTypeCode',
      'scoreBoardCode',
      'unitCode',
      'unitCode1',
      'unitCode2',
      'chemicalCode',
      'itemLineCode',
      'code',
      'errorGroupCode',
      'errorTypeCode',
      'inspectionGroupCode',
      'inspectionTypeCode',
      'inspectionCode',
      'evaluationStandardTypeCode',
      'inspectionSampleQuantityCode',
    ]);
    await Promise.all(
      tables.map(async (s) => {
        // if (s === 'inspection_sample_quantities') {
        //   const itemList =
        //     await this.baseProcessRepositoy.findAllByCodeAndTableName(
        //       s,
        //       uniqueCodesMap['inspectionSampleQuantityCode'],
        //     );
        //   if (itemList && itemList.length > 0) {
        //     const itemMap = await this.keyByCustom(itemList, 'code');
        //     mapData.set(s, itemMap);
        //   }
        // }
        const key = this.replaceLastCharacter(this.toCamelCase(s), 'Code');
        if (uniqueCodesMap[key]) {
          let itemList;
          if (s === 'vendors') {
            itemList = await this.vendorService.findAllVendorByCodes(
              uniqueCodesMap[key],
            );
          } else if (s === 'departments') {
            const request = new GetListDepartmentRequestDto();
            const filter = [];
            const codeFilter = new Filter();
            codeFilter.column = 'codes';
            codeFilter.text = uniqueCodesMap[key].join(',');
            filter.push(codeFilter);
            request.filter = filter;
            request.limit = 1000000000;

            itemList = await this.userService.getListRoomByCodes(request);
          } else {
            itemList =
              await this.baseProcessRepositoy.findAllByCodeAndTableName(
                s,
                uniqueCodesMap[key],
              );
          }
          if (itemList && itemList.length > 0) {
            const itemMap = await this.keyByCustom(itemList, 'code');
            mapData.set(s, itemMap);
          }
        }
        if (key === 'unitCode') {
          let combinedItemList: any[] = [];

          for (const unitCodeKey of ['unitCode1', 'unitCode2']) {
            if (uniqueCodesMap[unitCodeKey]) {
              const itemList =
                await this.baseProcessRepositoy.findAllByCodeAndTableName(
                  s,
                  uniqueCodesMap[unitCodeKey],
                );
              if (itemList && itemList.length > 0) {
                combinedItemList = combinedItemList.concat(itemList);
              }
            }
          }

          if (combinedItemList.length > 0) {
            const itemMap = await this.keyByCustom(combinedItemList, 'code');
            mapData.set(s, itemMap);
          }
        }
      }),
    );
    if (isEmpty(tableMain) || tableMain === TABLE_NAME.VENDOR) {
      return mapData;
    }
    if (isEmpty(uniqueCodesMap['code'])) {
      return mapData;
    }
    const itemMain = await this.baseProcessRepositoy.findAllByCodeAndTableName(
      tableMain,
      uniqueCodesMap['code'],
    );
    if (itemMain && itemMain.length > 0) {
      const itemMap = await this.keyByCustom(itemMain, 'code');
      mapData.set(tableMain, itemMap);
    }
    return mapData;
  }

  async buildMapDataByKey(
    data: any[],
    keys: string[],
    tableMain: string,
  ): Promise<Map<string, any[]>> {
    const mapData = new Map<string, any>();
    if (isEmpty(data) || isEmpty(keys)) {
      return mapData;
    }
    const uniqueKeys = this.getUniqueKeysMap(data, keys);
    if (isEmpty(tableMain)) {
      return mapData;
    }
    if (tableMain === TABLE_NAME.VENDOR) {
      const codes = data.map((vendor) => vendor.code);
      const vendorList = await this.vendorService.findAllVendorByCodes({
        codes: codes,
      });
      if (!vendorList.items || vendorList.items.length === 0) {
        return new Map<string, any>();
      }
      const vendorMap = new Map<string, any>(
        vendorList.items.map((vendor) => [vendor.code, vendor]),
      );
      return vendorMap;
    }
    const itemMains = await this.baseProcessRepositoy.findAllByTableNameAndKeys(
      tableMain,
      keys,
      uniqueKeys,
    );
    if (itemMains && itemMains.length > 0) {
      itemMains.forEach((item) => {
        const key = keys.map((k) => item[k]).join('|');
        mapData.set(key, item);
      });
    }
    return mapData;
  }

  async mapMasterInfoToResponse(data: any[]): Promise<any[]> {
    if (isEmpty(data)) {
      return [];
    }
    const uniqueIdsMap = this.getUniqueIdsMap(data, [
      'itemId',
      'qcRequestTypeId',
      'detectionProcessId',
      'processId',
      'errorProcessId',
      'responsibleProcessId',
      'itemUnitQcId',
      'itemUnitId',
      'vendorId',
      'checksheetId',
      'samplingRateId',
      'inspectionStandardId',
      'goodsTypeId',
      'customerId',
      'inspectionId',
      'departmentId',
      'plantId',
      'pId',
      'errorId',
      'errorTypeId',
      'checksheetDetailId',
      'errorHandlingStatusId',
      'itemTypeId',
      'criteriaTypeId',
      'scoreBoardId',
      'unitId',
      'chemicalId',
      'itemLineId',
      'sortingDepartmentId',
      'countermeasureReleaseDepartmentId',
      'requestDepartmentId',
      'priceProviderDepartmentId',
      'deviceId',
      'inspectionGroupId',
      'inspectionTypeId',
      'processChemicalDetailId',
      'equipmentId',
      'aqlSampleLimitId',
      'errorGroupId',
    ]);
    const mapData = new Map<string, any[]>();
    if (uniqueIdsMap['itemId']) {
      const itemList = await this.itemService.findAllDataByIds(
        uniqueIdsMap['itemId'],
      );
      if (itemList && itemList.length > 0) {
        const itemMap = await this.keyByCustom(itemList, 'id');
        mapData.set('items', itemMap);
      }
    }

    if (uniqueIdsMap['qcRequestTypeId']) {
      const qcRequestTypeList = await this.qcRequestTypeService.findAllByIds(
        uniqueIdsMap['qcRequestTypeId'],
      );
      if (qcRequestTypeList && qcRequestTypeList.length > 0) {
        const itemMap = await this.keyByCustom(qcRequestTypeList, 'id');
        mapData.set('qcRequestTypes', itemMap);
      }
    }

    const processIds = this.mergeUniqueIds(
      [
        'detectionProcessId',
        'processId',
        'errorProcessId',
        'responsibleProcessId',
      ],
      uniqueIdsMap,
    );
    if (processIds && processIds.length > 0) {
      const processList = await this.processService.getListProcessDetail(
        processIds,
      );
      if (processList && processList.length > 0) {
        const itemMap = await this.keyByCustom(processList, 'id');
        mapData.set('process', itemMap);
      }
    }

    const itemUnitIds = this.mergeUniqueIds(
      ['itemUnitQcId', 'itemUnitId'],
      uniqueIdsMap,
    );
    if (itemUnitIds && itemUnitIds.length > 0) {
      const itemUnitList = await this.itemUnitService.findAllItemUnitByIds(
        itemUnitIds,
      );
      if (itemUnitList && itemUnitList.length > 0) {
        const itemMap = await this.keyByCustom(itemUnitList, 'id');
        mapData.set('itemUnits', itemMap);
      }
    }

    if (uniqueIdsMap['vendorId']) {
      const vendorList = await this.vendorService.findAllVendorByIds(
        uniqueIdsMap['vendorId'],
      );
      if (vendorList && vendorList.length > 0) {
        const itemMap = await this.keyByCustom(vendorList, 'id');
        mapData.set('vendors', itemMap);
      }
    }

    if (uniqueIdsMap['checksheetId']) {
      const checksheetList = await this.checksheetService.getDetailList(
        uniqueIdsMap['checksheetId'],
      );
      if (checksheetList && checksheetList.length > 0) {
        const itemMap = await this.keyByCustom(checksheetList, 'id');
        mapData.set('checksheets', itemMap);
      }
    }

    if (uniqueIdsMap['samplingRateId']) {
      const samplingRateList = await this.samplingRateService.findAllByIds(
        uniqueIdsMap['samplingRateId'],
      );
      if (samplingRateList && samplingRateList.length > 0) {
        const itemMap = await this.keyByCustom(samplingRateList, 'id');
        mapData.set('samplingRates', itemMap);
      }
    }

    if (uniqueIdsMap['inspectionStandardId']) {
      const inspectionStandardList =
        await this.inspectionStandardService.findAllByIds(
          uniqueIdsMap['inspectionStandardId'],
        );
      if (inspectionStandardList && inspectionStandardList.length > 0) {
        const itemMap = await this.keyByCustom(inspectionStandardList, 'id');
        mapData.set('inspectionStandards', itemMap);
      }
    }

    if (uniqueIdsMap['goodsTypeId']) {
      const goodsTypeList = await this.goodsTypeService.findAllByIds(
        uniqueIdsMap['goodsTypeId'],
      );
      if (goodsTypeList && goodsTypeList.length > 0) {
        const itemMap = await this.keyByCustom(goodsTypeList, 'id');
        mapData.set('goodsTypes', itemMap);
      }
    }

    if (uniqueIdsMap['customerId']) {
      const customerList = await this.customerService.findAllByIds(
        uniqueIdsMap['customerId'],
      );
      if (customerList && customerList.length > 0) {
        const itemMap = await this.keyByCustom(customerList, 'id');
        mapData.set('customers', itemMap);
      }
    }

    if (uniqueIdsMap['inspectionId']) {
      const inspectionList = await this.inspectionService.getAllInfoByIds(
        uniqueIdsMap['inspectionId'],
      );
      if (inspectionList.data && inspectionList.data.length > 0) {
        const itemMap = await this.keyByCustom(inspectionList.data, 'id');
        mapData.set('inspections', itemMap);
      }
    }

    if (uniqueIdsMap['departmentId']) {
      const departmentList = await this.userService.getListRoomByIds(
        uniqueIdsMap['departmentId'],
      );
      if (departmentList && departmentList.length > 0) {
        const itemMap = await this.keyByCustom(departmentList, 'id');
        mapData.set('departments', itemMap);
      }
    }

    if (uniqueIdsMap['plantId']) {
      const plantList = await this.userService.getListManufacturingPlanByIds(
        uniqueIdsMap['plantId'],
      );
      if (plantList && plantList.length > 0) {
        const itemMap = await this.keyByCustom(plantList, 'id');
        mapData.set('plants', itemMap);
      }
    }

    if (uniqueIdsMap['pId']) {
      const pCodeList = await this.masterDataReferenceService.findAllPCodeByIds(
        uniqueIdsMap['pId'],
      );
      if (pCodeList && pCodeList.length > 0) {
        const itemMap = await this.keyByCustom(pCodeList, 'id');
        mapData.set('pCodes', itemMap);
      }
    }

    if (uniqueIdsMap['errorId']) {
      const errorList = await this.errorService.findAllDataByIds(
        uniqueIdsMap['errorId'],
      );
      if (errorList && errorList.length > 0) {
        const itemMap = await this.keyByCustom(errorList, 'id');
        mapData.set('errors', itemMap);
      }
    }

    if (uniqueIdsMap['errorTypeId']) {
      const errorTypeList = await this.errorTypeService.findAllByIds(
        uniqueIdsMap['errorTypeId'],
      );
      if (errorTypeList && errorTypeList.length > 0) {
        const itemMap = await this.keyByCustom(errorTypeList, 'id');
        mapData.set('errorTypes', itemMap);
      }
    }

    if (uniqueIdsMap['checksheetDetailId']) {
      const checksheetDetailList =
        await this.checksheetDetailService.getDetailList(
          uniqueIdsMap['checksheetDetailId'],
        );
      if (checksheetDetailList && checksheetDetailList.length > 0) {
        const itemMap = await this.keyByCustom(checksheetDetailList, 'id');
        mapData.set('checksheetDetails', itemMap);
      }
    }

    if (uniqueIdsMap['errorHandlingStatusId']) {
      const errorHandlingStatusList =
        await this.errorHandlingService.findAllByIds(
          uniqueIdsMap['errorHandlingStatusId'],
        );
      if (errorHandlingStatusList && errorHandlingStatusList.length > 0) {
        const itemMap = await this.keyByCustom(errorHandlingStatusList, 'id');
        mapData.set('errorHandlingStatuses', itemMap);
      }
    }

    if (uniqueIdsMap['itemTypeId']) {
      const itemTypeList = await this.itemTypeService.findAllByIds(
        uniqueIdsMap['itemTypeId'],
      );
      if (itemTypeList && itemTypeList.length > 0) {
        const itemMap = await this.keyByCustom(itemTypeList, 'id');
        mapData.set('itemTypes', itemMap);
      }
    }

    if (uniqueIdsMap['criteriaTypeId']) {
      const criteriaTypeList = await this.vendorService.findAllCriteriaByIds(
        uniqueIdsMap['criteriaTypeId'],
      );
      if (criteriaTypeList && criteriaTypeList.length > 0) {
        const itemMap = await this.keyByCustom(criteriaTypeList, 'id');
        mapData.set('criteriaTypes', itemMap);
      }
    }

    if (uniqueIdsMap['scoreBoardId']) {
      const scoreBoardList = await this.vendorService.findAllScoreBoardByIds(
        uniqueIdsMap['scoreBoardId'],
      );
      if (scoreBoardList && scoreBoardList.length > 0) {
        const itemMap = await this.keyByCustom(scoreBoardList, 'id');
        mapData.set('scoreBoards', itemMap);
      }
    }

    if (uniqueIdsMap['unitId']) {
      const unitList = await this.unitService.findAllByIds(
        uniqueIdsMap['unitId'],
      );
      if (unitList && unitList.length > 0) {
        const itemMap = await this.keyByCustom(unitList, 'id');
        mapData.set('units', itemMap);
      }
    }

    if (uniqueIdsMap['chemicalId']) {
      const chemicalList =
        await this.masterDataReferenceService.findAllChemicalAndUnitByIds(
          uniqueIdsMap['chemicalId'],
        );
      if (chemicalList && chemicalList.length > 0) {
        const itemMap = await this.keyByCustom(chemicalList, 'id');
        mapData.set('chemicals', itemMap);
      }
    }

    if (uniqueIdsMap['itemLineId']) {
      const itemList = await this.itemLineService.findAllByIds(
        uniqueIdsMap['itemLineId'],
      );
      if (itemList && itemList.length > 0) {
        const itemMap = await this.keyByCustom(itemList, 'id');
        mapData.set('itemLines', itemMap);
      }
    }
    if (uniqueIdsMap['sortingDepartmentId']) {
      const sortingDepartmentList = await this.userService.getListRoomByIds(
        uniqueIdsMap['sortingDepartmentId'],
      );
      if (sortingDepartmentList && sortingDepartmentList.length > 0) {
        const itemMap = await this.keyByCustom(sortingDepartmentList, 'id');
        mapData.set('sortingDepartments', itemMap);
      }
    }
    if (uniqueIdsMap['requestDepartmentId']) {
      const requestDepartmentList = await this.userService.getListRoomByIds(
        uniqueIdsMap['requestDepartmentId'],
      );
      if (requestDepartmentList && requestDepartmentList.length > 0) {
        const itemMap = await this.keyByCustom(requestDepartmentList, 'id');
        mapData.set('requestDepartments', itemMap);
      }
    }
    if (uniqueIdsMap['priceProviderDepartmentId']) {
      const priceProviderDepartmentList =
        await this.userService.getListRoomByIds(
          uniqueIdsMap['priceProviderDepartmentId'],
        );
      if (
        priceProviderDepartmentList &&
        priceProviderDepartmentList.length > 0
      ) {
        const itemMap = await this.keyByCustom(
          priceProviderDepartmentList,
          'id',
        );
        mapData.set('priceProviderDepartments', itemMap);
      }
    }
    if (uniqueIdsMap['countermeasureReleaseDepartmentId']) {
      const countermeasureDepartmentList =
        await this.userService.getListRoomByIds(
          uniqueIdsMap['countermeasureReleaseDepartmentId'],
        );
      if (
        countermeasureDepartmentList &&
        countermeasureDepartmentList.length > 0
      ) {
        const itemMap = await this.keyByCustom(
          countermeasureDepartmentList,
          'id',
        );
        mapData.set('countermeasureReleaseDepartments', itemMap);
      }
    }

    if (uniqueIdsMap['deviceId']) {
      const deviceList = await this.mmsDeviceService.getDeviceByIdsInternal({
        ids: uniqueIdsMap['deviceId'],
      });
      if (deviceList && deviceList.length > 0) {
        const deviceMap = await this.keyByCustom(deviceList, 'id');
        mapData.set('devices', deviceMap);
      }
    }

    if (uniqueIdsMap['equipmentId']) {
      const deviceList = await this.mmsDeviceService.getDeviceByIdsInternal({
        ids: uniqueIdsMap['equipmentId'],
      });
      if (deviceList && deviceList.length > 0) {
        const deviceMap = await this.keyByCustom(deviceList, 'id');
        mapData.set('equipments', deviceMap);
      }
    }

    if (uniqueIdsMap['inspectionGroupId']) {
      const itemList = await this.inspectionGroupService.findAllByIds(
        uniqueIdsMap['inspectionGroupId'],
      );
      if (itemList && itemList.length > 0) {
        const itemMap = await this.keyByCustom(itemList, 'id');
        mapData.set('inspectionGroups', itemMap);
      }
    }

    if (uniqueIdsMap['inspectionTypeId']) {
      const itemList = await this.inspectionTypeService.findAllByIds(
        uniqueIdsMap['inspectionTypeId'],
      );
      if (itemList && itemList.length > 0) {
        const itemMap = await this.keyByCustom(itemList, 'id');
        mapData.set('inspectionTypes', itemMap);
      }
    }

    if (uniqueIdsMap['processChemicalDetailId']) {
      const itemList =
        await this.masterDataReferenceService.findAllProcessChemicalDetailByIds(
          uniqueIdsMap['processChemicalDetailId'],
        );
      if (itemList && itemList.length > 0) {
        const itemMap = await this.keyByCustom(itemList, 'id');
        mapData.set('processChemicalDetails', itemMap);
      }
    }

    if (uniqueIdsMap['aqlSampleLimitId']) {
      const itemList =
        await this.masterDataReferenceService.findAllAqlSampleLimitByIds(
          uniqueIdsMap['aqlSampleLimitId'],
        );
      if (itemList && itemList.length > 0) {
        const itemMap = await this.keyByCustom(itemList, 'id');
        mapData.set('aqlSampleLimits', itemMap);
      }
    }

    if (uniqueIdsMap['errorGroupId']) {
      const itemList =
        await this.masterDataReferenceService.findAllErrorGroupByIds(
          uniqueIdsMap['errorGroupId'],
        );
      if (itemList && itemList.length > 0) {
        const itemMap = await this.keyByCustom(itemList, 'id');
        mapData.set('errorGroup', itemMap);
      }
    }

    const errors = mapData.get('errors');
    const errorTypes = mapData.get('errorTypes');
    const checksheetDetails = mapData.get('checksheetDetails');
    const errorHandlingStatuses = mapData.get('errorHandlingStatuses');
    const itemTypes = mapData.get('itemTypes');
    const criteriaTypes = mapData.get('criteriaTypes');
    const scoreBoards = mapData.get('scoreBoards');
    const units = mapData.get('units');
    const chemicals = mapData.get('chemicals');
    const pCodes = mapData.get('pCodes');
    const plants = mapData.get('plants');
    const departments = mapData.get('departments');
    const inspections = mapData.get('inspections');
    const customers = mapData.get('customers');
    const goodsTypes = mapData.get('goodsTypes');
    const inspectionStandards = mapData.get('inspectionStandards');
    const samplingRates = mapData.get('samplingRates');
    const checksheets = mapData.get('checksheets');
    const vendors = mapData.get('vendors');
    const itemUnits = mapData.get('itemUnits');
    const process = mapData.get('process');
    const qcRequestTypes = mapData.get('qcRequestTypes');
    const items = mapData.get('items');
    const itemLines = mapData.get('itemLines');
    const sortingDepartments = mapData.get('sortingDepartments');
    const priceProviderDepartments = mapData.get('priceProviderDepartments');
    const requestDepartments = mapData.get('requestDepartments');
    const countermeasureReleaseDepartments = mapData.get(
      'countermeasureReleaseDepartments',
    );
    const devices = mapData.get('devices');
    const inspectionGroups = mapData.get('inspectionGroups');
    const inspectionTypes = mapData.get('inspectionTypes');
    const processChemicalDetails = mapData.get('processChemicalDetails');
    const equipments = mapData.get('equipments');
    const aqlSampleLimits = mapData.get('aqlSampleLimits');
    const errorGroup = mapData.get('errorGroup');

    data = data?.map((item) => {
      const itemResponse = plainToInstance(Object, item) as any;
      if (items && items[item.itemId]) {
        itemResponse.item = items[item.itemId];
      }
      if (qcRequestTypes && qcRequestTypes[item.qcRequestTypeId]) {
        itemResponse.qcRequestType = qcRequestTypes[item.qcRequestTypeId];
      }
      if (process && process[item.processId]) {
        itemResponse.process = process[item.processId];
      }
      if (process && process[item.detectionProcessId]) {
        itemResponse.detectionProcess = process[item.detectionProcessId];
      }
      if (process && process[item.errorProcessId]) {
        itemResponse.errorProcess = process[item.errorProcessId];
      }
      if (process && process[item.responsibleProcessId]) {
        itemResponse.responsibleProcess = process[item.responsibleProcessId];
      }
      if (itemUnits && itemUnits[item.itemUnitId]) {
        itemResponse.itemUnit = itemUnits[item.itemUnitId];
      }
      if (itemUnits && itemUnits[item.itemUnitQcId]) {
        itemResponse.itemUnitQc = itemUnits[item.itemUnitQcId];
      }
      if (vendors && vendors[item.vendorId]) {
        itemResponse.vendor = vendors[item.vendorId];
      }
      if (checksheets && checksheets[item.checksheetId]) {
        itemResponse.checksheet = checksheets[item.checksheetId];
      }
      if (samplingRates && samplingRates[item.samplingRateId]) {
        itemResponse.samplingRate = samplingRates[item.samplingRateId];
      }
      if (
        inspectionStandards &&
        inspectionStandards[item.inspectionStandardId]
      ) {
        itemResponse.inspectionStandard =
          inspectionStandards[item.inspectionStandardId];
      }
      if (goodsTypes && goodsTypes[item.goodsTypeId]) {
        itemResponse.goodsType = goodsTypes[item.goodsTypeId];
      }
      if (customers && customers[item.customerId]) {
        itemResponse.customer = customers[item.customerId];
      }
      if (inspections && inspections[item.inspectionId]) {
        itemResponse.inspection = inspections[item.inspectionId];
      }
      if (departments && departments[item.departmentId]) {
        itemResponse.department = departments[item.departmentId];
      }
      if (plants && plants[item.plantId]) {
        itemResponse.manufacturingPlant = plants[item.plantId];
      }
      if (pCodes && pCodes[item.pId]) {
        itemResponse.pCode = pCodes[item.pId];
      }
      if (errors && errors[item.errorId]) {
        itemResponse.error = errors[item.errorId];
      }
      if (errorTypes && errorTypes[item.errorTypeId]) {
        itemResponse.errorType = errorTypes[item.errorTypeId];
      }
      if (checksheetDetails && checksheetDetails[item.checksheetDetailId]) {
        itemResponse.checksheetDetail =
          checksheetDetails[item.checksheetDetailId];
      }
      if (
        errorHandlingStatuses &&
        errorHandlingStatuses[item.errorHandlingStatusId]
      ) {
        itemResponse.errorHandlingStatus =
          errorHandlingStatuses[item.errorHandlingStatusId];
      }
      if (itemTypes && itemTypes[item.itemTypeId]) {
        itemResponse.itemType = itemTypes[item.itemTypeId];
      }
      if (criteriaTypes && criteriaTypes[item.criteriaTypeId]) {
        itemResponse.criteriaType = criteriaTypes[item.criteriaTypeId];
      }
      if (scoreBoards && scoreBoards[item.scoreBoardId]) {
        itemResponse.scoreBoard = scoreBoards[item.scoreBoardId];
      }
      if (units && units[item.unitId]) {
        itemResponse.unit = units[item.unitId];
      }
      if (chemicals && chemicals[item.chemicalId]) {
        itemResponse.chemical = chemicals[item.chemicalId];
      }
      if (itemLines && itemLines[item.itemLineId]) {
        itemResponse.itemLine = itemLines[item.itemLineId];
      }
      if (sortingDepartments && sortingDepartments[item.sortingDepartmentId]) {
        itemResponse.sortingDepartment =
          sortingDepartments[item.sortingDepartmentId];
      }
      if (requestDepartments && requestDepartments[item.requestDepartmentId]) {
        itemResponse.requestDepartment =
          requestDepartments[item.requestDepartmentId];
      }
      if (
        priceProviderDepartments &&
        priceProviderDepartments[item.priceProviderDepartmentId]
      ) {
        itemResponse.priceProviderDepartment =
          priceProviderDepartments[item.priceProviderDepartmentId];
      }
      if (
        countermeasureReleaseDepartments &&
        countermeasureReleaseDepartments[item.countermeasureReleaseDepartmentId]
      ) {
        itemResponse.countermeasureReleaseDepartment =
          countermeasureReleaseDepartments[
            item.countermeasureReleaseDepartmentId
          ];
      }
      if (devices && devices[item.deviceId]) {
        itemResponse.device = devices[item.deviceId];
      }
      if (inspectionGroups && inspectionGroups[item.inspectionGroupId]) {
        itemResponse.inspectionGroup = inspectionGroups[item.inspectionGroupId];
      }
      if (inspectionTypes && inspectionTypes[item.inspectionTypeId]) {
        itemResponse.inspectionType = inspectionTypes[item.inspectionTypeId];
      }
      if (
        processChemicalDetails &&
        processChemicalDetails[item.processChemicalDetailId]
      ) {
        itemResponse.processChemicalDetail =
          processChemicalDetails[item.processChemicalDetailId];
      }
      if (equipments && equipments[item.equipmentId]) {
        itemResponse.equipment = equipments[item.equipmentId];
      }
      if (aqlSampleLimits && aqlSampleLimits[item.aqlSampleLimitId]) {
        itemResponse.aqlSampleLimit = aqlSampleLimits[item.aqlSampleLimitId];
      }

      if (errorGroup && errorGroup[item.errorGroupId]) {
        itemResponse.errorGroup = errorGroup[item.errorGroupId];
      }

      return itemResponse;
    });
    return await this.mapUserInfoToResponse(data);
  }

  async validateMaster(data: any) {
    const {
      itemIds,
      itemUnitIds,
      itemUnitQcIds,
      processIds,
      qcRequestTypeIds,
      productionProcessIds,
      vendorIds,
      checksheetIds,
      samplingRateIds,
      inspectionStandardIds,
      goodsTypeIds,
      customerIds,
      pIds,
      departmentIds,
      checksheetDetailIds,
      errorIds,
      errorHandlingStatusIds,
      itemTypeIds,
      scoreBoardIds,
      criteriaTypeIds,
      unitIds,
      chemicalIds,
      inspectionByIds,
      deviceIds,
      equipmentIds,
      aqlSampleLimitIds,
      ruleSpcIds,
    } = data;
    const mapData = new Map<string, any[]>();
    if (qcRequestTypeIds && qcRequestTypeIds.length > 0) {
      const qcRequestTypes = await this.qcRequestTypeService.findAllByIds(
        qcRequestTypeIds,
      );
      if (
        isEmpty(qcRequestTypes) ||
        qcRequestTypes.length !== qcRequestTypeIds.length ||
        qcRequestTypes.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.QC_REQUEST_TYPE_IS_NOT_EXISTS',
        };
      }
      mapData.set('qcRequestTypes', qcRequestTypes);
    }
    if (itemIds && itemIds.length > 0) {
      const items = await this.itemService.findAllByIds(itemIds);
      if (
        isEmpty(items) ||
        items.length !== itemIds.length ||
        items.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return { result: false, messageError: 'error.ITEM_IS_NOT_EXISTS' };
      }
      mapData.set('items', items);
    }

    if (itemUnitIds && itemUnitIds.length > 0) {
      const itemUnits = await this.itemUnitService.findAllByIds(itemUnitIds);
      if (isEmpty(itemUnits) || itemUnits.length !== itemUnitIds.length) {
        return { result: false, messageError: 'error.ITEM_UNIT_IS_NOT_EXISTS' };
      }
    }

    if (itemUnitQcIds && itemUnitQcIds.length > 0) {
      const itemUnitQcs = await this.itemUnitService.findAllByIds(
        itemUnitQcIds,
      );
      if (isEmpty(itemUnitQcs) || itemUnitQcs.length !== itemUnitQcIds.length) {
        return {
          result: false,
          messageError: 'error.ITEM_UNIT_QC_IS_NOT_EXISTS',
        };
      }
    }

    if (processIds && processIds.length > 0) {
      const processes = await this.processService.findAllByIds(processIds);
      if (
        isEmpty(processes) ||
        processes.length !== processIds.length ||
        processes.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return { result: false, messageError: 'error.PROCESS_IS_NOT_EXISTS' };
      }
    }

    if (customerIds && customerIds.length > 0) {
      const customers = await this.customerService.findAllByIds(customerIds);
      if (
        isEmpty(customers) ||
        customers.length !== customerIds.length ||
        customers.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return { result: false, messageError: 'error.CUSTOMER_IS_NOT_EXISTS' };
      }
    }

    if (vendorIds && vendorIds.length > 0) {
      const vendors = await this.vendorService.findAllVendorByIds(vendorIds);
      if (
        isEmpty(vendors) ||
        vendors.length !== vendorIds.length ||
        vendors.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return { result: false, messageError: 'error.VENDOR_IS_NOT_EXISTS' };
      }
    }

    if (!isEmpty(productionProcessIds)) {
      const productionProcesses =
        await this.productionProcessService.findAllByIds(productionProcessIds);

      if (
        isEmpty(productionProcesses) ||
        productionProcesses.length !== productionProcessIds.length ||
        productionProcesses.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.PRODUCTION_PROCESS_IS_NOT_EXISTS',
        };
      }
    }

    if (checksheetIds && checksheetIds.length > 0) {
      const checksheets = await this.checksheetService.findAllByIds(
        checksheetIds,
      );
      if (
        isEmpty(checksheets) ||
        checksheets.length !== checksheetIds.length ||
        checksheets.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.CHECKSHEET_IS_NOT_EXISTS',
        };
      }
    }

    if (inspectionStandardIds && inspectionStandardIds.length > 0) {
      const inspectionStandards =
        await this.inspectionStandardService.findAllByIds(
          inspectionStandardIds,
        );
      if (
        isEmpty(inspectionStandards) ||
        inspectionStandards.length !== inspectionStandardIds.length ||
        inspectionStandards.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.INSPECTION_STANDARD_IS_NOT_EXISTS',
        };
      }
    }

    if (samplingRateIds && samplingRateIds.length > 0) {
      const samplingRates = await this.samplingRateService.findAllByIds(
        samplingRateIds,
      );
      if (
        isEmpty(samplingRates) ||
        samplingRates.length !== samplingRateIds.length ||
        samplingRates.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.SAMPLING_RATE_IS_NOT_EXISTS',
        };
      }
    }
    if (goodsTypeIds && goodsTypeIds.length > 0) {
      const goodsTypeList = await this.goodsTypeService.findAllByIds(
        goodsTypeIds,
      );
      if (
        isEmpty(goodsTypeList) ||
        goodsTypeList.length !== goodsTypeIds.length ||
        goodsTypeList.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.GOODS_TYPE_IS_NOT_EXISTS',
        };
      }
    }

    if (!isEmpty(customerIds)) {
      const customers = await this.customerService.findAllByIds(customerIds);
      if (
        isEmpty(customers) ||
        customers.length !== customerIds.length ||
        customers.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.CUSTOMER_IS_NOT_EXISTS',
        };
      }
    }

    if (!isEmpty(departmentIds)) {
      const departments = await this.userService.getListRoomByIds(
        departmentIds,
      );
      if (
        isEmpty(departments) ||
        departments.length !== departmentIds.length ||
        departments.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.DEPARTMENT_IS_NOT_EXISTS',
        };
      }
    }

    if (!isEmpty(inspectionByIds)) {
      const inspectors = await this.userService.getListUserByIds({
        userIds: inspectionByIds,
      });
      if (
        isEmpty(inspectors) ||
        inspectors.length !== inspectionByIds.length ||
        inspectors.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.INPECTION_BY_IS_NOT_EXISTS',
        };
      }
    }

    if (!isEmpty(pIds)) {
      const pCodes = await this.masterDataReferenceService.findAllPCodeByIds(
        pIds,
      );
      if (
        isEmpty(pCodes) ||
        pCodes.length !== pIds.length ||
        pCodes.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.P_CODE_IS_NOT_EXISTS',
        };
      }
    }

    if (checksheetDetailIds && checksheetDetailIds.length > 0) {
      const checksheetDetails = await this.checksheetDetailService.findAllByIds(
        checksheetDetailIds,
      );
      if (
        isEmpty(checksheetDetails) ||
        checksheetDetails.length !== checksheetDetailIds.length
      ) {
        return {
          result: false,
          messageError: 'error.CHECKSHEET_DETAIL_IS_NOT_EXISTS',
        };
      }
      if (
        !isEmpty(checksheetIds) &&
        checksheetDetails.some((s) => !checksheetIds.includes(+s.checksheetId))
      ) {
        return {
          result: false,
          messageError: 'error.CHECKSHEET_DETAIL_IS_NOT_EXISTS_IN_CHECKSHEET',
        };
      }
    }

    if (errorIds && errorIds.length > 0) {
      const errors = await this.errorService.findAllByIds(errorIds);
      if (
        isEmpty(errors) ||
        errors.length !== errorIds.length ||
        errors.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.ERROR_IS_NOT_EXISTS',
        };
      }
    }

    if (errorHandlingStatusIds && errorHandlingStatusIds.length > 0) {
      const errorHandlingStatuses =
        await this.errorHandlingService.findAllByIds(errorHandlingStatusIds);
      if (
        isEmpty(errorHandlingStatuses) ||
        errorHandlingStatuses.length !== errorHandlingStatusIds.length ||
        errorHandlingStatuses.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.ERROR_HANDLING_STATUS_IS_NOT_EXISTS',
        };
      }
    }

    if (!isEmpty(itemTypeIds)) {
      const itemTypes =
        await this.masterDataReferenceService.findAllItemTypeByIds(itemTypeIds);
      if (
        isEmpty(itemTypes) ||
        itemTypes.length !== itemTypeIds.length ||
        itemTypes.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.ITEM_TYPE_IS_NOT_EXISTS',
        };
      }
    }

    if (!isEmpty(criteriaTypeIds)) {
      const criteriaTypes = await this.vendorService.findAllCriteriaByIds(
        criteriaTypeIds,
      );
      if (
        isEmpty(criteriaTypes) ||
        criteriaTypes.length !== criteriaTypeIds.length ||
        criteriaTypes.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.CRITERIA_TYPE_IS_NOT_EXISTS',
        };
      }
    }
    if (!isEmpty(scoreBoardIds)) {
      const scoreBoards = await this.vendorService.findAllScoreBoardByIds(
        scoreBoardIds,
      );
      if (
        isEmpty(scoreBoards) ||
        scoreBoards.length !== scoreBoardIds.length ||
        scoreBoards.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.SCORE_BOARD_IS_NOT_EXISTS',
        };
      }
    }

    if (unitIds && unitIds.length > 0) {
      const units = await this.unitService.findAllByIds(unitIds);
      if (
        isEmpty(units) ||
        units.length !== unitIds.length ||
        units.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return { result: false, messageError: 'error.UNIT_IS_NOT_EXISTS' };
      }
    }

    if (chemicalIds && chemicalIds.length > 0) {
      const chemicals =
        await this.masterDataReferenceService.findAllChemicalByIds(chemicalIds);
      if (
        isEmpty(chemicals) ||
        chemicals.length !== chemicalIds.length ||
        chemicals.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return { result: false, messageError: 'error.CHEMICAL_IS_NOT_EXISTS' };
      }
    }

    if (deviceIds && deviceIds.length > 0) {
      const devices = await this.mmsDeviceService.getDeviceByIdsInternal({
        ids: deviceIds,
      });
      if (
        isEmpty(devices) ||
        devices.length !== deviceIds.length ||
        devices.some(
          (s) =>
            s.status !== DEVICE_STATUS_ENUM.BACKUP &&
            s.status !== DEVICE_STATUS_ENUM.USING,
        )
      ) {
        return { result: false, messageError: 'error.DEVICE_IS_NOT_EXISTS' };
      }
    }

    if (equipmentIds && equipmentIds.length > 0) {
      const devices = await this.mmsDeviceService.getDeviceByIdsInternal({
        ids: equipmentIds,
      });
      if (
        isEmpty(devices) ||
        devices.length !== equipmentIds.length ||
        devices.some(
          (s) =>
            s.status !== DEVICE_STATUS_ENUM.BACKUP &&
            s.status !== DEVICE_STATUS_ENUM.USING,
        )
      ) {
        return { result: false, messageError: 'error.DEVICE_IS_NOT_EXISTS' };
      }
    }

    if (aqlSampleLimitIds && aqlSampleLimitIds.length > 0) {
      const aqlSampleLimits =
        await this.masterDataReferenceService.findAllAqlSampleLimitByIds(
          aqlSampleLimitIds,
        );
      if (
        isEmpty(aqlSampleLimits) ||
        aqlSampleLimits.length !== aqlSampleLimitIds.length ||
        aqlSampleLimits.some((s) => s.status === StatusEnum.IN_ACTIVE)
      ) {
        return {
          result: false,
          messageError: 'error.AQL_SAMPLE_LIMIT_IS_NOT_EXISTS',
        };
      }
    }

    if (ruleSpcIds && ruleSpcIds.length > 0) {
      const ruleSpcs =
        await this.masterDataReferenceService.findAllRuleSpcByIds(ruleSpcIds);

      const isInvalid =
        isEmpty(ruleSpcs) ||
        ruleSpcs.length !== ruleSpcIds.length ||
        ruleSpcs.some((s) => [0, 2, 3].includes(s.status));

      if (isInvalid) {
        return {
          result: false,
          messageError: 'error.RULE_SPC_IS_NOT_VALID',
        };
      }
    }

    return { result: true, data: mapData };
  }

  hasDuplicates = (items: any[]): boolean => {
    const seen = new Set<string>();
    return items.some((item) => {
      const key = `${item.lotNo}-${item.itemId}-${item.processId}`;
      return seen.has(key) ? true : (seen.add(key), false);
    });
  };

  checkDuplicateByKey = (items: any[], keys: string[]): boolean => {
    const seen = new Set<string>();
    return items.some((item) => {
      const key = keys.map((k) => item[k]).join('-');
      return seen.has(key) ? true : (seen.add(key), false);
    });
  };

  async getInfoItemByIds(itemIds: number[]): Promise<any[]> {
    return await this.itemService.findAllByIds(itemIds);
  }

  async getChemicalDetail(chemicalId: number): Promise<ChemicalEntity> {
    const request = new GetDetailChemicalRequestDto();
    request.id = chemicalId;
    return await this.masterDataReferenceService.getChemicalDetail(request);
  }

  convertToDateYYYYMMDD(input: Date | string): Date {
    const date = new Date(input);
    const utcYear = date.getUTCFullYear();
    const utcMonth = date.getUTCMonth();
    const utcDate = date.getUTCDate();
    return new Date(Date.UTC(utcYear, utcMonth, utcDate));
  }

  async validateDataMasterUseAnotherService(request: ValidateMasterRequestDto) {
    if (await this.qmsxTicketService.validateMaster(request)) {
      return true;
    } else if (await this.qmsxPlanService.validateMaster(request)) {
      return true;
    } else if (await this.qmsxRequestService.validateMaster(request)) {
      return true;
    }
    return false;
  }

  async getProcessChemicalCount(chemicalId: number): Promise<number> {
    return await this.masterDataReferenceService.getProcessChemicalCount(
      chemicalId,
    );
  }

  async getInspectionGroupByInspectionTypeCodes(
    inspectionTypeCodes: string[],
  ): Promise<number> {
    return await this.inspectionGroupService.getInspectionGroupByInspectionTypeCodes(
      inspectionTypeCodes,
    );
  }

  async mapMasterInfoForAllOfKey(data: any[]): Promise<any[]> {
    if (isEmpty(data)) {
      return data;
    }

    const mapMasterInfo = async (obj: any) => {
      if (Array.isArray(obj)) {
        const results = await Promise.all(
          obj.map((item) => this.mapMasterInfoToResponse([item])),
        );
        return results.reduce((acc, val) => acc.concat(val), []);
      } else if (typeof obj === 'object' && obj != null) {
        const entries = await Promise.all(
          Object.entries(obj).map(async ([key, value]) => {
            if (
              Array.isArray(value) ||
              (typeof value === 'object' && value != null)
            ) {
              return [key, await mapMasterInfo(value)];
            }
            return [key, value];
          }),
        );
        return Object.fromEntries(entries);
      }
      return obj;
    };
    const dataMap = await Promise.all(data.map((item) => mapMasterInfo(item)));
    return await this.mapMasterInfoToResponse(dataMap);
  }

  async mapDeviceInfoByCodes(
    data: any[],
    deviceCodes: string[],
  ): Promise<any[]> {
    const devices = await this.mmsDeviceService.getDeviceByCodes({
      codes: deviceCodes,
    });

    const deviceMap = new Map<string, any>();
    for (const device of devices) {
      deviceMap.set(device.code, device);
    }

    for (const item of data) {
      if (item.deviceCode && deviceMap.has(item.deviceCode)) {
        item.device = deviceMap.get(item.deviceCode);
      } else {
        item.device = null;
      }
    }

    return data;
  }

  checkRule1X(avgValues: number[], UCL: number, LCL: number): boolean {
    for (const value of avgValues) {
      if (value > UCL || value < LCL) {
        return false;
      }
    }
    return true;
  }

  checkRule1R(avgValues: number[], UCL: number, LCL: number): boolean {
    for (const value of avgValues) {
      if (value > UCL || value < LCL) {
        return false;
      }
    }
    return true;
  }

  checkRule2(avgValues: number[]): boolean {
    for (let i = 0; i <= avgValues.length - 6; i++) {
      let isIncreasing = true;
      let isDecreasing = true;
      for (let j = i; j < i + 5; j++) {
        if (avgValues[j] >= avgValues[j + 1]) {
          isIncreasing = false;
        }
        if (avgValues[j] <= avgValues[j + 1]) {
          isDecreasing = false;
        }
      }
      if (isIncreasing || isDecreasing) {
        return false;
      }
    }
    return true;
  }

  checkRule3(
    avgValues: number[],
    UCL: number,
    LCL: number,
    CL: number,
  ): boolean {
    for (let i = 0; i <= avgValues.length - 5; i++) {
      let count = 0;
      for (let j = i; j < i + 5; j++) {
        const Ai = avgValues[j];
        const condition1 = Ai > CL + (UCL - CL) / 3 && Ai < UCL;
        const condition2 = Ai < CL - (CL - LCL) / 3 && Ai > LCL;
        if (condition1 || condition2) {
          count++;
        }
      }
      if (count >= 4) {
        return false;
      }
    }
    return true;
  }

  checkRule4(
    avgValues: number[],
    UCL: number,
    LCL: number,
    CL: number,
  ): boolean {
    for (let i = 0; i <= avgValues.length - 8; i++) {
      let allPointsValid = true;
      for (let j = i; j < i + 8; j++) {
        const value = avgValues[j];
        const condition1 = UCL > value && value > CL + (UCL - CL) / 3;
        const condition2 = LCL < value && value < CL - (CL - LCL) / 3;
        if (!condition1 && !condition2) {
          allPointsValid = false;
          break;
        }
      }
      if (allPointsValid) {
        return true;
      }
    }
    return false;
  }

  checkRule5(
    avgValues: number[],
    UCL: number,
    LCL: number,
    CL: number,
  ): boolean {
    for (let i = 0; i <= avgValues.length - 9; i++) {
      let allPointsValid = true;
      for (let j = i; j < i + 9; j++) {
        const value = avgValues[j];
        const condition1 = UCL > value && value > CL;
        const condition2 = LCL < value && value < CL;
        if (!condition1 && !condition2) {
          allPointsValid = false;
          break;
        }
      }
      if (allPointsValid) {
        return true;
      }
    }
    return false;
  }

  checkRule6(avgValues: number[]): boolean {
    for (let i = 0; i <= avgValues.length - 14; i++) {
      let condition1 = true;
      let condition2 = true;
      for (let j = i; j < i + 13; j++) {
        const current = avgValues[j];
        const next = avgValues[j + 1];
        if (j % 2 === 0) {
          if (current <= next) {
            condition1 = false;
          }
        } else {
          if (current >= next) {
            condition1 = false;
          }
        }
        if (j % 2 === 0) {
          if (current >= next) {
            condition2 = false;
          }
        } else {
          if (current <= next) {
            condition2 = false;
          }
        }
      }
      if (condition1 || condition2) {
        return true;
      }
    }
    return false;
  }

  checkRule7(
    avgValues: number[],
    UCL: number,
    LCL: number,
    CL: number,
  ): boolean {
    for (let i = 0; i <= avgValues.length - 3; i++) {
      let count = 0;
      for (let j = i; j < i + 3; j++) {
        const value = avgValues[j];
        const TH1_lower = CL + (2 * (UCL - CL)) / 3;
        const TH2_upper = CL - (2 * (CL - LCL)) / 3;
        if (UCL > value && value > TH1_lower) {
          count++;
        }
        if (LCL < value && value < TH2_upper) {
          count++;
        }
      }
      if (count >= 2) {
        return true;
      }
    }
    return false;
  }

  checkRule8(
    avgValues: number[],
    UCL: number,
    LCL: number,
    CL: number,
  ): boolean {
    const lowerLimit = CL - (CL - LCL) / 3;
    const upperLimit = CL + (UCL - CL) / 3;
    for (let i = 0; i <= avgValues.length - 15; i++) {
      const valid = avgValues.slice(i, i + 15).every((value) => {
        return value > lowerLimit && value < upperLimit;
      });
      if (valid) {
        return true;
      }
    }
    return false;
  }

  checkRule(data: any, rules: number[], UCL, LCL, CL) {
    const { results } = data;

    const avgValues = results.map((r) => r.avg ?? 0);
    const failedRules: string[] = [];

    const rulesToCheck = rules.length > 0 ? rules : [1, 2, 3, 4, 5, 6, 7, 8, 9];

    for (const rule of rulesToCheck) {
      switch (rule) {
        case 1:
          if (!this.checkRule1X(avgValues, UCL, LCL)) failedRules.push('1X');
          break;
        case 2:
          if (!this.checkRule1R(avgValues, UCL, LCL)) failedRules.push('1R');
          break;
        case 3:
          if (!this.checkRule2(avgValues)) failedRules.push('2');
          break;
        case 4:
          if (!this.checkRule3(avgValues, UCL, LCL, CL)) failedRules.push('3');
          break;
        case 5:
          if (!this.checkRule4(avgValues, UCL, LCL, CL)) failedRules.push('4');
          break;
        case 6:
          if (!this.checkRule5(avgValues, UCL, LCL, CL)) failedRules.push('5');
          break;
        case 7:
          if (!this.checkRule6(avgValues)) failedRules.push('6');
          break;
        case 8:
          if (!this.checkRule7(avgValues, UCL, LCL, CL)) failedRules.push('7');
          break;
        case 9:
          if (!this.checkRule8(avgValues, UCL, LCL, CL)) failedRules.push('8');
          break;
      }
    }

    const resultSpc = failedRules.length === 0;

    return {
      ...data,
      resultSpc,
      spcNg: failedRules.join('/'),
    };
  }

  transformDataSpcReport(data: any[]) {
    const newData = [];

    data.forEach((item) => {
      let existingItem = newData.find(
        (obj) =>
          obj.itemId === item.itemId &&
          obj.lotNo === item.lotNo &&
          obj.checksheetId === item.checksheetId &&
          obj.checksheetDetailId === item.checksheetDetailId &&
          obj.deviceId === item.deviceId,
      );

      if (!existingItem) {
        existingItem = {
          itemId: item.itemId,
          lotNo: item.lotNo,
          checksheetId: item.checksheetId,
          checksheetDetailId: item.checksheetDetailId,
          deviceId: item.deviceId,
          results: [],
        };
        newData.push(existingItem);
      }

      const valuesArray = item.values
        ? item.values.split(',').map((val) => parseFloat(val.trim()))
        : [];

      const avgValue =
        valuesArray.length > 0
          ? parseFloat(
              (
                valuesArray.reduce((sum, val) => sum + val, 0) /
                valuesArray.length
              ).toFixed(3),
            )
          : null;

      existingItem.results.push({
        approvedAt: item.approvedAt,
        values: valuesArray,
        avg: avgValue,
      });
    });

    newData.forEach((item) => {
      item.results.sort(
        (a, b) =>
          new Date(a.approvedAt).getTime() - new Date(b.approvedAt).getTime(),
      );

      const allValues = item.results.flatMap((r) => r.values);

      if (allValues.length > 0) {
        const avg =
          allValues.reduce((sum, val) => sum + val, 0) / allValues.length;
        const min = Math.min(...allValues);
        const max = Math.max(...allValues);

        item.avg = parseFloat(avg.toFixed(3));
        item.min = parseFloat(min.toFixed(3));
        item.max = parseFloat(max.toFixed(3));
      } else {
        item.avg = null;
        item.min = null;
        item.max = null;
      }
    });

    return newData;
  }

  round(value: number, digits = 3): number {
    return parseFloat(value.toFixed(digits));
  }

  transformSpcDataChart(
    data: any[],
    avgSubGroupSize,
    avgResultValue,
    maxResultValue,
    minResultValue,
    stdDev,
    a2,
    d4,
    d3,
    d2,
  ) {
    const hasValidSigmaParams =
      a2 !== null && d2 !== null && d3 !== null && d4 !== null;

    return data.map((item) => {
      const values = item.values
        .split(',')
        .map(Number)
        .filter((v) => !isNaN(v));

      const USL = this.round(
        item.checksheetDetail.spec + item.checksheetDetail.plusAbove,
      );
      const LSL = this.round(
        item.checksheetDetail.spec - item.checksheetDetail.minusBelow,
      );

      const avgValue = this.round(
        values.reduce((a, b) => a + b, 0) / values.length,
      );

      const maxValue = this.round(maxResultValue);
      const minValue = this.round(minResultValue);
      const valueDiff = this.round((USL + LSL - 2 * avgValue) / (USL - LSL), 3);

      let CLx = null,
        UCLx = null,
        LCLx = null;
      let CLr = null,
        UCLr = null,
        LCLr = null;
      let overallStdDev = null,
        groupStdDev = null;

      let Cpu = null,
        Cpl = null,
        Cp = null,
        Cpk = null;
      let Ppu = null,
        Ppl = null,
        Pp = null,
        Ppk = null;

      if (hasValidSigmaParams) {
        CLx = this.round(avgResultValue);
        UCLx = this.round(avgResultValue + a2 * avgSubGroupSize);
        LCLx = this.round(avgResultValue - a2 * avgSubGroupSize);

        CLr = this.round(avgSubGroupSize);
        UCLr = this.round(d4 * avgSubGroupSize);
        LCLr = this.round(d3 * avgSubGroupSize);

        overallStdDev = this.round(stdDev);
        groupStdDev = this.round(avgSubGroupSize / d2);

        Cpu = this.round((USL - avgResultValue) / (3 * groupStdDev));
        Cpl = this.round((avgResultValue - LSL) / (3 * groupStdDev));
        Cp = this.round((USL - LSL) / (6 * groupStdDev));
        Cpk = Math.min(Cpu, Cpl);

        Ppu = this.round((USL - avgResultValue) / (3 * overallStdDev));
        Ppl = this.round((avgResultValue - LSL) / (3 * overallStdDev));
        Pp = this.round((USL - LSL) / (6 * overallStdDev));
        Ppk = Math.min(Ppu, Ppl);
      }

      return {
        ...item,
        values: values,
        avgValue,
        CLx,
        UCLx,
        LCLx,
        CLr,
        UCLr,
        LCLr,
        overallStdDev,
        groupStdDev,
        maxValue,
        minValue,
        USL,
        LSL,
        valueDiff,
        Cpu,
        Cpl,
        Cp,
        Cpk,
        Ppu,
        Ppl,
        Pp,
        Ppk,
      };
    });
  }

  checkRuleForBarChart(
    data: any[],
    rules: number[],
    UCL: number,
    LCL: number,
    CL: number,
    groupStdDev: number,
  ) {
    const rulesToCheck = rules.length > 0 ? rules : [1, 3, 4, 5, 6, 7, 8, 9];

    // Làm tròn avgValue và gắn sẵn failedRules
    data.forEach((item) => {
      item.avgValue = this.round(item.avgValue);
      item.failedRules = [];
    });

    for (let i = 0; i < data.length; i++) {
      const A = data[i];

      for (const rule of rulesToCheck) {
        switch (rule) {
          case 1:
            if (A.avgValue > UCL || A.avgValue < LCL) {
              A.failedRules.push('1');
            }
            break;

          case 3:
            if (i + 5 < data.length) {
              const values = data.slice(i, i + 6).map((d) => d.avgValue);
              const increasing = values.every(
                (v, idx, arr) => idx === 0 || arr[idx] > arr[idx - 1],
              );
              const decreasing = values.every(
                (v, idx, arr) => idx === 0 || arr[idx] < arr[idx - 1],
              );
              if (increasing || decreasing) {
                for (let j = i; j < i + 6; j++) {
                  data[j].failedRules.push('2');
                }
              }
            }
            break;

          case 4:
            if (i + 4 < data.length) {
              const group = data.slice(i, i + 5);
              const countUpper = group.filter(
                (v) => v.avgValue > CL + groupStdDev && v.avgValue < UCL,
              ).length;
              const countLower = group.filter(
                (v) => v.avgValue < CL - groupStdDev && v.avgValue > LCL,
              ).length;
              if (countUpper >= 4 || countLower >= 4) {
                group.forEach((d) => d.failedRules.push('3'));
              }
            }
            break;

          case 5:
            if (i + 7 < data.length) {
              const group = data.slice(i, i + 8);
              const allUpper = group.every(
                (v) => v.avgValue > CL + groupStdDev && v.avgValue < UCL,
              );
              const allLower = group.every(
                (v) => v.avgValue < CL - groupStdDev && v.avgValue > LCL,
              );
              if (allUpper || allLower) {
                group.forEach((d) => d.failedRules.push('4'));
              }
            }
            break;

          case 6:
            if (i + 8 < data.length) {
              const group = data.slice(i, i + 9);
              const allAboveCL = group.every(
                (v) => v.avgValue > CL && v.avgValue < UCL,
              );
              const allBelowCL = group.every(
                (v) => v.avgValue < CL && v.avgValue > LCL,
              );
              if (allAboveCL || allBelowCL) {
                group.forEach((d) => d.failedRules.push('5'));
              }
            }
            break;

          case 7:
            if (i + 13 < data.length) {
              const values = data.slice(i, i + 14).map((d) => d.avgValue);
              const zigzagUpDown = values.every((v, idx, arr) =>
                idx < arr.length - 1
                  ? idx % 2 === 0
                    ? arr[idx] > arr[idx + 1]
                    : arr[idx] < arr[idx + 1]
                  : true,
              );
              const zigzagDownUp = values.every((v, idx, arr) =>
                idx < arr.length - 1
                  ? idx % 2 === 0
                    ? arr[idx] < arr[idx + 1]
                    : arr[idx] > arr[idx + 1]
                  : true,
              );
              if (zigzagUpDown || zigzagDownUp) {
                for (let j = i; j < i + 14; j++) {
                  data[j].failedRules.push('6');
                }
              }
            }
            break;

          case 8:
            if (i + 2 < data.length) {
              const group = data.slice(i, i + 3);
              const countUpper = group.filter(
                (v) => v.avgValue > CL + 2 * groupStdDev && v.avgValue < UCL,
              ).length;
              const countLower = group.filter(
                (v) => v.avgValue < CL - 2 * groupStdDev && v.avgValue > LCL,
              ).length;
              if (countUpper >= 2 || countLower >= 2) {
                group.forEach((d) => d.failedRules.push('7'));
              }
            }
            break;

          case 9:
            if (i + 14 < data.length) {
              const group = data.slice(i, i + 15);
              const allInZone = group.every(
                (v) =>
                  v.avgValue > CL - groupStdDev &&
                  v.avgValue < CL + groupStdDev &&
                  v.avgValue > LCL &&
                  v.avgValue < UCL,
              );
              if (allInZone) {
                group.forEach((d) => d.failedRules.push('8'));
              }
            }
            break;
        }
      }
    }

    data.forEach((item) => {
      item.spcNg = item.failedRules.join('/');
      delete item.failedRules;
    });

    return data;
  }

  normalDistribution(x: number, mean: number, sigma: number): number {
    const pi = Math.PI;
    const exponent = -((x - mean) ** 2) / (2 * sigma ** 2);
    const fx = (1 / (sigma * Math.sqrt(2 * pi))) * Math.exp(exponent);
    return fx;
  }

  generateDistributionData(
    avgValues: number[],
    avgResultValue: number,
    groupStdDev: number,
    overallStdDev: number,
  ) {
    return avgValues.map((value) => ({
      red: this.round(
        this.normalDistribution(value, avgResultValue, groupStdDev),
      ),
      black: this.round(
        this.normalDistribution(value, avgResultValue, overallStdDev),
      ),
    }));
  }
}
