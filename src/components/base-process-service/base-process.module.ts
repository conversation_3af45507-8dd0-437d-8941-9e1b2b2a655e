import { Module } from '@nestjs/common';
import { ChecksheetModule } from '../../components/checksheet/checksheet.module';
import { ItemUnitModule } from '../../components/item-unit/item-unit.module';
import { ItemModule } from '../../components/item/item.module';
import { ProcessModule } from '../../components/process/process.module';
import { QcRequestTypeModule } from '../../components/qc-request-type/qc-request-type.module';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { ChecksheetDetailModule } from '../checksheet-detail/checksheet-detail.module';
import { CustomerModule } from '../customer/customer.module';
import { ErrorHandlingStatusModule } from '../error-handling-status/error-handling-status.module';
import { ErrorTypeModule } from '../error-type/error-type.module';
import { ErrorModule } from '../error/error.module';
import { GoodsTypeModule } from '../goods-type/goods-type.module';
import { InspectionGroupModule } from '../inspection-group/inspection-group.module';
import { InspectionStandardModule } from '../inspection-standard/inspection-standard.module';
import { InspectionTypeModule } from '../inspection-type/inspection-type.module';
import { InspectionModule } from '../inspection/inspection.module';
import { ItemLineModule } from '../item-line/item-line.module';
import { ItemTypeModule } from '../item-type/item-type.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { ProductionProcessModule } from '../production-process/production-process.module';
import { SamplingRateModule } from '../sampling-rate/sampling-rate.module';
import { UnitModule } from '../unit/unit.module';
import { BaseProcessRepositoy } from './base-process.repository';
import { BaseProcessService } from './base-process.service';

@Module({
  imports: [
    AnotherServiceModule,
    ItemModule,
    QcRequestTypeModule,
    ItemUnitModule,
    ProcessModule,
    ChecksheetModule,
    ProductionProcessModule,
    InspectionStandardModule,
    MasterDataReferenceModule,
    SamplingRateModule,
    GoodsTypeModule,
    CustomerModule,
    InspectionModule,
    ErrorModule,
    ErrorTypeModule,
    ChecksheetDetailModule,
    ErrorHandlingStatusModule,
    ItemTypeModule,
    UnitModule,
    ItemLineModule,
    InspectionGroupModule,
    InspectionTypeModule,
  ],
  providers: [BaseProcessService, BaseProcessRepositoy],
  exports: [BaseProcessService],
  controllers: [],
})
export class BaseProcessModule {}
