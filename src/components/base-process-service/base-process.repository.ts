import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

@Injectable()
export class BaseProcessRepositoy {
  constructor(
    @InjectDataSource()
    private readonly connection: DataSource,
  ) {}
  async findAllByCodeAndTableName(tableName: string, codes: string[]) {
    const result = [];
    const chunkSize = 2000;

    for (let i = 0; i < codes.length; i += chunkSize) {
      const chunk = codes.slice(i, i + chunkSize).map((code) => code.trim());

      const chunkResult = await this.connection
        .createQueryBuilder()
        .select('*')
        .from(tableName, 't')
        .where(`t.code IN (:...codes)`, { codes: chunk })
        .getRawMany();

      result.push(...chunkResult);
    }

    return result.map((item) => {
      const camelCaseItem = {};
      Object.keys(item).forEach((key) => {
        const camelCaseKey = key.replace(/_([a-z])/g, (_, letter) =>
          letter.toUpperCase(),
        );
        camelCaseItem[camelCaseKey] = item[key];
      });
      return camelCaseItem;
    });
  }

  async findAllByTableNameAndKeys(
    tableName: string,
    keys: string[],
    uniqueKeys: string[],
  ) {
    const result = [];

    const chunkSize = 2000;
    for (let i = 0; i < uniqueKeys.length; i += chunkSize) {
      const chunk = uniqueKeys.slice(i, i + chunkSize);

      const alias = `table_${i}`;

      const concatColumns =
        keys.length === 1
          ? `${alias}.${keys[0]}`
          : `CONCAT_WS('|', ${keys
              .map((key) => `${alias}.${key}`)
              .join(', ')})`;

      const whereClause = `${concatColumns} IN (:...uniqueKeys)`;

      const chunkResult = await this.connection
        .createQueryBuilder()
        .select('*')
        .from(tableName, alias)
        .where(whereClause, { uniqueKeys: chunk })
        .getRawMany();

      result.push(...chunkResult);
    }

    const camelCaseResult = result.map((item) => {
      const camelCaseItem = {};
      Object.keys(item).forEach((key) => {
        const camelCaseKey = key.replace(/_([a-z])/g, (match, letter) =>
          letter.toUpperCase(),
        );
        camelCaseItem[camelCaseKey] = item[key];
      });
      return camelCaseItem;
    });

    return camelCaseResult;
  }
}
