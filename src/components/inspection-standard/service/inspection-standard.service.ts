import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { isEmpty, keyBy } from 'lodash';
import { I18nService } from 'nestjs-i18n';
import { DataSource, In } from 'typeorm';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { InspectionStandardEntity } from '../entities/inspection-standard.entity';
import { CreateInspectionStandardRequestDto } from './../request/create-inspection-standard.request.dto';
import { DeleteInspectionStandardRequestDto } from './../request/delete-inspection-standard.request.dto';
import { GetDetailInspectionStandardRequestDto } from './../request/get-detail-inspection-standard.request.dto';
import { GetListInspectionStandardRequestDto } from './../request/get-list-inspection-standard.request.dto';
import { UpdateInspectionStandardRequestDto } from './../request/update-inspection-standard.request.dto';
import { UpdateStatusInspectionStandardRequestDto } from './../request/update-status-inspection-standard.request.dto';

import { GetDetailInspectionStandardResponseDto } from './../response/get-detail-inspection-standard.response.dto';
import { GetListInspectionStandardResponseDto } from './../response/get-list-inspection-standard.response.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { ValidateResultCommonDto } from '../../../common/dtos/validate.result.common.dto';
import { StatusEnum } from '../../../common/enums/status.enum';
import { BaseService } from '../../../common/service/base.service';
import { IS_AQL_ENUM } from '../../../constant/common';
import { UserService } from '../../another-service/services/user-service';
import { ItemQcService } from '../../item-qc/service/item-qc.service';
import { MasterDataReferenceService } from '../../master-data-reference/service/master-data-reference.service';
import { SamplingRateService } from '../../sampling-rate/service/sampling-rate.service';
import { InspectionStandardRepository } from '../repository/inspection-standard.repository';

@Injectable()
export class InspectionStandardService extends BaseService {
  private readonly logger = new Logger(InspectionStandardService.name);

  constructor(
    private readonly inspectionStandardRepository: InspectionStandardRepository,

    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly samplingRateService: SamplingRateService,

    private readonly itemQcService: ItemQcService,

    private readonly masterDataReferenceService: MasterDataReferenceService,
  ) {
    super(userService);
  }

  async create(request: CreateInspectionStandardRequestDto): Promise<any> {
    const existCode = await this.inspectionStandardRepository.findOneByCode(
      request.code,
    );
    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate inspection-standard
    const resultValidate = await this.validateSaveInspectionStandard(request);
    if (resultValidate?.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(resultValidate?.messageError))
        .build();
    }

    const inspectionStandardEntity =
      this.inspectionStandardRepository.createEntity(request);
    const inspectionStandard = await this.inspectionStandardRepository.create(
      inspectionStandardEntity,
    );

    const dataMapSamplingRate = await this.mapSamplingRateToResponse([
      inspectionStandard,
    ]);
    const dataMapUser = await this.mapUserInfoToResponse(dataMapSamplingRate);
    const response = plainToInstance(
      GetDetailInspectionStandardResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListInspectionStandardRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.inspectionStandardRepository.getList(
      request,
    );

    const dataMapUser = await this.mapUserInfoToResponse(data);
    const response = plainToInstance(
      GetListInspectionStandardResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(
    request: GetDetailInspectionStandardRequestDto,
  ): Promise<any> {
    const inspectionStandard =
      await this.inspectionStandardRepository.getDetail(request);
    if (isEmpty(inspectionStandard)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMapSamplingRate = await this.mapSamplingRateToResponse([
      inspectionStandard,
    ]);
    const dataMapUser = await this.mapUserInfoToResponse(dataMapSamplingRate);
    const response = plainToInstance(
      GetDetailInspectionStandardResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateInspectionStandardRequestDto): Promise<any> {
    const { id } = request;
    const inspectionStandard =
      await this.inspectionStandardRepository.findOneById(id);

    if (isEmpty(inspectionStandard)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate inspection-standard in used
    const usedList = await this.validateInspectionStandardInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.INSPECTION_STANDARD_IN_USED'),
        )
        .build();
    }

    // Validate Code unique
    const existCode = await this.inspectionStandardRepository.findOneByCode(
      request.code,
    );
    if (!isEmpty(existCode) && existCode.id !== inspectionStandard.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate inspection-standard
    const resultValidate = await this.validateSaveInspectionStandard(request);
    if (resultValidate?.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(resultValidate?.messageError))
        .build();
    }

    const dataUpdate = this.inspectionStandardRepository.updateEntity(
      request,
      inspectionStandard,
    );
    const data = await this.inspectionStandardRepository.update(dataUpdate);

    const dataMapSamplingRate = await this.mapSamplingRateToResponse([data]);
    const dataMapUser = await this.mapUserInfoToResponse(dataMapSamplingRate);
    const response = plainToInstance(
      GetDetailInspectionStandardResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteInspectionStandardRequestDto): Promise<any> {
    const { id } = request;
    const inspectionStandard =
      await this.inspectionStandardRepository.findOneById(id);

    if (isEmpty(inspectionStandard)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate inspection-standard in used
    const usedList = await this.validateInspectionStandardInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.INSPECTION_STANDARD_IN_USED'),
        )
        .build();
    }

    await this.inspectionStandardRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(
    request: UpdateStatusInspectionStandardRequestDto,
  ): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.inspectionStandardRepository.findAllByIds(
      ids,
    );

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.inspectionStandardRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(
    request: UpdateStatusInspectionStandardRequestDto,
  ): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.inspectionStandardRepository.findAllByIds(
      ids,
    );

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.inspectionStandardRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async findOneById(id: number): Promise<InspectionStandardEntity> {
    return await this.inspectionStandardRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<InspectionStandardEntity[]> {
    return await this.inspectionStandardRepository.findAllByIds(ids);
  }

  async mapSamplingRateToResponse(
    inspectionStandards: InspectionStandardEntity[],
  ) {
    const ids: number[] = Array.from(
      new Set(
        inspectionStandards
          .flatMap((item) => item.samplingRateId)
          .filter((id) => id !== null && id !== undefined),
      ),
    );

    const samplingRates = await this.samplingRateService.findAllByIds(ids);
    if (isEmpty(samplingRates)) return inspectionStandards;

    const samplingRateMap = keyBy(samplingRates, 'id');
    return (
      inspectionStandards?.map((item) => {
        return {
          ...item,
          samplingRate: {
            id: samplingRateMap[item.samplingRateId]?.id,
            code: samplingRateMap[item.samplingRateId]?.code,
            samplingRate: samplingRateMap[item.samplingRateId]?.samplingRate,
          },
        };
      }) || []
    );
  }

  async findAllBySamplingRateIds(
    ids: number[],
  ): Promise<InspectionStandardEntity[]> {
    const filterCondition: any = {
      samplingRateId: In(ids),
    };
    return await this.inspectionStandardRepository.findByCondition(
      filterCondition,
    );
  }

  async validateSaveInspectionStandard(
    request: CreateInspectionStandardRequestDto,
  ): Promise<ValidateResultCommonDto> {
    // Validate exist sampling-rates
    if (
      request?.samplingRateId !== null &&
      request?.samplingRateId !== undefined
    ) {
      const existSamplingRate = await this.samplingRateService.findOneById(
        request.samplingRateId,
      );
      if (
        isEmpty(existSamplingRate) ||
        existSamplingRate.status === StatusEnum.IN_ACTIVE
      ) {
        return {
          result: true,
          messageError: 'error.SAMPLING_RATE_IS_NOT_EXISTS',
        };
      }
    }

    // Validate inspection_standard_type is not null
    if (
      request.isAql === IS_AQL_ENUM.YES &&
      (request?.inpectionStandardType === null ||
        request?.inpectionStandardType === undefined)
    ) {
      return {
        result: true,
        messageError: 'error.INSPECTION_STANDARD_TYPE_IS_NOT_NULL',
      };
    }

    return { result: false, messageError: '' };
  }

  async validateInspectionStandardInUsed(id: number): Promise<boolean> {
    // Validate used in item-qcs
    const itemQcList = await this.itemQcService.findAllByInspectionStandardIds([
      id,
    ]);
    if (!isEmpty(itemQcList)) {
      return true;
    }

    // Validate used in inspection-sample-symbol-details
    const inspectionSampleSymbols =
      await this.masterDataReferenceService.findAllIssByInspectionStandardIds([
        id,
      ]);
    if (!isEmpty(inspectionSampleSymbols)) {
      return true;
    }

    // Validate used in another service
    const isUse =
      await this.masterDataReferenceService.validateDataMasterUseAnotherService(
        new ValidateMasterRequestDto('inspection_standard_id', id),
      );
    if (isUse) {
      return true;
    }

    return false;
  }
}
