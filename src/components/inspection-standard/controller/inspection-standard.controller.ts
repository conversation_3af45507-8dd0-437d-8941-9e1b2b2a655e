import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  ACTIVE_INSPECTION_STANDARD_PERMISSION,
  CREATE_INSPECTION_STANDARD_PERMISSION,
  DELETE_INSPECTION_STANDARD_PERMISSION,
  DETAIL_INSPECTION_STANDARD_PERMISSION,
  INACTIVE_INSPECTION_STANDARD_PERMISSION,
  LIST_INSPECTION_STANDARD_PERMISSION,
  UPDATE_INSPECTION_STANDARD_PERMISSION,
} from '../../../utils/permissions/inspection-standard.permission';
import { CreateInspectionStandardRequestDto } from '../request/create-inspection-standard.request.dto';
import { DeleteInspectionStandardRequestDto } from '../request/delete-inspection-standard.request.dto';
import { GetDetailInspectionStandardRequestDto } from '../request/get-detail-inspection-standard.request.dto';
import { GetListInspectionStandardRequestDto } from '../request/get-list-inspection-standard.request.dto';
import { UpdateInspectionStandardRequestDto } from '../request/update-inspection-standard.request.dto';
import { UpdateStatusInspectionStandardRequestDto } from '../request/update-status-inspection-standard.request.dto';
import { InspectionStandardService } from '../service/inspection-standard.service';

@Controller('inspection-standards')
export class InspectionStandardController {
  constructor(
    private readonly inspectionStandardService: InspectionStandardService,
  ) {}

  @PermissionCode(DETAIL_INSPECTION_STANDARD_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Inspection-standards'],
    summary: 'Chi tiết Inspection-standards',
    description: 'Chi tiết Inspection-standards',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailInspectionStandardRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionStandardService.getDetail(request);
  }

  @PermissionCode(LIST_INSPECTION_STANDARD_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Inspection-standards'],
    summary: 'Danh sách Inspection-standards',
    description: 'Danh sách Inspection-standards',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListInspectionStandardRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionStandardService.getList(request);
  }

  @PermissionCode(CREATE_INSPECTION_STANDARD_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Inspection-standards'],
    summary: 'Tạo Inspection-standards mới',
    description: 'Tạo Inspection-standards mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateInspectionStandardRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionStandardService.create(request);
  }

  @PermissionCode(UPDATE_INSPECTION_STANDARD_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Inspection-standards'],
    summary: 'Cập nhật Inspection-standards',
    description: 'Cập nhật Inspection-standards',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateInspectionStandardRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionStandardService.update(request);
  }

  @PermissionCode(ACTIVE_INSPECTION_STANDARD_PERMISSION.code)
  @Put('/active')
  @ApiOperation({
    tags: ['Inspection-standards'],
    summary: 'Cập nhật Inspection-standards Status Active',
    description: 'Cập nhật Inspection-standards Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async active(
    @Body() body: UpdateStatusInspectionStandardRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionStandardService.active(request);
  }

  @PermissionCode(INACTIVE_INSPECTION_STANDARD_PERMISSION.code)
  @Put('/inactive')
  @ApiOperation({
    tags: ['Inspection-standards'],
    summary: 'Cập nhật Inspection-standards Status Inactive',
    description: 'Cập nhật Inspection-standards Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async inactive(
    @Body() body: UpdateStatusInspectionStandardRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionStandardService.inactive(request);
  }

  @PermissionCode(DELETE_INSPECTION_STANDARD_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Inspection-standards'],
    summary: 'Xóa Inspection-standards',
    description: 'Xóa Inspection-standards',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(
    @Param() param: DeleteInspectionStandardRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionStandardService.delete(request);
  }
}
