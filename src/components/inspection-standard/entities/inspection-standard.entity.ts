import { Column, Entity } from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';

@Entity({ name: 'inspection_standards' })
export class InspectionStandardEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    unique: true,
    nullable: false,
    length: 255,
  })
  code: string;

  @Column({
    type: 'nvarchar',
    nullable: false,
    length: 255,
  })
  name: string;

  @Column({
    type: 'tinyint',
    nullable: false,
  })
  isAql: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  inpectionStandardType: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  samplingRateId: number;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  description: string;

  @Column({
    type: 'tinyint',
  })
  status: number;
}
