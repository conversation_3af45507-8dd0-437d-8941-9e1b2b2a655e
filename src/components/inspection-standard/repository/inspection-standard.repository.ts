import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { isDateString } from 'class-validator';
import { Repository } from 'typeorm';
import { StatusEnum } from '../../../common/enums/status.enum';

import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import {
  escapeCharForSearch,
  parseJSONValueField,
} from '../../../utils/common';
import { InspectionStandardEntity } from '../entities/inspection-standard.entity';
import { GetDetailInspectionStandardRequestDto } from '../request/get-detail-inspection-standard.request.dto';
import { GetListInspectionStandardRequestDto } from '../request/get-list-inspection-standard.request.dto';
import { UpdateInspectionStandardRequestDto } from '../request/update-inspection-standard.request.dto';
import { UpdateStatusInspectionStandardRequestDto } from '../request/update-status-inspection-standard.request.dto';
import { CreateInspectionStandardRequestDto } from './../request/create-inspection-standard.request.dto';

@Injectable()
export class InspectionStandardRepository extends BaseAbstractRepository<InspectionStandardEntity> {
  constructor(
    @InjectRepository(InspectionStandardEntity)
    private readonly inspectionStandardRepository: Repository<InspectionStandardEntity>,
  ) {
    super(inspectionStandardRepository);
  }

  createEntity(
    request: CreateInspectionStandardRequestDto,
  ): InspectionStandardEntity {
    const inspectionStandardEntity = new InspectionStandardEntity();
    inspectionStandardEntity.code = request.code;
    inspectionStandardEntity.name = request.name;
    inspectionStandardEntity.isAql = request.isAql;
    inspectionStandardEntity.samplingRateId = request.samplingRateId;
    inspectionStandardEntity.inpectionStandardType =
      request.inpectionStandardType;
    inspectionStandardEntity.description = request.description;
    inspectionStandardEntity.status = StatusEnum.ACTIVE;
    inspectionStandardEntity.createdBy = request.userId;
    inspectionStandardEntity.updatedBy = request.userId;

    return inspectionStandardEntity;
  }

  updateEntity(
    request: UpdateInspectionStandardRequestDto,
    entity: InspectionStandardEntity,
  ): InspectionStandardEntity {
    entity.name = request?.name;
    entity.isAql = request.isAql;
    entity.samplingRateId = request.samplingRateId;
    entity.inpectionStandardType = request.inpectionStandardType;
    entity.description = request?.description;
    entity.updatedBy = request?.userId;

    return entity;
  }

  async getList(request: GetListInspectionStandardRequestDto): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.inspectionStandardRepository
      .createQueryBuilder('inspectionstandard')
      .select([
        'inspectionstandard.id AS id',
        'inspectionstandard.code AS "code"',
        'inspectionstandard.name AS "name"',
        'inspectionstandard.is_aql AS "isAql"',
        'inspectionstandard.inpection_standard_type AS "inpectionStandardType"',
        'inspectionstandard.sampling_rate_id AS "samplingRateId"',
        'inspectionstandard.description AS "description"',
        'inspectionstandard.status AS "status"',
        'inspectionstandard.created_by AS "createdBy"',
        'inspectionstandard.created_at AS "createdAt"',
        'inspectionstandard.updated_by AS "updatedBy"',
        'inspectionstandard.updated_at AS "updatedAt"',
      ])
      .leftJoin(
        'sampling_rates',
        'sr',
        'inspectionstandard.sampling_rate_id = sr.id',
      )
      .addSelect([
        `CASE WHEN COUNT(sr.id) = 0 THEN '{}' ELSE 
        (SELECT sr.id as id,sr.code as code,sr.sampling_rate as samplingRate FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "samplingRate"`,
      ]);

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("inspectionstandard"."code") like lower(:code) escape '\\' OR
              lower("inspectionstandard"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('inspectionstandard.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'code':
            query.andWhere(
              `lower("inspectionstandard"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'name':
            query.andWhere(
              `lower("inspectionstandard"."name") like lower(:name) escape '\\'`,
              {
                name: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'isAql':
            query.andWhere('"inspectionstandard"."is_aql" = :isAql', {
              isAql: Number(value),
            });
            break;
          case 'inpectionStandardType':
            query.andWhere(
              '"inspectionstandard"."inpection_standard_type" = :inpectionStandardType',
              {
                inpectionStandardType: Number(value),
              },
            );
            break;
          case 'description':
            query.andWhere(
              `lower("inspectionstandard"."description") like lower(:description) escape '\\'`,
              {
                description: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'status':
            query.andWhere('"inspectionstandard"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'createdById':
            query.andWhere('"inspectionstandard"."created_by" = :createdById', {
              createdById: Number(value),
            });
            break;
          case 'createdByIds':
            query.andWhere(
              '"inspectionstandard"."created_by" IN (:...createdByIds)',
              {
                createdByIds: value.split(',').map((id) => Number(id)),
              },
            );
            break;
          case 'createdAt':
            const createFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const createTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"inspectionstandard"."created_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: createFrom,
                dateTo: createTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"inspectionstandard"."updated_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: updateFrom,
                dateTo: upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'name':
            query.addOrderBy('"inspectionstandard"."name"', order);
            break;
          case 'code':
            query.addOrderBy('"inspectionstandard"."code"', order);
            break;
          case 'isAql':
            query.addOrderBy('"inspectionstandard"."is_aql"', order);
            break;
          case 'inpectionStandardType':
            query.addOrderBy(
              '"inspectionstandard"."inpection_standard_type"',
              order,
            );
            break;
          case 'status':
            query.addOrderBy('"inspectionstandard"."status"', order);
            break;
          case 'description':
            query.addOrderBy('"inspectionstandard"."description"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"inspectionstandard"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"inspectionstandard"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"inspectionstandard"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('inspectionstandard.id', 'DESC');
    }

    query
      .groupBy('inspectionstandard.id')
      .addGroupBy('inspectionstandard.code')
      .addGroupBy('inspectionstandard.name')
      .addGroupBy('inspectionstandard.is_aql')
      .addGroupBy('inspectionstandard.inpection_standard_type')
      .addGroupBy('inspectionstandard.sampling_rate_id')
      .addGroupBy('inspectionstandard.description')
      .addGroupBy('inspectionstandard.status')
      .addGroupBy('inspectionstandard.created_by')
      .addGroupBy('inspectionstandard.created_at')
      .addGroupBy('inspectionstandard.updated_by')
      .addGroupBy('inspectionstandard.updated_at')
      .addGroupBy('sr.id')
      .addGroupBy('sr.code')
      .addGroupBy('sr.sampling_rate');

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const data = await query.getRawMany();
    const count = await query.getCount();

    return {
      data: data?.map((item) => {
        return {
          ...item,
          samplingRate: parseJSONValueField(item.samplingRate),
        };
      }),
      count: count,
    };
  }

  async getDetail(
    request: GetDetailInspectionStandardRequestDto,
  ): Promise<InspectionStandardEntity> {
    const { id } = request;

    const data = await this.inspectionStandardRepository.findOne({
      where: { id: id },
    });

    return data;
  }

  async active(
    request: UpdateStatusInspectionStandardRequestDto,
  ): Promise<any> {
    const { ids } = request;
    const data = await this.inspectionStandardRepository
      .createQueryBuilder()
      .update(InspectionStandardEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(
    request: UpdateStatusInspectionStandardRequestDto,
  ): Promise<any> {
    const { ids } = request;
    const data = await this.inspectionStandardRepository
      .createQueryBuilder()
      .update(InspectionStandardEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }
}
