import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt } from 'class-validator';

import { CreateInspectionStandardRequestDto } from './create-inspection-standard.request.dto';

export class UpdateInspectionStandardRequestDto extends CreateInspectionStandardRequestDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;
}
