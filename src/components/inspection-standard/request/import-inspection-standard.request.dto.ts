import {
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
} from 'class-validator';

export class ImportInspectionStandardRequestDto {
  @IsNotEmpty()
  @MaxLength(50)
  @Matches(/^[a-zA-Z0-9_.-]+$/)
  code: string;

  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @IsOptional()
  @IsString()
  samplingRateCode: string;

  @IsOptional()
  @IsInt()
  inpectionStandardType: number;

  @IsNotEmpty()
  @IsNumber()
  isAql: number;

  @IsOptional()
  @MaxLength(255)
  description?: string;
}
