import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { BaseCommonResponseDto } from '../../../common/dtos/response/base.common.dto';
import { SamplingRateResponseDto } from '../../../common/dtos/response/sampling-rate.common.dto';

export class GetListInspectionStandardResponseDto extends BaseCommonResponseDto {
  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  isAql: number;

  @ApiProperty()
  @Expose()
  inpectionStandardType: number;

  @ApiProperty({ type: SamplingRateResponseDto })
  @Expose()
  @Type(() => SamplingRateResponseDto)
  samplingRate: SamplingRateResponseDto;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty()
  @Expose()
  status: number;
}
