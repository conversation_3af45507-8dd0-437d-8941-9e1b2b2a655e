import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { ItemQcModule } from '../item-qc/item-qc.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { SamplingRateModule } from '../sampling-rate/sampling-rate.module';
import { InspectionStandardController } from './controller/inspection-standard.controller';
import { InspectionStandardEntity } from './entities/inspection-standard.entity';
import { InspectionStandardRepository } from './repository/inspection-standard.repository';
import { InspectionStandardService } from './service/inspection-standard.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([InspectionStandardEntity]),
    AnotherServiceModule,
    SamplingRateModule,
    ItemQcModule,
    MasterDataReferenceModule,
  ],
  providers: [InspectionStandardService, InspectionStandardRepository],
  exports: [InspectionStandardService],
  controllers: [InspectionStandardController],
})
export class InspectionStandardModule {}
