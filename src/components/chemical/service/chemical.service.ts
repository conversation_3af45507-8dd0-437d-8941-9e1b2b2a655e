import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';
import { DataSource } from 'typeorm';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { ChemicalEntity } from '../entities/chemical.entity';
import { CreateChemicalRequestDto } from '../request/create-chemical.request.dto';
import { DeleteChemicalRequestDto } from '../request/delete-chemical.request.dto';
import { GetDetailChemicalRequestDto } from '../request/get-detail-chemical.request.dto';
import { GetListChemicalRequestDto } from '../request/get-list-chemical.request.dto';
import { UpdateChemicalRequestDto } from '../request/update-chemical.request.dto';
import { UpdateStatusChemicalRequestDto } from '../request/update-status-chemical.request.dto';

import { GetDetailChemicalResponseDto } from '../response/get-detail-chemical.response.dto';
import { GetListChemicalResponseDto } from '../response/get-list-chemical.response.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { BaseService } from '../../../common/service/base.service';
import { UserService } from '../../another-service/services/user-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { ChemicalType } from '../chemical.enum';
import { ChemicalRepository } from '../repository/chemical.repository';

@Injectable()
export class ChemicalService extends BaseService {
  private readonly logger = new Logger(ChemicalService.name);

  constructor(
    private readonly chemicalRepository: ChemicalRepository,

    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,

    private readonly baseService: BaseProcessService,

    protected readonly userService: UserService,
  ) {
    super(userService);
  }

  async validateChemical(data: any) {
    const { chemicalType, toLsl, toUsl, unitId } = data;

    if (chemicalType === ChemicalType.PURE_CHEMICAL) {
      if (data.standardValue === null) {
        return {
          result: false,
          messageError: 'error.STANDARD_VALUE_IS_REQUIRED',
        };
      }
      if (toLsl === null) {
        return {
          result: false,
          messageError: 'error.TO_LSL_IS_REQUIRED',
        };
      }
      if (toUsl === null) {
        return {
          result: false,
          messageError: 'error.TO_USL_IS_REQUIRED',
        };
      }
    }

    if (unitId !== null && unitId !== undefined) {
      return await this.baseService.validateMaster({
        unitIds: [unitId],
      });
    }

    return {
      result: true,
      messageError: '',
    };
  }

  async create(request: CreateChemicalRequestDto): Promise<any> {
    const existCode = await this.chemicalRepository.findOneByCode(request.code);
    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    const { result, messageError } = await this.validateChemical(request);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }

    const chemicalEntity = this.chemicalRepository.createEntity(request);
    const chemical = await this.chemicalRepository.create(chemicalEntity);

    const response = plainToInstance(GetDetailChemicalResponseDto, chemical);

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListChemicalRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.chemicalRepository.getList(request);

    let dataMapUser = await this.mapUserInfoToResponse(data);
    dataMapUser = await this.baseService.mapMasterInfoToResponse(dataMapUser);
    const response = plainToInstance(GetListChemicalResponseDto, dataMapUser);

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailChemicalRequestDto): Promise<any> {
    const chemical = await this.chemicalRepository.getDetail(request);
    if (isEmpty(chemical)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    let dataMapUser = await this.mapUserInfoToResponse([chemical]);
    dataMapUser = await this.baseService.mapMasterInfoToResponse(dataMapUser);
    const response = plainToInstance(GetDetailChemicalResponseDto, dataMapUser);

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateChemicalRequestDto): Promise<any> {
    const { id } = request;
    const chemical = await this.chemicalRepository.findOneById(id);

    if (isEmpty(chemical)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const isUse = await this.baseService.validateDataMasterUseAnotherService(
      new ValidateMasterRequestDto('chemical_id', id),
    );
    if (isUse) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.USE_ANOTHER_SERVICE'))
        .build();
    }
    const existCode = await this.chemicalRepository.findOneByCode(request.code);
    if (!isEmpty(existCode) && existCode.id !== chemical.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    const { result, messageError } = await this.validateChemical(request);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }

    const dataUpdate = this.chemicalRepository.updateEntity(request, chemical);
    const data = await this.chemicalRepository.update(dataUpdate);

    const response = plainToInstance(GetDetailChemicalResponseDto, data);

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteChemicalRequestDto): Promise<any> {
    const { id } = request;
    const chemical = await this.chemicalRepository.findOneById(id);

    if (isEmpty(chemical)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const isUse = await this.baseService.validateDataMasterUseAnotherService(
      new ValidateMasterRequestDto('chemical_id', id),
    );
    if (isUse) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.USE_ANOTHER_SERVICE'))
        .build();
    }
    // fine in process-chemical
    const isUseProcessChemical = await this.baseService.getProcessChemicalCount(
      id,
    );
    if (isUseProcessChemical > 0) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.USE_ANOTHER_SERVICE'))
        .build();
    }

    await this.chemicalRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(request: UpdateStatusChemicalRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.chemicalRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.chemicalRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(request: UpdateStatusChemicalRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.chemicalRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.chemicalRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async findOneById(id: number): Promise<ChemicalEntity> {
    return await this.chemicalRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<ChemicalEntity[]> {
    return await this.chemicalRepository.findAllByIds(ids);
  }
}
