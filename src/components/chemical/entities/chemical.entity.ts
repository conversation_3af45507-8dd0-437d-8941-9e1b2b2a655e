import { Column, Entity } from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';

@Entity({ name: 'chemicals' })
export class ChemicalEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    unique: true,
    nullable: false,
    length: 255,
  })
  code: string;

  @Column({
    type: 'nvarchar',
    nullable: false,
    length: 255,
  })
  name: string;

  @Column({
    type: 'int',
    nullable: false,
  })
  chemicalType: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  unitId: number;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  scope: string;

  @Column({
    type: 'float',
    nullable: true,
  })
  standardValue: number;

  @Column({
    type: 'float',
    nullable: true,
  })
  toLsl: number;

  @Column({
    type: 'float',
    nullable: true,
  })
  toUsl: number;

  @Column({
    type: 'tinyint',
  })
  status: number;
}
