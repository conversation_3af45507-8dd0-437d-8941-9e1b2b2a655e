import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseCommonResponseDto } from '../../../common/dtos/response/base.common.dto';

export class GetDetailChemicalResponseDto extends BaseCommonResponseDto {
  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  chemicalType: number;

  @ApiProperty()
  @Expose()
  unitId: number;

  @ApiProperty()
  @Expose()
  scope: string;

  @ApiProperty()
  @Expose()
  standardValue: number;

  @ApiProperty()
  @Expose()
  toLsl: number;

  @ApiProperty()
  @Expose()
  toUsl: number;

  @ApiProperty()
  @Expose()
  status: number;
}
