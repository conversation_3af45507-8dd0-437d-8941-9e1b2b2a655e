import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  ACTIVE_CHEMICAL_PERMISSION,
  CREATE_CHEMICAL_PERMISSION,
  DELETE_CHEMICAL_PERMISSION,
  DETAIL_CHEMICAL_PERMISSION,
  INACTIVE_CHEMICAL_PERMISSION,
  LIST_CHEMICAL_PERMISSION,
  UPDATE_CHEMICAL_PERMISSION,
} from '../../../utils/permissions/chemical.permission';
import { CreateChemicalRequestDto } from '../request/create-chemical.request.dto';
import { DeleteChemicalRequestDto } from '../request/delete-chemical.request.dto';
import { GetDetailChemicalRequestDto } from '../request/get-detail-chemical.request.dto';
import { GetListChemicalRequestDto } from '../request/get-list-chemical.request.dto';
import { UpdateChemicalRequestDto } from '../request/update-chemical.request.dto';
import { UpdateStatusChemicalRequestDto } from '../request/update-status-chemical.request.dto';
import { ChemicalService } from '../service/chemical.service';

@Controller('chemicals')
export class ChemicalController {
  constructor(private readonly chemicalService: ChemicalService) {}

  @PermissionCode(DETAIL_CHEMICAL_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Chemicals'],
    summary: 'Chi tiết Chemicals',
    description: 'Chi tiết Chemicals',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailChemicalRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalService.getDetail(request);
  }

  @PermissionCode(LIST_CHEMICAL_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Chemicals'],
    summary: 'Danh sách Chemicals',
    description: 'Danh sách Chemicals',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(@Query() query: GetListChemicalRequestDto): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.chemicalService.getList(request);
  }

  @PermissionCode(CREATE_CHEMICAL_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Chemicals'],
    summary: 'Tạo Chemicals mới',
    description: 'Tạo Chemicals mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateChemicalRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.chemicalService.create(request);
  }

  @PermissionCode(UPDATE_CHEMICAL_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Chemicals'],
    summary: 'Cập nhật Chemicals',
    description: 'Cập nhật Chemicals',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(@Body() body: UpdateChemicalRequestDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalService.update(request);
  }

  @PermissionCode(ACTIVE_CHEMICAL_PERMISSION.code)
  @Put('/active')
  @ApiOperation({
    tags: ['Chemicals'],
    summary: 'Cập nhật Chemicals Status Active',
    description: 'Cập nhật Chemicals Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async active(
    @Body() body: UpdateStatusChemicalRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalService.active(request);
  }

  @PermissionCode(INACTIVE_CHEMICAL_PERMISSION.code)
  @Put('/inactive')
  @ApiOperation({
    tags: ['Chemicals'],
    summary: 'Cập nhật Chemicals Status Inactive',
    description: 'Cập nhật Chemicals Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async inactive(
    @Body() body: UpdateStatusChemicalRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalService.inactive(request);
  }

  @PermissionCode(DELETE_CHEMICAL_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Chemicals'],
    summary: 'Xóa Chemicals',
    description: 'Xóa Chemicals',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(@Param() param: DeleteChemicalRequestDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.chemicalService.delete(request);
  }
}
