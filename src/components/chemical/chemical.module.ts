import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { ChemicalController } from './controller/chemical.controller';
import { ChemicalEntity } from './entities/chemical.entity';
import { ChemicalRepository } from './repository/chemical.repository';
import { ChemicalService } from './service/chemical.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([ChemicalEntity]),
    AnotherServiceModule,
    BaseProcessModule,
  ],
  providers: [ChemicalService, ChemicalRepository],
  exports: [ChemicalService],
  controllers: [ChemicalController],
})
export class ChemicalModule {}
