import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { StatusEnum } from '../../../common/enums/status.enum';
import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import {
  escapeCharForSearch,
  parseJSONValueField,
} from '../../../utils/common';
import { ChemicalEntity } from '../entities/chemical.entity';
import { CreateChemicalRequestDto } from '../request/create-chemical.request.dto';
import { GetDetailChemicalRequestDto } from '../request/get-detail-chemical.request.dto';
import { GetListChemicalRequestDto } from '../request/get-list-chemical.request.dto';
import { UpdateChemicalRequestDto } from '../request/update-chemical.request.dto';
import { UpdateStatusChemicalRequestDto } from '../request/update-status-chemical.request.dto';

@Injectable()
export class ChemicalRepository extends BaseAbstractRepository<ChemicalEntity> {
  constructor(
    @InjectRepository(ChemicalEntity)
    private readonly chemicalRepository: Repository<ChemicalEntity>,
  ) {
    super(chemicalRepository);
  }

  createEntity(request: CreateChemicalRequestDto): ChemicalEntity {
    const chemicalEntity = new ChemicalEntity();
    Object.assign(chemicalEntity, request);
    chemicalEntity.status = StatusEnum.ACTIVE;
    chemicalEntity.createdBy = request.userId;

    return chemicalEntity;
  }

  updateEntity(
    request: UpdateChemicalRequestDto,
    entity: ChemicalEntity,
  ): ChemicalEntity {
    const chemicalEntity = new ChemicalEntity();
    Object.assign(chemicalEntity, request);
    chemicalEntity.createdBy = entity.createdBy;
    chemicalEntity.updatedBy = request.userId;

    return chemicalEntity;
  }

  async getList(request: GetListChemicalRequestDto): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.chemicalRepository.createQueryBuilder('chemical');

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("chemical"."code") like lower(:code) escape '\\' OR
              lower("chemical"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('chemical.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'code':
            query.andWhere(
              `lower("chemical"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'chemicalTypes':
            query.andWhere(
              '"chemical"."chemical_type" IN (:...chemicalTypes)',
              {
                chemicalTypes: value.split(',').map((id) => Number(id)),
              },
            );
            break;
          case 'status':
            query.andWhere('"chemical"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'createdByIds':
            query.andWhere('"chemical"."created_by" IN (:...createdByIds)', {
              createdByIds: value.split(',').map((id) => Number(id)),
            });
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'name':
            query.addOrderBy('"chemical"."name"', order);
            break;
          case 'code':
            query.addOrderBy('"chemical"."code"', order);
            break;
          case 'chemicalType':
            query.addOrderBy('"chemical"."chemical_type"', order);
            break;
          case 'status':
            query.addOrderBy('"chemical"."status"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"chemical"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"chemical"."created_by"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('chemical.id', 'DESC');
    }

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const [data, count] = await query.getManyAndCount();

    return { data, count };
  }

  async getAllByIds(ids: number[]): Promise<any> {
    {
      const query = this.chemicalRepository
        .createQueryBuilder('chemical')
        .select([
          `chemical.id as id,
          chemical.created_by as createdBy,
          chemical.created_at as createdAt,
          chemical.code as code,
          chemical.name as name,
          chemical.chemical_type as chemicalType,
          chemical.unit_id as unitId,
          chemical.scope as scope,
          chemical.standard_value as standardValue,
          chemical.to_lsl as toLsl,
          chemical.to_usl as toUsl,
          chemical.status as status,
          chemical.updated_by as updatedBy,
          chemical.updated_at as updatedAt`,
        ])
        .leftJoin('units', 'u', 'chemical.unit_id = u.id')
        .addSelect([
          `CASE WHEN COUNT(u.id) = 0 THEN '{}' ELSE 
          (SELECT u.id as id, u.code as code , u.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "unit"`,
        ])
        .where('chemical.id IN (:...ids)', {
          ids,
        })
        .groupBy('u.id')
        .addGroupBy('chemical.id')
        .addGroupBy('chemical.created_by')
        .addGroupBy('chemical.created_at')
        .addGroupBy('chemical.code')
        .addGroupBy('chemical.name')
        .addGroupBy('chemical.chemical_type')
        .addGroupBy('chemical.unit_id')
        .addGroupBy('chemical.scope')
        .addGroupBy('chemical.standard_value')
        .addGroupBy('chemical.to_lsl')
        .addGroupBy('chemical.to_usl')
        .addGroupBy('chemical.status')
        .addGroupBy('chemical.updated_by')
        .addGroupBy('chemical.updated_at')
        .addGroupBy('u.code')
        .addGroupBy('u.name');

      const result = await query.getRawMany();
      return result?.map((item) => {
        return {
          ...item,
          unit: parseJSONValueField(item.unit),
        };
      });
    }
  }

  async getDetail(
    request: GetDetailChemicalRequestDto,
  ): Promise<ChemicalEntity> {
    const { id } = request;

    const data = await this.chemicalRepository.findOne({
      where: { id: id },
    });

    return data;
  }

  async active(request: UpdateStatusChemicalRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.chemicalRepository
      .createQueryBuilder()
      .update(ChemicalEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(request: UpdateStatusChemicalRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.chemicalRepository
      .createQueryBuilder()
      .update(ChemicalEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }
}
