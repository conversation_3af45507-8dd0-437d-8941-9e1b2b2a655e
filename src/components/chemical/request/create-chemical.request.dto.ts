import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, MaxLength } from 'class-validator';

export class CreateChemicalRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  code: string;

  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  chemicalType: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  unitId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  scope: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  standardValue: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  toLsl: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  toUsl: number;
}
