import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsNotEmpty } from 'class-validator';
import { BaseDto } from 'src/core/dto/base.dto';

export class GetWeekByMonthAndYearRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  @Transform(({ value }) => Number(value))
  month: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  @Transform(({ value }) => Number(value))
  year: number;
}
