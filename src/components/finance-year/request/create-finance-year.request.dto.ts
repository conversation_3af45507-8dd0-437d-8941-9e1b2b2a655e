import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  MaxLength,
  Min,
} from 'class-validator';

export class CreateFinanceYearRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  year: number;

  // @ApiProperty()
  // @IsNotEmpty()
  // @IsDateString()
  // effectiveDateFrom: Date;

  // @ApiProperty()
  // @IsNotEmpty()
  // @IsDateString()
  // effectiveDateTo: Date;

  // @ApiProperty()
  // @IsNotEmpty()
  // @IsNumber()
  // numberOfWeek: number;

  @ApiProperty()
  @IsNotEmpty()
  @Min(0)
  @IsNumber()
  startDayOfWeek: number;

  // @ApiProperty()
  // @IsNotEmpty()
  // @IsNumber()
  // endDayOfWeek: number;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  note?: string;

  // @ApiPropertyOptional({ type: [CreateFinanceYearDetailRequestDto] })
  // @IsOptional()
  // @IsArray()
  // @ValidateNested()
  // @Type(() => CreateFinanceYearDetailRequestDto)
  // financeYearDetails: FinanceYearDetailEntity[];
}
