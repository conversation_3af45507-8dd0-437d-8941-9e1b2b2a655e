import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { isDateString } from 'class-validator';
import * as moment from 'moment';
import { StatusEnum } from '../../../common/enums/status.enum';
import connectionOptions from '../../../config/database.config';
import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { FinanceYearDetailEntity } from '../../finance-year-detail/entities/finance-year-detail.entity';
import { PERIOD_ENUM } from '../../sales-quality-report/sales-quality-report.constant';
import { FinanceYearEntity } from '../entities/finance-year.entity';
import { GetDetailFinanceYearRequestDto } from '../request/get-detail-finance-year.request.dto';
import { GetLastMonthAndWeek } from '../request/get-last-month-and-week.dto';
import { GetListFinanceYearRequestDto } from '../request/get-list-finance-year.request.dto';
import { UpdateFinanceYearRequestDto } from '../request/update-finance-year.request.dto';
import { UpdateStatusFinanceYearRequestDto } from '../request/update-status-finance-year.request.dto';

@Injectable()
export class FinanceYearRepository extends BaseAbstractRepository<FinanceYearEntity> {
  constructor(
    @InjectRepository(FinanceYearEntity)
    private readonly financeYearRepository: Repository<FinanceYearEntity>,
  ) {
    super(financeYearRepository);
  }

  createEntity(request: any): FinanceYearEntity {
    const financeYearEntity = new FinanceYearEntity();
    Object.assign(financeYearEntity, request);
    financeYearEntity.effectiveDateFrom = moment(
      request.effectiveDateFrom,
      'YYYY-MM-DD',
    ).toDate();
    financeYearEntity.effectiveDateTo = moment(
      request.effectiveDateTo,
      'YYYY-MM-DD',
    ).toDate();
    financeYearEntity.status = StatusEnum.ACTIVE;

    const financeYearDetails = [];
    request?.financeYearDetails?.map((weekData) => {
      const detail = new FinanceYearDetailEntity();
      detail.week = weekData.week;
      detail.month = weekData.month;
      detail.startDate = moment(weekData.startDate, 'YYYY-MM-DD').toDate();
      detail.endDate = moment(weekData.endDate, 'YYYY-MM-DD').toDate();

      financeYearDetails.push(detail);
    });
    financeYearEntity.financeYearDetails = financeYearDetails;

    return financeYearEntity;
  }

  updateEntity(
    request: UpdateFinanceYearRequestDto,
    entity: FinanceYearEntity,
  ): FinanceYearEntity {
    Object.assign(entity, request);
    entity.updatedBy = request?.userId;
    return entity;
  }

  async getList(request: GetListFinanceYearRequestDto): Promise<any> {
    const { skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.financeYearRepository.createQueryBuilder('financeyear');

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('financeyear.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'year':
            query.andWhere('"financeyear"."year" = :year', {
              year: Number(value),
            });
            break;
          case 'years':
            query.andWhere('"financeyear"."year" IN (:...years)', {
              years: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'status':
            query.andWhere('"financeyear"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'createdById':
            query.andWhere('"financeyear"."created_by" = :createdById', {
              createdById: Number(value),
            });
            break;
          case 'createdByIds':
            query.andWhere('"financeyear"."created_by" IN (:...createdByIds)', {
              createdByIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'createdAt':
            const createFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const createTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"financeyear"."created_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: createFrom,
                dateTo: createTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"financeyear"."updated_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: updateFrom,
                dateTo: upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'year':
            query.addOrderBy('"financeyear"."year"', order);
            break;
          case 'effectiveDateFrom':
            query.addOrderBy('"financeyear"."effective_date_from"', order);
            break;
          case 'effectiveDateTo':
            query.addOrderBy('"financeyear"."effective_date_to"', order);
            break;
          case 'numberOfWeek':
            query.addOrderBy('"financeyear"."number_of_week"', order);
            break;
          case 'startDayOfWeek':
            query.addOrderBy('"financeyear"."start_day_of_week"', order);
            break;
          case 'endDayOfWeek':
            query.addOrderBy('"financeyear"."end_day_of_week"', order);
            break;
          case 'note':
            query.addOrderBy('"financeyear"."note"', order);
            break;
          case 'status':
            query.addOrderBy('"financeyear"."status"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"financeyear"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"financeyear"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"financeyear"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('financeyear.id', 'DESC');
    }

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const [data, count] = await query.getManyAndCount();

    return { data, count };
  }

  async getDetail(
    request: GetDetailFinanceYearRequestDto,
  ): Promise<FinanceYearEntity> {
    const { id } = request;

    const data = await this.financeYearRepository.findOne({
      where: { id: id },
    });

    return data;
  }

  async active(request: UpdateStatusFinanceYearRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.financeYearRepository
      .createQueryBuilder()
      .update(FinanceYearEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(request: UpdateStatusFinanceYearRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.financeYearRepository
      .createQueryBuilder()
      .update(FinanceYearEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  // async getAllByEffectiveDate(
  //   request: CreateFinanceYearRequestDto,
  //   id: number,
  // ): Promise<FinanceYearEntity[]> {
  //   const query = this.financeYearRepository
  //     .createQueryBuilder('financeyear')
  //     .where(
  //       `(
  //             (financeYear.effective_date_from = :requestEffectiveDateFrom AND :requestEffectiveDateTo = financeYear.effective_date_to) OR
  //             (financeYear.effective_date_from <= :requestEffectiveDateFrom AND :requestEffectiveDateFrom <= financeYear.effective_date_to) OR
  //             (financeYear.effective_date_from <= :requestEffectiveDateTo AND :requestEffectiveDateTo <= financeYear.effective_date_to)
  //         )`,
  //       {
  //         requestEffectiveDateFrom: request.effectiveDateFrom,
  //         requestEffectiveDateTo: request.effectiveDateTo,
  //       },
  //     );

  //   if (id) {
  //     query.andWhere('financeYear.id != :excludedId', { excludedId: id });
  //   }

  //   return query.getMany();
  // }

  async getDataDetailsByFinanceYearIds(financeYearIds: number[]) {
    const data = await this.financeYearRepository
      .createQueryBuilder('fy')
      .innerJoinAndSelect('fy.financeYearDetails', 'fyd')
      .select([
        'fy.id as financeYearId',
        'fyd.id as id',
        'fyd.week as week',
        'fyd.start_date as startDate',
        'fyd.end_date as endDate',
        'fyd.month as month',
      ])
      .where('fy.id IN (:...financeYearIds)', { financeYearIds })
      .getRawMany();

    return data;
  }

  async findOneByYear(year: number): Promise<any> {
    const data = await this.financeYearRepository
      .createQueryBuilder('fy')
      .select()
      .where('fy.year = :year', {
        year: year,
      })
      .getRawOne();

    return data;
  }

  async findOneByDate(date: Date): Promise<any> {
    const data = await this.financeYearRepository
      .createQueryBuilder('fy')
      .innerJoinAndSelect('fy.financeYearDetails', 'fyd')
      .select([
        'fy.id as financeYearId',
        'fyd.id as id',
        'fy.year as year',
        'fyd.week as week',
        'fyd.start_date as startDate',
        'fyd.end_date as endDate',
        'fyd.month as month',
      ])
      .where(':date between fyd.start_date and fyd.end_date', {
        date: date,
      })
      .getRawOne();

    return data;
  }

  async findOneByNewDate(date: Date): Promise<any> {
    const data = await this.financeYearRepository
      .createQueryBuilder('fy')
      .innerJoinAndSelect('fy.financeYearDetails', 'fyd')
      .select([
        'fy.id as financeYearId',
        'fyd.id as id',
        'fy.year as year',
        'fyd.week as week',
        'fyd.start_date as startDate',
        'fyd.end_date as endDate',
        'fyd.month as month',
      ])
      .where(':date between fyd.start_date and fyd.end_date', {
        date: date.toISOString(),
      })
      .getRawOne();

    return data;
  }

  async findByTimePoint(
    timePoint: number,
    year: number,
    period: PERIOD_ENUM,
  ): Promise<any> {
    const query = this.financeYearRepository
      .createQueryBuilder('fy')
      .innerJoinAndSelect('fy.financeYearDetails', 'fyd')
      .select(
        period === PERIOD_ENUM.WEEK && timePoint != null
          ? [
              'fyd.start_date as startDate',
              'fyd.end_date as endDate',
              'fyd.month as month',
            ]
          : [
              'MIN(fyd.start_date) as startDate',
              'MAX(fyd.end_date) as endDate',
            ],
      )
      .where('fy.year = :year', { year });
    if (period != null && timePoint != null) {
      if (period === PERIOD_ENUM.WEEK) {
        query.andWhere('fyd.week = :week', { week: timePoint });
      } else {
        query.andWhere('fyd.month = :month', { month: timePoint });
      }
    }

    return query.getRawOne();
  }

  async getLastMonthAndWeek(request: GetLastMonthAndWeek): Promise<any> {
    const query = this.financeYearRepository
      .createQueryBuilder('fy')
      .innerJoinAndSelect('fy.financeYearDetails', 'fyd')
      .select([
        `CASE WHEN fy.year = :year1 AND fyd.month = :month1 THEN fyd.start_date END as startMonth1,
        CASE WHEN fy.year = :year1 AND fyd.month = :month1 THEN fyd.end_date END as endMonth1,
        CASE WHEN fy.year = :year2 AND fyd.month = :month2 THEN fyd.start_date END as startMonth2,
        CASE WHEN fy.year = :year2 AND fyd.month = :month2 THEN fyd.end_date END as endMonth2,
        CASE WHEN fy.year = :year3 AND fyd.week = :week1 THEN fyd.start_date END as startWeek1,
        CASE WHEN fy.year = :year3 AND fyd.week = :week1 THEN fyd.end_date END as endWeek1,
        CASE WHEN fy.year = :year4 AND fyd.week = :week2 THEN fyd.start_date END as startWeek2,
        CASE WHEN fy.year = :year4 AND fyd.week = :week2 THEN fyd.end_date END as endWeek2`,
      ])
      .setParameters({
        year1: request.year1,
        year2: request.year2,
        year3: request.year3,
        year4: request.year4,
        month1: request.month1,
        month2: request.month2,
        week1: request.week1,
        week2: request.week2,
      });
    const query1 = connectionOptions
      .createQueryBuilder()
      .select([
        `MIN(t1.startMonth1) as startMonth1,
        MAX(t1.endMonth1) as endMonth1,
        MIN(t1.startMonth2) as startMonth2,
        MAX(t1.endMonth2) as endMonth2,
        MIN(t1.startWeek1) as startWeek1,
        MAX(t1.endWeek1) as endWeek1,
        MIN(t1.startWeek2) as startWeek2,
        MAX(t1.endWeek2) as endWeek2`,
      ])
      .from(`(${query.getQuery()})`, 't1')
      .setParameters(query.getParameters());
    return query1.getRawOne();
  }

  async findTimeByYearAndWeek(year: number, week: number): Promise<any> {
    const query = this.financeYearRepository
      .createQueryBuilder('fy')
      .innerJoinAndSelect('fy.financeYearDetails', 'fyd')
      .select(['fyd.start_date as startDate', 'fyd.end_date as endDate'])
      .where('fy.year = :year', { year })
      .andWhere('fyd.week = :week', { week });

    return query.getRawOne();
  }

  async findWeekOfMonthByYear(year: number, month: number): Promise<number[]> {
    const query = this.financeYearRepository
      .createQueryBuilder('fy')
      .innerJoinAndSelect('fy.financeYearDetails', 'fyd')
      .select('fyd.week as week')
      .where('fy.year = :year AND fyd.month = :month', { year, month });

    const lastDateOfMonth = new Date(year, month, 0);
    const today = new Date();

    if (today < lastDateOfMonth) {
      query.andWhere(
        `CAST(fyd.start_date AT TIME ZONE 'UTC' AS DATE) <= :date`,
        {
          date: today,
        },
      );
    }

    const data = await query.getRawMany();
    return data?.map((row) => row.week);
  }

  async getFinancialMonthsByYear(year: number) {
    const data = await this.financeYearRepository
      .createQueryBuilder('fy')
      .innerJoin('fy.financeYearDetails', 'fyd')
      .select([
        'fyd.month AS month',
        'MIN(fyd.start_date) AS start_date',
        'MAX(fyd.end_date) AS end_date',
      ])
      .where('fy.year = :year', { year })
      .groupBy('fyd.month')
      .orderBy('fyd.month')
      .getRawMany();

    return data;
  }
}
