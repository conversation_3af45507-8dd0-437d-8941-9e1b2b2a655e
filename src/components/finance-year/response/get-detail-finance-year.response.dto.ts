import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseCommonResponseDto } from '../../../common/dtos/response/base.common.dto';

export class GetFinanceYearDetailsResponseDto {
  @ApiProperty()
  @Expose()
  id: number;

  @ApiProperty()
  @Expose()
  financeYearId: number;

  @ApiProperty()
  @Expose()
  week: number;

  @ApiProperty()
  @Expose()
  startDate: Date;

  @ApiProperty()
  @Expose()
  endDate: Date;

  @ApiProperty()
  @Expose()
  month: number;
}

export class GetDetailFinanceYearResponseDto extends BaseCommonResponseDto {
  @ApiProperty()
  @Expose()
  year: string;

  @ApiProperty()
  @Expose()
  effectiveDateFrom: Date;

  @ApiProperty()
  @Expose()
  effectiveDateTo: Date;

  @ApiProperty()
  @Expose()
  numberOfWeek: number;

  @ApiProperty()
  @Expose()
  startDayOfWeek: number;

  @ApiProperty()
  @Expose()
  endDayOfWeek: number;

  @ApiProperty()
  @Expose()
  note: string;

  @ApiProperty()
  @Expose()
  status: number;

  @ApiProperty()
  @Expose()
  financeYearDetails: GetFinanceYearDetailsResponseDto[];
}
