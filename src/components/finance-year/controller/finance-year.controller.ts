import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  CREATE_FINANCE_YEAR_PERMISSION,
  DELETE_FINANCE_YEAR_PERMISSION,
  DETAIL_FINANCE_YEAR_PERMISSION,
  LIST_FINANCE_YEAR_PERMISSION,
} from '../../../utils/permissions/finance-year.permission';
import { CreateFinanceYearRequestDto } from '../request/create-finance-year.request.dto';
import { DeleteFinanceYearRequestDto } from '../request/delete-finance-year.request.dto';
import { GetDetailFinanceYearRequestDto } from '../request/get-detail-finance-year.request.dto';
import { GetFinanceYearDetailByDateRequestDto } from '../request/get-finance-year-detail-by-date.request.dto';
import { GetListFinanceYearRequestDto } from '../request/get-list-finance-year.request.dto';
import { GetWeekByMonthAndYearRequestDto } from '../request/get-week-by-month-year.request.dto copy';
import { FinanceYearService } from '../service/finance-year.service';

@Controller('finance-years')
export class FinanceYearController {
  constructor(private readonly financeYearService: FinanceYearService) {}

  @PermissionCode(DETAIL_FINANCE_YEAR_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Finance-years'],
    summary: 'Chi tiết Finance-years',
    description: 'Chi tiết Finance-years',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailFinanceYearRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.financeYearService.getDetail(request);
  }

  @PermissionCode(LIST_FINANCE_YEAR_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Finance-years'],
    summary: 'Danh sách Finance-years',
    description: 'Danh sách Finance-years',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(@Query() query: GetListFinanceYearRequestDto): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.financeYearService.getList(request);
  }

  @PermissionCode(LIST_FINANCE_YEAR_PERMISSION.code)
  @Get('/detail-by-date')
  @ApiOperation({
    tags: ['Finance-years'],
    summary: 'Danh sách Finance-years',
    description: 'Danh sách Finance-years',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getFinanceYearDetailByDate(
    @Query() query: GetFinanceYearDetailByDateRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.financeYearService.findOneByDate(request?.date);
  }

  @PermissionCode(LIST_FINANCE_YEAR_PERMISSION.code)
  @Get('/week-by-month-year')
  @ApiOperation({
    tags: ['Finance-years'],
    summary: 'Danh sách Finance-years',
    description: 'Danh sách Finance-years',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getWeekByMonthAndYear(
    @Query() query: GetWeekByMonthAndYearRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.financeYearService.findWeekOfMonthByYear(
      request?.year,
      request?.month,
    );
  }

  @PermissionCode(CREATE_FINANCE_YEAR_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Finance-years'],
    summary: 'Tạo Finance-years mới',
    description: 'Tạo Finance-years mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateFinanceYearRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.financeYearService.create(request);
  }

  @PermissionCode(CREATE_FINANCE_YEAR_PERMISSION.code)
  @Post('/generate-weeks')
  @ApiOperation({
    tags: ['Finance-years'],
    summary: 'Cập nhật danh sách các tuần trong Finance-years',
    description: 'Cập nhật danh sách các tuần trong Finance-years',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public generateWeekOfYear(@Body() payload: CreateFinanceYearRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.financeYearService.generateWeekOfYear(request);
  }

  // @PermissionCode(UPDATE_FINANCE_YEAR_PERMISSION.code)
  // @Put()
  // @ApiOperation({
  //   tags: ['Finance-years'],
  //   summary: 'Cập nhật Finance-years',
  //   description: 'Cập nhật Finance-years',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Thành công',
  // })
  // public async update(@Body() body: UpdateFinanceYearRequestDto): Promise<any> {
  //   const { request, responseError } = body;

  //   if (responseError && !isEmpty(responseError)) {
  //     return responseError;
  //   }

  //   return await this.financeYearService.update(request);
  // }

  // @PermissionCode(ACTIVE_FINANCE_YEAR_PERMISSION.code)
  // @Put('/active')
  // @ApiOperation({
  //   tags: ['Finance-years'],
  //   summary: 'Cập nhật Finance-years Status Active',
  //   description: 'Cập nhật Finance-years Status Active',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Thành công',
  // })
  // public async active(
  //   @Body() body: UpdateStatusFinanceYearRequestDto,
  // ): Promise<any> {
  //   const { request, responseError } = body;

  //   if (responseError && !isEmpty(responseError)) {
  //     return responseError;
  //   }

  //   return await this.financeYearService.active(request);
  // }

  // @PermissionCode(INACTIVE_FINANCE_YEAR_PERMISSION.code)
  // @Put('/inactive')
  // @ApiOperation({
  //   tags: ['Finance-years'],
  //   summary: 'Cập nhật Finance-years Status Inactive',
  //   description: 'Cập nhật Finance-years Status Inactive',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Thành công',
  // })
  // public async inactive(
  //   @Body() body: UpdateStatusFinanceYearRequestDto,
  // ): Promise<any> {
  //   const { request, responseError } = body;

  //   if (responseError && !isEmpty(responseError)) {
  //     return responseError;
  //   }

  //   return await this.financeYearService.inactive(request);
  // }

  @PermissionCode(DELETE_FINANCE_YEAR_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Finance-years'],
    summary: 'Xóa Finance-years',
    description: 'Xóa Finance-years',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(@Param() param: DeleteFinanceYearRequestDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.financeYearService.delete(request);
  }
}
