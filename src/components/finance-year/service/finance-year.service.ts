import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import * as moment from 'moment';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { FinanceYearEntity } from '../entities/finance-year.entity';
import { CreateFinanceYearRequestDto } from './../request/create-finance-year.request.dto';
import { DeleteFinanceYearRequestDto } from './../request/delete-finance-year.request.dto';
import { GetDetailFinanceYearRequestDto } from './../request/get-detail-finance-year.request.dto';
import { GetListFinanceYearRequestDto } from './../request/get-list-finance-year.request.dto';
import { UpdateFinanceYearRequestDto } from './../request/update-finance-year.request.dto';
import { UpdateStatusFinanceYearRequestDto } from './../request/update-status-finance-year.request.dto';

import { GetDetailFinanceYearResponseDto } from './../response/get-detail-finance-year.response.dto';
import { GetListFinanceYearResponseDto } from './../response/get-list-finance-year.response.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ValidateResultCommonDto } from '../../../common/dtos/validate.result.common.dto';
import { BaseService } from '../../../common/service/base.service';
import { UserService } from '../../another-service/services/user-service';
import { PERIOD_ENUM } from '../../sales-quality-report/sales-quality-report.constant';
import { FinanceYearRepository } from '../repository/finance-year.repository';
import { GetLastMonthAndWeek } from '../request/get-last-month-and-week.dto';

@Injectable()
export class FinanceYearService extends BaseService {
  private readonly logger = new Logger(FinanceYearService.name);

  constructor(
    private readonly financeYearRepository: FinanceYearRepository,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,
  ) {
    super(userService);
  }

  async create(request: CreateFinanceYearRequestDto): Promise<any> {
    const resultValidate = await this.validateSaveFinanceYear(request);
    if (resultValidate?.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(resultValidate.messageError))
        .build();
    }

    const dataWeek = await this.getFinancialWeeks(request);
    const financeYearEntity = this.financeYearRepository.createEntity(dataWeek);
    financeYearEntity.createdBy = request.userId;
    const financeYear = await this.financeYearRepository.create(
      financeYearEntity,
    );

    const response = plainToInstance(
      GetDetailFinanceYearResponseDto,
      financeYear,
    );

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async generateWeekOfYear(request: CreateFinanceYearRequestDto): Promise<any> {
    const data = await this.getFinancialWeeks(request);

    return new ResponseBuilder(data)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListFinanceYearRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.financeYearRepository.getList(request);

    const dataMapUser = await this.mapUserInfoToResponse(data);
    const response = plainToInstance(
      GetListFinanceYearResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailFinanceYearRequestDto): Promise<any> {
    const financeYear = await this.financeYearRepository.getDetail(request);
    if (isEmpty(financeYear)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMapUser = await this.mapUserInfoToResponse([financeYear]);
    const response = plainToInstance(
      GetDetailFinanceYearResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateFinanceYearRequestDto): Promise<any> {
    const { id } = request;
    const financeYear = await this.financeYearRepository.findOneById(id);

    if (isEmpty(financeYear)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const resultValidate = await this.validateSaveFinanceYear(request);
    if (resultValidate?.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(resultValidate.messageError))
        .build();
    }

    const dataUpdate = this.financeYearRepository.updateEntity(
      request,
      financeYear,
    );
    const data = await this.financeYearRepository.update(dataUpdate);

    const response = plainToInstance(GetDetailFinanceYearResponseDto, data);

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteFinanceYearRequestDto): Promise<any> {
    const { id } = request;
    const financeYear = await this.financeYearRepository.findOneById(id);

    if (isEmpty(financeYear)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const currentYear = new Date().getFullYear();
    if (currentYear >= financeYear.year) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.FINANCE_YEAR_CANNOT_DELETE'),
        )
        .build();
    }

    await this.financeYearRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(request: UpdateStatusFinanceYearRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.financeYearRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.financeYearRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(request: UpdateStatusFinanceYearRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.financeYearRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.financeYearRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async findOneById(id: number): Promise<FinanceYearEntity> {
    return await this.financeYearRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<FinanceYearEntity[]> {
    return await this.financeYearRepository.findAllByIds(ids);
  }

  async findOneByDate(date: Date): Promise<any> {
    return await this.financeYearRepository.findOneByDate(date);
  }

  async findOneByNewDate(date: Date): Promise<any> {
    return await this.financeYearRepository.findOneByNewDate(date);
  }

  async findTimeByYearAndWeek(year: number, week: number): Promise<any> {
    return await this.financeYearRepository.findTimeByYearAndWeek(year, week);
  }

  async findWeekOfMonthByYear(year: number, month: number): Promise<number[]> {
    return await this.financeYearRepository.findWeekOfMonthByYear(year, month);
  }

  async getFinancialMonthsByYear(year: number): Promise<any[]> {
    return await this.financeYearRepository.getFinancialMonthsByYear(year);
  }

  async validateSaveFinanceYear(
    request: CreateFinanceYearRequestDto,
  ): Promise<ValidateResultCommonDto> {
    const yearExist = await this.financeYearRepository.findOneByYear(
      request?.year,
    );

    if (!isEmpty(yearExist)) {
      return {
        result: true,
        messageError: 'error.EXISTS_IN_DATA_BASE',
      };
    }

    return { result: false, messageError: '' };
  }

  async mapDataDetailToResponse(data: FinanceYearEntity[]): Promise<any> {
    if (data.length === 0) return data;
    const idsSet: Set<number> = new Set();
    data?.map((item) => {
      if (item.id !== null && item.id !== undefined) {
        idsSet.add(item.id);
      }
    });

    const ids: number[] = Array.from(idsSet);
    const dataDetails =
      await this.financeYearRepository.getDataDetailsByFinanceYearIds(ids);
    if (dataDetails.length === 0) return data;

    const dataDetailMap = dataDetails?.reduce((acc, item) => {
      const key = `${item.financeYearId}`;
      if (isEmpty(acc[key])) {
        acc[key] = { detail: [item] };
      } else {
        acc[key].detail.push(item);
      }
      return acc;
    }, {});

    return (
      data?.map((item) => {
        return {
          ...item,
          financeYearDetails: dataDetailMap[item.id].detail,
        };
      }) || []
    );
  }

  async findTimeFrameByTimePoint(
    timePoint: number,
    year: number,
    period: PERIOD_ENUM,
  ): Promise<any> {
    return await this.financeYearRepository.findByTimePoint(
      timePoint,
      year,
      period,
    );
  }

  async getLastMonthAndWeek(request: GetLastMonthAndWeek): Promise<any> {
    return await this.financeYearRepository.getLastMonthAndWeek(request);
  }

  async getFinancialWeeks(request: CreateFinanceYearRequestDto): Promise<any> {
    const { year, startDayOfWeek, note } = request;

    let yearString = year.toString();
    if (year < 1000) {
      yearString = String(year).padStart(4, '0');
    }

    const startDate = moment.utc(`${yearString}-01-01T00:00:00.000Z`);
    const endDate = moment.utc(`${yearString}-12-31T23:59:59.999Z`);

    const endDay = ((startDayOfWeek + 6 - 1) % 7) + 1;

    const firstWeekEnd = startDate.clone();
    while (firstWeekEnd.isoWeekday() !== endDay) {
      firstWeekEnd.add(1, 'days');
    }

    const weeks = [
      {
        week: 1,
        startDate: startDate,
        endDate: firstWeekEnd.clone().endOf('day'),
        month: 1,
      },
    ];

    let weekNumber = 2;
    let currentStart = firstWeekEnd.clone().add(1, 'days');

    while (currentStart.isBefore(endDate)) {
      if (weekNumber > 53) {
        break;
      }

      let currentEnd = currentStart.clone().add(6, 'days');

      if (currentEnd.isAfter(endDate)) {
        currentEnd = endDate.clone();
      }

      const monthDays = {};
      const tempDate = currentStart.clone();
      while (tempDate.isSameOrBefore(currentEnd)) {
        const month = tempDate.month() + 1;
        monthDays[month] = (monthDays[month] || 0) + 1;
        tempDate.add(1, 'day');
      }
      const mostDaysMonth = Object.entries(monthDays).reduce((a, b) =>
        b[1] > a[1] ? b : a,
      )[0];

      weeks.push({
        week: weekNumber,
        startDate: currentStart,
        endDate: currentEnd.clone().endOf('day'),
        month: parseInt(mostDaysMonth, 10),
      });

      weekNumber++;
      currentStart = currentEnd.clone().add(1, 'days');
    }

    const lastWeek = weeks[weeks.length - 1];
    if (lastWeek.endDate !== endDate) {
      lastWeek.endDate = endDate.clone().endOf('day');
    }

    return {
      year: year,
      effectiveDateFrom: startDate,
      effectiveDateTo: endDate,
      numberOfWeek: lastWeek.week,
      startDayOfWeek: startDayOfWeek,
      endDayOfWeek: endDay,
      note: note,
      financeYearDetails: weeks,
    };
  }
}
