import { Column, Entity, OneToMany } from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { FinanceYearDetailEntity } from '../../finance-year-detail/entities/finance-year-detail.entity';

@Entity({ name: 'finance_years' })
export class FinanceYearEntity extends BaseEntity {
  @Column({
    type: 'int',
    nullable: false,
  })
  year: number;

  @Column({
    type: 'datetimeoffset',
    nullable: false,
  })
  effectiveDateFrom: Date;

  @Column({
    type: 'datetimeoffset',
    nullable: false,
  })
  effectiveDateTo: Date;

  @Column({
    type: 'int',
    nullable: false,
  })
  numberOfWeek: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  startDayOfWeek: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  endDayOfWeek: number;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  note: string;

  @Column({
    type: 'tinyint',
  })
  status: number;

  @OneToMany(() => FinanceYearDetailEntity, (detail) => detail.financeYear, {
    cascade: ['insert', 'update', 'remove'],
    onDelete: 'CASCADE',
    eager: true,
  })
  financeYearDetails: FinanceYearDetailEntity[];
}
