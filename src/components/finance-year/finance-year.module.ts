import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { FinanceYearController } from './controller/finance-year.controller';
import { FinanceYearEntity } from './entities/finance-year.entity';
import { FinanceYearRepository } from './repository/finance-year.repository';
import { FinanceYearService } from './service/finance-year.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([FinanceYearEntity]),
    AnotherServiceModule,
  ],
  providers: [FinanceYearService, FinanceYearRepository],
  exports: [FinanceYearService],
  controllers: [FinanceYearController],
})
export class FinanceYearModule {}
