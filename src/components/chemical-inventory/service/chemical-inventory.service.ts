import { Injectable, Logger } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';

import { ResponseCodeEnum } from '@constant/response-code.enum';

import { exportExcel } from '../../../helper/export.helper';
import { Filter } from '../../../utils/dto/request/pagination.query';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';
import { addSevenHoursToAllItems } from '../../../utils/helper';
import { QmsxTicketService } from '../../another-service/services/qmsx-ticket-service';
import { UserService } from '../../another-service/services/user-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { ExportExcelColumnProperty } from '../../export-excel/dto/export-excel-column-property.dto';
import {
  chemicalInventoryColumnProperties,
  chemicalInventoryExportSheets,
} from '../../export-excel/template/export-chemical-inventory-template';
import { GetListChemicalInventoryRequestDto } from '../request/get-list-chemical-inventory.request.dto';

@Injectable()
export class ChemicalInventoryService {
  private readonly logger = new Logger(ChemicalInventoryService.name);

  constructor(
    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly qmsxTicketService: QmsxTicketService,

    private readonly baseService: BaseProcessService,
  ) {}

  async getListChemicalInventory(
    request: GetListChemicalInventoryRequestDto,
  ): Promise<any> {
    const { page, filter } = request;

    if (filter && !filter.some((item) => item?.column === 'date')) {
      const now = new Date();
      const startOfYear = new Date(now.getFullYear(), 0, 1, 0, 0, 0, 0);
      startOfYear.setHours(startOfYear.getHours() - 7);
      const endOfToday = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        23,
        59,
        59,
        999,
      );
      endOfToday.setHours(endOfToday.getHours() - 7);

      const dateFilter = new Filter();
      dateFilter.column = 'date';
      dateFilter.text = `${startOfYear.toISOString()}|${endOfToday.toISOString()}`;
      filter.push(dateFilter);
    }

    if (!filter) {
      const now = new Date();
      const startOfYear = new Date(now.getFullYear(), 0, 1, 0, 0, 0, 0);
      startOfYear.setHours(startOfYear.getHours() - 7);
      const endOfToday = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        23,
        59,
        59,
        999,
      );
      endOfToday.setHours(endOfToday.getHours() - 7);

      const dateFilter = new Filter();
      dateFilter.column = 'date';
      dateFilter.text = `${startOfYear.toISOString()}|${endOfToday.toISOString()}`;

      const newFilter: Filter[] = [];
      newFilter.push(dateFilter);
      request.filter = newFilter;
    }

    const { data, count } =
      await this.qmsxTicketService.getListChemicalInventory(request);

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async export(request: GetListChemicalInventoryRequestDto): Promise<any> {
    const { data } = await this.getListChemicalInventory(request);
    const dataMapping = addSevenHoursToAllItems(data);
    const mapData = new Map<string, any[]>();
    const mapColumn = new Map<string, ExportExcelColumnProperty[]>();
    mapColumn.set(
      chemicalInventoryExportSheets[0],
      chemicalInventoryColumnProperties,
    );
    mapData.set(chemicalInventoryExportSheets[0], dataMapping.items);
    const fileName = await this.i18n.translate(
      'export.chemicalInventory.fileName',
    );
    const buffer = await exportExcel(
      chemicalInventoryExportSheets,
      mapData,
      mapColumn,
      fileName,
      this.i18n,
    );
    return new ResponseBuilder(buffer)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
