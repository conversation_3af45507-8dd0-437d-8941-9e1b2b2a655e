import { Modu<PERSON> } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { ChemicalInventoryController } from './controller/chemical-inventory.controller';
import { ChemicalInventoryService } from './service/chemical-inventory.service';

@Module({
  imports: [AnotherServiceModule, BaseProcessModule],
  providers: [ChemicalInventoryService],
  exports: [ChemicalInventoryService],
  controllers: [ChemicalInventoryController],
})
export class ChemicalInventoryModule {}
