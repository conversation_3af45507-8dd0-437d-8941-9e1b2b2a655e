import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';
import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  EXPORT_CHEMICAL_INVENTORY_PERMISSION,
  LIST_CHEMICAL_INVENTORY_PERMISSION,
} from '../../../utils/permissions/chemical-inventory.permission';
import { GetListChemicalInventoryRequestDto } from '../request/get-list-chemical-inventory.request.dto';
import { ChemicalInventoryService } from '../service/chemical-inventory.service';

@Controller('chemical-inventory')
export class ChemicalInventoryController {
  constructor(
    private readonly chemicalInventoryService: ChemicalInventoryService,
  ) {}

  @PermissionCode(LIST_CHEMICAL_INVENTORY_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['chemical-inventory'],
    summary: 'Danh sách chemical-inventory',
    description: 'Danh sách chemical-inventory',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getListChemicalInventory(
    @Query() query: GetListChemicalInventoryRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.chemicalInventoryService.getListChemicalInventory(request);
  }

  @PermissionCode(EXPORT_CHEMICAL_INVENTORY_PERMISSION.code)
  @Get('/export')
  @ApiOperation({
    tags: ['Export-excel'],
    summary: 'Export in list screen',
    description: 'Export in list screen',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public export(
    @Query() query: GetListChemicalInventoryRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return this.chemicalInventoryService.export(request);
  }
}
