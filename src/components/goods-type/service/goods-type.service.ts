import { Injectable, Logger } from '@nestjs/common';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { GoodsTypeEntity } from '../entities/goods-type.entity';
import { CreateGoodsTypeRequestDto } from './../request/create-goods-type.request.dto';
import { DeleteGoodsTypeRequestDto } from './../request/delete-goods-type.request.dto';
import { GetDetailGoodsTypeRequestDto } from './../request/get-detail-goods-type.request.dto';
import { GetListGoodsTypeRequestDto } from './../request/get-list-goods-type.request.dto';
import { UpdateGoodsTypeRequestDto } from './../request/update-goods-type.request.dto';
import { UpdateStatusGoodsTypeRequestDto } from './../request/update-status-goods-type.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { BaseService } from '../../../common/service/base.service';
import { UserService } from '../../another-service/services/user-service';
import { MasterDataReferenceService } from '../../master-data-reference/service/master-data-reference.service';
import { GoodsTypeRepository } from '../repository/goods-type.repository';

@Injectable()
export class GoodsTypeService extends BaseService {
  private readonly logger = new Logger(GoodsTypeService.name);

  constructor(
    private readonly goodsTypeRepository: GoodsTypeRepository,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly masterReferenceService: MasterDataReferenceService,
  ) {
    super(userService);
  }

  async create(request: CreateGoodsTypeRequestDto): Promise<any> {
    const existCode = await this.goodsTypeRepository.findOneByCode(
      request.code,
    );

    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    const goodsTypeEntity = this.goodsTypeRepository.createEntity(request);
    const goodsType = await this.goodsTypeRepository.create(goodsTypeEntity);

    const response = await this.mapUserInfoToResponse([goodsType]);

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListGoodsTypeRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.goodsTypeRepository.getList(request);

    const response = await this.mapUserInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getListFromProcesses(
    request: GetListGoodsTypeRequestDto,
  ): Promise<any> {
    const { page } = request;
    const { data, count } = await this.goodsTypeRepository.getListFromProcess(
      request,
    );

    const response = await this.mapUserInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailGoodsTypeRequestDto): Promise<any> {
    const goodsType = await this.goodsTypeRepository.getDetail(request);

    if (isEmpty(goodsType)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const response = await this.mapUserInfoToResponse([goodsType]);

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateGoodsTypeRequestDto): Promise<any> {
    const { id } = request;
    const goodsType = await this.goodsTypeRepository.findOneById(id);

    if (isEmpty(goodsType)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate goods-type in used
    const usedList = await this.validateGoodsTypeInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.GOODS_TYPE_IN_USED'))
        .build();
    }

    // Validate Code unique
    const existCode = await this.goodsTypeRepository.findOneByCode(
      request.code,
    );

    if (!isEmpty(existCode) && existCode.id !== goodsType.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    const dataUpdate = this.goodsTypeRepository.updateEntity(
      request,
      goodsType,
    );
    const data = await this.goodsTypeRepository.update(dataUpdate);

    const response = await this.mapUserInfoToResponse([data]);

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteGoodsTypeRequestDto): Promise<any> {
    const { id } = request;
    const goodsType = await this.goodsTypeRepository.findOneById(id);

    if (isEmpty(goodsType)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate goods-type in used
    const usedList = await this.validateGoodsTypeInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.GOODS_TYPE_IN_USED'))
        .build();
    }

    await this.goodsTypeRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(request: UpdateStatusGoodsTypeRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.goodsTypeRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.goodsTypeRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(request: UpdateStatusGoodsTypeRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.goodsTypeRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.goodsTypeRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async validateGoodsTypeInUsed(id: number): Promise<boolean> {
    // Validate used in items
    const itemList =
      await this.masterReferenceService.findAllItemByGoodsTypeIds([id]);
    if (!isEmpty(itemList)) {
      return true;
    }

    // Validate used in process
    const processList =
      await this.masterReferenceService.findAllProcessByGoodsTypeIds([id]);
    if (!isEmpty(processList)) {
      return true;
    }

    // Validate used in production-process
    const productionProcessList =
      await this.masterReferenceService.findAllProductionProcessByGoodsTypeIds([
        id,
      ]);
    if (!isEmpty(productionProcessList)) {
      return true;
    }

    // Validate used in another service
    const isUse =
      await this.masterReferenceService.validateDataMasterUseAnotherService(
        new ValidateMasterRequestDto('goods_type_id', id),
      );
    if (isUse) {
      return true;
    }

    return false;
  }

  async findOneById(id: number): Promise<GoodsTypeEntity> {
    return await this.goodsTypeRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<GoodsTypeEntity[]> {
    return await this.goodsTypeRepository.findAllByIds(ids);
  }
}
