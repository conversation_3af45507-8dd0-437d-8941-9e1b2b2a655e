import { Column, Entity } from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';

@Entity({ name: 'goods_types' })
export class GoodsTypeEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    unique: true,
    nullable: false,
    length: 255,
  })
  code: string;

  @Column({
    type: 'nvarchar',
    nullable: false,
    length: 255,
  })
  name: string;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  description: string;

  @Column({
    type: 'tinyint',
  })
  status: number;
}
