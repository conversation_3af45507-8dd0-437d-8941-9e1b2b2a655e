import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { GoodsTypeController } from './controller/goods-type.controller';
import { GoodsTypeEntity } from './entities/goods-type.entity';
import { GoodsTypeRepository } from './repository/goods-type.repository';
import { GoodsTypeService } from './service/goods-type.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([GoodsTypeEntity]),
    AnotherServiceModule,
    MasterDataReferenceModule,
  ],
  providers: [GoodsTypeService, GoodsTypeRepository],
  exports: [GoodsTypeService],
  controllers: [GoodsTypeController],
})
export class GoodsTypeModule {}
