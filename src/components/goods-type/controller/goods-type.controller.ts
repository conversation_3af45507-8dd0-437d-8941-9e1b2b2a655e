import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiConsumes, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  ACTIVE_GOODS_TYPE_PERMISSION,
  CREATE_GOODS_TYPE_PERMISSION,
  DELETE_GOODS_TYPE_PERMISSION,
  DETAIL_GOODS_TYPE_PERMISSION,
  INACTIVE_GOODS_TYPE_PERMISSION,
  LIST_GOODS_TYPE_PERMISSION,
  UPDATE_GOODS_TYPE_PERMISSION,
} from '../../../utils/permissions/goods-type.permission';
import { ImportExcelRequest } from '../../import-excel/request/import-excel.request.dto';
import { CreateGoodsTypeRequestDto } from '../request/create-goods-type.request.dto';
import { DeleteGoodsTypeRequestDto } from '../request/delete-goods-type.request.dto';
import { GetDetailGoodsTypeRequestDto } from '../request/get-detail-goods-type.request.dto';
import { GetListGoodsTypeRequestDto } from '../request/get-list-goods-type.request.dto';
import { UpdateGoodsTypeRequestDto } from '../request/update-goods-type.request.dto';
import { UpdateStatusGoodsTypeRequestDto } from '../request/update-status-goods-type.request.dto';
import { GoodsTypeService } from '../service/goods-type.service';

@Controller('goods-types')
export class GoodsTypeController {
  constructor(private readonly goodsTypeService: GoodsTypeService) {}

  @PermissionCode(DETAIL_GOODS_TYPE_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Goods-types'],
    summary: 'Chi tiết Goods-types',
    description: 'Chi tiết Goods-types',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailGoodsTypeRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.goodsTypeService.getDetail(request);
  }

  @PermissionCode(LIST_GOODS_TYPE_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Goods-types'],
    summary: 'Danh sách Goods-types',
    description: 'Danh sách Goods-types',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(@Query() query: GetListGoodsTypeRequestDto): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.goodsTypeService.getList(request);
  }

  @PermissionCode(LIST_GOODS_TYPE_PERMISSION.code)
  @Get('/list-from-processes')
  @ApiOperation({
    tags: ['Goods-types'],
    summary: 'Danh sách Goods-types',
    description: 'Danh sách Goods-types',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getListFromProcesses(
    @Query() query: GetListGoodsTypeRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.goodsTypeService.getListFromProcesses(request);
  }

  @PermissionCode(CREATE_GOODS_TYPE_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Goods-types'],
    summary: 'Tạo Goods-types mới',
    description: 'Tạo Goods-types mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateGoodsTypeRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.goodsTypeService.create(request);
  }

  @PermissionCode(UPDATE_GOODS_TYPE_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Goods-types'],
    summary: 'Cập nhật Goods-types',
    description: 'Cập nhật Goods-types',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(@Body() body: UpdateGoodsTypeRequestDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.goodsTypeService.update(request);
  }

  @PermissionCode(ACTIVE_GOODS_TYPE_PERMISSION.code)
  @Put('/active')
  @ApiOperation({
    tags: ['Goods-types'],
    summary: 'Cập nhật Goods-types Status Active',
    description: 'Cập nhật Goods-types Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async active(
    @Body() body: UpdateStatusGoodsTypeRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.goodsTypeService.active(request);
  }

  @PermissionCode(INACTIVE_GOODS_TYPE_PERMISSION.code)
  @Put('/inactive')
  @ApiOperation({
    tags: ['Goods-types'],
    summary: 'Cập nhật Goods-types Status Inactive',
    description: 'Cập nhật Goods-types Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async inactive(
    @Body() body: UpdateStatusGoodsTypeRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.goodsTypeService.inactive(request);
  }

  @PermissionCode(DELETE_GOODS_TYPE_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Goods-types'],
    summary: 'Xóa Goods-types',
    description: 'Xóa Goods-types',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(@Param() param: DeleteGoodsTypeRequestDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.goodsTypeService.delete(request);
  }

  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async import(@Body() payload: ImportExcelRequest): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    console.log(request);

    return;
  }
}
