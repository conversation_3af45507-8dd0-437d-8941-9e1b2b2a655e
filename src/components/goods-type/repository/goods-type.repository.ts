import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { isDateString } from 'class-validator';
import { Repository } from 'typeorm';
import { StatusEnum } from '../../../common/enums/status.enum';

import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { escapeCharForSearch } from '../../../utils/common';
import { GoodsTypeEntity } from '../entities/goods-type.entity';
import { GetDetailGoodsTypeRequestDto } from '../request/get-detail-goods-type.request.dto';
import { GetListGoodsTypeRequestDto } from '../request/get-list-goods-type.request.dto';
import { UpdateGoodsTypeRequestDto } from '../request/update-goods-type.request.dto';
import { UpdateStatusGoodsTypeRequestDto } from '../request/update-status-goods-type.request.dto';
import { CreateGoodsTypeRequestDto } from './../request/create-goods-type.request.dto';

@Injectable()
export class GoodsTypeRepository extends BaseAbstractRepository<GoodsTypeEntity> {
  constructor(
    @InjectRepository(GoodsTypeEntity)
    private readonly goodsTypeRepository: Repository<GoodsTypeEntity>,
  ) {
    super(goodsTypeRepository);
  }

  createEntity(request: CreateGoodsTypeRequestDto): GoodsTypeEntity {
    const goodsTypeEntity = new GoodsTypeEntity();
    goodsTypeEntity.code = request.code;
    goodsTypeEntity.name = request.name;
    goodsTypeEntity.description = request.description;
    goodsTypeEntity.status = StatusEnum.ACTIVE;
    goodsTypeEntity.createdBy = request.userId;
    goodsTypeEntity.updatedBy = request.userId;

    return goodsTypeEntity;
  }

  updateEntity(
    request: UpdateGoodsTypeRequestDto,
    entity: GoodsTypeEntity,
  ): GoodsTypeEntity {
    entity.name = request?.name;
    entity.description = request?.description;
    entity.updatedBy = request?.userId;

    return entity;
  }

  async getList(request: GetListGoodsTypeRequestDto): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.goodsTypeRepository.createQueryBuilder('goodstype');

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("goodstype"."code") like lower(:code) escape '\\' OR
              lower("goodstype"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('goodstype.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'code':
            query.andWhere(
              `lower("goodstype"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'name':
            query.andWhere(
              `lower("goodstype"."name") like lower(:name) escape '\\'`,
              {
                name: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'description':
            query.andWhere(
              `lower("goodstype"."description") like lower(:description) escape '\\'`,
              {
                description: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'status':
            query.andWhere('"goodstype"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'createdById':
            query.andWhere('"goodstype"."created_by" = :createdById', {
              createdById: Number(value),
            });
            break;
          case 'createdByIds':
            query.andWhere('"goodstype"."created_by" IN (:...createdByIds)', {
              createdByIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'createdAt':
            const dateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const dateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"goodstype"."created_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: dateFrom,
                dateTo: dateTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"goodstype"."updated_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: updateFrom,
                dateTo: upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'name':
            query.addOrderBy('"goodstype"."name"', order);
            break;
          case 'code':
            query.addOrderBy('"goodstype"."code"', order);
            break;
          case 'status':
            query.addOrderBy('"goodstype"."status"', order);
            break;
          case 'description':
            query.addOrderBy('"goodstype"."description"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"goodstype"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"goodstype"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"goodstype"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('goodstype.id', 'DESC');
    }

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const [data, count] = await query.getManyAndCount();

    return { data, count };
  }

  async getListFromProcess(request: GetListGoodsTypeRequestDto): Promise<any> {
    const { keyword, skip, take, filter, queryIds, isGetAll } = request;

    let query = this.goodsTypeRepository
      .createQueryBuilder('goodstype')
      .innerJoin(
        'process_goods_types',
        'pgt',
        'pgt.goods_type_id = goodstype.id',
      );

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("goodstype"."code") like lower(:code) escape '\\' OR
              lower("goodstype"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('goodstype.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'status':
            query.andWhere('"goodstype"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'processId':
            query.andWhere('pgt.process_id = :processId', {
              processId: Number(value),
            });
            break;
          case 'processIds':
            query.andWhere('pgt.process_id IN (:...processIds)', {
              processIds: value.split(',').map((id) => Number(id)),
            });
            break;
          default:
            break;
        }
      });
    }
    query = query.orderBy('goodstype.id', 'DESC');

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const [data, count] = await query.getManyAndCount();

    return { data, count };
  }

  async getDetail(
    request: GetDetailGoodsTypeRequestDto,
  ): Promise<GoodsTypeEntity> {
    const { id } = request;

    const data = await this.goodsTypeRepository.findOne({
      where: { id: id },
    });

    return data;
  }

  async active(request: UpdateStatusGoodsTypeRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.goodsTypeRepository
      .createQueryBuilder()
      .update(GoodsTypeEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(request: UpdateStatusGoodsTypeRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.goodsTypeRepository
      .createQueryBuilder()
      .update(GoodsTypeEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }
}
