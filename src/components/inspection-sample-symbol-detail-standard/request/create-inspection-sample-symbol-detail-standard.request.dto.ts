import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateInspectionSampleSymbolDetailStandardRequestDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  id: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  inspectionSampleSymbolDetailId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  inspectionStandardId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  inspectionSampleQuantityId: number;
}
