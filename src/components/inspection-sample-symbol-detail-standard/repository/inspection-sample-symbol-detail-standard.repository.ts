import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { InspectionSampleSymbolDetailStandardEntity } from '../entities/inspection-sample-symbol-detail-standard.entity';

@Injectable()
export class InspectionSampleSymbolDetailStandardRepository extends BaseAbstractRepository<InspectionSampleSymbolDetailStandardEntity> {
  constructor(
    @InjectRepository(InspectionSampleSymbolDetailStandardEntity)
    private readonly repository: Repository<InspectionSampleSymbolDetailStandardEntity>,
  ) {
    super(repository);
  }
}
