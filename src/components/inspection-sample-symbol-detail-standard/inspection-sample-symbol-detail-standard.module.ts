import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InspectionSampleSymbolDetailStandardEntity } from './entities/inspection-sample-symbol-detail-standard.entity';
import { InspectionSampleSymbolDetailStandardRepository } from './repository/inspection-sample-symbol-detail-standard.repository';
import { InspectionSampleSymbolDetailStandardService } from './service/inspection-sample-symbol-detail-standard.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([InspectionSampleSymbolDetailStandardEntity]),
  ],
  providers: [
    InspectionSampleSymbolDetailStandardService,
    InspectionSampleSymbolDetailStandardRepository,
  ],
  exports: [InspectionSampleSymbolDetailStandardService],
  controllers: [],
})
export class InspectionSampleSymbolDetailStandardModule {}
