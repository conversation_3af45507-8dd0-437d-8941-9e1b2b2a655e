import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { DataSource } from 'typeorm';

import { UserService } from '../../another-service/services/user-service';
import { InspectionSampleSymbolDetailStandardRepository } from '../repository/inspection-sample-symbol-detail-standard.repository';

@Injectable()
export class InspectionSampleSymbolDetailStandardService {
  private readonly logger = new Logger(
    InspectionSampleSymbolDetailStandardService.name,
  );

  constructor(
    private readonly inspectionSampleSymbolDetailStandardRepository: InspectionSampleSymbolDetailStandardRepository,

    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,
  ) {}
}
