import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { InspectionSampleSymbolDetailEntity } from '../../inspection-sample-symbol-detail/entities/inspection-sample-symbol-detail.entity';

@Entity({ name: 'inspection_sample_symbol_detail_standards' })
export class InspectionSampleSymbolDetailStandardEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  inspectionSampleSymbolDetailId: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  inspectionStandardId: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  inspectionSampleQuantityId: number;

  @ManyToOne(
    () => InspectionSampleSymbolDetailEntity,
    (detail) => detail.detailStandards,
    {
      orphanedRowAction: 'delete',
    },
  )
  @JoinColumn({
    name: 'inspection_sample_symbol_detail_id',
    referencedColumnName: 'id',
  })
  inspectionSampleSymbolDetail: InspectionSampleSymbolDetailEntity;
}
