import { Module } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { ChemicalAnalysisResultModule } from '../chemical-analysis-result/chemical-analysis-result.module';
import { ChemicalInventoryModule } from '../chemical-inventory/chemical-inventory.module';
import { ChemicalTcrsModule } from '../chemical-tcrs/chemical-tcrs.module';
import { CustomerClaimFeeModule } from '../customer-claim-fee/customer-claim-fee.module';
import { NgSortingTicketModule } from '../ng-sorting-ticket/ng-sorting-ticket.module';
import { ProductionProcessModule } from '../production-process/production-process.module';
import { ExportExcelController } from './controller/export-excel.controller';
import { ExportExcelService } from './service/export-excel.service';

@Module({
  imports: [
    AnotherServiceModule,
    ProductionProcessModule,
    ChemicalTcrsModule,
    ChemicalAnalysisResultModule,
    NgSortingTicketModule,
    CustomerClaimFeeModule,
    ChemicalInventoryModule,
  ],
  providers: [ExportExcelService],
  exports: [ExportExcelService],
  controllers: [ExportExcelController],
})
export class ExportExcelModule {}
