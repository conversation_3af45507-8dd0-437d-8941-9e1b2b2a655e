import { ExportExcelColumnProperty } from '../dto/export-excel-column-property.dto';

export const chemicalTcrsExportSheets = ['export.chemicalTcrs.sheet1'];

export const chemicalTcrsColumnProperties: ExportExcelColumnProperty[] = [
  new ExportExcelColumnProperty({ header: 'export.no', key: 'no', width: 20 }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalTcrs.measurementDate',
    key: 'measurementDate',
    width: 20,
    transform: (row) =>
      row.measurementDate
        ? new Date(row.measurementDate).toLocaleDateString('vi-VN', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
          })
        : '',
    styleColumn: { alignment: { horizontal: 'center' } },
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalTcrs.workshift',
    key: 'workshift',
    width: 20,
    styleColumn: { alignment: { horizontal: 'center' } },
    transform: (row) => 'Ca ' + (Number(row.workshift) + 1),
  }),
  new ExportExcelColumnProperty({
    header: 'export.process.name',
    key: 'process.name',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.deviceType.name',
    key: 'device.deviceGroup.name',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.device.name',
    key: 'device.name',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalTcrs.managementCategoryName',
    key: 'managementCategoryName',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemical.name',
    key: 'chemical.name',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalTcrs.scope',
    key: 'scope',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalTcrs.managementType',
    key: 'managementType',
    width: 20,
    transform: (row) =>
      row.managementType === 0
        ? 'NORMAL'
        : row.managementType === 1
        ? 'SPC'
        : '',
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalTcrs.measurementIndex',
    key: 'measurementIndex',
    width: 20,
    transform: (row) =>
      row.measurementIndex === 0
        ? 'Lần đo chính'
        : row.measurementIndex === 1
        ? 'Bổ sung lần 1'
        : row.measurementIndex === 2
        ? 'Bổ sung lần 2'
        : '',
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalTcrs.measurementValue',
    key: 'measurementValue',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalTcrs.adjustmentValue',
    key: 'adjustmentValue',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalTcrs.pm',
    key: 'pm',
    width: 20,
    styleColumn: { alignment: { horizontal: 'center' } },
    transform: (row) => (row.pm === 0 ? 'N' : row.pm === 1 ? 'Y' : ''),
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalTcrs.resolvedDate',
    key: 'resolvedDate',
    width: 20,
    transform: (row) => {
      if (!row.resolvedDate) return;
      const date = new Date(row.resolvedDate);
      return `${String(date.getUTCDate()).padStart(2, '0')}/${String(
        date.getUTCMonth() + 1,
      ).padStart(2, '0')}/${date.getUTCFullYear()}`;
    },
    styleColumn: {
      alignment: { horizontal: 'center' },
    },
  }),
  new ExportExcelColumnProperty({
    header: 'export.resolvedBy.fullName',
    key: 'resolvedBy.fullName',
    width: 20,
    styleColumn: {
      alignment: { horizontal: 'center' },
    },
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalTcrs.resolvedContent',
    key: 'resolvedContent',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalTcrs.status',
    key: 'status',
    width: 20,
    transform: (row) =>
      row.status === 0 ? 'Chưa xử lý' : row.status === 1 ? 'Đã xử lý' : '',
    styleColumn: {
      alignment: { horizontal: 'center' },
    },
  }),
];
