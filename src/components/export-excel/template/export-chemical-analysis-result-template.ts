import { ExportExcelColumnProperty } from '../dto/export-excel-column-property.dto';

export const chemicalAnalysisResultExportSheets = [
  'export.chemicalAnalysisResult.sheet1',
];
export const chemicalAnalysisResultColumnProperties: ExportExcelColumnProperty[] =
  [
    new ExportExcelColumnProperty({
      header: 'export.no',
      key: 'no',
      width: 20,
      styleColumn: { alignment: { horizontal: 'center' } },
    }),
    new ExportExcelColumnProperty({
      header: 'export.chemicalAnalysisResult.workshift',
      key: 'workshift',
      width: 20,
      transform: (row) =>
        row.workshift != null ? 'Ca ' + (Number(row.workshift) + 1) : '',
      styleColumn: { alignment: { horizontal: 'center' } },
    }),
    new ExportExcelColumnProperty({
      header: 'export.chemicalAnalysisResult.measurementIndex',
      key: 'index',
      width: 20,
      transform: (row) =>
        row.index === 0
          ? '<PERSON><PERSON><PERSON> đo chính'
          : row.index === 1
          ? '<PERSON><PERSON> sung lần 1'
          : row.index === 2
          ? 'Bổ sung lần 2'
          : '',
    }),
    new ExportExcelColumnProperty({
      header: 'export.chemical.name',
      key: 'chemical.name',
      width: 20,
    }),
    new ExportExcelColumnProperty({
      header: 'export.chemicalAnalysisResult.scope',
      key: 'scope',
      width: 20,
    }),
    new ExportExcelColumnProperty({
      header: 'export.chemicalAnalysisResult.managementType',
      key: 'managementType',
      width: 20,
      transform: (row) =>
        row.managementType === 0
          ? 'NORMAL'
          : row.managementType === 1
          ? 'SPC'
          : '',
    }),
  ];
