import { ExportExcelColumnProperty } from '../dto/export-excel-column-property.dto';
export const ngSortingTicketExportSheets = ['export.ngSortingTicket.sheet1'];
export const ngSortingTicketDetailColumnProperties: ExportExcelColumnProperty[] =
  [
    new ExportExcelColumnProperty({
      header: 'export.no',
      key: 'no',
      width: 20,
    }),
    new ExportExcelColumnProperty({
      header: 'export.checksheetDetail.inspectionType',
      key: 'checksheetDetail.inspectionType.name',
      width: 20,
    }),
    new ExportExcelColumnProperty({
      header: 'export.checksheetDetail.inspection',
      key: 'checksheetDetail.inspection.name',
      width: 20,
    }),
    new ExportExcelColumnProperty({
      header: 'export.ngQuantity',
      key: 'ngQuantity',
      width: 20,
    }),
    new ExportExcelColumnProperty({
      header: 'export.errorCode',
      key: 'error.code',
      width: 20,
    }),
    new ExportExcelColumnProperty({
      header: 'export.note',
      key: 'note',
      width: 20,
    }),
  ];
