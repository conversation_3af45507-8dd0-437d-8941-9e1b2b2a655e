import { ExportExcelColumnProperty } from '../dto/export-excel-column-property.dto';
const productionProcessExportSheets = [
  'export.productionProcess.sheet1',
  'export.productionProcess.sheet2',
];

const productionProcessColumnProperties: ExportExcelColumnProperty[] = [
  new ExportExcelColumnProperty({ header: 'export.no', key: 'no', width: 20 }),
  new ExportExcelColumnProperty({
    header: 'export.keyMapping',
    key: 'keyMapping',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.productionProcess.code',
    key: 'code',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.productionProcess.name',
    key: 'name',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.itemType.name',
    key: 'itemType.name',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.itemLine.name',
    key: 'itemLine.name',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.goodsType.name',
    key: 'goodsTypes',
    width: 20,
    transform: (row) => {
      const data = row.goodsTypes || [];
      return data.map((item) => item.goodsType.name).join(', ');
    },
  }),
  new ExportExcelColumnProperty({
    header: 'export.item.name',
    key: 'item.name',
    width: 20,
    transform: (row) => {
      const data = row.items || [];
      return data.map((item) => item.item.name).join(', ');
    },
  }),
  new ExportExcelColumnProperty({
    header: 'export.description',
    key: 'description',
    width: 20,
  }),
];

const productionProcessDetailColumnProperties: ExportExcelColumnProperty[] = [
  new ExportExcelColumnProperty({
    header: 'export.keyMapping',
    key: 'keyMapping',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.process.name',
    key: 'process.name',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.productionProcess.processQc',
    key: 'isProcessQc',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.productionProcess.executionNo',
    key: 'executionNo',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.description',
    key: 'description',
    width: 20,
  }),
];
export {
  productionProcessColumnProperties,
  productionProcessDetailColumnProperties,
  productionProcessExportSheets,
};
