import { ExportExcelColumnProperty } from '../dto/export-excel-column-property.dto';

export const chemicalTcrsResearchExportSheets = ['export.chemicalTcrs.sheet1'];

export const chemicalTcrsResearchColumnProperties: ExportExcelColumnProperty[] =
  [
    new ExportExcelColumnProperty({
      header: 'export.no',
      key: 'no',
      width: 20,
      styleColumn: { alignment: { horizontal: 'center' } },
    }),
    new ExportExcelColumnProperty({
      header: 'export.chemicalTcrsResearch.measurementDate',
      key: 'measurementDate',
      width: 20,
      transform: (row) =>
        row.measurementDate
          ? new Date(row.measurementDate).toLocaleDateString('vi-VN', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '',
      styleColumn: { alignment: { horizontal: 'center' } },
    }),
    new ExportExcelColumnProperty({
      header: 'export.chemicalTcrsResearch.workshift',
      key: 'workshift',
      width: 20,
      styleColumn: { alignment: { horizontal: 'center' } },
      transform: (row) =>
        row.workshift != null ? 'Ca ' + (Number(row.workshift) + 1) : '',
    }),
    new ExportExcelColumnProperty({
      header: 'export.chemicalTcrsResearch.measurementTime',
      key: 'measurementTime',
      width: 20,
    }),
    new ExportExcelColumnProperty({
      header: 'export.chemicalTcrsResearch.measurementIndex',
      key: 'measurementIndex',
      width: 20,
      transform: (row) =>
        row.measurementIndex === 0
          ? 'Lần đo chính'
          : row.measurementIndex === 1
          ? 'Bổ sung lần 1'
          : row.measurementIndex === 2
          ? 'Bổ sung lần 2'
          : '',
    }),
    new ExportExcelColumnProperty({
      header: 'export.chemical.name',
      key: 'chemical.name',
      width: 20,
    }),
    new ExportExcelColumnProperty({
      header: 'export.chemicalTcrsResearch.scope',
      key: 'scope',
      width: 20,
    }),
    new ExportExcelColumnProperty({
      header: 'export.chemicalTcrsResearch.managementType',
      key: 'managementType',
      width: 20,
      transform: (row) =>
        row.managementType === 0
          ? 'NORMAL'
          : row.managementType === 1
          ? 'SPC'
          : '',
    }),
    new ExportExcelColumnProperty({
      header: 'export.chemicalTcrsResearch.measurementValue',
      key: 'measurementValue',
      width: 20,
    }),
    new ExportExcelColumnProperty({
      header: 'export.chemicalTcrsResearch.adjustmentValue',
      key: 'adjustmentValue',
      width: 20,
    }),
    new ExportExcelColumnProperty({
      header: 'export.chemicalTcrsResearch.pm',
      key: 'pm',
      width: 20,
      styleColumn: { alignment: { horizontal: 'center' } },
      transform: (row) => (row.pm === 0 ? 'N' : row.pm === 1 ? 'Y' : ''),
    }),
  ];
