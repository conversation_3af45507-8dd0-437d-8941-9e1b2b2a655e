import { ExportExcelColumnProperty } from '../dto/export-excel-column-property.dto';

export const chemicalInventoryExportSheets = [
  'export.chemicalInventory.sheet1',
];

export const chemicalInventoryColumnProperties: ExportExcelColumnProperty[] = [
  new ExportExcelColumnProperty({ header: 'export.no', key: 'no', width: 10 }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalInventory.chemicalCode',
    key: 'chemical.code',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalInventory.chemicalName',
    key: 'chemical.name',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalInventory.unit',
    key: 'chemical.unit.name',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalInventory.beginningBalance',
    key: 'beginningBalance',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalInventory.totalImportQuantity',
    key: 'totalImportQuantity',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalInventory.totalExportQuantity',
    key: 'totalExportQuantity',
    width: 20,
  }),
  new ExportExcelColumnProperty({
    header: 'export.chemicalInventory.endingBalance',
    key: 'endingBalance',
    width: 20,
  }),
];
