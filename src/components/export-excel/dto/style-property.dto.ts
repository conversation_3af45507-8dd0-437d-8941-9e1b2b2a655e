export class StyleHeaderPropertyDto {
  font?: {
    name?: string;
    size?: number;
    bold?: boolean;
  } = {
    bold: true,
    name: 'Times New Roman',
    size: 13,
  };

  alignment?: {
    horizontal?: 'center' | 'left' | 'right';
    vertical?: 'top' | 'middle' | 'bottom';
  } = {
    horizontal: 'center',
    vertical: 'middle',
  };

  border?: {
    top?: { style: 'thin' | 'medium' | 'thick'; color: { argb: string } };
    bottom?: { style: 'thin' | 'medium' | 'thick'; color: { argb: string } };
    left?: { style: 'thin' | 'medium' | 'thick'; color: { argb: string } };
    right?: { style: 'thin' | 'medium' | 'thick'; color: { argb: string } };
  } = {
    top: {
      style: 'thin',
      color: {
        argb: '000000',
      },
    },
    bottom: {
      style: 'thin',
      color: {
        argb: '000000',
      },
    },
    right: {
      style: 'thin',
      color: {
        argb: '000000',
      },
    },
    left: {
      style: 'thin',
      color: {
        argb: '000000',
      },
    },
  };

  fill?: {
    type: 'pattern';
    pattern: 'solid';
    fgColor?: { argb: string };
  } = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: {
      argb: 'dae7f3',
    },
  };
}

export class StyleColumnPropertyDto {
  font?: {
    name?: string;
    size?: number;
    bold?: boolean;
  } = {
    bold: false,
    name: 'Times New Roman',
    size: 12,
  };

  alignment?: {
    horizontal?: 'center' | 'left' | 'right';
    vertical?: 'top' | 'middle' | 'bottom';
  } = {
    horizontal: 'left',
    vertical: 'middle',
  };

  border?: {
    top?: { style: 'thin' | 'medium' | 'thick'; color: { argb: string } };
    bottom?: { style: 'thin' | 'medium' | 'thick'; color: { argb: string } };
    left?: { style: 'thin' | 'medium' | 'thick'; color: { argb: string } };
    right?: { style: 'thin' | 'medium' | 'thick'; color: { argb: string } };
  } = {
    top: {
      style: 'thin',
      color: {
        argb: '000000',
      },
    },
    bottom: {
      style: 'thin',
      color: {
        argb: '000000',
      },
    },
    right: {
      style: 'thin',
      color: {
        argb: '000000',
      },
    },
    left: {
      style: 'thin',
      color: {
        argb: '000000',
      },
    },
  };

  fill?: {
    type: 'pattern';
    pattern: 'solid';
    fgColor?: { argb: string };
  };
}
