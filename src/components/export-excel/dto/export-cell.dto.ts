import { StyleColumnPropertyDto } from './style-property.dto';

export class ExportCellStringDto {
  value?: string;
  style?: StyleColumnPropertyDto;

  constructor(init?: Partial<ExportCellStringDto>) {
    this.style = new StyleColumnPropertyDto();

    if (init) {
      Object.assign(this, init);

      if (init.style) {
        this.style = Object.assign(new StyleColumnPropertyDto(), init.style);
      }
    }
  }
}
export class ExportCellNumberDto {
  value?: number;
  style?: StyleColumnPropertyDto;
  nmtFmt?: string;

  constructor(init?: Partial<ExportCellNumberDto>) {
    this.style = new StyleColumnPropertyDto();

    if (init) {
      Object.assign(this, init);

      if (init.style) {
        this.style = Object.assign(new StyleColumnPropertyDto(), init.style);
      }
    }
    this.nmtFmt =
      this.value != null && this.value % 1 === 0 ? '#,##0' : '#,##0.##';
  }
}
export class ExportCellDateDto {
  value: Date;
  style?: StyleColumnPropertyDto;

  constructor(init?: Partial<ExportCellDateDto>) {
    this.style = new StyleColumnPropertyDto();

    if (init) {
      Object.assign(this, init);

      if (init.style) {
        this.style = Object.assign(new StyleColumnPropertyDto(), init.style);
      }
    }
  }
}
