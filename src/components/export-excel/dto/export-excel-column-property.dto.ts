import {
  StyleColumnPropertyDto,
  StyleHeaderPropertyDto,
} from './style-property.dto';

export class ExportExcelColumnProperty {
  header: string;

  key: string;

  width?: number;

  styleColumn?: StyleColumnPropertyDto;

  styleHeader?: StyleHeaderPropertyDto;

  outLineLevel?: number;

  hidden?: boolean;

  numFmt?: string;

  defaultValue?: string;

  merge?: number;

  required?: boolean;

  transform?: (row: any) => string;

  constructor(init?: Partial<ExportExcelColumnProperty>) {
    this.styleColumn = new StyleColumnPropertyDto();
    this.styleHeader = new StyleHeaderPropertyDto();

    if (init) {
      Object.assign(this, init);

      if (init.styleColumn) {
        this.styleColumn = Object.assign(
          new StyleColumnPropertyDto(),
          init.styleColumn,
        );
      }

      if (init.styleHeader) {
        this.styleHeader = Object.assign(
          new StyleHeaderPropertyDto(),
          init.styleHeader,
        );
      }
    }
  }
}
