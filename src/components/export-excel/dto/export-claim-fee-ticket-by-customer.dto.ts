import { ExportCellNumberDto, ExportCellStringDto } from './export-cell.dto';

export class ExportClaimFeeTicketByCustomerDto {
  itemCode: ExportCellStringDto;
  itemName: ExportCellStringDto;
  itemLineName: ExportCellStringDto;
  goodsTypeName: ExportCellStringDto;
  unitName: ExportCellStringDto;
  claimQuantity: ExportCellNumberDto;
  estimatedUnitPrice: ExportCellNumberDto;
  estimatedAmount: ExportCellNumberDto;
  errorName: ExportCellStringDto;
  passQuantity: ExportCellNumberDto;
  ngBillingQuantity: ExportCellNumberDto;
  billingUnitPrice: ExportCellNumberDto;
  billingAmount: ExportCellNumberDto;

  constructor() {
    const properties = [
      'itemCode',
      'itemName',
      'itemLineName',
      'goodsTypeName',
      'unitName',
      'claimQuantity',
      'estimatedUnitPrice',
      'estimatedAmount',
      'errorName',
      'passQuantity',
      'ngBillingQuantity',
      'billingUnitPrice',
      'billingAmount',
    ];

    properties.forEach((prop) => {
      Object.defineProperty(this, prop, {
        enumerable: true,
        writable: true,
        configurable: true,
      });
    });
  }
}
