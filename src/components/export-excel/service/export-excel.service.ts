import { Injectable, Logger } from '@nestjs/common';
import * as fs from 'fs';
import { I18nService } from 'nestjs-i18n';

import path from 'path';
import { BaseService } from '../../../common/service/base.service';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { SRC_DIR } from '../../../main';
import { ResponseBuilder } from '../../../utils/response-builder';
import { UserService } from '../../another-service/services/user-service';
import { ChemicalAnalysisResultService } from '../../chemical-analysis-result/service/chemical-analysis-result.service';
import { ChemicalInventoryService } from '../../chemical-inventory/service/chemical-inventory.service';
import { ChemicalTcrsService } from '../../chemical-tcrs/service/chemical-tcrs.service';
import { CustomerClaimFeeService } from '../../customer-claim-fee/service/customer-claim-fee.service';
import { NgSortingTicketService } from '../../ng-sorting-ticket/service/ng-sorting-ticket.service';
import { ProductionProcessService } from '../../production-process/service/production-process.service';
import {
  CUSTOMER_CLAIM_FEE_BY_CUSTOMER_TEMPLATE,
  EXPORT_TYPE,
} from '../constants/export-excel.constants';
import { GetDetailExportExcelRequestDto } from '../request/get-detail-export-excel.request.dto';
import { GetListExportExcelRequestDto } from '../request/get-list-export-excel.request.dto';
import { exportChemicalAnalysisResult } from './export-chemical-analysis-result.service';
import { exportChemicalInventory } from './export-chemical-inventory.service';
import { exportChemicalTcrs } from './export-chemical-tcrs.service';
import { exportCustomerClaimFeeByCustomer } from './export-customer-claim-fee-by-customer.service';
import { exportNgSortingTicketDetail } from './export-ng-sorting-ticket.service';
import { exportProductionProcess } from './export-production-process.service';
import { exportChemicalTcrsResearch } from './export-research-chemical-tcrs.service';

@Injectable()
export class ExportExcelService extends BaseService {
  private readonly logger = new Logger(ExportExcelService.name);

  constructor(
    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly productionProcessService: ProductionProcessService,
    private readonly chemicalTcrsService: ChemicalTcrsService,
    private readonly chemicalInventoryService: ChemicalInventoryService,
    private readonly chemicalAnalysisResultService: ChemicalAnalysisResultService,
    private readonly ngSortingTicketService: NgSortingTicketService,
    private readonly customerClaimFeeService: CustomerClaimFeeService,
  ) {
    super(userService);
  }

  async export(request: GetListExportExcelRequestDto): Promise<any> {
    switch (request.typeScreen) {
      case EXPORT_TYPE.PRODUCTION_PROCESS:
        return await exportProductionProcess(
          request,
          this.i18n,
          this.productionProcessService,
        );
      case EXPORT_TYPE.CHEMICAL_TCRS:
        return await exportChemicalTcrs(
          request,
          this.i18n,
          this.chemicalTcrsService,
        );
      case EXPORT_TYPE.CHEMICAL_ANALYSIS_RESULT:
        return await exportChemicalAnalysisResult(
          request,
          this.i18n,
          this.chemicalAnalysisResultService,
        );
      case EXPORT_TYPE.CHEMICAL_TCRS_RESEARCH:
        return await exportChemicalTcrsResearch(
          request,
          this.i18n,
          this.chemicalTcrsService,
        );
      case EXPORT_TYPE.CHEMICAL_INVENTORY:
        return await exportChemicalInventory(
          request,
          this.i18n,
          this.chemicalInventoryService,
        );
      default:
        return new ResponseBuilder()
          .withCode(ResponseCodeEnum.SUCCESS)
          .withMessage(await this.i18n.translate('success.SUCCESS'))
          .build();
    }
  }

  async exportDetail(request: GetDetailExportExcelRequestDto): Promise<any> {
    switch (request.typeScreen) {
      case EXPORT_TYPE.NG_SORTING_TICKET_DETAIL:
        return await exportNgSortingTicketDetail(
          request,
          this.i18n,
          this.ngSortingTicketService,
        );
      case EXPORT_TYPE.CUSTOMER_CLAIM_FEE_BY_CUSTOMER:
        const templatePath = this.pathFileName(
          CUSTOMER_CLAIM_FEE_BY_CUSTOMER_TEMPLATE.name,
        );
        if (!fs.existsSync(templatePath)) {
          return new ResponseBuilder()
            .withCode(ResponseCodeEnum.BAD_REQUEST)
            .withMessage(await this.i18n.translate('error.FILE_NOT_FOUND'))
            .build();
        }
        return await exportCustomerClaimFeeByCustomer(
          request,
          this.i18n,
          this.customerClaimFeeService,
          templatePath,
        );
      default:
        return new ResponseBuilder()
          .withCode(ResponseCodeEnum.SUCCESS)
          .withMessage(await this.i18n.translate('success.SUCCESS'))
          .build();
    }
  }

  private pathFileName(fileName: string) {
    return path.join(SRC_DIR, '..', 'export-template', fileName);
  }
}
