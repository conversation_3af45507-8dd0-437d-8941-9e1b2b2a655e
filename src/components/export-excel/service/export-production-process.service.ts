import { I18nService } from 'nestjs-i18n';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { buildKeyMapping, exportExcel } from '../../../helper/export.helper';
import { ResponseBuilder } from '../../../utils/response-builder';
import { ProductionProcessService } from '../../production-process/service/production-process.service';
import { ExportExcelColumnProperty } from '../dto/export-excel-column-property.dto';
import { GetListExportExcelRequestDto } from '../request/get-list-export-excel.request.dto';
import {
  productionProcessColumnProperties,
  productionProcessDetailColumnProperties,
  productionProcessExportSheets,
} from '../template/export-production-process-template';

export async function exportProductionProcess(
  request: GetListExportExcelRequestDto,
  i18n: I18nService,
  productionProcessService: ProductionProcessService,
): Promise<any> {
  const { data } = await productionProcessService.getList(request);
  const mapData = new Map<string, any[]>();
  const mapColumn = new Map<string, ExportExcelColumnProperty[]>();
  mapColumn.set(
    productionProcessExportSheets[0],
    productionProcessColumnProperties,
  );
  mapColumn.set(
    productionProcessExportSheets[1],
    productionProcessDetailColumnProperties,
  );
  const productionProcessData = buildKeyMapping(data.items);
  const productionProcessDetailData = productionProcessData.flatMap(
    (item) => item.dataList,
  );
  mapData.set(productionProcessExportSheets[0], productionProcessData);
  mapData.set(productionProcessExportSheets[1], productionProcessDetailData);
  const fileName = await i18n.translate('export.productionProcess.fileName');
  const buffer = await exportExcel(
    productionProcessExportSheets,
    mapData,
    mapColumn,
    fileName,
    i18n,
  );
  return new ResponseBuilder(buffer)
    .withCode(ResponseCodeEnum.SUCCESS)
    .withMessage(await i18n.translate('success.SUCCESS'))
    .build();
}
