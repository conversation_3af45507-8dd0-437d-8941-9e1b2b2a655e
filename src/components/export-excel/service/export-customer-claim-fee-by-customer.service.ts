import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { writeDataToExcel } from '../../../helper/export.helper';
import { IdParamSqlDto } from '../../../utils/dto/request/param-id-sql.request.dto';
import { formatDateWithOffset } from '../../../utils/helper';
import { ResponseBuilder } from '../../../utils/response-builder';
import { CustomerClaimFeeService } from '../../customer-claim-fee/service/customer-claim-fee.service';
import { CUSTOMER_CLAIM_FEE_BY_CUSTOMER_TEMPLATE } from '../constants/export-excel.constants';
import {
  ExportCellNumberDto,
  ExportCellStringDto,
} from '../dto/export-cell.dto';
import { ExportClaimFeeTicketByCustomerDto } from '../dto/export-claim-fee-ticket-by-customer.dto';

export async function exportCustomerClaimFeeByCustomer(
  request: IdParamSqlDto,
  i18n: I18nService,
  customerClaimFeeService: CustomerClaimFeeService,
  templatePath: string,
): Promise<any> {
  const { data } = await customerClaimFeeService.getDetail(request);

  if (isEmpty(data)) {
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.NOT_FOUND)
      .withMessage(await i18n.translate('error.NOT_FOUND'))
      .build();
  }

  const mapReplace: Map<string, string> = new Map<string, string>();

  const mapData: Map<number, ExportClaimFeeTicketByCustomerDto[]> = new Map<
    number,
    ExportClaimFeeTicketByCustomerDto[]
  >();
  const formattedFromDate = formatDateWithOffset(new Date(data.fromDate), 7);
  const formattedToDate = formatDateWithOffset(new Date(data.toDate), 7);

  mapReplace.set('customerName', data.customer.name);
  mapReplace.set('claimPeriod', data.claimPeriod);
  mapReplace.set('datePeriod', `${formattedFromDate} - ${formattedToDate}`);
  mapReplace.set('totalEstimatedAmount', data.totalEstimatedAmount);
  mapReplace.set('totalBillingAmount', data.totalBillingAmount);

  const listExportDto: ExportClaimFeeTicketByCustomerDto[] =
    data.customerClaimFeeDetail.map((ticket) => {
      const exportDto = new ExportClaimFeeTicketByCustomerDto();
      exportDto.itemCode = new ExportCellStringDto({
        value: ticket.item.code,
      });
      exportDto.itemName = new ExportCellStringDto({
        value: ticket.item.name,
      });
      exportDto.itemLineName = new ExportCellStringDto({
        value: ticket.itemLine.name,
      });
      exportDto.goodsTypeName = new ExportCellStringDto({
        value: ticket.item.goodsType.name ?? '',
      });
      exportDto.unitName = new ExportCellStringDto({
        value: ticket.item.itemUnits[0].unitName,
      });
      exportDto.claimQuantity = new ExportCellNumberDto({
        value: ticket.claimQuantity,
        style: { alignment: { horizontal: 'right' } },
      });
      exportDto.estimatedUnitPrice = new ExportCellNumberDto({
        value: ticket.estimatedUnitPrice,
        style: { alignment: { horizontal: 'right' } },
      });
      exportDto.estimatedAmount = new ExportCellNumberDto({
        value: ticket.claimQuantity * ticket.estimatedUnitPrice,
        style: { alignment: { horizontal: 'right' } },
      });
      exportDto.errorName = new ExportCellStringDto({
        value: ticket.error.name,
      });
      exportDto.passQuantity = new ExportCellNumberDto({
        value: ticket.passQuantity,
        style: { alignment: { horizontal: 'right' } },
      });
      exportDto.ngBillingQuantity = new ExportCellNumberDto({
        value: ticket.ngBillingQuantity,
        style: { alignment: { horizontal: 'right' } },
      });
      exportDto.billingUnitPrice = new ExportCellNumberDto({
        value: ticket.billingUnitPrice,
        style: { alignment: { horizontal: 'right' } },
      });
      exportDto.billingAmount = new ExportCellNumberDto({
        value: ticket.ngBillingQuantity * ticket.billingUnitPrice,
        style: { alignment: { horizontal: 'right' } },
      });
      return exportDto;
    });

  mapData.set(CUSTOMER_CLAIM_FEE_BY_CUSTOMER_TEMPLATE.indexRow, listExportDto);

  const buffer = await writeDataToExcel({
    templatePath: templatePath,
    mapData: mapData,
    mapReplace: mapReplace,
  });
  return new ResponseBuilder(buffer)
    .withCode(ResponseCodeEnum.SUCCESS)
    .withMessage(await i18n.translate('success.SUCCESS'))
    .build();
}
