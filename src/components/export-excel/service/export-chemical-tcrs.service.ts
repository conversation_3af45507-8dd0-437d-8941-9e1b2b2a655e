import { I18nService } from 'nestjs-i18n';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { exportExcel } from '../../../helper/export.helper';
import { addSevenHoursToAllItems } from '../../../utils/helper';
import { ResponseBuilder } from '../../../utils/response-builder';
import { ChemicalTcrsService } from '../../chemical-tcrs/service/chemical-tcrs.service';
import { ExportExcelColumnProperty } from '../dto/export-excel-column-property.dto';
import { GetListExportExcelRequestDto } from '../request/get-list-export-excel.request.dto';
import {
  chemicalTcrsColumnProperties,
  chemicalTcrsExportSheets,
} from '../template/export-chemical-tcrs-template';

export async function exportChemicalTcrs(
  request: GetListExportExcelRequestDto,
  i18n: I18nService,
  chemicalTcrsService: ChemicalTcrsService,
): Promise<any> {
  const { data } = await chemicalTcrsService.getList(request);
  const dataMapping = addSevenHoursToAllItems(data);
  const mapData = new Map<string, any[]>();
  const mapColumn = new Map<string, ExportExcelColumnProperty[]>();
  mapColumn.set(chemicalTcrsExportSheets[0], chemicalTcrsColumnProperties);
  mapData.set(chemicalTcrsExportSheets[0], dataMapping.items);
  const fileName = await i18n.translate('export.chemicalTcrs.fileName');
  const buffer = await exportExcel(
    chemicalTcrsExportSheets,
    mapData,
    mapColumn,
    fileName,
    i18n,
  );
  return new ResponseBuilder(buffer)
    .withCode(ResponseCodeEnum.SUCCESS)
    .withMessage(await i18n.translate('success.SUCCESS'))
    .build();
}
