import { I18nService } from 'nestjs-i18n';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { exportExcel } from '../../../helper/export.helper';
import { IdParamSqlDto } from '../../../utils/dto/request/param-id-sql.request.dto';
import { ResponseBuilder } from '../../../utils/response-builder';
import { NgSortingTicketService } from '../../ng-sorting-ticket/service/ng-sorting-ticket.service';
import { ExportExcelColumnProperty } from '../dto/export-excel-column-property.dto';
import {
  ngSortingTicketDetailColumnProperties,
  ngSortingTicketExportSheets,
} from '../template/export-ng-sorting-ticket-template';

export async function exportNgSortingTicketDetail(
  request: IdParamSqlDto,
  i18n: I18nService,
  ngSortingTicketService: NgSortingTicketService,
): Promise<any> {
  const { data } = await ngSortingTicketService.getDetail(request);
  const mapData = new Map<string, any[]>();
  const mapColumn = new Map<string, ExportExcelColumnProperty[]>();
  mapColumn.set(
    ngSortingTicketExportSheets[0],
    ngSortingTicketDetailColumnProperties,
  );
  mapData.set(ngSortingTicketExportSheets[0], data.ngSortingTicketDetail);
  const buffer = await exportExcel(
    ngSortingTicketExportSheets,
    mapData,
    mapColumn,
    null,
    i18n,
  );
  return new ResponseBuilder(buffer)
    .withCode(ResponseCodeEnum.SUCCESS)
    .withMessage(await i18n.translate('success.SUCCESS'))
    .build();
}
