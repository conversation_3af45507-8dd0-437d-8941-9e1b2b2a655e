import { I18nService } from 'nestjs-i18n';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { exportExcel } from '../../../helper/export.helper';
import { addSevenHoursToAllItems } from '../../../utils/helper';
import { ResponseBuilder } from '../../../utils/response-builder';
import { ChemicalTcrsService } from '../../chemical-tcrs/service/chemical-tcrs.service';
import { ExportExcelColumnProperty } from '../dto/export-excel-column-property.dto';
import { GetListExportExcelRequestDto } from '../request/get-list-export-excel.request.dto';
import {
  chemicalTcrsResearchColumnProperties,
  chemicalTcrsResearchExportSheets,
} from '../template/export-chemical-tcrs-research-template';

export async function exportChemicalTcrsResearch(
  request: GetListExportExcelRequestDto,
  i18n: I18nService,
  chemicalTcrsService: ChemicalTcrsService,
): Promise<any> {
  const { data } = await chemicalTcrsService.getList(request);
  const dataMapping = addSevenHoursToAllItems(data);
  const mapData = new Map<string, any[]>();
  const mapColumn = new Map<string, ExportExcelColumnProperty[]>();
  mapColumn.set(
    chemicalTcrsResearchExportSheets[0],
    chemicalTcrsResearchColumnProperties,
  );
  mapData.set(chemicalTcrsResearchExportSheets[0], dataMapping.items);
  const fileName = await i18n.translate('export.chemicalTcrsResearch.fileName');
  const buffer = await exportExcel(
    chemicalTcrsResearchExportSheets,
    mapData,
    mapColumn,
    fileName,
    i18n,
  );
  return new ResponseBuilder(buffer)
    .withCode(ResponseCodeEnum.SUCCESS)
    .withMessage(await i18n.translate('success.SUCCESS'))
    .build();
}
