import { I18nService } from 'nestjs-i18n';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { exportExcel } from '../../../helper/export.helper';
import { addSevenHoursToAllItems } from '../../../utils/helper';
import { ResponseBuilder } from '../../../utils/response-builder';
import { ChemicalInventoryService } from '../../chemical-inventory/service/chemical-inventory.service';
import { ExportExcelColumnProperty } from '../dto/export-excel-column-property.dto';
import { GetListExportExcelRequestDto } from '../request/get-list-export-excel.request.dto';
import {
  chemicalInventoryColumnProperties,
  chemicalInventoryExportSheets,
} from '../template/export-chemical-inventory-template';

export async function exportChemicalInventory(
  request: GetListExportExcelRequestDto,
  i18n: I18nService,
  chemicalInventoryService: ChemicalInventoryService,
): Promise<any> {
  const { data } = await chemicalInventoryService.getListChemicalInventory(
    request,
  );
  const dataMapping = addSevenHoursToAllItems(data);
  const mapData = new Map<string, any[]>();
  const mapColumn = new Map<string, ExportExcelColumnProperty[]>();
  mapColumn.set(
    chemicalInventoryExportSheets[0],
    chemicalInventoryColumnProperties,
  );
  mapData.set(chemicalInventoryExportSheets[0], dataMapping.items);
  const fileName = await i18n.translate('export.chemicalInventory.fileName');
  const buffer = await exportExcel(
    chemicalInventoryExportSheets,
    mapData,
    mapColumn,
    fileName,
    i18n,
  );
  return new ResponseBuilder(buffer)
    .withCode(ResponseCodeEnum.SUCCESS)
    .withMessage(await i18n.translate('success.SUCCESS'))
    .build();
}
