import { I18nService } from 'nestjs-i18n';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';
import { exportExcel } from '../../../helper/export.helper';
import { addSevenHoursToAllItems } from '../../../utils/helper';
import { ResponseBuilder } from '../../../utils/response-builder';
import { ChemicalAnalysisResultService } from '../../chemical-analysis-result/service/chemical-analysis-result.service';
import { ExportExcelColumnProperty } from '../dto/export-excel-column-property.dto';
import { GetListExportExcelRequestDto } from '../request/get-list-export-excel.request.dto';
import {
  chemicalAnalysisResultColumnProperties,
  chemicalAnalysisResultExportSheets,
} from '../template/export-chemical-analysis-result-template';

export async function exportChemicalAnalysisResult(
  request: GetListExportExcelRequestDto,
  i18n: I18nService,
  chemicalAnalysisResultService: ChemicalAnalysisResultService,
): Promise<any> {
  const { data } = await chemicalAnalysisResultService.getList(request);
  const dataMapping = addSevenHoursToAllItems(data);
  const dates = new Set<string>();

  const transformedData = dataMapping.items.map((item) => {
    const transformedItem = { ...item };
    item.results.forEach((result) => {
      const date = new Date(result.measurementDate);

      const year = date.getUTCFullYear();
      const month = String(date.getUTCMonth() + 1).padStart(2, '0');
      const day = String(date.getUTCDate()).padStart(2, '0');

      const formattedDate = `${day}/${month}/${year}`;
      dates.add(formattedDate);
      const measurementDateField = `measurementDate${formattedDate}`;
      const adjustmentValueField = `adjustmentValue${formattedDate}`;
      const pmField = `pm${formattedDate}`;
      transformedItem[measurementDateField] = result.measurementValue;
      transformedItem[adjustmentValueField] = result.adjustmentValue;
      transformedItem[pmField] =
        result.pm === 0 ? 'N' : result.pm === 1 ? 'Y' : '';
    });
    return transformedItem;
  });
  const sortedDates = [...dates].sort((a, b) => {
    const [dayA, monthA, yearA] = a.split('/');
    const [dayB, monthB, yearB] = b.split('/');

    const dateA = new Date(Number(yearA), Number(monthA) - 1, Number(dayA));
    const dateB = new Date(Number(yearB), Number(monthB) - 1, Number(dayB));

    return dateA.getTime() - dateB.getTime();
  });
  const mapData = new Map<string, any[]>();
  const mapColumn = new Map<string, ExportExcelColumnProperty[]>();
  const colomnProperties = [...chemicalAnalysisResultColumnProperties];
  sortedDates.forEach((s) => {
    colomnProperties.push(
      new ExportExcelColumnProperty({
        header: s,
        key: 'measurementDate' + s,
        width: 20,
        styleColumn: {
          alignment: {
            horizontal: 'right',
          },
        },
      }),
    );

    colomnProperties.push(
      new ExportExcelColumnProperty({
        header: 'export.chemicalAnalysisResult.adjustmentValue',
        key: 'adjustmentValue' + s,
        width: 20,
      }),
    );

    colomnProperties.push(
      new ExportExcelColumnProperty({
        header: 'export.chemicalAnalysisResult.pm',
        key: 'pm' + s,
        width: 20,
        styleColumn: {
          alignment: {
            horizontal: 'center',
          },
        },
      }),
    );
  });
  mapColumn.set(chemicalAnalysisResultExportSheets[0], colomnProperties);
  mapData.set(chemicalAnalysisResultExportSheets[0], transformedData);
  const fileName = await i18n.translate(
    'export.chemicalAnalysisResult.fileName',
  );
  const buffer = await exportExcel(
    chemicalAnalysisResultExportSheets,
    mapData,
    mapColumn,
    fileName,
    i18n,
  );
  return new ResponseBuilder(buffer)
    .withCode(ResponseCodeEnum.SUCCESS)
    .withMessage(await i18n.translate('success.SUCCESS'))
    .build();
}
