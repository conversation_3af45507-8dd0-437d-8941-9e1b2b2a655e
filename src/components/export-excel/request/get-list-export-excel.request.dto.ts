import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { PaginationQuery } from '../../../utils/dto/request/pagination.query';
import { EXPORT_TYPE } from '../constants/export-excel.constants';

const exportTypeDescription = Object.entries(EXPORT_TYPE)
  .filter(([, value]) => typeof value === 'number')
  .map(([key, value]) => `- ${value}: ${key}`)
  .join('\n');
export class GetListExportExcelRequestDto extends PaginationQuery {
  @ApiProperty({
    enum: EXPORT_TYPE,
    enumName: 'EXPORT_TYPE',
    description: `List export screen type:\n${exportTypeDescription}`,
  })
  @IsNotEmpty()
  @Type(() => Number)
  @IsEnum(EXPORT_TYPE)
  typeScreen: EXPORT_TYPE;
}
