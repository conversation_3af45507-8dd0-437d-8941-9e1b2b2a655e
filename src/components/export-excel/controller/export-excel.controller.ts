import { Controller, Get, Query } from '@nestjs/common';

import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'class-validator';
import { GetDetailExportExcelRequestDto } from '../request/get-detail-export-excel.request.dto';
import { GetListExportExcelRequestDto } from '../request/get-list-export-excel.request.dto';
import { ExportExcelService } from '../service/export-excel.service';

@Controller('export-excels')
export class ExportExcelController {
  constructor(private readonly exportExcelService: ExportExcelService) {}

  @Get('/export')
  @ApiOperation({
    tags: ['Export-excel'],
    summary: 'Export in list screen',
    description: 'Export in list screen',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public export(@Query() query: GetListExportExcelRequestDto): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return this.exportExcelService.export(request);
  }

  @Get('/export-detail')
  @ApiOperation({
    tags: ['Export-excel'],
    summary: 'Export in detail screen',
    description: 'Export in detail screen',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public exportDetail(
    @Query() query: GetDetailExportExcelRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return this.exportExcelService.exportDetail(request);
  }
}
