import { Inject, Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { DeleteEquipmentCalibrationTicketRequestDto } from '../request/delete-equipment-calibration-ticket.request.dto';
import { GetDetailEquipmentCalibrationTicketRequestDto } from '../request/get-detail-equipment-calibration-ticket.request.dto';
import { GetListEquipmentCalibrationTicketRequestDto } from '../request/get-list-equipment-calibration-ticket.request.dto';
import { UpdateEquipmentCalibrationTicketFormDto } from '../request/update-equipment-calibration-ticket-form.request.dto';
import { UpdateStatusEquipmentCalibrationTicketRequestDto } from '../request/update-status-equipment-calibration-ticket.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { FileResource } from '../../../core/components/file/constant/file-upload.constant';
import { FileService } from '../../../core/components/file/file.service';
import {
  FileDelete,
  FileUpload,
} from '../../../core/dto/request/file.request.dto';
import { MmsDeviceService } from '../../another-service/services/mms-device-service';
import { QmsxPlanService } from '../../another-service/services/qmsx-plan-service';
import { QmsxTicketService } from '../../another-service/services/qmsx-ticket-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { FileRequestDto } from '../../common/request/file.request.dto';
import { EQUIPMENT_CALIBRATION_PLAN_STATUS_ENUM } from '../../equipment-calibration-plan/enums/equipment-calibration-plan-status.enum';
import { EQUIPMENT_CALIBRATION_TICKET_STATUS_ENUM } from '../enums/equipment-calibration-ticket-status.enum';
import { CreateEquipmentCalibrationTicketsDto } from '../request/create-equipment-calibration-ticket.request.dto';
import { NoteEquipmentCalibrationTicketRequestDto } from '../request/note-equipment-calibration-ticket.request.dto';
import { UpdateEquipmentCalibrationTicketDto } from '../request/update-equipment-calibration-ticket.request.dto';
import { UpdateLastCalibrationRequestDto } from '../request/update-last-calibration.request.dto';

@Injectable()
export class EquipmentCalibrationTicketService {
  private readonly logger = new Logger(EquipmentCalibrationTicketService.name);

  constructor(
    private readonly i18n: I18nService,

    @Inject('FileServiceInterface')
    private readonly fileService: FileService,

    private readonly qmsxPlanService: QmsxPlanService,

    private readonly mmsDeviceService: MmsDeviceService,

    private readonly qmsxTicketService: QmsxTicketService,

    private readonly baseService: BaseProcessService,
  ) {}

  async handleFile(uploadFiles: FileUpload[], deleteFiles: FileDelete[]) {
    const savedFileResponse = await this.fileService.handleSaveFiles({
      resource: FileResource.QMSX_TICKET,
      deleteFiles: deleteFiles,
      uploadFiles: uploadFiles,
    });

    if (savedFileResponse.statusCode !== ResponseCodeEnum.SUCCESS) {
      return savedFileResponse;
    }

    const filesResponse = await this.fileService.getFilesByIds(
      savedFileResponse.data,
    );

    return filesResponse?.map((res) => {
      const fileRequestDto = new FileRequestDto();
      fileRequestDto.fileId = res.id;
      fileRequestDto.fileName = res.fileNameRaw;
      fileRequestDto.fileUrl = res.fileUrl;
      return fileRequestDto;
    });
  }

  async create(request: CreateEquipmentCalibrationTicketsDto): Promise<any> {
    const equipmentCalibrationTicket = plainToInstance(
      CreateEquipmentCalibrationTicketsDto,
      request,
    );
    equipmentCalibrationTicket.createdBy = request.userId;
    const response =
      await this.qmsxTicketService.createEquipmentCalibrationTicket(
        equipmentCalibrationTicket,
      );

    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(
    request: GetListEquipmentCalibrationTicketRequestDto,
  ): Promise<any> {
    const { page } = request;
    const { data, count } =
      await this.qmsxTicketService.getListQmsxEquipmentCalibrationRequest(
        request,
      );

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(
    request: GetDetailEquipmentCalibrationTicketRequestDto,
  ): Promise<any> {
    const equipmentCalibrationTicket =
      await this.qmsxTicketService.getDetailQmsxEquipmentCalibrationTicketById(
        request,
      );

    if (isEmpty(equipmentCalibrationTicket)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMapUser = await this.baseService.mapMasterInfoToResponse([
      equipmentCalibrationTicket,
    ]);
    const details = await this.baseService.mapMasterInfoToResponse(
      equipmentCalibrationTicket.equipmentCalibrationTicketDetail,
    );
    const calibrationHistoryMaster =
      await this.baseService.mapMasterInfoToResponse(
        equipmentCalibrationTicket.calibrationActivityHistory,
      );
    dataMapUser.forEach((s) => {
      s.equipmentCalibrationTicketDetail = details;
      s.calibrationActivityHistory = calibrationHistoryMaster;
    });
    return new ResponseBuilder(dataMapUser[0] ? dataMapUser[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateEquipmentCalibrationTicketFormDto): Promise<any> {
    const { id } = request;

    const equipmentCalibrationTicket =
      await this.qmsxTicketService.getDetailQmsxEquipmentCalibrationTicketById({
        id: id,
      });

    if (isEmpty(equipmentCalibrationTicket)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const equipmentCalibrationTicketUpdate = plainToInstance(
      UpdateEquipmentCalibrationTicketDto,
      request,
    );
    equipmentCalibrationTicketUpdate.id = id;
    equipmentCalibrationTicketUpdate.updatedBy = request.userId;
    const response =
      await this.qmsxTicketService.updateEquipmentCalibrationTicket(
        equipmentCalibrationTicketUpdate,
      );
    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    await this.qmsxPlanService.updateStatusEquipmentCalibration({
      ids: [equipmentCalibrationTicketUpdate.planId],
      status: EQUIPMENT_CALIBRATION_PLAN_STATUS_ENUM.INPROGRESS,
    });

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async note(request: NoteEquipmentCalibrationTicketRequestDto): Promise<any> {
    const { id } = request;

    const equipmentCalibrationTicket =
      await this.qmsxTicketService.getDetailQmsxEquipmentCalibrationTicketById({
        id: id,
      });

    if (isEmpty(equipmentCalibrationTicket)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    request.createdBy = request.userId;
    const response =
      await this.qmsxTicketService.noteEquipmentCalibrationTicket(request);

    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(
    request: DeleteEquipmentCalibrationTicketRequestDto,
  ): Promise<any> {
    const equipmentCalibrationTicket =
      await this.qmsxTicketService.getDetailQmsxEquipmentCalibrationTicketById(
        request,
      );

    if (isEmpty(equipmentCalibrationTicket)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const { result, messageError } =
      await this.qmsxTicketService.deleteEquipmentCalibration(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    await this.fileService.handleSaveFiles({
      resource: FileResource.QMSX_TICKET,
      deleteFiles: equipmentCalibrationTicket.files,
      uploadFiles: [],
    });
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async updateStatus(
    request: UpdateStatusEquipmentCalibrationTicketRequestDto,
  ): Promise<any> {
    const { result, messageError } =
      await this.qmsxTicketService.updateStatusEquipmentCalibration(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async cancel(
    request: GetDetailEquipmentCalibrationTicketRequestDto,
  ): Promise<any> {
    const { result, messageError } =
      await this.qmsxTicketService.cancelEquipmentCalibrationTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('error.SUCCESS'))
      .build();
  }

  async approve(
    request: GetDetailEquipmentCalibrationTicketRequestDto,
  ): Promise<any> {
    const { result, messageError, data } =
      await this.qmsxTicketService.approveEquipmentCalibrationTicket(request);
    if (!result || isEmpty(data?.equipmentCalibrationTicketDetail)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }

    const updateLastCalibrationRequest = new UpdateLastCalibrationRequestDto();
    updateLastCalibrationRequest.deviceId = data.equipmentId;
    updateLastCalibrationRequest.lastCalibrationDate = new Date();

    await this.mmsDeviceService.updateLastCalibration(
      updateLastCalibrationRequest,
    );

    const isDonePlan =
      await this.qmsxTicketService.checkEquipmentCalibrationTicketStatusByPlan({
        id: data.planId,
        status: EQUIPMENT_CALIBRATION_TICKET_STATUS_ENUM.DONE,
      });

    if (isDonePlan) {
      await this.qmsxPlanService.updateStatusEquipmentCalibration({
        ids: [data.planId],
        status: EQUIPMENT_CALIBRATION_PLAN_STATUS_ENUM.DONE,
      });
    }

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('error.SUCCESS'))
      .build();
  }
}
