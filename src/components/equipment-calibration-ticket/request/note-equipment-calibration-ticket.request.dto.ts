import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsNotEmpty, IsOptional } from 'class-validator';
import { BaseDto } from '../../../core/dto/base.dto';

export class NoteEquipmentCalibrationTicketRequestDto extends BaseDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;

  @ApiProperty()
  @IsNotEmpty()
  note?: string;

  @ApiPropertyOptional()
  @IsOptional()
  createdBy?: number;
}
