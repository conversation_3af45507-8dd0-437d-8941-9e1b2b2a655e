import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateEquipmentCalibrationTicketsDto } from './create-equipment-calibration-ticket.request.dto';

export class UpdateEquipmentCalibrationTicketDto extends CreateEquipmentCalibrationTicketsDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  updatedBy?: number;
}
