import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsDateString,
  IsEnum,
  IsN<PERSON>ber,
  IsOptional,
} from 'class-validator';
import { BaseDto } from '../../../core/dto/base.dto';
import { FileRequestDto } from '../../common/request/file.request.dto';
import {
  EQUIPMENT_CALIBRATION_DETAIL_RESULT,
  EQUIPMENT_CALIBRATION_TICKET_STATUS_ENUM,
  EQUIPMENT_CALIBRATION_TYPE_ENUM,
} from '../enums/equipment-calibration-ticket-status.enum';

export class CreateEquipmentCalibrationTicketDetailDto {
  @ApiProperty()
  @IsOptional()
  @IsEnum(EQUIPMENT_CALIBRATION_TYPE_ENUM)
  calibrationType: EQUIPMENT_CALIBRATION_TYPE_ENUM;

  @ApiProperty()
  @IsOptional()
  @IsEnum(EQUIPMENT_CALIBRATION_DETAIL_RESULT)
  result: EQUIPMENT_CALIBRATION_DETAIL_RESULT;

  @ApiProperty()
  @IsOptional()
  description: string;

  @ApiProperty({ type: [FileRequestDto] })
  @IsArray()
  @IsOptional()
  @Type(() => FileRequestDto)
  files: FileRequestDto[];
}

export class CreateEquipmentCalibrationTicketsDto extends BaseDto {
  @ApiProperty()
  @IsOptional()
  code: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum(EQUIPMENT_CALIBRATION_TICKET_STATUS_ENUM)
  status: EQUIPMENT_CALIBRATION_TICKET_STATUS_ENUM;

  @ApiProperty()
  @IsOptional()
  equipmentId: number;

  @ApiProperty()
  @IsOptional()
  equipmentName: string;

  @ApiProperty()
  @IsOptional()
  equipmentCode: string;

  lastCalibrationDate: Date;

  expectedCalibrationDate: Date;

  @ApiProperty()
  @IsOptional()
  equipmentDetail: string;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  ticketDateFrom: Date;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  ticketDateTo: Date;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  planDateFrom: Date;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  planDateTo: Date;

  @ApiProperty()
  @IsOptional()
  pic: number;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  nextCalibrationDate: Date;

  @ApiProperty()
  @IsOptional()
  reportNumber: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum(EQUIPMENT_CALIBRATION_DETAIL_RESULT)
  ticketResult: EQUIPMENT_CALIBRATION_DETAIL_RESULT;

  @ApiProperty()
  @IsOptional()
  planId: number;

  @ApiProperty()
  @IsOptional()
  planCode: string;

  @ApiProperty()
  @IsOptional()
  planName: string;

  @ApiProperty()
  @IsOptional()
  vendorId: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  createdBy: number;

  @ApiProperty({ type: [FileRequestDto] })
  @IsArray()
  @IsOptional()
  @Type(() => FileRequestDto)
  files: FileRequestDto[];

  @ApiProperty({ type: [CreateEquipmentCalibrationTicketDetailDto] })
  @IsArray()
  @Type(() => CreateEquipmentCalibrationTicketDetailDto)
  equipmentCalibrationTicketDetails: CreateEquipmentCalibrationTicketDetailDto[];
}
