import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt } from 'class-validator';
import { CreateEquipmentCalibrationTicketsDto } from './create-equipment-calibration-ticket.request.dto';

export class UpdateEquipmentCalibrationTicketFormDto extends CreateEquipmentCalibrationTicketsDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;
}
