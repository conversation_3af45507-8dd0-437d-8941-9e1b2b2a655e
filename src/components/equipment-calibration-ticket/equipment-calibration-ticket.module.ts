import { Module } from '@nestjs/common';
import { FileService } from '../../core/components/file/file.service';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { ChecksheetModule } from '../checksheet/checksheet.module';
import { EquipmentCalibrationTicketController } from './controller/equipment-calibration-ticket.controller';
import { EquipmentCalibrationTicketService } from './service/equipment-calibration-ticket.service';

@Module({
  imports: [AnotherServiceModule, BaseProcessModule, ChecksheetModule],
  providers: [
    EquipmentCalibrationTicketService,
    {
      provide: 'FileServiceInterface',
      useClass: FileService,
    },
  ],
  exports: [EquipmentCalibrationTicketService],
  controllers: [EquipmentCalibrationTicketController],
})
export class EquipmentCalibrationTicketModule {}
