import { Body, Controller, Get, Param, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  APPROVE_EQUIPMENT_CALIBRATION_TICKET_PERMISSION,
  CANCEL_EQUIPMENT_CALIBRATION_TICKET_PERMISSION,
  DETAIL_EQUIPMENT_CALIBRATION_TICKET_PERMISSION,
  LIST_EQUIPMENT_CALIBRATION_TICKET_PERMISSION,
  UPDATE_EQUIPMENT_CALIBRATION_TICKET_PERMISSION,
} from '../../../utils/permissions/equipment-calibration-ticket.permission';
import { GetDetailEquipmentCalibrationTicketRequestDto } from '../request/get-detail-equipment-calibration-ticket.request.dto';
import { GetListEquipmentCalibrationTicketRequestDto } from '../request/get-list-equipment-calibration-ticket.request.dto';
import { NoteEquipmentCalibrationTicketRequestDto } from '../request/note-equipment-calibration-ticket.request.dto';
import { UpdateEquipmentCalibrationTicketFormDto } from '../request/update-equipment-calibration-ticket-form.request.dto';
import { EquipmentCalibrationTicketService } from '../service/equipment-calibration-ticket.service';

@Controller('equipment-calibration-tickets')
export class EquipmentCalibrationTicketController {
  constructor(
    private readonly equipmentCalibrationTicketService: EquipmentCalibrationTicketService,
  ) {}

  @PermissionCode(DETAIL_EQUIPMENT_CALIBRATION_TICKET_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Equipment-calibration-tickets'],
    summary: 'Chi tiết Equipment-calibration-tickets',
    description: 'Chi tiết Equipment-calibration-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailEquipmentCalibrationTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.equipmentCalibrationTicketService.getDetail(request);
  }

  @PermissionCode(LIST_EQUIPMENT_CALIBRATION_TICKET_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Equipment-calibration-tickets'],
    summary: 'Danh sách Equipment-calibration-tickets',
    description: 'Danh sách Equipment-calibration-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListEquipmentCalibrationTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.equipmentCalibrationTicketService.getList(request);
  }

  @PermissionCode(UPDATE_EQUIPMENT_CALIBRATION_TICKET_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Equipment-calibration-tickets'],
    summary: 'Cập nhật Equipment-calibration-tickets',
    description: 'Cập nhật Equipment-calibration-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateEquipmentCalibrationTicketFormDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.equipmentCalibrationTicketService.update(request);
  }

  @PermissionCode(UPDATE_EQUIPMENT_CALIBRATION_TICKET_PERMISSION.code)
  @Post('/note')
  @ApiOperation({
    tags: ['Equipment-calibration-tickets'],
    summary: 'Ghi chú Equipment-calibration-tickets',
    description: 'Ghi chú Equipment-calibration-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async note(
    @Body() body: NoteEquipmentCalibrationTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.equipmentCalibrationTicketService.note(request);
  }

  @PermissionCode(CANCEL_EQUIPMENT_CALIBRATION_TICKET_PERMISSION.code)
  @Put('/canceled')
  @ApiOperation({
    tags: ['Equipment-calibration-tickets'],
    summary: 'Canceled Equipment-calibration-tickets',
    description: 'Canceled Equipment-calibration-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async cancel(
    @Body() body: GetDetailEquipmentCalibrationTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.equipmentCalibrationTicketService.cancel(request);
  }

  @PermissionCode(APPROVE_EQUIPMENT_CALIBRATION_TICKET_PERMISSION.code)
  @Put('/approved')
  @ApiOperation({
    tags: ['Equipment-calibration-tickets'],
    summary: 'Approved Equipment-Calibration-tickets',
    description: 'Approved Equipment-Calibration-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async approve(
    @Body() body: GetDetailEquipmentCalibrationTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.equipmentCalibrationTicketService.approve(request);
  }
}
