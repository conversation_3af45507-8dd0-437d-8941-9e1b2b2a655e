import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { isEmpty, keyBy } from 'lodash';
import { I18nService } from 'nestjs-i18n';
import { DataSource } from 'typeorm';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { InspectionSampleSymbolEntity } from '../entities/inspection-sample-symbol.entity';
import { CreateInspectionSampleSymbolRequestDto } from './../request/create-inspection-sample-symbol.request.dto';
import { DeleteInspectionSampleSymbolRequestDto } from './../request/delete-inspection-sample-symbol.request.dto';
import { GetDetailInspectionSampleSymbolRequestDto } from './../request/get-detail-inspection-sample-symbol.request.dto';
import { GetListInspectionSampleSymbolRequestDto } from './../request/get-list-inspection-sample-symbol.request.dto';
import { UpdateInspectionSampleSymbolRequestDto } from './../request/update-inspection-sample-symbol.request.dto';
import { UpdateStatusInspectionSampleSymbolRequestDto } from './../request/update-status-inspection-sample-symbol.request.dto';

import { GetDetailInspectionSampleSymbolResponseDto } from './../response/get-detail-inspection-sample-symbol.response.dto';
import { GetListInspectionSampleSymbolResponseDto } from './../response/get-list-inspection-sample-symbol.response.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { ValidateResultCommonDto } from '../../../common/dtos/validate.result.common.dto';
import { StatusEnum } from '../../../common/enums/status.enum';
import { BaseService } from '../../../common/service/base.service';
import { UserService } from '../../another-service/services/user-service';
import { InspectionSampleQuantityService } from '../../inspection-sample-quantity/service/inspection-sample-quantity.service';
import { GetInspectionQuantityRequestDto } from '../../inspection-sample-symbol-detail/request/get-inspection-quantity.request.dto';
import { InspectionSampleSymbolDetailService } from '../../inspection-sample-symbol-detail/service/inspection-sample-symbol-detail.service';
import { InspectionStandardService } from '../../inspection-standard/service/inspection-standard.service';
import { MasterDataReferenceService } from '../../master-data-reference/service/master-data-reference.service';
import { InspectionSampleSymbolRepository } from '../repository/inspection-sample-symbol.repository';

@Injectable()
export class InspectionSampleSymbolService extends BaseService {
  private readonly logger = new Logger(InspectionSampleSymbolService.name);

  constructor(
    private readonly inspectionSampleSymbolRepository: InspectionSampleSymbolRepository,

    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly inspectionStandardService: InspectionStandardService,

    private readonly inspectionSampleQuantityService: InspectionSampleQuantityService,

    private readonly detailService: InspectionSampleSymbolDetailService,

    private readonly masterReferenceService: MasterDataReferenceService,
  ) {
    super(userService);
  }

  async create(request: CreateInspectionSampleSymbolRequestDto): Promise<any> {
    const existCode = await this.inspectionSampleSymbolRepository.findOneByCode(
      request.code,
    );
    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate inspection-sample-symbol
    const resultValidate = await this.validateSaveInspectionSampleSymbol(
      request,
    );
    if (resultValidate?.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(resultValidate.messageError))
        .build();
    }

    const inspectionSampleSymbolEntity =
      this.inspectionSampleSymbolRepository.createEntity(request);
    const inspectionSampleSymbol =
      await this.inspectionSampleSymbolRepository.create(
        inspectionSampleSymbolEntity,
      );

    const response = plainToInstance(
      GetDetailInspectionSampleSymbolResponseDto,
      inspectionSampleSymbol,
    );

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(
    request: GetListInspectionSampleSymbolRequestDto,
  ): Promise<any> {
    const { page } = request;
    const { data, count } = await this.inspectionSampleSymbolRepository.getList(
      request,
    );

    const dataMapUser = await this.mapUserInfoToResponse(data);
    const response = plainToInstance(
      GetListInspectionSampleSymbolResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(
    request: GetDetailInspectionSampleSymbolRequestDto,
  ): Promise<any> {
    const inspectionSampleSymbol =
      await this.inspectionSampleSymbolRepository.getDetail(request);
    if (isEmpty(inspectionSampleSymbol)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMap = await this.mapDataDetailToResponse([
      inspectionSampleSymbol,
    ]);
    const dataMapUser = await this.mapUserInfoToResponse(dataMap);
    const response = plainToInstance(
      GetDetailInspectionSampleSymbolResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateInspectionSampleSymbolRequestDto): Promise<any> {
    const { id } = request;
    const inspectionSampleSymbol =
      await this.inspectionSampleSymbolRepository.findOneById(id);

    if (isEmpty(inspectionSampleSymbol)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate inspection-sample-symbols in used
    const usedList = await this.validateInspectionSampleSymbolInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.INSPECTION_SAMPLE_SYMBOL_IN_USED'),
        )
        .build();
    }

    // Validate Code unique
    const existCode = await this.inspectionSampleSymbolRepository.findOneByCode(
      request.code,
    );
    if (!isEmpty(existCode) && existCode.id !== inspectionSampleSymbol.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate inspection-sample-symbol
    const resultValidate = await this.validateSaveInspectionSampleSymbol(
      request,
    );
    if (resultValidate?.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(resultValidate.messageError))
        .build();
    }

    const dataUpdate = this.inspectionSampleSymbolRepository.updateEntity(
      request,
      inspectionSampleSymbol,
    );
    const data = await this.inspectionSampleSymbolRepository.update(dataUpdate);

    const response = plainToInstance(
      GetDetailInspectionSampleSymbolResponseDto,
      data,
    );

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteInspectionSampleSymbolRequestDto): Promise<any> {
    const { id } = request;
    const inspectionSampleSymbol =
      await this.inspectionSampleSymbolRepository.findOneById(id);

    if (isEmpty(inspectionSampleSymbol)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate inspection-sample-symbols in used
    const usedList = await this.validateInspectionSampleSymbolInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.INSPECTION_SAMPLE_SYMBOL_IN_USED'),
        )
        .build();
    }

    await this.inspectionSampleSymbolRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(
    request: UpdateStatusInspectionSampleSymbolRequestDto,
  ): Promise<any> {
    const { ids } = request;

    const listExitsInDB =
      await this.inspectionSampleSymbolRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.inspectionSampleSymbolRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(
    request: UpdateStatusInspectionSampleSymbolRequestDto,
  ): Promise<any> {
    const { ids } = request;

    const listExitsInDB =
      await this.inspectionSampleSymbolRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.inspectionSampleSymbolRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async findOneById(id: number): Promise<InspectionSampleSymbolEntity> {
    return await this.inspectionSampleSymbolRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<InspectionSampleSymbolEntity[]> {
    return await this.inspectionSampleSymbolRepository.findAllByIds(ids);
  }

  async validateSaveInspectionSampleSymbol(
    request: CreateInspectionSampleSymbolRequestDto,
  ): Promise<ValidateResultCommonDto> {
    // Validate lotSizeFrom, lotSizeTo overlapping range
    const sortedDetails = [...request?.dataDetails].sort(
      (a, b) => a.lotSizeFrom - b.lotSizeFrom,
    );

    let isLotSizeOverlappingRange: boolean = false;
    for (let i = 0; i < sortedDetails.length - 1; i++) {
      const current = sortedDetails[i];
      const next = sortedDetails[i + 1];

      if (current.lotSizeTo > next.lotSizeFrom) {
        isLotSizeOverlappingRange = true;
        break;
      }
    }
    if (isLotSizeOverlappingRange) {
      return {
        result: true,
        messageError:
          'error.INSPECTION_SAMPLE_QUANTITY_LOT_SIZE_OVERLAPPING_RANGE',
      };
    }

    // Validate exist inspection-standards
    const inspectionStandardIds = new Set<number>();
    const inspectionSampleQuantityIds = new Set<number>();
    let isDetailStandardsEmpty: boolean = false;
    for (const detail of request?.dataDetails || []) {
      if (isEmpty(detail?.detailStandards)) {
        isDetailStandardsEmpty = true;
        break;
      } else {
        detail?.detailStandards?.map((detailStandard) => {
          if (
            detailStandard?.inspectionStandardId !== undefined &&
            detailStandard?.inspectionStandardId !== null
          ) {
            inspectionStandardIds.add(detailStandard?.inspectionStandardId);
          }

          if (
            detailStandard?.inspectionSampleQuantityId !== undefined &&
            detailStandard?.inspectionSampleQuantityId !== null
          ) {
            inspectionSampleQuantityIds.add(
              detailStandard?.inspectionSampleQuantityId,
            );
          }
        });
      }
    }

    if (isDetailStandardsEmpty) {
      return {
        result: true,
        messageError:
          'error.INSPECTION_SAMPLE_QUANTITY_DETAIL_STANDARD_IS_EMPTY',
      };
    }

    const existInspectionSampleQuantities =
      await this.inspectionSampleQuantityService.findAllByIds(
        Array.from(inspectionSampleQuantityIds),
      );
    const inspectionSampleQuantityInactive =
      existInspectionSampleQuantities?.filter(
        (item) => item.status === StatusEnum.IN_ACTIVE,
      );

    if (
      isEmpty(inspectionSampleQuantityIds) ||
      isEmpty(existInspectionSampleQuantities) ||
      !isEmpty(inspectionSampleQuantityInactive) ||
      inspectionSampleQuantityIds?.size !==
        existInspectionSampleQuantities?.length
    ) {
      return {
        result: true,
        messageError: 'error.INSPECTION_SAMPLE_QUANTITY_IS_NOT_EXISTS',
      };
    }

    const existInspectionStandards =
      await this.inspectionStandardService.findAllByIds(
        Array.from(inspectionStandardIds),
      );
    const inspectionStandardInactive = existInspectionStandards?.filter(
      (item) => item.status === StatusEnum.IN_ACTIVE,
    );

    if (
      isEmpty(inspectionStandardIds) ||
      isEmpty(existInspectionStandards) ||
      !isEmpty(inspectionStandardInactive) ||
      inspectionStandardIds?.size !== existInspectionStandards?.length
    ) {
      return {
        result: true,
        messageError: 'error.INSPECTION_STANDARD_IS_NOT_EXISTS',
      };
    }

    return { result: false, messageError: '' };
  }

  async validateInspectionSampleSymbolInUsed(id: number): Promise<boolean> {
    // Validate used in another service
    const isUse =
      await this.masterReferenceService.validateDataMasterUseAnotherService(
        new ValidateMasterRequestDto('inspection_sample_symbol_id', id),
      );
    if (isUse) {
      return true;
    }

    return false;
  }

  async mapDataDetailToResponse(data: InspectionSampleSymbolEntity[]) {
    const inspectionSampleSymbolIds = new Set<number>();
    const inspectionStandardIds = new Set<number>();
    const inspectionSampleQuantityIds = new Set<number>();

    data?.map((inspecionSampleSymbol) => {
      inspectionSampleSymbolIds.add(+inspecionSampleSymbol.id);

      inspecionSampleSymbol?.dataDetails?.map((detail) => {
        detail?.detailStandards?.map((detailStandard) => {
          inspectionStandardIds.add(+detailStandard?.inspectionStandardId);

          inspectionSampleQuantityIds.add(
            +detailStandard?.inspectionSampleQuantityId,
          );
        });
      });
    });

    if (
      isEmpty(inspectionSampleSymbolIds) ||
      isEmpty(inspectionStandardIds) ||
      isEmpty(inspectionSampleQuantityIds)
    ) {
      return data;
    }

    const inspectionStandardList =
      await this.inspectionStandardService.findAllByIds(
        Array.from(inspectionStandardIds),
      );
    if (isEmpty(inspectionStandardList)) return data;

    const inspectionStandardMap = keyBy(inspectionStandardList, 'id');

    const inspectionSampleQuantityList =
      await this.inspectionSampleQuantityService.findAllByIds(
        Array.from(inspectionSampleQuantityIds),
      );
    if (isEmpty(inspectionSampleQuantityList)) return data;

    const inspectionSampleQuantityMap = keyBy(
      inspectionSampleQuantityList,
      'id',
    );

    return (
      data?.map((item) => {
        return {
          ...item,
          inspectionStandards: inspectionStandardList,
          dataDetails: item?.dataDetails?.map((dataDetail) => {
            return {
              ...dataDetail,
              detailStandards: dataDetail?.detailStandards?.map(
                (detailStandard) => {
                  return {
                    ...detailStandard,
                    inspectionSampleQuantity:
                      inspectionSampleQuantityMap[
                        detailStandard.inspectionSampleQuantityId
                      ],
                    inspectionStandardId:
                      inspectionStandardMap[
                        detailStandard.inspectionStandardId
                      ],
                  };
                },
              ),
            };
          }),
        };
      }) || []
    );
  }

  async getInspectionQuantity(
    request: GetInspectionQuantityRequestDto,
  ): Promise<any> {
    const { result, response } =
      await this.masterReferenceService.findInspectionQuantity(request);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.AQL_EXCEEDED'))
        .build();
    }

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
