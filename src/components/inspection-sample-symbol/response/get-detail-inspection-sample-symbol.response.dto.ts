import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { BaseCommonResponseDto } from '../../../common/dtos/response/base.common.dto';
import { ResponseCommonDto } from '../../../common/dtos/response/response.common.dto';

export class GetDetailInspectionSampleSymbolDataStandardResponseDto {
  @ApiProperty({ type: ResponseCommonDto })
  @Expose()
  @Type(() => ResponseCommonDto)
  inspectionStandard: ResponseCommonDto;

  @ApiProperty({ type: ResponseCommonDto })
  @Expose()
  @Type(() => ResponseCommonDto)
  inspectionSampleQiantity: ResponseCommonDto;
}

export class GetDetailInspectionSampleSymbolDataResponseDto {
  @ApiProperty()
  @Expose()
  lotSizeFrom: number;

  @ApiProperty()
  @Expose()
  lotSizeTo: number;

  @ApiProperty({ type: GetDetailInspectionSampleSymbolDataStandardResponseDto })
  @Expose()
  @Type(() => GetDetailInspectionSampleSymbolDataStandardResponseDto)
  dataStandards: GetDetailInspectionSampleSymbolDataStandardResponseDto[];
}

export class GetDetailInspectionSampleSymbolResponseDto extends BaseCommonResponseDto {
  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty()
  @Expose()
  status: number;

  @ApiProperty({ type: GetDetailInspectionSampleSymbolDataResponseDto })
  @Expose()
  @Type(() => GetDetailInspectionSampleSymbolDataResponseDto)
  dataDetails: GetDetailInspectionSampleSymbolDataResponseDto[];
}
