import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { InspectionSampleQuantityModule } from '../inspection-sample-quantity/inspection-sample-quantity.module';
import { InspectionSampleSymbolDetailModule } from '../inspection-sample-symbol-detail/inspection-sample-symbol-detail.module';
import { InspectionStandardModule } from '../inspection-standard/inspection-standard.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { InspectionSampleSymbolController } from './controller/inspection-sample-symbol.controller';
import { InspectionSampleSymbolEntity } from './entities/inspection-sample-symbol.entity';
import { InspectionSampleSymbolRepository } from './repository/inspection-sample-symbol.repository';
import { InspectionSampleSymbolService } from './service/inspection-sample-symbol.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([InspectionSampleSymbolEntity]),
    AnotherServiceModule,
    InspectionSampleQuantityModule,
    InspectionStandardModule,
    InspectionSampleSymbolDetailModule,
    MasterDataReferenceModule,
  ],
  providers: [InspectionSampleSymbolService, InspectionSampleSymbolRepository],
  exports: [InspectionSampleSymbolService],
  controllers: [InspectionSampleSymbolController],
})
export class InspectionSampleSymbolModule {}
