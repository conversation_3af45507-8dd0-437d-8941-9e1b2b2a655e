import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt } from 'class-validator';
import { CreateInspectionSampleSymbolRequestDto } from './create-inspection-sample-symbol.request.dto';

export class UpdateInspectionSampleSymbolRequestDto extends CreateInspectionSampleSymbolRequestDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;
}
