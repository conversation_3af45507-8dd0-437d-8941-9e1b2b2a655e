import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsString,
  Matches,
  MaxLength,
} from 'class-validator';

export class ImportInspectionSampleSymbolDetailStandardRequestDto {
  @IsNotEmpty()
  @IsString()
  keyMapping: string;

  @IsNotEmpty()
  @IsString()
  inspectionStandardCode: string;

  @IsNotEmpty()
  @IsString()
  inspectionSampleQuantityCode: string;
}

export class ImportInspectionSampleSymbolDetailRequestDto {
  @IsNotEmpty()
  @IsString()
  keyMapping: string;

  @IsNotEmpty()
  @IsNumber()
  lotSizeFrom: number;

  @IsNotEmpty()
  @IsNumber()
  lotSizeTo: number;
}

export class ImportInspectionSampleSymbolRequestDto {
  @IsNotEmpty()
  @IsString()
  keyMapping: string;

  @IsNotEmpty()
  @MaxLength(50)
  @Matches(/^[a-zA-Z0-9_.-]+$/)
  code: string;

  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @IsOptional()
  @MaxLength(255)
  description?: string;
}
