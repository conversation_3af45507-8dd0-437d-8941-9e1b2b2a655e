import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsNotEmpty,
  IsOptional,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { InspectionSampleSymbolDetailEntity } from '../../inspection-sample-symbol-detail/entities/inspection-sample-symbol-detail.entity';
import { CreateInspectionSampleSymbolDetailRequestDto } from '../../inspection-sample-symbol-detail/request/create-inspection-sample-symbol-detail.request.dto';

export class CreateInspectionSampleSymbolRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  code: string;

  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  inspectionStandardsType: string;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  description?: string;

  @ApiProperty({ type: [CreateInspectionSampleSymbolDetailRequestDto] })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested()
  @Type(() => CreateInspectionSampleSymbolDetailRequestDto)
  dataDetails: InspectionSampleSymbolDetailEntity[];
}
