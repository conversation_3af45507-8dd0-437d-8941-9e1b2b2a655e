import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  ACTIVE_INSPECTION_SAMPLE_SYMBOL_PERMISSION,
  CREATE_INSPECTION_SAMPLE_SYMBOL_PERMISSION,
  DELETE_INSPECTION_SAMPLE_SYMBOL_PERMISSION,
  DETAIL_INSPECTION_SAMPLE_SYMBOL_PERMISSION,
  INACTIVE_INSPECTION_SAMPLE_SYMBOL_PERMISSION,
  LIST_INSPECTION_SAMPLE_SYMBOL_PERMISSION,
  UPDATE_INSPECTION_SAMPLE_SYMBOL_PERMISSION,
} from '../../../utils/permissions/inspection-sample-symbol.permission';
import { GetInspectionQuantityRequestDto } from '../../inspection-sample-symbol-detail/request/get-inspection-quantity.request.dto';
import { InspectionSampleSymbolDetailService } from '../../inspection-sample-symbol-detail/service/inspection-sample-symbol-detail.service';
import { CreateInspectionSampleSymbolRequestDto } from '../request/create-inspection-sample-symbol.request.dto';
import { DeleteInspectionSampleSymbolRequestDto } from '../request/delete-inspection-sample-symbol.request.dto';
import { GetDetailInspectionSampleSymbolRequestDto } from '../request/get-detail-inspection-sample-symbol.request.dto';
import { GetListInspectionSampleSymbolRequestDto } from '../request/get-list-inspection-sample-symbol.request.dto';
import { UpdateInspectionSampleSymbolRequestDto } from '../request/update-inspection-sample-symbol.request.dto';
import { UpdateStatusInspectionSampleSymbolRequestDto } from '../request/update-status-inspection-sample-symbol.request.dto';
import { InspectionSampleSymbolService } from '../service/inspection-sample-symbol.service';

@Controller('inspection-sample-symbols')
export class InspectionSampleSymbolController {
  constructor(
    private readonly inspectionSampleSymbolService: InspectionSampleSymbolService,
    private readonly inspectionSampleSymbolDetailService: InspectionSampleSymbolDetailService,
  ) {}

  @PermissionCode(DETAIL_INSPECTION_SAMPLE_SYMBOL_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Inspection-sample-symbols'],
    summary: 'Chi tiết Inspection-sample-symbols',
    description: 'Chi tiết Inspection-sample-symbols',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailInspectionSampleSymbolRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionSampleSymbolService.getDetail(request);
  }

  @PermissionCode(LIST_INSPECTION_SAMPLE_SYMBOL_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Inspection-sample-symbols'],
    summary: 'Danh sách Inspection-sample-symbols',
    description: 'Danh sách Inspection-sample-symbols',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListInspectionSampleSymbolRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionSampleSymbolService.getList(request);
  }

  @PermissionCode(CREATE_INSPECTION_SAMPLE_SYMBOL_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Inspection-sample-symbols'],
    summary: 'Tạo Inspection-sample-symbols mới',
    description: 'Tạo Inspection-sample-symbols mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateInspectionSampleSymbolRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionSampleSymbolService.create(request);
  }

  @PermissionCode(UPDATE_INSPECTION_SAMPLE_SYMBOL_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Inspection-sample-symbols'],
    summary: 'Cập nhật Inspection-sample-symbols',
    description: 'Cập nhật Inspection-sample-symbols',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateInspectionSampleSymbolRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionSampleSymbolService.update(request);
  }

  @PermissionCode(ACTIVE_INSPECTION_SAMPLE_SYMBOL_PERMISSION.code)
  @Put('/active')
  @ApiOperation({
    tags: ['Inspection-sample-symbols'],
    summary: 'Cập nhật Inspection-sample-symbols Status Active',
    description: 'Cập nhật Inspection-sample-symbols Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async active(
    @Body() body: UpdateStatusInspectionSampleSymbolRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionSampleSymbolService.active(request);
  }

  @PermissionCode(INACTIVE_INSPECTION_SAMPLE_SYMBOL_PERMISSION.code)
  @Put('/inactive')
  @ApiOperation({
    tags: ['Inspection-sample-symbols'],
    summary: 'Cập nhật Inspection-sample-symbols Status Inactive',
    description: 'Cập nhật Inspection-sample-symbols Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async inactive(
    @Body() body: UpdateStatusInspectionSampleSymbolRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionSampleSymbolService.inactive(request);
  }

  @PermissionCode(DELETE_INSPECTION_SAMPLE_SYMBOL_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Inspection-sample-symbols'],
    summary: 'Xóa Inspection-sample-symbols',
    description: 'Xóa Inspection-sample-symbols',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(
    @Param() param: DeleteInspectionSampleSymbolRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionSampleSymbolService.delete(request);
  }

  @Post('/get-inspection-quantity')
  @ApiOperation({
    tags: ['Inspection-sample-symbols'],
    summary: 'get Inspection-quantity',
    description: 'get Inspection-quantity',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getInspectionQuantity(
    @Body() body: GetInspectionQuantityRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionSampleSymbolService.getInspectionQuantity(
      request,
    );
  }
}
