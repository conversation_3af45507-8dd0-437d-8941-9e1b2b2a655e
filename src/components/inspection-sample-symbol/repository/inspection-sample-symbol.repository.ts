import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { isDateString } from 'class-validator';
import { StatusEnum } from '../../../common/enums/status.enum';
import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { escapeCharForSearch } from '../../../utils/common';
import { InspectionSampleSymbolEntity } from '../entities/inspection-sample-symbol.entity';
import { GetDetailInspectionSampleSymbolRequestDto } from '../request/get-detail-inspection-sample-symbol.request.dto';
import { GetListInspectionSampleSymbolRequestDto } from '../request/get-list-inspection-sample-symbol.request.dto';
import { UpdateInspectionSampleSymbolRequestDto } from '../request/update-inspection-sample-symbol.request.dto';
import { UpdateStatusInspectionSampleSymbolRequestDto } from '../request/update-status-inspection-sample-symbol.request.dto';
import { CreateInspectionSampleSymbolRequestDto } from './../request/create-inspection-sample-symbol.request.dto';

@Injectable()
export class InspectionSampleSymbolRepository extends BaseAbstractRepository<InspectionSampleSymbolEntity> {
  constructor(
    @InjectRepository(InspectionSampleSymbolEntity)
    private readonly inspectionSampleSymbolRepository: Repository<InspectionSampleSymbolEntity>,
  ) {
    super(inspectionSampleSymbolRepository);
  }

  createEntity(
    request: CreateInspectionSampleSymbolRequestDto,
  ): InspectionSampleSymbolEntity {
    const inspectionSampleSymbolEntity = new InspectionSampleSymbolEntity();
    Object.assign(inspectionSampleSymbolEntity, request);
    inspectionSampleSymbolEntity.status = StatusEnum.ACTIVE;
    inspectionSampleSymbolEntity.createdBy = request.userId;
    inspectionSampleSymbolEntity.updatedBy = request.userId;

    return inspectionSampleSymbolEntity;
  }

  updateEntity(
    request: UpdateInspectionSampleSymbolRequestDto,
    entity: InspectionSampleSymbolEntity,
  ): InspectionSampleSymbolEntity {
    Object.assign(entity, request);
    entity.updatedBy = request?.userId;

    return entity;
  }

  async getList(
    request: GetListInspectionSampleSymbolRequestDto,
  ): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.inspectionSampleSymbolRepository.createQueryBuilder(
      'inspectionsamplesymbol',
    );

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("inspectionsamplesymbol"."code") like lower(:code) escape '\\' OR
              lower("inspectionsamplesymbol"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('inspectionsamplesymbol.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'code':
            query.andWhere(
              `lower("inspectionsamplesymbol"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'name':
            query.andWhere(
              `lower("inspectionsamplesymbol"."name") like lower(:name) escape '\\'`,
              {
                name: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'description':
            query.andWhere(
              `lower("inspectionsamplesymbol"."description") like lower(:description) escape '\\'`,
              {
                description: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'status':
            query.andWhere('"inspectionsamplesymbol"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'createdById':
            query.andWhere(
              '"inspectionsamplesymbol"."created_by" = :createdById',
              {
                createdById: Number(value),
              },
            );
            break;
          case 'createdByIds':
            query.andWhere(
              '"inspectionsamplesymbol"."created_by" IN (:...createdByIds)',
              {
                createdByIds: value.split(',').map((id) => Number(id)),
              },
            );
            break;
          case 'createdAt':
            const createFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const createTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"inspectionsamplesymbol"."created_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: createFrom,
                dateTo: createTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"inspectionsamplesymbol"."updated_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: updateFrom,
                dateTo: upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'name':
            query.addOrderBy('"inspectionsamplesymbol"."name"', order);
            break;
          case 'code':
            query.addOrderBy('"inspectionsamplesymbol"."code"', order);
            break;
          case 'status':
            query.addOrderBy('"inspectionsamplesymbol"."status"', order);
            break;
          case 'description':
            query.addOrderBy('"inspectionsamplesymbol"."description"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"inspectionsamplesymbol"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"inspectionsamplesymbol"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"inspectionsamplesymbol"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('inspectionsamplesymbol.id', 'DESC');
    }

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const [data, count] = await query.getManyAndCount();

    return { data, count };
  }

  async getDetail(
    request: GetDetailInspectionSampleSymbolRequestDto,
  ): Promise<InspectionSampleSymbolEntity> {
    const { id } = request;

    const data = await this.inspectionSampleSymbolRepository.findOne({
      where: { id: id },
    });

    return data;
  }

  async active(
    request: UpdateStatusInspectionSampleSymbolRequestDto,
  ): Promise<any> {
    const { ids } = request;
    const data = await this.inspectionSampleSymbolRepository
      .createQueryBuilder()
      .update(InspectionSampleSymbolEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(
    request: UpdateStatusInspectionSampleSymbolRequestDto,
  ): Promise<any> {
    const { ids } = request;
    const data = await this.inspectionSampleSymbolRepository
      .createQueryBuilder()
      .update(InspectionSampleSymbolEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }
}
