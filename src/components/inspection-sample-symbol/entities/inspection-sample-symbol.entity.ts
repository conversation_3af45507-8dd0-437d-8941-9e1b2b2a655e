import { Column, Entity, OneToMany } from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { InspectionSampleSymbolDetailEntity } from '../../inspection-sample-symbol-detail/entities/inspection-sample-symbol-detail.entity';

@Entity({ name: 'inspection_sample_symbols' })
export class InspectionSampleSymbolEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    unique: true,
    nullable: false,
    length: 255,
  })
  code: string;

  @Column({
    type: 'nvarchar',
    nullable: false,
    length: 255,
  })
  name: string;

  @Column({
    type: 'nvarchar',
    nullable: false,
    length: 255,
  })
  inspectionStandardsType: string;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  description: string;

  @Column({
    type: 'tinyint',
  })
  status: number;

  @OneToMany(
    () => InspectionSampleSymbolDetailEntity,
    (detail) => detail.inspectionSampleSymbol,
    {
      cascade: ['insert', 'update', 'remove'],
      onDelete: 'CASCADE',
      eager: true,
    },
  )
  dataDetails: InspectionSampleSymbolDetailEntity[];
}
