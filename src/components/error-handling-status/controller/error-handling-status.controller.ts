import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  ACTIVE_ERROR_HANDLING_STATUS_PERMISSION,
  CREATE_ERROR_HANDLING_STATUS_PERMISSION,
  DELETE_ERROR_HANDLING_STATUS_PERMISSION,
  DETAIL_ERROR_HANDLING_STATUS_PERMISSION,
  INACTIVE_ERROR_HANDLING_STATUS_PERMISSION,
  LIST_ERROR_HANDLING_STATUS_PERMISSION,
  UPDATE_ERROR_HANDLING_STATUS_PERMISSION,
} from '../../../utils/permissions/error-handling-status.permission';
import { CreateErrorHandlingStatusRequestDto } from '../request/create-error-handling-status.request.dto';
import { DeleteErrorHandlingStatusRequestDto } from '../request/delete-error-handling-status.request.dto';
import { GetDetailErrorHandlingStatusRequestDto } from '../request/get-detail-error-handling-status.request.dto';
import { GetListErrorHandlingStatusRequestDto } from '../request/get-list-error-handling-status.request.dto';
import { UpdateErrorHandlingStatusRequestDto } from '../request/update-error-handling-status.request.dto';
import { UpdateStatusErrorHandlingStatusRequestDto } from '../request/update-status-error-handling-status.request.dto';
import { ErrorHandlingStatusService } from '../service/error-handling-status.service';

@Controller('error-handling-statuses')
export class ErrorHandlingStatusController {
  constructor(
    private readonly errorHandlingStatusService: ErrorHandlingStatusService,
  ) {}

  @PermissionCode(DETAIL_ERROR_HANDLING_STATUS_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Error-handling-statuses'],
    summary: 'Chi tiết Error-handling-statuses',
    description: 'Chi tiết Error-handling-statuses',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailErrorHandlingStatusRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorHandlingStatusService.getDetail(request);
  }

  @PermissionCode(LIST_ERROR_HANDLING_STATUS_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Error-handling-statuses'],
    summary: 'Danh sách Error-handling-statuses',
    description: 'Danh sách Error-handling-statuses',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListErrorHandlingStatusRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.errorHandlingStatusService.getList(request);
  }

  @PermissionCode(CREATE_ERROR_HANDLING_STATUS_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Error-handling-statuses'],
    summary: 'Tạo Error-handling-statuses mới',
    description: 'Tạo Error-handling-statuses mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateErrorHandlingStatusRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.errorHandlingStatusService.create(request);
  }

  @PermissionCode(UPDATE_ERROR_HANDLING_STATUS_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Error-handling-statuses'],
    summary: 'Cập nhật Error-handling-statuses',
    description: 'Cập nhật Error-handling-statuses',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateErrorHandlingStatusRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorHandlingStatusService.update(request);
  }

  @PermissionCode(ACTIVE_ERROR_HANDLING_STATUS_PERMISSION.code)
  @Put('/active')
  @ApiOperation({
    tags: ['Error-handling-statuses'],
    summary: 'Cập nhật Error-handling-statuses Status Active',
    description: 'Cập nhật Error-handling-statuses Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async active(
    @Body() body: UpdateStatusErrorHandlingStatusRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorHandlingStatusService.active(request);
  }

  @PermissionCode(INACTIVE_ERROR_HANDLING_STATUS_PERMISSION.code)
  @Put('/inactive')
  @ApiOperation({
    tags: ['Error-handling-statuses'],
    summary: 'Cập nhật Error-handling-statuses Status Inactive',
    description: 'Cập nhật Error-handling-statuses Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async inactive(
    @Body() body: UpdateStatusErrorHandlingStatusRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorHandlingStatusService.inactive(request);
  }

  @PermissionCode(DELETE_ERROR_HANDLING_STATUS_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Error-handling-statuses'],
    summary: 'Xóa Error-handling-statuses',
    description: 'Xóa Error-handling-statuses',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(
    @Param() param: DeleteErrorHandlingStatusRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.errorHandlingStatusService.delete(request);
  }
}
