import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { isDateString } from 'class-validator';
import { StatusEnum } from '../../../common/enums/status.enum';
import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { escapeCharForSearch } from '../../../utils/common';
import { ErrorHandlingStatusEntity } from '../entities/error-handling-status.entity';
import { GetDetailErrorHandlingStatusRequestDto } from '../request/get-detail-error-handling-status.request.dto';
import { GetListErrorHandlingStatusRequestDto } from '../request/get-list-error-handling-status.request.dto';
import { UpdateErrorHandlingStatusRequestDto } from '../request/update-error-handling-status.request.dto';
import { UpdateStatusErrorHandlingStatusRequestDto } from '../request/update-status-error-handling-status.request.dto';
import { CreateErrorHandlingStatusRequestDto } from './../request/create-error-handling-status.request.dto';

@Injectable()
export class ErrorHandlingStatusRepository extends BaseAbstractRepository<ErrorHandlingStatusEntity> {
  constructor(
    @InjectRepository(ErrorHandlingStatusEntity)
    private readonly errorHandlingStatusRepository: Repository<ErrorHandlingStatusEntity>,
  ) {
    super(errorHandlingStatusRepository);
  }

  createEntity(
    request: CreateErrorHandlingStatusRequestDto,
  ): ErrorHandlingStatusEntity {
    const errorHandlingStatusEntity = new ErrorHandlingStatusEntity();
    errorHandlingStatusEntity.code = request.code;
    errorHandlingStatusEntity.name = request.name;
    errorHandlingStatusEntity.applicableObject = request.applicableObject;
    errorHandlingStatusEntity.isClaim = request.isClaim;
    errorHandlingStatusEntity.description = request.description;
    errorHandlingStatusEntity.status = StatusEnum.ACTIVE;
    errorHandlingStatusEntity.createdBy = request.userId;
    errorHandlingStatusEntity.updatedBy = request.userId;

    return errorHandlingStatusEntity;
  }

  updateEntity(
    request: UpdateErrorHandlingStatusRequestDto,
    entity: ErrorHandlingStatusEntity,
  ): ErrorHandlingStatusEntity {
    entity.name = request?.name;
    entity.applicableObject = request.applicableObject;
    entity.isClaim = request.isClaim;
    entity.description = request?.description;
    entity.updatedBy = request?.userId;

    return entity;
  }

  async getList(request: GetListErrorHandlingStatusRequestDto): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.errorHandlingStatusRepository.createQueryBuilder(
      'errorhandlingstatus',
    );

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("errorhandlingstatus"."code") like lower(:code) escape '\\' OR
              lower("errorhandlingstatus"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('errorhandlingstatus.id IN (:...ids)', { ids });
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('errorhandlingstatus.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'code':
            query.andWhere(
              `lower("errorhandlingstatus"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'name':
            query.andWhere(
              `lower("errorhandlingstatus"."name") like lower(:name) escape '\\'`,
              {
                name: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'applicableObject':
            query.andWhere(
              '"errorhandlingstatus"."applicable_object" = :applicableObject',
              {
                applicableObject: Number(value),
              },
            );
            break;
          case 'applicableObjects':
            query.andWhere(
              '"errorhandlingstatus"."applicable_object" IN (:...applicableObjects)',
              {
                applicableObjects: value.split(',').map((id) => Number(id)),
              },
            );
            break;
          case 'description':
            query.andWhere(
              `lower("errorhandlingstatus"."description") like lower(:description) escape '\\'`,
              {
                description: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'status':
            query.andWhere('"errorhandlingstatus"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'isClaim':
            query.andWhere('"errorhandlingstatus"."is_claim" = :isClaim', {
              isClaim: Number(value),
            });
            break;
          case 'createdById':
            query.andWhere(
              '"errorhandlingstatus"."created_by" = :createdById',
              {
                createdById: Number(value),
              },
            );
            break;
          case 'createdByIds':
            query.andWhere(
              '"errorhandlingstatus"."created_by" IN (:...createdByIds)',
              {
                createdByIds: value.split(',').map((id) => Number(id)),
              },
            );
            break;
          case 'createdAt':
            const createFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const createTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"errorhandlingstatus"."created_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: createFrom,
                dateTo: createTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"errorhandlingstatus"."updated_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: updateFrom,
                dateTo: upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'name':
            query.addOrderBy('"errorhandlingstatus"."name"', order);
            break;
          case 'code':
            query.addOrderBy('"errorhandlingstatus"."code"', order);
            break;
          case 'applicableObject':
            query.addOrderBy(
              '"errorhandlingstatus"."applicable_object"',
              order,
            );
            break;
          case 'isClaim':
            query.addOrderBy('"errorhandlingstatus"."is_claim"', order);
            break;
          case 'status':
            query.addOrderBy('"errorhandlingstatus"."status"', order);
            break;
          case 'description':
            query.addOrderBy('"errorhandlingstatus"."description"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"errorhandlingstatus"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"errorhandlingstatus"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"errorhandlingstatus"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('errorhandlingstatus.id', 'DESC');
    }

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const [data, count] = await query.getManyAndCount();

    return { data, count };
  }

  async getDetail(
    request: GetDetailErrorHandlingStatusRequestDto,
  ): Promise<ErrorHandlingStatusEntity> {
    const { id } = request;

    const data = await this.errorHandlingStatusRepository.findOne({
      where: { id: id },
    });

    return data;
  }

  async active(
    request: UpdateStatusErrorHandlingStatusRequestDto,
  ): Promise<any> {
    const { ids } = request;
    const data = await this.errorHandlingStatusRepository
      .createQueryBuilder()
      .update(ErrorHandlingStatusEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(
    request: UpdateStatusErrorHandlingStatusRequestDto,
  ): Promise<any> {
    const { ids } = request;
    const data = await this.errorHandlingStatusRepository
      .createQueryBuilder()
      .update(ErrorHandlingStatusEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }
}
