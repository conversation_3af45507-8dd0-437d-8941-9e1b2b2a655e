import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { ErrorHandlingStatusController } from './controller/error-handling-status.controller';
import { ErrorHandlingStatusEntity } from './entities/error-handling-status.entity';
import { ErrorHandlingStatusRepository } from './repository/error-handling-status.repository';
import { ErrorHandlingStatusService } from './service/error-handling-status.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([ErrorHandlingStatusEntity]),
    AnotherServiceModule,
    MasterDataReferenceModule,
  ],
  providers: [ErrorHandlingStatusService, ErrorHandlingStatusRepository],
  exports: [ErrorHandlingStatusService],
  controllers: [ErrorHandlingStatusController],
})
export class ErrorHandlingStatusModule {}
