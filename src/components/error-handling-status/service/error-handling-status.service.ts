import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { ErrorHandlingStatusEntity } from '../entities/error-handling-status.entity';
import { CreateErrorHandlingStatusRequestDto } from './../request/create-error-handling-status.request.dto';
import { DeleteErrorHandlingStatusRequestDto } from './../request/delete-error-handling-status.request.dto';
import { GetDetailErrorHandlingStatusRequestDto } from './../request/get-detail-error-handling-status.request.dto';
import { GetListErrorHandlingStatusRequestDto } from './../request/get-list-error-handling-status.request.dto';
import { UpdateErrorHandlingStatusRequestDto } from './../request/update-error-handling-status.request.dto';
import { UpdateStatusErrorHandlingStatusRequestDto } from './../request/update-status-error-handling-status.request.dto';

import { GetDetailErrorHandlingStatusResponseDto } from './../response/get-detail-error-handling-status.response.dto';
import { GetListErrorHandlingStatusResponseDto } from './../response/get-list-error-handling-status.response.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { BaseService } from '../../../common/service/base.service';
import { UserService } from '../../another-service/services/user-service';
import { MasterDataReferenceService } from '../../master-data-reference/service/master-data-reference.service';
import { ErrorHandlingStatusRepository } from '../repository/error-handling-status.repository';

@Injectable()
export class ErrorHandlingStatusService extends BaseService {
  private readonly logger = new Logger(ErrorHandlingStatusService.name);

  constructor(
    private readonly errorHandlingStatusRepository: ErrorHandlingStatusRepository,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly masterReferenceService: MasterDataReferenceService,
  ) {
    super(userService);
  }

  async create(request: CreateErrorHandlingStatusRequestDto): Promise<any> {
    const existCode = await this.errorHandlingStatusRepository.findOneByCode(
      request.code,
    );
    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    const errorHandlingStatusEntity =
      this.errorHandlingStatusRepository.createEntity(request);
    const errorHandlingStatus = await this.errorHandlingStatusRepository.create(
      errorHandlingStatusEntity,
    );

    const response = plainToInstance(
      GetDetailErrorHandlingStatusResponseDto,
      errorHandlingStatus,
    );

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListErrorHandlingStatusRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.errorHandlingStatusRepository.getList(
      request,
    );

    const dataMapUser = await this.mapUserInfoToResponse(data);
    const response = plainToInstance(
      GetListErrorHandlingStatusResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(
    request: GetDetailErrorHandlingStatusRequestDto,
  ): Promise<any> {
    const errorHandlingStatus =
      await this.errorHandlingStatusRepository.getDetail(request);
    if (isEmpty(errorHandlingStatus)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMapUser = await this.mapUserInfoToResponse([errorHandlingStatus]);
    const response = plainToInstance(
      GetDetailErrorHandlingStatusResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateErrorHandlingStatusRequestDto): Promise<any> {
    const { id } = request;
    const errorHandlingStatus =
      await this.errorHandlingStatusRepository.findOneById(id);

    if (isEmpty(errorHandlingStatus)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const isUse =
      await this.masterReferenceService.validateDataMasterUseAnotherService(
        new ValidateMasterRequestDto('error_handling_status_id', id),
      );
    if (isUse) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.USE_ANOTHER_SERVICE'))
        .build();
    }

    // Validate Code unique
    const existCode = await this.errorHandlingStatusRepository.findOneByCode(
      request.code,
    );
    if (!isEmpty(existCode) && existCode.id !== errorHandlingStatus.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    const dataUpdate = this.errorHandlingStatusRepository.updateEntity(
      request,
      errorHandlingStatus,
    );
    const data = await this.errorHandlingStatusRepository.update(dataUpdate);

    const response = plainToInstance(
      GetDetailErrorHandlingStatusResponseDto,
      data,
    );

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteErrorHandlingStatusRequestDto): Promise<any> {
    const { id } = request;
    const errorHandlingStatus =
      await this.errorHandlingStatusRepository.findOneById(id);

    if (isEmpty(errorHandlingStatus)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.errorHandlingStatusRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(
    request: UpdateStatusErrorHandlingStatusRequestDto,
  ): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.errorHandlingStatusRepository.findAllByIds(
      ids,
    );

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.errorHandlingStatusRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(
    request: UpdateStatusErrorHandlingStatusRequestDto,
  ): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.errorHandlingStatusRepository.findAllByIds(
      ids,
    );

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.errorHandlingStatusRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async findOneById(id: number): Promise<ErrorHandlingStatusEntity> {
    return await this.errorHandlingStatusRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<ErrorHandlingStatusEntity[]> {
    return await this.errorHandlingStatusRepository.findAllByIds(ids);
  }
}
