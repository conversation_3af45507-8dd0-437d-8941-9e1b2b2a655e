import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseCommonResponseDto } from '../../../common/dtos/response/base.common.dto';

export class GetListErrorHandlingStatusResponseDto extends BaseCommonResponseDto {
  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  applicableObject: number;

  @ApiProperty()
  @Expose()
  isClaim: number;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty()
  @Expose()
  status: number;
}
