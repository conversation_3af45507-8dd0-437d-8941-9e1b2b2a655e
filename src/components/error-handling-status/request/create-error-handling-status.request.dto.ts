import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsOptional, MaxLength } from 'class-validator';

export class CreateErrorHandlingStatusRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  code: string;

  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  applicableObject: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  isClaim: number;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  description?: string;
}
