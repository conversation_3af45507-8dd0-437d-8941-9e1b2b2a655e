import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt } from 'class-validator';
import { CreateErrorHandlingStatusRequestDto } from './create-error-handling-status.request.dto';

export class UpdateErrorHandlingStatusRequestDto extends CreateErrorHandlingStatusRequestDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;
}
