import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Matches,
  MaxLength,
} from 'class-validator';

export class ImportErrorHandlingStatusRequestDto {
  @IsNotEmpty()
  @MaxLength(50)
  @Matches(/^[a-zA-Z0-9_.-]+$/)
  code: string;

  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @IsNotEmpty()
  @IsNumber()
  applicableObject: number;

  @IsNotEmpty()
  @IsNumber()
  isClaim: number;

  @IsOptional()
  @MaxLength(255)
  description?: string;
}
