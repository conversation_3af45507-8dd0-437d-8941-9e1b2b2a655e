import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
  ValidateNested,
} from 'class-validator';
import { INDEX_CONFIG_INDEX_ENUM } from '../constants/index-config-sample-size.constants';
import { IndexConfigSampleSizeDetailEntity } from '../entities/index-config-sample-size-detail.entity';
import { IndexConfigSampleSizeEntity } from '../entities/index-config-sample-size.entity';

export class UpdateIndexConfigSampleSizeDetailRequestDto {
  @ApiPropertyOptional()
  @IsOptional()
  id: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  indexConfigSampleSizeId: number;

  @ApiProperty({
    example: INDEX_CONFIG_INDEX_ENUM.A2_1,
  })
  @IsNotEmpty()
  @IsEnum(INDEX_CONFIG_INDEX_ENUM)
  index: INDEX_CONFIG_INDEX_ENUM;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  value: number;
}

export class UpdateIndexConfigSampleSizeRequestDto {
  @ApiPropertyOptional()
  @IsOptional()
  id: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  @Min(2)
  sampleSize: number;

  @ApiProperty({ type: [UpdateIndexConfigSampleSizeDetailRequestDto] })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => UpdateIndexConfigSampleSizeDetailRequestDto)
  details: IndexConfigSampleSizeDetailEntity[];
}

export class UpdateRequestDto extends BaseDto {
  @ApiProperty({ type: [UpdateIndexConfigSampleSizeRequestDto] })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => UpdateIndexConfigSampleSizeRequestDto)
  data: IndexConfigSampleSizeEntity[];
}
