import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { IndexConfigSampleSizeController } from './controller/index-config-sample-size.controller';
import { IndexConfigSampleSizeEntity } from './entities/index-config-sample-size.entity';
import { IndexConfigSampleSizeRepository } from './repository/index-config-sample-size.repository';
import { IndexConfigSampleSizeService } from './service/index-config-sample-size.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([IndexConfigSampleSizeEntity]),
    AnotherServiceModule,
  ],
  providers: [IndexConfigSampleSizeService, IndexConfigSampleSizeRepository],
  exports: [IndexConfigSampleSizeService],
  controllers: [IndexConfigSampleSizeController],
})
export class IndexConfigSampleSizeModule {}
