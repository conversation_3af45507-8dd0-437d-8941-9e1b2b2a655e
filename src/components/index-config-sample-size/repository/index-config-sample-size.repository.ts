import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { IndexConfigSampleSizeEntity } from '../entities/index-config-sample-size.entity';

import { UpdateRequestDto } from '../request/update-index-config-sample-size.request.dto';

@Injectable()
export class IndexConfigSampleSizeRepository extends BaseAbstractRepository<IndexConfigSampleSizeEntity> {
  constructor(
    @InjectRepository(IndexConfigSampleSizeEntity)
    private readonly indexConfigSampleSizeRepository: Repository<IndexConfigSampleSizeEntity>,
  ) {
    super(indexConfigSampleSizeRepository);
  }

  updateEntity(request: UpdateRequestDto): IndexConfigSampleSizeEntity[] {
    const data: IndexConfigSampleSizeEntity[] = [];
    request?.data?.forEach((item) => {
      const entity = new IndexConfigSampleSizeEntity();
      if (item?.id) {
        entity.id = item?.id;
      }
      entity.sampleSize = item.sampleSize;
      entity.details = item.details;
      entity.updatedBy = request.userId;
      if (!item?.id) {
        entity.createdBy = request.userId;
      }

      data.push(entity);
    });

    return data;
  }

  async getDetail(): Promise<IndexConfigSampleSizeEntity[]> {
    const data = await this.indexConfigSampleSizeRepository.find();
    return data;
  }

  async getValueBySampleSizeAndIndex(
    sampleSize: number,
    index: number,
  ): Promise<any> {
    const data = await this.indexConfigSampleSizeRepository
      .createQueryBuilder('icss')
      .innerJoin('icss.details', 'icssd')
      .select(['icssd.value as value'])
      .where('icssd.index = :index', { index })
      .orderBy('ABS(icss.sample_size - :sampleSize)', 'ASC')
      .setParameter('sampleSize', sampleSize)
      .limit(1)
      .getRawOne();

    return data;
  }
}
