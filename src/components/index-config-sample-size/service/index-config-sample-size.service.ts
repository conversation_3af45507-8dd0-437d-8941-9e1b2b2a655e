import { Injectable, Logger } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { isEmpty } from 'lodash';
import { In, Not } from 'typeorm';
import { BaseService } from '../../../common/service/base.service';
import { UserService } from '../../another-service/services/user-service';
import { IndexConfigSampleSizeRepository } from '../repository/index-config-sample-size.repository';
import { UpdateRequestDto } from '../request/update-index-config-sample-size.request.dto';

@Injectable()
export class IndexConfigSampleSizeService extends BaseService {
  private readonly logger = new Logger(IndexConfigSampleSizeService.name);

  constructor(
    private readonly indexConfigSampleSizeRepository: IndexConfigSampleSizeRepository,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,
  ) {
    super(userService);
  }

  async getDetail(): Promise<any> {
    const indexConfigSampleSize =
      await this.indexConfigSampleSizeRepository.getDetail();

    return new ResponseBuilder(indexConfigSampleSize)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getValueBySampleSizeAndIndex(
    sampleSize: number,
    index: number,
  ): Promise<any> {
    const value =
      await this.indexConfigSampleSizeRepository.getValueBySampleSizeAndIndex(
        sampleSize,
        index,
      );

    return new ResponseBuilder(value)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateRequestDto): Promise<any> {
    const resultValidate = await this.validateSave(request);
    if (resultValidate?.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(resultValidate.messageError))
        .build();
    }

    const filterCondition = {
      id: Not(In(resultValidate?.ids)),
    };
    await this.indexConfigSampleSizeRepository.removeByCondition(
      filterCondition,
    );

    const dataUpdate =
      this.indexConfigSampleSizeRepository.updateEntity(request);
    const data = await this.indexConfigSampleSizeRepository.update(dataUpdate);

    return new ResponseBuilder(data)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async validateSave(request: UpdateRequestDto) {
    const idsSet: Set<number> = new Set();

    let sampleSizeCheck = 0;
    let messageError = null;
    for (const item of request?.data ?? []) {
      if (item.id !== null && item.id !== undefined) {
        idsSet.add(item.id);
      }

      if (item?.sampleSize < sampleSizeCheck) {
        messageError = 'error.INDEX_CONFIG_SAMPLE_SIZE_VALIDATE';
        break;
      } else {
        sampleSizeCheck = item.sampleSize;
      }
    }

    if (!isEmpty(messageError)) {
      return { result: true, messageError: messageError };
    }

    const ids = Array.from(idsSet);
    if (!isEmpty(ids)) {
      const entities = await this.indexConfigSampleSizeRepository.findAllByIds(
        ids,
      );

      if (isEmpty(entities) || entities.length !== ids.length) {
        return { result: true, messageError: 'error.NOT_FOUND' };
      }
    }

    return { result: false, messageError: '', ids };
  }
}
