import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { IndexConfigSampleSizeEntity } from './index-config-sample-size.entity';

@Entity({ name: 'index_config_sample_size_details' })
export class IndexConfigSampleSizeDetailEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  indexConfigSampleSizeId: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  index: number;

  @Column({
    type: 'decimal',
    precision: 19,
    scale: 4,
    nullable: false,
    default: 0,
  })
  value: number;

  @ManyToOne(() => IndexConfigSampleSizeEntity, (e) => e.details, {
    orphanedRowAction: 'delete',
  })
  @JoinColumn({
    name: 'index_config_sample_size_id',
    referencedColumnName: 'id',
  })
  indexConfigSampleSize: IndexConfigSampleSizeEntity;
}
