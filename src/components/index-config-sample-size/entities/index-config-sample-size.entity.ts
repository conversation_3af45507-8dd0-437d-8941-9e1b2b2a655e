import { Column, Entity, OneToMany } from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { IndexConfigSampleSizeDetailEntity } from './index-config-sample-size-detail.entity';

@Entity({ name: 'index_config_sample_sizes' })
export class IndexConfigSampleSizeEntity extends BaseEntity {
  @Column({
    type: 'int',
  })
  sampleSize: number;

  @OneToMany(
    () => IndexConfigSampleSizeDetailEntity,
    (detail) => detail.indexConfigSampleSize,
    {
      cascade: ['insert', 'update', 'remove'],
      onDelete: 'CASCADE',
      eager: true,
    },
  )
  details: IndexConfigSampleSizeDetailEntity[];
}
