import { Body, Controller, Get, Put } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { UpdateRequestDto } from '../request/update-index-config-sample-size.request.dto';
import { IndexConfigSampleSizeService } from '../service/index-config-sample-size.service';

@Controller('index-config-sample-sizes')
export class IndexConfigSampleSizeController {
  constructor(
    private readonly indexConfigSampleSizeService: IndexConfigSampleSizeService,
  ) {}

  @Get()
  @ApiOperation({
    tags: ['Index-config-sample-sizes'],
    summary: 'Chi tiết Index-config-sample-sizes',
    description: 'Chi tiết Index-config-sample-sizes',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(): Promise<any> {
    return await this.indexConfigSampleSizeService.getDetail();
  }

  @Put()
  @ApiOperation({
    tags: ['Index-config-sample-sizes'],
    summary: 'Cập nhật Index-config-sample-sizes',
    description: 'Cập nhật Index-config-sample-sizes',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(@Body() body: UpdateRequestDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.indexConfigSampleSizeService.update(request);
  }
}
