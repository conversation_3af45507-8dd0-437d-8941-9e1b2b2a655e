import { Module } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { ChemicalTcrsResultController } from './controller/chemical-tcrs-result.controller';
import { ChemicalTcrsResultService } from './service/chemical-tcrs-result.service';

@Module({
  imports: [AnotherServiceModule, BaseProcessModule],
  providers: [ChemicalTcrsResultService],
  exports: [ChemicalTcrsResultService],
  controllers: [ChemicalTcrsResultController],
})
export class ChemicalTcrsResultModule {}
