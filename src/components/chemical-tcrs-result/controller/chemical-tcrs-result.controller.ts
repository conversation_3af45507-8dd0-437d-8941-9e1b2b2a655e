import { Controller, Get, Query } from '@nestjs/common';

import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';
import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  EXPORT_CHEMICAL_TCRS_RESULT_PERMISSION,
  LIST_CHEMICAL_TCRS_RESULT_PERMISSION,
} from '../../../utils/permissions/chemical-tcrs-result.permission';
import { GetListChemicalTcrsResultRequestDto } from '../request/get-list-chemical-tcrs-result.request.dto';
import { ChemicalTcrsResultService } from '../service/chemical-tcrs-result.service';

@Controller('chemical-tcrs-results')
export class ChemicalTcrsResultController {
  constructor(
    private readonly chemicalTcrsResultService: ChemicalTcrsResultService,
  ) {}

  @PermissionCode(LIST_CHEMICAL_TCRS_RESULT_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Chemical-Tcrs-result'],
    summary: 'Danh sách Chemical-Tcrs-result',
    description: 'Danh sách Chemical-Tcrs-result',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListChemicalTcrsResultRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && responseError?.statusCode && !isEmpty(responseError)) {
      return responseError;
    }

    return this.chemicalTcrsResultService.getList(request);
  }

  @PermissionCode(EXPORT_CHEMICAL_TCRS_RESULT_PERMISSION.code)
  @Get('/export')
  @ApiOperation({
    tags: ['Export-excel'],
    summary: 'Export in list screen',
    description: 'Export in list screen',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public export(
    @Query() query: GetListChemicalTcrsResultRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return this.chemicalTcrsResultService.export(request);
  }
}
