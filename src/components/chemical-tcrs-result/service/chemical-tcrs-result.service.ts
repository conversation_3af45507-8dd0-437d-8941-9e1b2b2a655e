import { Injectable, Logger } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { ResponseCodeEnum } from '../../../constant/response-code.enum';

import { exportExcel } from '../../../helper/export.helper';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';
import { addSevenHoursToAllItems } from '../../../utils/helper';
import { ResponseBuilder } from '../../../utils/response-builder';
import { QmsxTcrsService } from '../../another-service/services/qmsx-tcrs-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { ExportExcelColumnProperty } from '../../export-excel/dto/export-excel-column-property.dto';
import {
  chemicalTcrsResearchColumnProperties,
  chemicalTcrsResearchExportSheets,
} from '../../export-excel/template/export-chemical-tcrs-research-template';
import { GetListChemicalTcrsResultRequestDto } from '../request/get-list-chemical-tcrs-result.request.dto';

@Injectable()
export class ChemicalTcrsResultService {
  private readonly logger = new Logger(ChemicalTcrsResultService.name);

  constructor(
    private readonly i18n: I18nService,
    private readonly baseService: BaseProcessService,

    protected readonly qmsxTcrsService: QmsxTcrsService,
  ) {}

  async getList(request: GetListChemicalTcrsResultRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.qmsxTcrsService.getListChemicalTcrs(
      request,
    );

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('error.SUCCESS'))
      .build();
  }

  async export(request: GetListChemicalTcrsResultRequestDto): Promise<any> {
    const { data } = await this.getList(request);
    const dataMapping = addSevenHoursToAllItems(data);
    const mapData = new Map<string, any[]>();
    const mapColumn = new Map<string, ExportExcelColumnProperty[]>();
    mapColumn.set(
      chemicalTcrsResearchExportSheets[0],
      chemicalTcrsResearchColumnProperties,
    );
    mapData.set(chemicalTcrsResearchExportSheets[0], dataMapping.items);
    const fileName = await this.i18n.translate(
      'export.chemicalTcrsResearch.fileName',
    );
    const buffer = await exportExcel(
      chemicalTcrsResearchExportSheets,
      mapData,
      mapColumn,
      fileName,
      this.i18n,
    );
    return new ResponseBuilder(buffer)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
