import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FinanceYearDetailEntity } from './entities/finance-year-detail.entity';
import { FinanceYearDetailRepository } from './repository/finance-year-detail.repository';
import { FinanceYearDetailService } from './service/finance-year-detail.service';

@Module({
  imports: [TypeOrmModule.forFeature([FinanceYearDetailEntity])],
  providers: [FinanceYearDetailService, FinanceYearDetailRepository],
  exports: [FinanceYearDetailService],
  controllers: [],
})
export class FinanceYearDetailModule {}
