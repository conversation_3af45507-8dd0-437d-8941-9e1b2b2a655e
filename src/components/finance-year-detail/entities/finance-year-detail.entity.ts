import {
  Column,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { FinanceYearEntity } from '../../finance-year/entities/finance-year.entity';

@Entity({ name: 'finance_year_details' })
export class FinanceYearDetailEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  financeYearId: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  week: number;

  @Column({
    type: 'datetimeoffset',
    nullable: true,
  })
  startDate: Date;

  @Column({
    type: 'datetimeoffset',
    nullable: true,
  })
  endDate: Date;

  @Column({
    type: 'int',
    nullable: true,
  })
  month: number;

  @ManyToOne(
    () => FinanceYearEntity,
    (financeYear) => financeYear.financeYearDetails,
    {
      orphanedRowAction: 'delete',
    },
  )
  @JoinColumn({
    name: 'finance_year_id',
    referencedColumnName: 'id',
  })
  financeYear: FinanceYearEntity;
}
