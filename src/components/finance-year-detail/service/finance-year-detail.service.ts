import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { DataSource } from 'typeorm';

import { FinanceYearDetailRepository } from '../repository/finance-year-detail.repository';

@Injectable()
export class FinanceYearDetailService {
  private readonly logger = new Logger(FinanceYearDetailService.name);

  constructor(
    private readonly financeYearDetailRepository: FinanceYearDetailRepository,

    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,
  ) {}
}
