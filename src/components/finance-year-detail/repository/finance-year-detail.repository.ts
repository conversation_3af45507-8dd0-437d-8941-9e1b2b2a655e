import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { FinanceYearDetailEntity } from '../entities/finance-year-detail.entity';

@Injectable()
export class FinanceYearDetailRepository extends BaseAbstractRepository<FinanceYearDetailEntity> {
  constructor(
    @InjectRepository(FinanceYearDetailEntity)
    private readonly financeYearDetailRepository: Repository<FinanceYearDetailEntity>,
  ) {
    super(financeYearDetailRepository);
  }
}
