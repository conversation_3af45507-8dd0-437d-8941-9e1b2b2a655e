import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { AqlSampleLimitEntity } from '../entities/aql-sample-limit.entity';
import { CreateAqlSampleLimitRequestDto } from './../request/create-aql-sample-limit.request.dto';
import { DeleteAqlSampleLimitRequestDto } from './../request/delete-aql-sample-limit.request.dto';
import { GetDetailAqlSampleLimitRequestDto } from './../request/get-detail-aql-sample-limit.request.dto';
import { GetListAqlSampleLimitRequestDto } from './../request/get-list-aql-sample-limit.request.dto';
import { UpdateAqlSampleLimitRequestDto } from './../request/update-aql-sample-limit.request.dto';
import { UpdateStatusAqlSampleLimitRequestDto } from './../request/update-status-aql-sample-limit.request.dto';

import { GetDetailAqlSampleLimitResponseDto } from './../response/get-detail-aql-sample-limit.response.dto';
import { GetListAqlSampleLimitResponseDto } from './../response/get-list-aql-sample-limit.response.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { ValidateResultCommonDto } from '../../../common/dtos/validate.result.common.dto';
import { StatusEnum } from '../../../common/enums/status.enum';
import { BaseService } from '../../../common/service/base.service';
import { IS_AQL_ENUM } from '../../../constant/common';
import { UserService } from '../../another-service/services/user-service';
import { AqlSampleLimitDetailService } from '../../aql-sample-limit-detail/service/aql-sample-limit-detail.service';
import { AqlSampleLimitSamplingRateService } from '../../aql-sample-limit-sampling-rate/service/aql-sample-limit-sampling-rate.service';
import { InspectionSampleQuantityService } from '../../inspection-sample-quantity/service/inspection-sample-quantity.service';
import { MasterDataReferenceService } from '../../master-data-reference/service/master-data-reference.service';
import { SamplingRateService } from '../../sampling-rate/service/sampling-rate.service';
import { AqlSampleLimitRepository } from '../repository/aql-sample-limit.repository';

@Injectable()
export class AqlSampleLimitService extends BaseService {
  private readonly logger = new Logger(AqlSampleLimitService.name);

  constructor(
    private readonly aqlSampleLimitRepository: AqlSampleLimitRepository,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly samplingRateService: SamplingRateService,

    private readonly inspectionSampleQuantityService: InspectionSampleQuantityService,

    private readonly aqlSamplingRateService: AqlSampleLimitSamplingRateService,

    private readonly aqlSampleLimitDetailService: AqlSampleLimitDetailService,

    private readonly masterDataReferenceService: MasterDataReferenceService,
  ) {
    super(userService);
  }

  async create(request: CreateAqlSampleLimitRequestDto): Promise<any> {
    const existCode = await this.aqlSampleLimitRepository.findOneByCode(
      request.code,
    );
    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate only one AQL active
    // const { result, messageError } = await this.validateOnlyOneAqlActive();
    // if (result) {
    //   return new ResponseBuilder()
    //     .withCode(ResponseCodeEnum.BAD_REQUEST)
    //     .withMessage(await this.i18n.translate(messageError))
    //     .build();
    // }

    // Validate save aql-sample-limit
    const resultValidate = await this.validateSaveAqlSampleLimit(request);
    if (resultValidate?.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(resultValidate.messageError))
        .build();
    }

    const aqlSampleLimitEntity =
      await this.aqlSampleLimitRepository.createEntity(request);
    const aqlSampleLimit = await this.aqlSampleLimitRepository.create(
      aqlSampleLimitEntity,
    );

    const response = plainToInstance(
      GetDetailAqlSampleLimitResponseDto,
      aqlSampleLimit,
    );

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListAqlSampleLimitRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.aqlSampleLimitRepository.getList(
      request,
    );

    const dataMapUser = await this.mapUserInfoToResponse(data);
    const response = plainToInstance(
      GetListAqlSampleLimitResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailAqlSampleLimitRequestDto): Promise<any> {
    const aqlSampleLimit = await this.aqlSampleLimitRepository.getDetail(
      request,
    );
    if (isEmpty(aqlSampleLimit)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    if (aqlSampleLimit.aqlSampleLimitCustomers.length > 0) {
      const customerIds = aqlSampleLimit.aqlSampleLimitCustomers.map(
        (item) => item.customerId,
      );
      const listCustomers =
        await this.masterDataReferenceService.findAllCustomerByIds(customerIds);
      const customerMap = listCustomers.reduce((map, customer) => {
        map[customer.id] = customer;
        return map;
      }, {});
      aqlSampleLimit.aqlSampleLimitCustomers =
        aqlSampleLimit.aqlSampleLimitCustomers.map((item) => {
          return {
            ...item,
            customer: customerMap[item.customerId],
          };
        });
    }
    if (aqlSampleLimit.aqlSampleLimitVendors.length > 0) {
      const vendorIds = aqlSampleLimit.aqlSampleLimitVendors.map(
        (item) => item.vendorId,
      );
      const listVendors =
        await this.masterDataReferenceService.findAllVendorByIds(vendorIds);
      const customerMap = listVendors.reduce((map, vendor) => {
        map[vendor.id] = vendor;
        return map;
      }, {});
      aqlSampleLimit.aqlSampleLimitVendors =
        aqlSampleLimit.aqlSampleLimitVendors.map((item) => {
          return {
            ...item,
            vendor: customerMap[item.vendorId],
          };
        });
    }

    const inspectionSampleSymbol =
      await this.masterDataReferenceService.findOneInspectionSampleSymbol(
        aqlSampleLimit.inspectionSampleSymbolId,
      );
    aqlSampleLimit.inspectionSampleSymbol = inspectionSampleSymbol;
    const dataMap = await this.mapAqlDataDetailToResponse([aqlSampleLimit]);
    const dataMapUser = await this.mapUserInfoToResponse(dataMap);
    const response = plainToInstance(
      GetDetailAqlSampleLimitResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateAqlSampleLimitRequestDto): Promise<any> {
    const { id } = request;
    const aqlSampleLimit = await this.aqlSampleLimitRepository.findOneById(id);

    if (isEmpty(aqlSampleLimit)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const isUse =
      await this.masterDataReferenceService.validateDataMasterUseAnotherService(
        new ValidateMasterRequestDto('aql_sample_limit_id', id),
      );
    if (isUse) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.USE_ANOTHER_SERVICE'))
        .build();
    }

    // Validate Code unique
    const existCode = await this.aqlSampleLimitRepository.findOneByCode(
      request.code,
    );
    if (!isEmpty(existCode) && existCode.id !== aqlSampleLimit.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate save aql-sample-limit
    const resultValidate = await this.validateSaveAqlSampleLimit(request);
    if (resultValidate?.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(resultValidate.messageError))
        .build();
    }

    const dataUpdate = await this.aqlSampleLimitRepository.updateEntity(
      request,
      aqlSampleLimit,
    );
    const data = await this.aqlSampleLimitRepository.update(dataUpdate);

    const response = plainToInstance(GetDetailAqlSampleLimitResponseDto, data);

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteAqlSampleLimitRequestDto): Promise<any> {
    const { id } = request;
    const aqlSampleLimit = await this.aqlSampleLimitRepository.findOneById(id);

    if (isEmpty(aqlSampleLimit)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const isUse =
      await this.masterDataReferenceService.validateDataMasterUseAnotherService(
        new ValidateMasterRequestDto('aql_sample_limit_id', id),
      );
    if (isUse) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.USE_ANOTHER_SERVICE'))
        .build();
    }

    await this.aqlSampleLimitRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(request: UpdateStatusAqlSampleLimitRequestDto): Promise<any> {
    // Validate only one AQL active
    // const resultValidate = await this.validateOnlyOneAqlActive();
    // if (resultValidate?.result) {
    //   return new ResponseBuilder()
    //     .withCode(ResponseCodeEnum.BAD_REQUEST)
    //     .withMessage(await this.i18n.translate(resultValidate.messageError))
    //     .build();
    // }

    const { ids } = request;

    const listExitsInDB = await this.aqlSampleLimitRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.aqlSampleLimitRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(request: UpdateStatusAqlSampleLimitRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.aqlSampleLimitRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.aqlSampleLimitRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async findOneById(id: number): Promise<AqlSampleLimitEntity> {
    return await this.aqlSampleLimitRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<AqlSampleLimitEntity[]> {
    return await this.aqlSampleLimitRepository.findAllByIds(ids);
  }

  async validateOnlyOneAqlActive(): Promise<ValidateResultCommonDto> {
    const aqlActive =
      await this.aqlSampleLimitRepository.findAqlSampleLimitActive();

    if (!isEmpty(aqlActive)) {
      return {
        result: true,
        messageError: 'error.AQL_SAMPLE_LIMIT_IS_NOT_ALLOWED_ACTIVE',
      };
    }

    return { result: false, messageError: '' };
  }

  async validateSaveAqlSampleLimit(
    request: CreateAqlSampleLimitRequestDto,
  ): Promise<ValidateResultCommonDto> {
    // Validate exist sampling-rates
    const samplingRateIds = request?.samplingRates?.flatMap(
      (item) => item.samplingRateId,
    );
    const existSamplingRates = await this.samplingRateService.findAllByIds(
      samplingRateIds,
    );
    const inactiveSamplingRates = existSamplingRates?.filter(
      (samplingRate) =>
        samplingRate.status === StatusEnum.IN_ACTIVE ||
        samplingRate.isAql === IS_AQL_ENUM.NO,
    );
    if (
      isEmpty(samplingRateIds) ||
      isEmpty(existSamplingRates) ||
      !isEmpty(inactiveSamplingRates) ||
      samplingRateIds?.length !== existSamplingRates?.length
    ) {
      return {
        result: true,
        messageError: 'error.SAMPLING_RATE_IS_NOT_EXISTS',
      };
    }

    // Validate exist inspection-sample-quantities
    const isqIds: number[] = Array.from(
      new Set(
        request?.dataDetails
          ?.flatMap((item) => item?.inspectionSampleQuantityId)
          .filter((id) => id !== null && id !== undefined),
      ),
    );

    if (request?.dataDetails?.length !== isqIds.length) {
      return {
        result: true,
        messageError:
          'error.AQL_SAMPLE_LIMIT_INSPECTION_SAMPLE_QUANTITY_HAS_DUPLICATE',
      };
    }

    const existInspectionSampleQuantities =
      await this.inspectionSampleQuantityService.findAllByIds(isqIds);
    const isqInactive = existInspectionSampleQuantities?.filter(
      (item) => item.status === StatusEnum.IN_ACTIVE,
    );
    if (
      isEmpty(isqIds) ||
      isEmpty(existInspectionSampleQuantities) ||
      !isEmpty(isqInactive) ||
      isqIds?.length !== existInspectionSampleQuantities?.length
    ) {
      return {
        result: true,
        messageError: 'error.INSPECTION_SAMPLE_QUANTITY_IS_NOT_EXISTS',
      };
    }

    if (request?.dataDetails?.length !== isqIds.length) {
      return {
        result: true,
        messageError:
          'error.AQL_SAMPLE_LIMIT_INSPECTION_SAMPLE_QUANTITY_HAS_DUPLICATE',
      };
    }

    return { result: false, messageError: '' };
  }

  async mapAqlDataDetailToResponse(data: AqlSampleLimitEntity[]) {
    const aqlSampleLimitIds: number[] = Array.from(
      new Set(data?.flatMap((item) => item.id)),
    );
    if (isEmpty(aqlSampleLimitIds)) return data;

    const aqlSamplingRates =
      await this.aqlSamplingRateService.findAllByAqlSampleLimitIds(
        aqlSampleLimitIds,
      );
    if (isEmpty(aqlSamplingRates)) return data;

    const aqlSamplingRateMap = aqlSamplingRates?.reduce((acc, item) => {
      const key = `${item.aqlSampleLimitId}`;
      if (isEmpty(acc[key])) {
        acc[key] = { samplingRates: [item] };
      } else {
        acc[key].samplingRates.push(item);
      }
      return acc;
    }, {});

    const inspectionSampleQuantities =
      await this.aqlSampleLimitDetailService.findAllByAqlSampleLimitIds(
        aqlSampleLimitIds,
      );

    const inspectionSampleQuantityIds: number[] = Array.from(
      new Set(
        inspectionSampleQuantities?.flatMap(
          (item) => item.inspectionSampleQuantityId,
        ),
      ),
    );
    if (isEmpty(inspectionSampleQuantityIds)) return data;

    const isqDetailMap =
      await this.inspectionSampleQuantityService.getIsqDataDetail(
        inspectionSampleQuantityIds,
      );

    const inspectionSampleQuantityMap = inspectionSampleQuantities?.reduce(
      (acc, item) => {
        const key = `${item.inspectionSampleQuantityId}`;
        item.inspectionSampleQuantity.dataDetails =
          isqDetailMap[item.inspectionSampleQuantityId].dataDetails;
        acc[key] = item?.inspectionSampleQuantity;
        return acc;
      },
      {},
    );

    return (
      data?.map((item) => {
        return {
          ...item,
          samplingRates: aqlSamplingRateMap[item.id]?.samplingRates,
          dataDetails: item?.dataDetails?.map((detail) => {
            return {
              ...detail,
              inspectionSampleQuantity:
                inspectionSampleQuantityMap[detail.inspectionSampleQuantityId],
            };
          }),
        };
      }) || []
    );
  }
}
