import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { isDateString } from 'class-validator';
import { StatusEnum } from '../../../common/enums/status.enum';
import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { escapeCharForSearch } from '../../../utils/common';
import { AqlSampleLimitEntity } from '../entities/aql-sample-limit.entity';
import { GetDetailAqlSampleLimitRequestDto } from '../request/get-detail-aql-sample-limit.request.dto';
import { GetListAqlSampleLimitRequestDto } from '../request/get-list-aql-sample-limit.request.dto';
import { UpdateAqlSampleLimitRequestDto } from '../request/update-aql-sample-limit.request.dto';
import { UpdateStatusAqlSampleLimitRequestDto } from '../request/update-status-aql-sample-limit.request.dto';
import { CreateAqlSampleLimitRequestDto } from './../request/create-aql-sample-limit.request.dto';

@Injectable()
export class AqlSampleLimitRepository extends BaseAbstractRepository<AqlSampleLimitEntity> {
  constructor(
    @InjectRepository(AqlSampleLimitEntity)
    private readonly aqlSampleLimitRepository: Repository<AqlSampleLimitEntity>,
  ) {
    super(aqlSampleLimitRepository);
  }

  async createEntity(
    request: CreateAqlSampleLimitRequestDto,
  ): Promise<AqlSampleLimitEntity> {
    const aqlSampleLimitEntity = new AqlSampleLimitEntity();
    Object.assign(aqlSampleLimitEntity, request);
    aqlSampleLimitEntity.status = StatusEnum.ACTIVE;
    aqlSampleLimitEntity.createdBy = request.userId;
    aqlSampleLimitEntity.updatedBy = request.userId;

    return aqlSampleLimitEntity;
  }

  async updateEntity(
    request: UpdateAqlSampleLimitRequestDto,
    entity: AqlSampleLimitEntity,
  ): Promise<AqlSampleLimitEntity> {
    Object.assign(entity, request);
    entity.name = request?.name;
    entity.description = request?.description || null;
    entity.updatedBy = request?.userId;

    return entity;
  }

  async getList(request: GetListAqlSampleLimitRequestDto): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query =
      this.aqlSampleLimitRepository.createQueryBuilder('aqlsamplelimit');

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("aqlsamplelimit"."code") like lower(:code) escape '\\' OR
              lower("aqlsamplelimit"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('aqlsamplelimit.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'ids':
            query.andWhere('"aqlsamplelimit"."id" IN (:...ids)', {
              ids: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'code':
            query.andWhere(
              `lower("aqlsamplelimit"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'name':
            query.andWhere(
              `lower("aqlsamplelimit"."name") like lower(:name) escape '\\'`,
              {
                name: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'description':
            query.andWhere(
              `lower("aqlsamplelimit"."description") like lower(:description) escape '\\'`,
              {
                description: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'status':
            query.andWhere('"aqlsamplelimit"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'createdById':
            query.andWhere('"aqlsamplelimit"."created_by" = :createdById', {
              createdById: Number(value),
            });
            break;
          case 'createdByIds':
            query.andWhere(
              '"aqlsamplelimit"."created_by" IN (:...createdByIds)',
              {
                createdByIds: value.split(',').map((id) => Number(id)),
              },
            );
            break;
          case 'createdAt':
            const createFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const createTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"aqlsamplelimit"."created_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: createFrom,
                dateTo: createTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"aqlsamplelimit"."updated_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: updateFrom,
                dateTo: upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'name':
            query.addOrderBy('"aqlsamplelimit"."name"', order);
            break;
          case 'code':
            query.addOrderBy('"aqlsamplelimit"."code"', order);
            break;
          case 'status':
            query.addOrderBy('"aqlsamplelimit"."status"', order);
            break;
          case 'description':
            query.addOrderBy('"aqlsamplelimit"."description"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"aqlsamplelimit"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"aqlsamplelimit"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"aqlsamplelimit"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('aqlsamplelimit.id', 'DESC');
    }

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const [data, count] = await query.getManyAndCount();

    return { data, count };
  }

  async getDetail(request: GetDetailAqlSampleLimitRequestDto): Promise<any> {
    const { id } = request;

    const data = await this.aqlSampleLimitRepository.findOne({
      where: { id: id },
    });

    return data;
  }

  async findAqlSampleLimitActive(): Promise<AqlSampleLimitEntity[]> {
    return await this.aqlSampleLimitRepository.find({
      where: { status: StatusEnum.ACTIVE },
    });
  }

  async active(request: UpdateStatusAqlSampleLimitRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.aqlSampleLimitRepository
      .createQueryBuilder()
      .update(AqlSampleLimitEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(request: UpdateStatusAqlSampleLimitRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.aqlSampleLimitRepository
      .createQueryBuilder()
      .update(AqlSampleLimitEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }
}
