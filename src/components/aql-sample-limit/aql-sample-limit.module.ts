import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { AqlSampleLimitDetailModule } from '../aql-sample-limit-detail/aql-sample-limit-detail.module';
import { AqlSampleLimitSamplingRateModule } from '../aql-sample-limit-sampling-rate/aql-sample-limit-sampling-rate.module';
import { InspectionSampleQuantityModule } from '../inspection-sample-quantity/inspection-sample-quantity.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { SamplingRateModule } from '../sampling-rate/sampling-rate.module';
import { AqlSampleLimitController } from './controller/aql-sample-limit.controller';
import { AqlSampleLimitEntity } from './entities/aql-sample-limit.entity';
import { AqlSampleLimitRepository } from './repository/aql-sample-limit.repository';
import { AqlSampleLimitService } from './service/aql-sample-limit.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([AqlSampleLimitEntity]),
    AnotherServiceModule,
    SamplingRateModule,
    InspectionSampleQuantityModule,
    AqlSampleLimitSamplingRateModule,
    AqlSampleLimitDetailModule,
    MasterDataReferenceModule,
  ],
  providers: [AqlSampleLimitService, AqlSampleLimitRepository],
  exports: [AqlSampleLimitService],
  controllers: [AqlSampleLimitController],
})
export class AqlSampleLimitModule {}
