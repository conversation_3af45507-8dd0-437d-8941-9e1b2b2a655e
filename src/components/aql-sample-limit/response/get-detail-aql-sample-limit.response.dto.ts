import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { BaseCommonResponseDto } from '../../../common/dtos/response/base.common.dto';
import { ResponseCommonDto } from '../../../common/dtos/response/response.common.dto';
import { SamplingRateResponseDto } from '../../../common/dtos/response/sampling-rate.common.dto';
import { AqlSampleLimitVendorEntity } from '../entities/aql-sample-limit-vendor.entity';

export class GetDetailAqlDataDetailDto {
  @ApiProperty()
  @Expose()
  id: number;

  @ApiProperty()
  @Expose()
  aqlSampleLimitid: number;

  @ApiProperty({ type: ResponseCommonDto })
  @Expose()
  @Type(() => ResponseCommonDto)
  inspectionSampleQuantity: ResponseCommonDto;
}

export class GetDetailAqlSampleLimitResponseDto extends BaseCommonResponseDto {
  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty()
  @Expose()
  status: number;

  @Expose()
  inspectionSampleSymbolId: number;

  @Expose()
  inspectionSampleSymbol: any;

  @ApiProperty({ type: [SamplingRateResponseDto] })
  @Expose()
  @Type(() => SamplingRateResponseDto)
  samplingRates: SamplingRateResponseDto[];

  @ApiProperty({ type: [GetDetailAqlDataDetailDto] })
  @Expose()
  @Type(() => GetDetailAqlDataDetailDto)
  dataDetails: GetDetailAqlDataDetailDto[];

  @Expose()
  aqlSampleLimitCustomers: any[];

  @Expose()
  @Type(() => AqlSampleLimitVendorEntity)
  aqlSampleLimitVendors: any[];
}
