import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  ACTIVE_AQL_SIMPLE_LITMIT_PERMISSION,
  CREATE_AQL_SIMPLE_LITMIT_PERMISSION,
  DELETE_AQL_SIMPLE_LITMIT_PERMISSION,
  DETAIL_AQL_SIMPLE_LITMIT_PERMISSION,
  INACTIVE_AQL_SIMPLE_LITMIT_PERMISSION,
  LIST_AQL_SIMPLE_LITMIT_PERMISSION,
  UPDATE_AQL_SIMPLE_LITMIT_PERMISSION,
} from '../../../utils/permissions/aql-sample-limit.permission';
import { CreateAqlSampleLimitRequestDto } from '../request/create-aql-sample-limit.request.dto';
import { DeleteAqlSampleLimitRequestDto } from '../request/delete-aql-sample-limit.request.dto';
import { GetDetailAqlSampleLimitRequestDto } from '../request/get-detail-aql-sample-limit.request.dto';
import { GetListAqlSampleLimitRequestDto } from '../request/get-list-aql-sample-limit.request.dto';
import { UpdateAqlSampleLimitRequestDto } from '../request/update-aql-sample-limit.request.dto';
import { UpdateStatusAqlSampleLimitRequestDto } from '../request/update-status-aql-sample-limit.request.dto';
import { AqlSampleLimitService } from '../service/aql-sample-limit.service';

@Controller('aql-sample-limits')
export class AqlSampleLimitController {
  constructor(private readonly aqlSampleLimitService: AqlSampleLimitService) {}

  @PermissionCode(DETAIL_AQL_SIMPLE_LITMIT_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Aql-sample-limits'],
    summary: 'Chi tiết Aql-sample-limits',
    description: 'Chi tiết Aql-sample-limits',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailAqlSampleLimitRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.aqlSampleLimitService.getDetail(request);
  }

  @PermissionCode(LIST_AQL_SIMPLE_LITMIT_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Aql-sample-limits'],
    summary: 'Danh sách Aql-sample-limits',
    description: 'Danh sách Aql-sample-limits',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListAqlSampleLimitRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.aqlSampleLimitService.getList(request);
  }

  @PermissionCode(CREATE_AQL_SIMPLE_LITMIT_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Aql-sample-limits'],
    summary: 'Tạo Aql-sample-limits mới',
    description: 'Tạo Aql-sample-limits mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateAqlSampleLimitRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.aqlSampleLimitService.create(request);
  }

  @PermissionCode(UPDATE_AQL_SIMPLE_LITMIT_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Aql-sample-limits'],
    summary: 'Cập nhật Aql-sample-limits',
    description: 'Cập nhật Aql-sample-limits',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateAqlSampleLimitRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.aqlSampleLimitService.update(request);
  }

  @PermissionCode(ACTIVE_AQL_SIMPLE_LITMIT_PERMISSION.code)
  @Put('/active')
  @ApiOperation({
    tags: ['Aql-sample-limits'],
    summary: 'Cập nhật Aql-sample-limits Status Active',
    description: 'Cập nhật Aql-sample-limits Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async active(
    @Body() body: UpdateStatusAqlSampleLimitRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.aqlSampleLimitService.active(request);
  }

  @PermissionCode(INACTIVE_AQL_SIMPLE_LITMIT_PERMISSION.code)
  @Put('/inactive')
  @ApiOperation({
    tags: ['Aql-sample-limits'],
    summary: 'Cập nhật Aql-sample-limits Status Inactive',
    description: 'Cập nhật Aql-sample-limits Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async inactive(
    @Body() body: UpdateStatusAqlSampleLimitRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.aqlSampleLimitService.inactive(request);
  }

  @PermissionCode(DELETE_AQL_SIMPLE_LITMIT_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Aql-sample-limits'],
    summary: 'Xóa Aql-sample-limits',
    description: 'Xóa Aql-sample-limits',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(@Param() param: DeleteAqlSampleLimitRequestDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.aqlSampleLimitService.delete(request);
  }
}
