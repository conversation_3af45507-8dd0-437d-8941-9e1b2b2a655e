import { Column, Entity, OneToMany } from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { AqlSampleLimitDetailEntity } from '../../aql-sample-limit-detail/entities/aql-sample-limit-detail.entity';
import { AqlSampleLimitSamplingRateEntity } from '../../aql-sample-limit-sampling-rate/entities/aql-sample-limit-sampling-rate.entity';
import { AqlSampleLimitCustomerEntity } from './aql-sample-limit-customer.entity';
import { AqlSampleLimitVendorEntity } from './aql-sample-limit-vendor.entity';

@Entity({ name: 'aql_sample_limits' })
export class AqlSampleLimitEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    unique: true,
    nullable: false,
    length: 255,
  })
  code: string;

  @Column({
    type: 'nvarchar',
    nullable: false,
    length: 255,
  })
  name: string;

  @Column({
    type: 'int',
    nullable: false,
  })
  inspectionSampleSymbolId: number;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  description: string;

  @Column({
    type: 'tinyint',
  })
  status: number;

  @OneToMany(
    () => AqlSampleLimitSamplingRateEntity,
    (aqlSampleLimitSamplingRate) => aqlSampleLimitSamplingRate.aqlSampleLimit,
    {
      cascade: ['insert', 'update', 'remove'],
      onDelete: 'CASCADE',
      eager: true,
    },
  )
  samplingRates: AqlSampleLimitSamplingRateEntity[];

  @OneToMany(
    () => AqlSampleLimitDetailEntity,
    (qlSampleLimitDetail) => qlSampleLimitDetail.aqlSampleLimit,
    {
      cascade: ['insert', 'update', 'remove'],
      onDelete: 'CASCADE',
      eager: true,
    },
  )
  dataDetails: AqlSampleLimitDetailEntity[];

  @OneToMany(
    () => AqlSampleLimitVendorEntity,
    (aqlSampleLimitSamplingRate) => aqlSampleLimitSamplingRate.aqlSampleLimit,
    {
      cascade: ['insert', 'update', 'remove'],
      onDelete: 'CASCADE',
      eager: true,
    },
  )
  aqlSampleLimitVendors: AqlSampleLimitVendorEntity[];

  @OneToMany(
    () => AqlSampleLimitCustomerEntity,
    (aqlSampleLimitSamplingRate) => aqlSampleLimitSamplingRate.aqlSampleLimit,
    {
      cascade: ['insert', 'update', 'remove'],
      onDelete: 'CASCADE',
      eager: true,
    },
  )
  aqlSampleLimitCustomers: AqlSampleLimitCustomerEntity[];
}
