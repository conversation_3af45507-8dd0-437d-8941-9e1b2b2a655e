import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AqlSampleLimitEntity } from '../../aql-sample-limit/entities/aql-sample-limit.entity';

@Entity({ name: 'aql_sample_limit_vendors' })
export class AqlSampleLimitVendorEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  aqlSampleLimitId: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  vendorId: number;

  @ManyToOne(
    () => AqlSampleLimitEntity,
    (aqlSampleLimit) => aqlSampleLimit.aqlSampleLimitVendors,
    {
      orphanedRowAction: 'delete',
    },
  )
  @JoinColumn({ name: 'aql_sample_limit_id', referencedColumnName: 'id' })
  aqlSampleLimit: AqlSampleLimitEntity;
}
