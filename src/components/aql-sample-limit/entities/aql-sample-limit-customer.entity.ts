import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AqlSampleLimitEntity } from './aql-sample-limit.entity';

@Entity({ name: 'aql_sample_limit_customers' })
export class AqlSampleLimitCustomerEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  aqlSampleLimitId: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  customerId: number;

  @ManyToOne(
    () => AqlSampleLimitEntity,
    (aqlSampleLimit) => aqlSampleLimit.aqlSampleLimitCustomers,
    {
      orphanedRowAction: 'delete',
    },
  )
  @JoinColumn({ name: 'aql_sample_limit_id', referencedColumnName: 'id' })
  aqlSampleLimit: AqlSampleLimitEntity;
}
