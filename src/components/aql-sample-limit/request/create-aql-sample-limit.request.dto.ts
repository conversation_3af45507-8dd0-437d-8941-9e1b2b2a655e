import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { AqlSampleLimitDetailEntity } from '../../aql-sample-limit-detail/entities/aql-sample-limit-detail.entity';
import { CreateAqlSampleLimitDetailRequestDto } from '../../aql-sample-limit-detail/request/create-aql-sample-limit-detail.request.dto';
import { AqlSampleLimitSamplingRateEntity } from '../../aql-sample-limit-sampling-rate/entities/aql-sample-limit-sampling-rate.entity';
import { CreateAqlSampleLimitSamplingRateRequestDto } from '../../aql-sample-limit-sampling-rate/request/create-aql-sample-limit-sampling-rate.request.dto';
import { AqlSampleLimitCustomerEntity } from '../entities/aql-sample-limit-customer.entity';
import { AqlSampleLimitVendorEntity } from '../entities/aql-sample-limit-vendor.entity';
import { CreateAqlSampleLimitCustomerRequestDto } from './create-aql-sample-limit-customer.request.dto';
import { CreateAqlSampleLimitVendorRequestDto } from './create-aql-sample-limit-vendor.request.dto';

export class CreateAqlSampleLimitRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  code: string;

  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  inspectionSampleSymbolId: number = 0;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  description?: string;

  @ApiProperty({ type: [CreateAqlSampleLimitSamplingRateRequestDto] })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CreateAqlSampleLimitSamplingRateRequestDto)
  samplingRates: AqlSampleLimitSamplingRateEntity[];

  @ApiProperty({ type: [CreateAqlSampleLimitDetailRequestDto] })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested()
  @Type(() => CreateAqlSampleLimitDetailRequestDto)
  dataDetails: AqlSampleLimitDetailEntity[];

  @ApiPropertyOptional({ type: [CreateAqlSampleLimitVendorRequestDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateAqlSampleLimitVendorRequestDto)
  aqlSampleLimitVendors: AqlSampleLimitVendorEntity[];

  @ApiPropertyOptional({ type: [CreateAqlSampleLimitCustomerRequestDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateAqlSampleLimitCustomerRequestDto)
  aqlSampleLimitCustomers: AqlSampleLimitCustomerEntity[];
}
