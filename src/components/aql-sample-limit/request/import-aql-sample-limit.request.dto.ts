import {
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
} from 'class-validator';

export class ImportAqlSampleLimitRequestDto {
  @IsNotEmpty()
  @MaxLength(50)
  @Matches(/^[a-zA-Z0-9_.-]+$/)
  code: string;

  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @IsOptional()
  @MaxLength(255)
  description?: string;

  @IsNotEmpty()
  @IsString()
  samplingRateCode: string;

  @IsNotEmpty()
  @IsString()
  inspectionSampleQuantityCode: string;
}
