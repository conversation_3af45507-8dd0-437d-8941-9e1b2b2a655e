import { Injectable } from '@nestjs/common';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { CreateClaimFeeTicketRequestDto } from './../request/create-claim-fee-ticket.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ErrorData } from '../../../common/errors/base.error';
import { IdParamDto } from '../../../core/dto/request/id-param.request.dto';
import { CreateClaimFeeTicketRequest } from '../../another-service/request/claim-fee-ticket/create-claim-fee-ticket.request';
import { UpdateClaimFeeTicketRequest } from '../../another-service/request/claim-fee-ticket/update-claim-fee-ticket.request';
import { QmsxReportService } from '../../another-service/services/qmsx-report-service';
import { VendorService } from '../../another-service/services/vendor-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { CreateClaimVendorReportRequestDto } from '../../claim-vendor-report/request/create-claim-vendor-report.request.dto';
import { ClaimVendorReportService } from '../../claim-vendor-report/service/claim-vendor-report.service';
import { FinanceYearService } from '../../finance-year/service/finance-year.service';
import { CLAIM_FEE_TICKET_STATUS } from '../claim-fee-ticket.constant';
import { GetDataForCreateClaimFeeTicketRequestDto } from '../request/get-data-for-create-claim-fee-ticket.request.dto';
import { GetListClaimFeeTicketRequestDto } from '../request/get-list-claim-fee-ticket.request.dto';
import { UpdateClaimFeeTicketRequestDto } from '../request/update-claim-fee-ticket.request.dto';

@Injectable()
export class ClaimFeeTicketService {
  constructor(
    private readonly i18n: I18nService,

    private readonly vendorService: VendorService,

    private readonly baseService: BaseProcessService,

    private readonly claimVendorReportService: ClaimVendorReportService,

    private readonly fianceYearService: FinanceYearService,

    private readonly qmsxReportService: QmsxReportService,
  ) {}

  async create(request: CreateClaimFeeTicketRequestDto): Promise<any> {
    const { result, messageError } = await this.validateCreateUpdate(request);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }
    const requestCreate = await this.buildCreateClaimFeeTicketRequest(request);
    const entity = await this.vendorService.createClaimTicketFee(requestCreate);
    if (isEmpty(entity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    return new ResponseBuilder(entity)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateClaimFeeTicketRequestDto): Promise<any> {
    const { result, messageError } = await this.validateCreateUpdate(request);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }
    const entity = await this.vendorService.getDetailClaimFeeTicketById({
      id: request.id,
    });
    if (isEmpty(entity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const updateRequest = await this.buildUpdateClaimFeeTicketRequest(request);
    const responseEntity = await this.vendorService.updateClaimFeeTicket(
      updateRequest,
    );
    if (isEmpty(responseEntity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }
    return new ResponseBuilder(responseEntity)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: IdParamDto): Promise<any> {
    const entity = await this.vendorService.getDetailClaimFeeTicketById(
      request,
    );
    if (isEmpty(entity)) {
      throw ErrorData.Validate.claimUnitPriceDoesNotExist(this.i18n);
    }

    const dataMapUser = await this.baseService.mapMasterInfoToResponse([
      entity,
    ]);
    const details = await this.baseService.mapMasterInfoToResponse(
      entity.claimFeeTicketDetail,
    );
    dataMapUser.forEach((s) => {
      s.claimFeeTicketDetail = details;
    });
    return new ResponseBuilder(dataMapUser[0] ? dataMapUser[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async approve(request: IdParamDto): Promise<any> {
    const entity = await this.vendorService.getDetailClaimFeeTicketById({
      id: request.id,
    });
    let responseReport;
    if (entity.status == CLAIM_FEE_TICKET_STATUS.WAITING_CONFIRM) {
      const reportRequest = new CreateClaimVendorReportRequestDto();
      const finance = await this.fianceYearService.findOneByDate(
        entity.voucherDate,
      );
      reportRequest.vendorId = entity.vendorId;
      reportRequest.month = finance
        ? finance.month
        : new Date(entity.voucherDate).getMonth() + 1;
      reportRequest.year = finance
        ? finance.year
        : new Date(entity.voucherDate).getFullYear();
      reportRequest.totalAmount = entity.claimFeeTicketDetail.reduce(
        (total, detail) => total + detail.billingQuantity * detail.unitPrice,
        0,
      );
      responseReport = await this.claimVendorReportService.create(
        reportRequest,
      );
      if (responseReport.statusCode !== ResponseCodeEnum.SUCCESS) {
        return new ResponseBuilder()
          .withCode(ResponseCodeEnum.BAD_REQUEST)
          .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
          .build();
      }
    }
    const { result, messageError } =
      await this.vendorService.approveClaimFeeTicket(request);
    if (!result) {
      if (responseReport != null) {
        await this.qmsxReportService.rollBackClaimVendorReport({
          id: responseReport.data.id,
        });
      }
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async reject(request: IdParamDto): Promise<any> {
    const { result, messageError } =
      await this.vendorService.rejectClaimFeeTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: IdParamDto): Promise<any> {
    const { result, messageError } =
      await this.vendorService.deleteClaimFeeTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async validateCreateUpdate(
    request: UpdateClaimFeeTicketRequestDto | CreateClaimFeeTicketRequestDto,
    id?: number,
  ) {
    const keyDup = ['lotNo', 'itemId', 'unitId', 'processId'];

    if (
      this.baseService.checkDuplicateByKey(request.claimFeeTicketDetail, keyDup)
    ) {
      return {
        result: false,
        messageError: 'error.ClAIM_FEE_TICKET_HAS_DUPLICATE_KEY',
      };
    }

    const existInPeriod =
      await this.vendorService.getClaimFeeTicketByVendorIdInPeriod({
        vendorId: request.vendorId,
        fromDate: request.aggregationFromDate,
        toDate: request.aggregationToDate,
      });
    const listExistId = existInPeriod.map((item) => item.id);
    if (id == null && existInPeriod.length > 0) {
      return {
        result: false,
        messageError: 'error.CLAIM_FEE_TICKET_EXIST_IN_PERIOD',
      };
    }
    if (
      id != null &&
      (listExistId.length > 1 ||
        (!listExistId.includes(id) && listExistId.length == 1))
    ) {
      return {
        result: false,
        messageError: 'error.CLAIM_FEE_TICKET_EXIST_IN_PERIOD',
      };
    }
    const { itemIds, unitIds, processIds } =
      request.claimFeeTicketDetail.reduce(
        (result, item) => {
          if (item.itemId !== null && item.itemId !== undefined) {
            result.itemIds.add(item.itemId);
          }
          if (item.processId !== null && item.processId !== undefined) {
            result.processIds.add(item.processId);
          }
          if (item.unitId !== null && item.unitId !== undefined) {
            result.unitIds.add(item.unitId);
          }
          return result;
        },
        {
          unitIds: new Set<number>(),
          itemIds: new Set<number>(),
          processIds: new Set<number>(),
        },
      );
    return await this.baseService.validateMaster({
      itemIds: Array.from(itemIds),
      vendorIds: [+request.vendorId],
      unitIds: Array.from(unitIds),
      processIds: Array.from(processIds),
      departmentIds: request.requestDepartmentId
        ? [+request.requestDepartmentId]
        : [],
    });
  }

  async buildCreateClaimFeeTicketRequest(
    request: CreateClaimFeeTicketRequestDto,
  ): Promise<CreateClaimFeeTicketRequest> {
    const createClaimFeeTicketRequest = new CreateClaimFeeTicketRequest();
    Object.assign(createClaimFeeTicketRequest, request);
    createClaimFeeTicketRequest.createdBy = request.userId;
    createClaimFeeTicketRequest.updatedBy = request.userId;
    return createClaimFeeTicketRequest;
  }

  async buildUpdateClaimFeeTicketRequest(
    request: UpdateClaimFeeTicketRequestDto,
  ): Promise<UpdateClaimFeeTicketRequest> {
    const updateEntity = new UpdateClaimFeeTicketRequest();
    Object.assign(updateEntity, request);
    updateEntity.id = request.id;
    updateEntity.updatedBy = request.userId;
    return updateEntity;
  }

  async getList(request: GetListClaimFeeTicketRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.vendorService.listClaimFeeTicket(
      request,
    );

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDataForCreateClaimFeeTicket(
    request: GetDataForCreateClaimFeeTicketRequestDto,
  ): Promise<any> {
    const data = await this.vendorService.getDataForCreateClaimFeeTicket(
      request,
    );
    if (isEmpty(data)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(await this.i18n.translate('success.SUCCESS'))
        .build();
    }
    const response = await this.baseService.mapMasterInfoToResponse(data);
    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
