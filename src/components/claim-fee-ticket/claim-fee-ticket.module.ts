import { Module } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { ClaimVendorReportModule } from '../claim-vendor-report/claim-vendor-report.module';
import { FinanceYearModule } from '../finance-year/finance-year.module';
import { ItemModule } from '../item/item.module';
import { ClaimFeeTicketController } from './controller/claim-fee-ticket.controller';
import { ClaimFeeTicketService } from './service/claim-fee-ticket.service';

@Module({
  imports: [
    AnotherServiceModule,
    ItemModule,
    BaseProcessModule,
    ClaimVendorReportModule,
    FinanceYearModule,
  ],
  providers: [ClaimFeeTicketService],
  exports: [ClaimFeeTicketService],
  controllers: [ClaimFeeTicketController],
})
export class ClaimFeeTicketModule {}
