import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty } from 'class-validator';
import { BaseDto } from 'src/core/dto/base.dto';

export class GetDataForCreateClaimFeeTicketRequestDto extends BaseDto {
  @ApiProperty({ example: '2024-10-15' })
  @IsNotEmpty()
  @IsDateString()
  aggregationFromDate: Date;

  @ApiProperty({ example: '2024-11-16' })
  @IsNotEmpty()
  @IsDateString()
  aggregationToDate: Date;

  @ApiProperty({ example: '2024-11-16' })
  @IsNotEmpty()
  @IsDateString()
  voucherDate: Date;

  @ApiProperty()
  @IsNotEmpty()
  vendorId: number;
}
