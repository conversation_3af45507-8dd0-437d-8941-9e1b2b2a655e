import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsDateString,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { FileDto } from '../../../utils/dto/request/file.request.dto';
import { CreateClaimFeeTicketDetailRequestDto } from './create-claim-fee-ticket-detail.request.dto';

export class CreateClaimFeeTicketRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  requestDepartmentId: number;

  @ApiProperty()
  @IsNotEmpty()
  vendorId: number;

  @ApiProperty({ example: '2024-10-15' })
  @IsNotEmpty()
  @IsDateString()
  aggregationFromDate: Date;

  @ApiProperty({ example: '2024-11-16' })
  @IsNotEmpty()
  @IsDateString()
  aggregationToDate: Date;

  @ApiProperty({ example: '2024-11-16' })
  @IsNotEmpty()
  @IsDateString()
  voucherDate: Date;

  @ApiPropertyOptional()
  @IsOptional()
  description: string;

  @ApiPropertyOptional({ type: [FileDto] })
  @IsArray()
  @IsOptional()
  @Type(() => FileDto)
  files: FileDto[];

  @ApiProperty({ type: [CreateClaimFeeTicketDetailRequestDto] })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CreateClaimFeeTicketDetailRequestDto)
  claimFeeTicketDetail: CreateClaimFeeTicketDetailRequestDto[];
}
