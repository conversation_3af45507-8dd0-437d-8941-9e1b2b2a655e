import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateClaimFeeTicketRequestDto } from './create-claim-fee-ticket.request.dto';

export class UpdateClaimFeeTicketRequestDto extends CreateClaimFeeTicketRequestDto {
  @ApiPropertyOptional({ example: 1 })
  @IsInt()
  @IsOptional()
  @Transform(({ value }) => +value)
  id: number;
}
