import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import { IdParamDto } from '../../../core/dto/request/id-param.request.dto';
import {
  APPROVE_CLAIM_FEE_TICKET_PERMISSION,
  CREATE_CLAIM_FEE_TICKET_PERMISSION,
  DELETE_CLAIM_FEE_TICKET_PERMISSION,
  DETAIL_CLAIM_FEE_TICKET_PERMISSION,
  LIST_CLAIM_FEE_TICKET_PERMISSION,
  REJECT_CLAIM_FEE_TICKET_PERMISSION,
  UPDATE_CLAIM_FEE_TICKET_PERMISSION,
} from '../../../utils/permissions/claim-fee-ticket.permission';
import { CreateClaimFeeTicketRequestDto } from '../request/create-claim-fee-ticket.request.dto';
import { GetDataForCreateClaimFeeTicketRequestDto } from '../request/get-data-for-create-claim-fee-ticket.request.dto';
import { GetListClaimFeeTicketRequestDto } from '../request/get-list-claim-fee-ticket.request.dto';
import { UpdateClaimFeeTicketRequestDto } from '../request/update-claim-fee-ticket.request.dto';
import { ClaimFeeTicketService } from '../service/claim-fee-ticket.service';

@Controller('claim-fee-tickets')
export class ClaimFeeTicketController {
  constructor(private readonly claimFeeTicketService: ClaimFeeTicketService) {}

  @PermissionCode(DETAIL_CLAIM_FEE_TICKET_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Claim-fee-tickets'],
    summary: 'Chi tiết Claim-fee-tickets',
    description: 'Chi tiết Claim-fee-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(@Param() param: IdParamDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.claimFeeTicketService.getDetail(request);
  }

  @PermissionCode(LIST_CLAIM_FEE_TICKET_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Claim-fee-tickets'],
    summary: 'Danh sách Claim-fee-tickets',
    description: 'Danh sách Claim-fee-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListClaimFeeTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.claimFeeTicketService.getList(request);
  }

  @PermissionCode(CREATE_CLAIM_FEE_TICKET_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Claim-fee-tickets'],
    summary: 'Tạo Claim-fee-tickets mới',
    description: 'Tạo Claim-fee-tickets mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateClaimFeeTicketRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.claimFeeTicketService.create(request);
  }

  @PermissionCode(UPDATE_CLAIM_FEE_TICKET_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Claim-fee-tickets'],
    summary: 'Cập nhật Claim-fee-tickets',
    description: 'Cập nhật Claim-fee-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateClaimFeeTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.claimFeeTicketService.update(request);
  }

  @PermissionCode(DELETE_CLAIM_FEE_TICKET_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Claim-fee-tickets'],
    summary: 'Xóa Claim-fee-tickets',
    description: 'Xóa Claim-fee-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(@Param() param: IdParamDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.claimFeeTicketService.delete(request);
  }

  @PermissionCode(CREATE_CLAIM_FEE_TICKET_PERMISSION.code)
  @Post('/get-data-for-create-claim-fee-ticket')
  @ApiOperation({
    tags: ['Claim-fee-tickets'],
    summary: 'Get data for create claim fee ticket',
    description: 'Get data for create claim fee ticket',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDataForCreateClaimFeeTicket(
    @Body() param: GetDataForCreateClaimFeeTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.claimFeeTicketService.getDataForCreateClaimFeeTicket(
      request,
    );
  }

  @PermissionCode(APPROVE_CLAIM_FEE_TICKET_PERMISSION.code)
  @Put('/:id/approve')
  @ApiOperation({
    tags: ['Claim-fee-tickets'],
    summary: 'Xác nhận claim-fee-ticket',
    description: 'Xác nhận claim-fee-ticket',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async approve(@Param() param: IdParamDto): Promise<any> {
    const { request, responseError } = param;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.claimFeeTicketService.approve(request);
  }

  @PermissionCode(REJECT_CLAIM_FEE_TICKET_PERMISSION.code)
  @Put('/:id/reject')
  @ApiOperation({
    tags: ['Claim-fee-tickets'],
    summary: 'Từ chối claim-fee-tickets',
    description: 'Từ chối claim-fee-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async reject(@Param() param: IdParamDto): Promise<any> {
    const { request, responseError } = param;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.claimFeeTicketService.reject(request);
  }
}
