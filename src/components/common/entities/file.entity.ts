import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ChecksheetEntity } from '../../checksheet/entities/checksheet.entity';
import { SpcByQcTypeEntity } from '../../spc-by-qc-type/entities/spc-by-qc-type.entity';

@Entity({ name: 'files' })
export class FileEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  checksheetId: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  spcByQcTypeId: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  fileId: number;

  @Column({
    type: 'nvarchar',
    nullable: false,
    length: 255,
  })
  fileName: string;

  @Column({
    type: 'nvarchar',
    nullable: false,
    length: 255,
  })
  fileUrl: string;

  @Column({
    type: 'int',
    nullable: true,
  })
  fileType: number;

  @Column({
    type: 'nvarchar',
    nullable: true,
    length: 255,
  })
  documentName: string;

  @Column({
    type: 'nvarchar',
    nullable: true,
    length: 255,
  })
  note: string;

  @ManyToOne(() => ChecksheetEntity, (s) => s.files, {
    orphanedRowAction: 'delete',
  })
  @JoinColumn({ name: 'checksheet_id', referencedColumnName: 'id' })
  checksheet: ChecksheetEntity;

  @ManyToOne(() => SpcByQcTypeEntity, (s) => s.files, {
    orphanedRowAction: 'delete',
  })
  @JoinColumn({ name: 'spc_by_qc_type_id', referencedColumnName: 'id' })
  spcByQcType: SpcByQcTypeEntity;
}
