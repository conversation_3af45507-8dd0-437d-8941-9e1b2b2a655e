import { Injectable, Logger } from '@nestjs/common';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';

import { GetDetailAuditVendorResultRequestDto } from './../request/get-detail-audit-vendor-result.request.dto';
import { GetListAuditVendorResultRequestDto } from './../request/get-list-audit-vendor-result.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { BaseService } from '../../../common/service/base.service';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';
import { QmsxReportService } from '../../another-service/services/qmsx-report-service';
import { UserService } from '../../another-service/services/user-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';

@Injectable()
export class AuditVendorResultService extends BaseService {
  private readonly logger = new Logger(AuditVendorResultService.name);

  constructor(
    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly qmsxReportService: QmsxReportService,

    private readonly baseService: BaseProcessService,
  ) {
    super(userService);
  }

  async getList(request: GetListAuditVendorResultRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } =
      await this.qmsxReportService.getListAuditVendorResult(request);

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async report(request: GetListAuditVendorResultRequestDto): Promise<any> {
    const { data, total } = await this.qmsxReportService.getReportAuditVendor(
      request,
    );

    const dataMap = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder({
      items: dataMap,
      meta: { total },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailAuditVendorResultRequestDto): Promise<any> {
    const auditVendorResult =
      await this.qmsxReportService.getDetailAuditVendorResult(request);
    if (isEmpty(auditVendorResult)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMapUser = await this.baseService.mapMasterInfoToResponse([
      auditVendorResult,
    ]);
    const generalInformation = await this.baseService.mapMasterInfoToResponse([
      auditVendorResult.generalInformation,
    ]);
    const evaluatedByGroup = await this.baseService.mapMasterInfoToResponse(
      auditVendorResult.evaluatedByGroup,
    );
    const details = await this.baseService.mapMasterInfoToResponse(
      auditVendorResult.details,
    );

    dataMapUser.forEach((s) => {
      s.generalInformation = generalInformation[0];
      s.evaluatedByGroup = evaluatedByGroup;
      s.details = details;
    });

    return new ResponseBuilder(dataMapUser[0] ? dataMapUser[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
