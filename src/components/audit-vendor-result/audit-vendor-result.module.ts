import { Modu<PERSON> } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { AuditVendorResultController } from './controller/audit-vendor-result.controller';
import { AuditVendorResultService } from './service/audit-vendor-result.service';

@Module({
  imports: [AnotherServiceModule, BaseProcessModule],
  providers: [AuditVendorResultService],
  exports: [AuditVendorResultService],
  controllers: [AuditVendorResultController],
})
export class AuditVendorResultModule {}
