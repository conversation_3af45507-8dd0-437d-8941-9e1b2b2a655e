import { Controller, Get, Param, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  DETAIL_AUDIT_VENDOR_RESULT_PERMISSION,
  LIST_AUDIT_VENDOR_RESULT_PERMISSION,
} from '../../../utils/permissions/audit-vendor-result.permission';
import { LIST_REPORT_DETAIL_AUDIT_RESULT_PERMISSION } from '../../../utils/permissions/report-detail-audit-result.permission';
import { GetDetailAuditVendorResultRequestDto } from '../request/get-detail-audit-vendor-result.request.dto';
import { GetListAuditVendorResultRequestDto } from '../request/get-list-audit-vendor-result.request.dto';
import { AuditVendorResultService } from '../service/audit-vendor-result.service';

@Controller('audit-vendor-results')
export class AuditVendorResultController {
  constructor(
    private readonly auditVendorResultService: AuditVendorResultService,
  ) {}

  @PermissionCode(DETAIL_AUDIT_VENDOR_RESULT_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Audit-vendor-results'],
    summary: 'Chi tiết Audit-vendor-results',
    description: 'Chi tiết Audit-vendor-results',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailAuditVendorResultRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.auditVendorResultService.getDetail(request);
  }

  @PermissionCode(LIST_REPORT_DETAIL_AUDIT_RESULT_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Audit-vendor-results'],
    summary: 'Danh sách Audit-vendor-results',
    description: 'Danh sách Audit-vendor-results',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListAuditVendorResultRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.auditVendorResultService.getList(request);
  }

  @PermissionCode(LIST_AUDIT_VENDOR_RESULT_PERMISSION.code)
  @Get('/report')
  @ApiOperation({
    tags: ['Audit-vendor-results'],
    summary: 'Báo cáo Audit-vendor-results',
    description: 'Báo cáo Audit-vendor-results',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public report(
    @Query() query: GetListAuditVendorResultRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.auditVendorResultService.report(request);
  }
}
