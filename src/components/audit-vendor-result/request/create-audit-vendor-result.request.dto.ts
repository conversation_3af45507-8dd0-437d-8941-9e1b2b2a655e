import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsDateString,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
  ValidateNested,
} from 'class-validator';
import { VENDOR_AUDIT_TYPE } from '../../vendor-audit-plan/vendor-audit-plan.constant';
import { CreateAuditVendorResultDetailRequestDto } from './create-audit-vendor-result-detail.request.dto';

export class CreateAuditVendorResultRequestDto extends BaseDto {
  @ApiPropertyOptional()
  @IsInt()
  @IsOptional()
  vendorAuditPlanId?: number;

  @ApiPropertyOptional({ maxLength: 255 })
  @IsString()
  @IsOptional()
  @Length(1, 255)
  vendorAuditPlanCode?: string;

  @ApiPropertyOptional({ maxLength: 255 })
  @IsString()
  @IsOptional()
  @Length(1, 255)
  vendorAuditTicketCode?: string;

  @ApiPropertyOptional()
  @IsInt()
  @IsOptional()
  vendorAuditTicketId?: number;

  @ApiProperty({ maxLength: 7 })
  @IsString()
  @IsNotEmpty()
  @Length(1, 7)
  auditPeriod: string;

  @ApiPropertyOptional()
  @IsInt()
  @IsOptional()
  vendorAuditPlanDetailId?: number;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  vendorId: number;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  processId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  goodsTypeId: number;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  checksheetId: number;

  @ApiPropertyOptional({ enum: VENDOR_AUDIT_TYPE })
  @IsOptional()
  @IsEnum(VENDOR_AUDIT_TYPE)
  auditType: VENDOR_AUDIT_TYPE;

  @ApiPropertyOptional()
  @IsInt()
  @IsOptional()
  auditorId?: number;

  @ApiPropertyOptional()
  @IsInt()
  @IsOptional()
  evaluator?: number;

  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  auditDate?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  score?: number;

  @ApiPropertyOptional()
  @IsOptional()
  targetPoint?: number;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  rank?: string;

  @ApiProperty({ type: [CreateAuditVendorResultDetailRequestDto] })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CreateAuditVendorResultDetailRequestDto)
  auditVendorResultDetail: CreateAuditVendorResultDetailRequestDto[];
}
