import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateAuditVendorResultDetailRequestDto {
  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  vendorAuditTicketId: number;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  checksheetDetailId: number;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  inspectionGroupId: number;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  inspectionTypeId: number;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  inspectionId: number;

  @ApiProperty()
  @IsNotEmpty()
  scoreScale: number;

  @ApiPropertyOptional()
  @IsInt()
  @IsOptional()
  errorId?: number;

  @ApiPropertyOptional()
  @IsOptional()
  assessmentScore?: number;

  @ApiPropertyOptional()
  @IsInt()
  @IsOptional()
  result?: number;
}
