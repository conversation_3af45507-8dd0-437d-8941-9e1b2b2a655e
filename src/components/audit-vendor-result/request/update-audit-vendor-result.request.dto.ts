import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt } from 'class-validator';
import { CreateAuditVendorResultRequestDto } from './create-audit-vendor-result.request.dto';

export class UpdateAuditVendorResultRequestDto extends CreateAuditVendorResultRequestDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;
}
