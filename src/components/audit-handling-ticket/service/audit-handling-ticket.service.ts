import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';
import { DataSource } from 'typeorm';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { BaseService } from '../../../common/service/base.service';
import { IdParamDto } from '../../../core/dto/request/id-param.request.dto';
import { QmsxTicketService } from '../../another-service/services/qmsx-ticket-service';
import { UserService } from '../../another-service/services/user-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { AUDIT_HANDLING_TICKET_STATUS } from '../audit-handling-ticket.constant';
import { CreateAuditHandlingTicketRequestDto } from '../request/create-audit-handling-ticket.request.dto';
import { GetListAuditHandlingTicketRequestDto } from '../request/get-list-audit-handling-ticket.request.dto';
import { UpdateAuditHandlingTicketRequestDto } from '../request/update-audit-handling-ticket.request.dto';

@Injectable()
export class AuditHandlingTicketService extends BaseService {
  private readonly logger = new Logger(AuditHandlingTicketService.name);

  constructor(
    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly qmsxTicketService: QmsxTicketService,

    private readonly baseService: BaseProcessService,
  ) {
    super(userService);
  }

  async create(request: CreateAuditHandlingTicketRequestDto): Promise<any> {
    const { result, messageError } = await this.validateCreateUpdate(request);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }
    request.createdBy = request.userId;
    request.updatedBy = request.userId;
    const entity = await this.qmsxTicketService.createAuditHandlingTicket(
      request,
    );
    if (isEmpty(entity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    return new ResponseBuilder(entity)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async validateCreateUpdate(
    request:
      | CreateAuditHandlingTicketRequestDto
      | UpdateAuditHandlingTicketRequestDto,
  ) {
    return await this.baseService.validateMaster({
      processIds: [+request.processId],
      goodsTypeIds: [+request.goodsTypeId],
      vendorIds: [+request.vendorId],
    });
  }

  async update(request: UpdateAuditHandlingTicketRequestDto): Promise<any> {
    const entity =
      await this.qmsxTicketService.getDetailAuditHandlingTicketById({
        id: request.id,
      });
    if (isEmpty(entity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const updateRequest = await this.handleRequestUpdate(request);
    const responseEntity =
      await this.qmsxTicketService.updateAuditHandlingTicket(updateRequest);
    if (isEmpty(responseEntity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }
    return new ResponseBuilder(responseEntity)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: IdParamDto): Promise<any> {
    const entity =
      await this.qmsxTicketService.getDetailAuditHandlingTicketById(request);
    if (isEmpty(entity)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const vendorAuditTicket =
      await this.qmsxTicketService.getDetailVendorAuditTicketById({
        id: entity.vendorAuditTicketId,
      });
    if (!isEmpty(vendorAuditTicket)) {
      entity.vendorAuditPlanCode = vendorAuditTicket.vendorAuditPlanCode;
    }
    const dataMapUser = await this.baseService.mapMasterInfoToResponse([
      entity,
    ]);
    const details = await this.baseService.mapMasterInfoToResponse(
      entity.auditHandlingTicketDetail,
    );
    dataMapUser.forEach((s) => {
      s.auditHandlingTicketDetail = details;
    });
    return new ResponseBuilder(dataMapUser[0] ? dataMapUser[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async handleRequestUpdate(request: UpdateAuditHandlingTicketRequestDto) {
    const updateRequest = plainToInstance(
      UpdateAuditHandlingTicketRequestDto,
      request,
    );
    updateRequest.updatedBy = request.userId;
    updateRequest.isSubmittedForApproval = request.isSubmittedForApproval;
    updateRequest.id = request.id;
    return updateRequest;
  }

  async getList(request: GetListAuditHandlingTicketRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } =
      await this.qmsxTicketService.getListAuditHandlingTicket(request);

    const listVendorAuditTicketIds = data
      .filter((item) => item.vendorAuditTicketId != null)
      .map((item) => item.vendorAuditTicketId);

    const listVendorAuditTicket =
      await this.qmsxTicketService.getListVendorAuditTicketByIds({
        ids: listVendorAuditTicketIds,
      });

    const mapVendorAuditTicket = new Map<number, any>();
    if (listVendorAuditTicket.length >= 0) {
      listVendorAuditTicket.forEach((ticket) => {
        mapVendorAuditTicket.set(ticket.id, ticket);
      });
    }

    data.forEach((item) => {
      if (
        item.vendorAuditTicketId != null &&
        mapVendorAuditTicket.has(item.vendorAuditTicketId)
      ) {
        item.vendorAuditPlanId = mapVendorAuditTicket.get(
          item.vendorAuditTicketId,
        )?.vendorAuditPlanId;
        item.vendorAuditPlanCode = mapVendorAuditTicket.get(
          item.vendorAuditTicketId,
        )?.vendorAuditPlanCode;
      }
    });

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async approve(request: IdParamDto): Promise<any> {
    const { result, messageError } =
      await this.qmsxTicketService.approveAuditHandlingTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async submit(request: IdParamDto): Promise<any> {
    const auditHandlingTicket =
      await this.qmsxTicketService.getDetailAuditHandlingTicketById({
        id: request.id,
      });
    if (
      isEmpty(auditHandlingTicket) ||
      (!isEmpty(auditHandlingTicket) &&
        auditHandlingTicket.status != AUDIT_HANDLING_TICKET_STATUS.IN_PROGRESS)
    ) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.CAN_NOT_SUBMIT'))
        .build();
    }
    const { result, messageError } =
      await this.qmsxTicketService.approveAuditHandlingTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async reject(request: IdParamDto): Promise<any> {
    const { result, messageError } =
      await this.qmsxTicketService.rejectAuditHandlingTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async ignore(request: IdParamDto): Promise<any> {
    const ticket =
      await this.qmsxTicketService.getDetailAuditHandlingTicketById({
        id: request.id,
      });
    if (
      isEmpty(ticket) ||
      (!isEmpty(ticket) &&
        ticket.status != AUDIT_HANDLING_TICKET_STATUS.PENDING)
    ) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.CAN_NOT_IGNORE'))
        .build();
    }
    const { result, messageError } =
      await this.qmsxTicketService.rejectAuditHandlingTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
