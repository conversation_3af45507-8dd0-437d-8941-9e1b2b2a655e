import { Modu<PERSON> } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { AuditHandlingTicketController } from './controller/audit-handling-ticket.controller';
import { AuditHandlingTicketService } from './service/audit-handling-ticket.service';

@Module({
  imports: [AnotherServiceModule, BaseProcessModule],
  providers: [AuditHandlingTicketService],
  exports: [AuditHandlingTicketService],
  controllers: [AuditHandlingTicketController],
})
export class AuditHandlingTicketModule {}
