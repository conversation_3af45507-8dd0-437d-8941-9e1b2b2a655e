import { Body, Controller, Get, Param, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import { IdParamDto } from '../../../core/dto/request/id-param.request.dto';
import {
  APPROVE_AUDIT_HANDLING_TICKET_PERMISSION,
  CREATE_AUDIT_HANDLING_TICKET_PERMISSION,
  DETAIL_AUDIT_HANDLING_TICKET_PERMISSION,
  IGNORE_AUDIT_HANDLING_TICKET_PERMISSION,
  LIST_AUDIT_HANDLING_TICKET_PERMISSION,
  REJECT_AUDIT_HANDLING_TICKET_PERMISSION,
  UPDATE_AUDIT_HANDLING_TICKET_PERMISSION,
} from '../../../utils/permissions/audit-handling-ticket.permission';
import { CreateAuditHandlingTicketRequestDto } from '../request/create-audit-handling-ticket.request.dto';
import { GetListAuditHandlingTicketRequestDto } from '../request/get-list-audit-handling-ticket.request.dto';
import { UpdateAuditHandlingTicketRequestDto } from '../request/update-audit-handling-ticket.request.dto';
import { AuditHandlingTicketService } from '../service/audit-handling-ticket.service';

@Controller('audit-handling-tickets')
export class AuditHandlingTicketController {
  constructor(
    private readonly auditHandlingTicketService: AuditHandlingTicketService,
  ) {}

  @PermissionCode(DETAIL_AUDIT_HANDLING_TICKET_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Audit-handling-tickets'],
    summary: 'Chi tiết Audit-handling-tickets',
    description: 'Chi tiết Audit-handling-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(@Param() param: IdParamDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.auditHandlingTicketService.getDetail(request);
  }

  @PermissionCode(LIST_AUDIT_HANDLING_TICKET_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Audit-handling-tickets'],
    summary: 'Danh sách Audit-handling-tickets',
    description: 'Danh sách Audit-handling-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListAuditHandlingTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.auditHandlingTicketService.getList(request);
  }

  @PermissionCode(CREATE_AUDIT_HANDLING_TICKET_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Audit-handling-tickets'],
    summary: 'Tạo Audit-handling-tickets mới',
    description: 'Tạo Audit-handling-tickets mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateAuditHandlingTicketRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.auditHandlingTicketService.create(request);
  }

  @PermissionCode(UPDATE_AUDIT_HANDLING_TICKET_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Audit-handling-tickets'],
    summary: 'Cập nhật Audit-handling-tickets',
    description: 'Cập nhật Audit-handling-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateAuditHandlingTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.auditHandlingTicketService.update(request);
  }

  @PermissionCode(APPROVE_AUDIT_HANDLING_TICKET_PERMISSION.code)
  @Put('/:id/approve')
  @ApiOperation({
    tags: ['Audit-handling-tickets'],
    summary: 'Approve Audit-handling-tickets Status',
    description: 'Approve Audit-handling-tickets Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async approve(@Param() body: IdParamDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.auditHandlingTicketService.approve(request);
  }

  @PermissionCode(UPDATE_AUDIT_HANDLING_TICKET_PERMISSION.code)
  @Put('/:id/submit')
  @ApiOperation({
    tags: ['Audit-handling-tickets'],
    summary: 'Submit Audit-handling-tickets Status',
    description: 'Submit Audit-handling-tickets Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async submit(@Param() body: IdParamDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.auditHandlingTicketService.submit(request);
  }

  @PermissionCode(REJECT_AUDIT_HANDLING_TICKET_PERMISSION.code)
  @Put('/:id/reject')
  @ApiOperation({
    tags: ['Audit-handling-tickets'],
    summary: 'Reject Audit-handling-tickets Status',
    description: 'Reject Audit-handling-tickets Status',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async reject(@Param() body: IdParamDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.auditHandlingTicketService.reject(request);
  }

  @PermissionCode(IGNORE_AUDIT_HANDLING_TICKET_PERMISSION.code)
  @Put('/:id/ignore')
  @ApiOperation({
    tags: ['Audit-handling-tickets'],
    summary: 'Reject Audit-handling-tickets Status',
    description: 'Reject Audit-handling-tickets Status',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async ignore(@Param() body: IdParamDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.auditHandlingTicketService.ignore(request);
  }
}
