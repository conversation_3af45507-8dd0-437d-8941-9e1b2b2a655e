import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt } from 'class-validator';
import { CreateAuditHandlingTicketRequestDto } from './create-audit-handling-ticket.request.dto';

export class UpdateAuditHandlingTicketRequestDto extends CreateAuditHandlingTicketRequestDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;
}
