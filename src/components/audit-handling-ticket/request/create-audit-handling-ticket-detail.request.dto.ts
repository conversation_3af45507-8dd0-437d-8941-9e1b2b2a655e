import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';
import { FileRequestDto } from '../../common/request/file.request.dto';
import { AUDIT_HANDLING_TICKET_DETAIL_RESULT } from '../audit-handling-ticket.constant';

export class CreateAuditHandlingTicketDetailRequestDto extends BaseDto {
  @ApiPropertyOptional()
  @IsOptional()
  id: number;

  @ApiProperty()
  @IsNotEmpty()
  checksheetDetailId: number;

  @ApiPropertyOptional()
  @IsOptional()
  errorId: number;

  @ApiPropertyOptional()
  @IsOptional()
  resolutionContent: string;

  @ApiPropertyOptional()
  @IsOptional()
  result: AUDIT_HANDLING_TICKET_DETAIL_RESULT;

  @ApiPropertyOptional()
  @IsOptional()
  comment: string;

  @ApiPropertyOptional({ type: [FileRequestDto] })
  @IsArray()
  @IsOptional()
  @Type(() => FileRequestDto)
  files: FileRequestDto[];
}
