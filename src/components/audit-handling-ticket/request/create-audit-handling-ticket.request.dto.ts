import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { FileRequestDto } from '../../common/request/file.request.dto';
import { VENDOR_AUDIT_TYPE } from '../../vendor-audit-ticket/vendor-audit-ticket.constant';
import { CreateAuditHandlingTicketDetailRequestDto } from './create-audit-handling-ticket-detail.request.dto';

export class CreateAuditHandlingTicketRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  vendorAuditTicketId: number;

  @ApiProperty()
  @IsNotEmpty()
  vendorAuditTicketCode: string;

  @ApiPropertyOptional()
  @IsEnum(VENDOR_AUDIT_TYPE)
  @IsOptional()
  auditType: VENDOR_AUDIT_TYPE;

  @ApiProperty()
  @IsNotEmpty()
  auditPeriod: string;

  @ApiProperty()
  @IsNotEmpty()
  processId: number;

  @ApiProperty()
  @IsNotEmpty()
  vendorId: number;

  @ApiPropertyOptional()
  @IsOptional()
  goodsTypeId: number;

  @ApiPropertyOptional()
  @IsOptional()
  auditDate: Date;

  @ApiPropertyOptional()
  @IsOptional()
  handlingRequestDate: Date;

  @ApiPropertyOptional()
  @IsOptional()
  responseDate: Date;

  @ApiPropertyOptional()
  @IsOptional()
  expiredDate: Date;

  @ApiPropertyOptional()
  @IsOptional()
  responseDeadline: number;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  description?: string;

  @ApiPropertyOptional()
  @IsOptional()
  createdBy: number;

  @ApiPropertyOptional()
  @IsOptional()
  updatedBy: number;

  @ApiPropertyOptional({ type: [FileRequestDto] })
  @IsArray()
  @IsOptional()
  @Type(() => FileRequestDto)
  files: FileRequestDto[];

  @ApiProperty({ type: [CreateAuditHandlingTicketDetailRequestDto] })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CreateAuditHandlingTicketDetailRequestDto)
  auditHandlingTicketDetail: CreateAuditHandlingTicketDetailRequestDto[];

  @ApiProperty({ default: false })
  @IsNotEmpty()
  isSubmittedForApproval: boolean = false;
}
