import { Module } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { ChemicalImportTicketController } from './controller/chemical-import-ticket.controller';
import { ChemicalImportTicketService } from './service/chemical-import-ticket.service';

@Module({
  imports: [AnotherServiceModule, BaseProcessModule],
  providers: [ChemicalImportTicketService],
  exports: [ChemicalImportTicketService],
  controllers: [ChemicalImportTicketController],
})
export class ChemicalImportTicketModule {}
