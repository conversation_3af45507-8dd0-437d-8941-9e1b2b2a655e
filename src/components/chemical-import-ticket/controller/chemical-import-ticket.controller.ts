import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  APPROVE_CHEMICAL_IMPORT_TICKET_PERMISSION,
  CANCEL_CHEMICAL_IMPORT_TICKET_PERMISSION,
  CREATE_CHEMICAL_IMPORT_TICKET_PERMISSION,
  DELETE_CHEMICAL_IMPORT_TICKET_PERMISSION,
  DETAIL_CHEMICAL_IMPORT_TICKET_PERMISSION,
  LIST_CHEMICAL_IMPORT_TICKET_PERMISSION,
  UPDATE_CHEMICAL_IMPORT_TICKET_PERMISSION,
} from '../../../utils/permissions/chemical-import-ticket.permission';
import { CreateChemicalImportTicketFormDto } from '../request/create-chemical-import-ticket-form.request.dto';
import { DeleteChemicalImportTicketRequestDto } from '../request/delete-chemical-import-ticket.request.dto';
import { GetDetailChemicalImportTicketRequestDto } from '../request/get-detail-chemical-import-ticket.request.dto';
import { GetListChemicalImportTicketRequestDto } from '../request/get-list-chemical-import-ticket.request.dto';
import { UpdateChemicalImportTicketFormRequestDto } from '../request/update-chemical-import-ticket-form.request.dto';
import { ChemicalImportTicketService } from '../service/chemical-import-ticket.service';

@Controller('chemical-import-tickets')
export class ChemicalImportTicketController {
  constructor(
    private readonly chemicalImportTicketService: ChemicalImportTicketService,
  ) {}

  @PermissionCode(DETAIL_CHEMICAL_IMPORT_TICKET_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['chemical-import-tickets'],
    summary: 'Chi tiết chemical-import-tickets',
    description: 'Chi tiết chemical-import-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailChemicalImportTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalImportTicketService.getDetail(request);
  }

  @PermissionCode(LIST_CHEMICAL_IMPORT_TICKET_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['chemical-import-tickets'],
    summary: 'Danh sách chemical-import-tickets',
    description: 'Danh sách chemical-import-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListChemicalImportTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.chemicalImportTicketService.getList(request);
  }

  @PermissionCode(UPDATE_CHEMICAL_IMPORT_TICKET_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['chemical-import-tickets'],
    summary: 'Cập nhật chemical-import-tickets',
    description: 'Cập nhật chemical-import-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateChemicalImportTicketFormRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalImportTicketService.update(request);
  }

  @PermissionCode(CREATE_CHEMICAL_IMPORT_TICKET_PERMISSION.code)
  @Post('create-ticket')
  @ApiOperation({
    tags: ['chemical-import-tickets'],
    summary: 'Chi tiết chemical-import-tickets',
    description: 'Chi tiết chemical-import-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async create(
    @Body() param: CreateChemicalImportTicketFormDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalImportTicketService.create(request);
  }

  @PermissionCode(APPROVE_CHEMICAL_IMPORT_TICKET_PERMISSION.code)
  @Put('/approved')
  @ApiOperation({
    tags: ['chemical-import-tickets'],
    summary: 'Approved chemical-import-tickets',
    description: 'Approved chemical-import-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async approve(
    @Body() body: GetDetailChemicalImportTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalImportTicketService.approve(request);
  }

  @PermissionCode(CANCEL_CHEMICAL_IMPORT_TICKET_PERMISSION.code)
  @Put('/canceled')
  @ApiOperation({
    tags: ['chemical-import-tickets'],
    summary: 'Canceled chemical-import-tickets',
    description: 'Canceled chemical-import-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async canceled(
    @Body() body: GetDetailChemicalImportTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.chemicalImportTicketService.cancel(request);
  }

  @PermissionCode(DELETE_CHEMICAL_IMPORT_TICKET_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['chemical-import-tickets'],
    summary: 'Xóa chemical-import-tickets',
    description: 'Xóa chemical-import-tickets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(
    @Param() param: DeleteChemicalImportTicketRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.chemicalImportTicketService.delete(request);
  }
}
