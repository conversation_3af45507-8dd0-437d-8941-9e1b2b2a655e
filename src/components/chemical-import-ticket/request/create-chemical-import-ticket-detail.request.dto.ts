import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';

export class CreateChemicalImportTicketDetailRequestDto {
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  chemicalImportTicketId: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  chemicalId: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  importQuantity: number;

  @ApiProperty()
  @IsOptional()
  note: string;
}
