import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsDateString,
  IsInt,
  IsNumber,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { FileRequestDto } from '../../common/request/file.request.dto';
import { CreateChemicalImportTicketDetailRequestDto } from './create-chemical-import-ticket-detail.request.dto';

export class CreateChemicalImportTicketRequestDto extends BaseDto {
  @ApiProperty()
  @IsOptional()
  @IsDateString()
  importedDate: Date;

  @IsNumber()
  @ApiProperty()
  @IsOptional()
  importedBy: number;

  @ApiProperty()
  @IsOptional()
  note: string;

  @IsNumber()
  @ApiProperty()
  @IsOptional()
  status: number;

  @ApiProperty({ type: [FileRequestDto] })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FileRequestDto)
  files: FileRequestDto[];

  @IsOptional()
  @IsInt()
  createdBy: number;

  @ApiProperty({ type: [CreateChemicalImportTicketDetailRequestDto] })
  @IsArray()
  @Type(() => CreateChemicalImportTicketDetailRequestDto)
  @ValidateNested({ each: true })
  ticketDetails: CreateChemicalImportTicketDetailRequestDto[];
}
