import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateChemicalImportTicketFormDto } from './create-chemical-import-ticket-form.request.dto';
export class UpdateChemicalImportTicketFormRequestDto extends CreateChemicalImportTicketFormDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;

  @Transform(({ value }) => Number(value))
  @IsInt()
  @IsOptional()
  updatedBy?: number;
}
