import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateChemicalImportTicketRequestDto } from './create-chemical-import-ticket.request.dto';

export class UpdateChemicalImportTicketDto extends CreateChemicalImportTicketRequestDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  updatedBy?: number;
}
