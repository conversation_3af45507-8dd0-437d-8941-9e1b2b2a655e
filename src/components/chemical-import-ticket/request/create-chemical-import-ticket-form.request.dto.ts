import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';
import { CreateChemicalImportTicketRequestDto } from './create-chemical-import-ticket.request.dto';

export class CreateChemicalImportTicketFormDto extends BaseDto {
  @ApiProperty({})
  @ValidateNested({ each: true })
  @Type(() => CreateChemicalImportTicketRequestDto)
  data: CreateChemicalImportTicketRequestDto;
}
