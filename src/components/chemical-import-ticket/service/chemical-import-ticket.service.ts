import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';

import { GetDetailChemicalImportTicketRequestDto } from '../request/get-detail-chemical-import-ticket.request.dto';
import { GetListChemicalImportTicketRequestDto } from '../request/get-list-chemical-import-ticket.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';

import { PaginationResponse } from '../../../utils/dto/response/pagination.response';
import { QmsxTicketService } from '../../another-service/services/qmsx-ticket-service';
import { UserService } from '../../another-service/services/user-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { CreateChemicalImportTicketFormDto } from '../request/create-chemical-import-ticket-form.request.dto';
import { CreateChemicalImportTicketRequestDto } from '../request/create-chemical-import-ticket.request.dto';
import { DeleteChemicalImportTicketRequestDto } from '../request/delete-chemical-import-ticket.request.dto';
import { UpdateChemicalImportTicketFormRequestDto } from '../request/update-chemical-import-ticket-form.request.dto';
import { UpdateChemicalImportTicketDto } from '../request/update-chemical-import-ticket.dto';

@Injectable()
export class ChemicalImportTicketService {
  private readonly logger = new Logger(ChemicalImportTicketService.name);

  constructor(
    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly qmsxTicketService: QmsxTicketService,

    private readonly baseService: BaseProcessService,
  ) {}

  async getList(request: GetListChemicalImportTicketRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } =
      await this.qmsxTicketService.getListChemicalImportTicket(request);

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(
    request: GetDetailChemicalImportTicketRequestDto,
  ): Promise<any> {
    const chemicalImportTicket =
      await this.qmsxTicketService.getDetailChemicalImportTicketById(request);

    if (isEmpty(chemicalImportTicket)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const dataMapUser = await this.baseService.mapMasterInfoToResponse([
      chemicalImportTicket,
    ]);
    const details = await this.baseService.mapMasterInfoToResponse(
      chemicalImportTicket.chemicalImportTicketDetails,
    );
    dataMapUser.forEach((s) => {
      s.chemicalImportTicketDetails = details;
    });
    return new ResponseBuilder(dataMapUser[0] ? dataMapUser[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(
    request: UpdateChemicalImportTicketFormRequestDto,
  ): Promise<any> {
    const { id, data } = request;

    const chemicalImportTicket =
      await this.qmsxTicketService.getDetailChemicalImportTicketById({
        id: id,
      });

    if (isEmpty(chemicalImportTicket)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const { result, messageError } = await this.validateImportTicket(data);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }

    const chemicalImportTicketUpdate = plainToInstance(
      UpdateChemicalImportTicketDto,
      data,
    );
    chemicalImportTicketUpdate.id = id;
    request.updatedBy = request.userId;
    request.data = chemicalImportTicketUpdate;

    const response = await this.qmsxTicketService.updateChemicalImportTicket(
      request,
    );
    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }
    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async validateImportTicket(data: any) {
    if (
      this.baseService.checkDuplicateByKey(data.ticketDetails, ['chemicalId'])
    ) {
      return {
        result: false,
        messageError: 'error.CHEMICAL_IMPORT_TICKET_DETAIL_DUPLICATE_KEY',
      };
    }

    const { chemicalIds } = data.ticketDetails.reduce(
      (result, item) => {
        if (item.chemicalId !== null && item.chemicalId !== undefined) {
          result.chemicalIds.add(item.chemicalId);
        }
        return result;
      },
      {
        chemicalIds: new Set<number>(),
      },
    );

    return await this.baseService.validateMaster({
      chemicalIds: Array.from(chemicalIds),
    });
  }

  async create(request: CreateChemicalImportTicketFormDto): Promise<any> {
    const { data } = request;

    const { result, messageError } = await this.validateImportTicket(data);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }
    const chemicalImportTicket = plainToInstance(
      CreateChemicalImportTicketRequestDto,
      data,
    );
    chemicalImportTicket.createdBy = request.userId;
    const response = await this.qmsxTicketService.createChemicalImportTicket(
      chemicalImportTicket,
    );

    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async approve(
    request: GetDetailChemicalImportTicketRequestDto,
  ): Promise<any> {
    const { result, messageError } =
      await this.qmsxTicketService.approveChemicalImportTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async cancel(request: GetDetailChemicalImportTicketRequestDto): Promise<any> {
    const { result, messageError } =
      await this.qmsxTicketService.cancelChemicalImportTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteChemicalImportTicketRequestDto): Promise<any> {
    const { result, messageError } =
      await this.qmsxTicketService.deleteChemicalImportTicket(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
