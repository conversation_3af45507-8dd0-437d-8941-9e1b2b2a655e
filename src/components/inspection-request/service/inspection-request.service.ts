import { Inject, Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { DeleteInspectionRequestRequestDto } from '../request/delete-inspection-request.request.dto';
import { GetDetailInspectionRequestRequestDto } from '../request/get-detail-inspection-request.request.dto';
import { GetListInspectionRequestRequestDto } from '../request/get-list-inspection-request.request.dto';
import { UpdateInspectionRequestFormDto } from '../request/update-inspection-request-form.request.dto';
import { UpdateStatusInspectionRequestRequestDto } from '../request/update-status-inspection-request.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { FileResource } from '../../../core/components/file/constant/file-upload.constant';
import { FileService } from '../../../core/components/file/file.service';
import {
  FileDelete,
  FileUpload,
} from '../../../core/dto/request/file.request.dto';
import { QmsxRequestService } from '../../another-service/services/qmsx-request-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { FileRequestDto } from '../../common/request/file.request.dto';
import { CreateInspectionRequestFormDto } from '../request/create-inspection-request-form.request.dto';
import { CreateInspectionRequestsDto } from '../request/create-inspection-request.request.dto';
import { UpdateInspectionRequestDto } from '../request/update-inspection-request.request.dto';

@Injectable()
export class InspectionRequestService {
  private readonly logger = new Logger(InspectionRequestService.name);

  constructor(
    private readonly i18n: I18nService,

    @Inject('FileServiceInterface')
    private readonly fileService: FileService,

    private readonly qmsxRequestService: QmsxRequestService,

    private readonly baseService: BaseProcessService,
  ) {}

  hasDuplicates = (items: any[]): boolean => {
    const seen = new Set<string>();
    return items.some((item) => {
      const key = `${item.lotNo}-${item.itemId}-${item.processId}`;
      return seen.has(key) ? true : (seen.add(key), false);
    });
  };

  async validateInspectionRequest(data: any) {
    if (this.hasDuplicates(data.inspectionRequestDetails)) {
      return {
        result: false,
        messageError: 'error.INSPECTION_REQUEST_DETAIL_DUPLICATE_KEY',
      };
    }

    const itemIds = data.inspectionRequestDetails.map(
      (item) => item?.itemId ?? null,
    );

    return await this.baseService.validateMaster({
      itemIds: Array.from(itemIds),
    });
  }

  async handleFile(uploadFiles: FileUpload[], deleteFiles: FileDelete[]) {
    const savedFileResponse = await this.fileService.handleSaveFiles({
      resource: FileResource.QMSX_REQUEST,
      deleteFiles: deleteFiles,
      uploadFiles: uploadFiles,
    });

    if (savedFileResponse.statusCode !== ResponseCodeEnum.SUCCESS) {
      return savedFileResponse;
    }

    const filesResponse = await this.fileService.getFilesByIds(
      savedFileResponse.data,
    );

    return filesResponse?.map((res) => {
      const fileRequestDto = new FileRequestDto();
      fileRequestDto.fileId = res.id;
      fileRequestDto.fileName = res.fileNameRaw;
      fileRequestDto.fileUrl = res.fileUrl;
      return fileRequestDto;
    });
  }

  async create(request: CreateInspectionRequestFormDto): Promise<any> {
    const { data, uploadFiles, deleteFiles } = request;

    const { result, messageError } = await this.validateInspectionRequest(data);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }
    const files = await this.handleFile(uploadFiles, deleteFiles);
    const inspectionRequest = plainToInstance(
      CreateInspectionRequestsDto,
      data,
    );
    inspectionRequest.files = files;
    inspectionRequest.createdBy = request.userId;
    const response = await this.qmsxRequestService.createInspectionRequest(
      inspectionRequest,
    );

    if (isEmpty(response)) {
      await this.fileService.handleSaveFiles({
        resource: FileResource.QMSX_REQUEST,
        deleteFiles: inspectionRequest.files,
        uploadFiles: [],
      });

      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListInspectionRequestRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } =
      await this.qmsxRequestService.getListQmsxInspectionRequest(request);

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailInspectionRequestRequestDto): Promise<any> {
    const inspectionRequest =
      await this.qmsxRequestService.getDetailQmsxInspectionRequestById(request);

    if (isEmpty(inspectionRequest)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMapUser = await this.baseService.mapMasterInfoToResponse([
      inspectionRequest,
    ]);
    const details = await this.baseService.mapMasterInfoToResponse(
      inspectionRequest.inspectionRequestDetails,
    );
    dataMapUser.forEach((s) => {
      s.inspectionRequestDetails = details;
    });
    return new ResponseBuilder(dataMapUser[0] ? dataMapUser[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateInspectionRequestFormDto): Promise<any> {
    const { id, data, uploadFiles, deleteFiles } = request;

    const inspectionRequest =
      await this.qmsxRequestService.getDetailQmsxInspectionRequestById({
        id: id,
      });

    if (isEmpty(inspectionRequest)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const { result, messageError } = await this.validateInspectionRequest(data);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }
    const files = await this.handleFile(uploadFiles, deleteFiles);
    const inspectionRequestUpdate = plainToInstance(
      UpdateInspectionRequestDto,
      data,
    );
    inspectionRequestUpdate.id = id;
    inspectionRequestUpdate.files = [
      ...(inspectionRequestUpdate.files || []),
      ...(files || []),
    ];
    inspectionRequestUpdate.updatedBy = request.userId;
    const response = await this.qmsxRequestService.updateInspectionRequest(
      inspectionRequestUpdate,
    );
    if (isEmpty(response)) {
      await this.fileService.handleSaveFiles({
        resource: FileResource.QMSX_REQUEST,
        deleteFiles: inspectionRequest.files,
        uploadFiles: [],
      });
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }
    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteInspectionRequestRequestDto): Promise<any> {
    const inspectionRequest =
      await this.qmsxRequestService.getDetailQmsxInspectionRequestById(request);

    if (isEmpty(inspectionRequest)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const { result, messageError } =
      await this.qmsxRequestService.deleteInspection(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    await this.fileService.handleSaveFiles({
      resource: FileResource.QMSX_REQUEST,
      deleteFiles: inspectionRequest.files,
      uploadFiles: [],
    });
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async updateStatus(
    request: UpdateStatusInspectionRequestRequestDto,
  ): Promise<any> {
    const { result, messageError } =
      await this.qmsxRequestService.updateStatusInspection(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async approve(
    request: UpdateStatusInspectionRequestRequestDto,
  ): Promise<any> {
    const { result, messageError } =
      await this.qmsxRequestService.approveInspection(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async reject(request: UpdateStatusInspectionRequestRequestDto): Promise<any> {
    const { result, messageError } =
      await this.qmsxRequestService.rejectInspection(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
