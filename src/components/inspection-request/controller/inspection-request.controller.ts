import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiConsumes, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { CreateInspectionRequestFormDto } from '../request/create-inspection-request-form.request.dto';
import { DeleteInspectionRequestRequestDto } from '../request/delete-inspection-request.request.dto';
import { GetDetailInspectionRequestRequestDto } from '../request/get-detail-inspection-request.request.dto';
import { GetListInspectionRequestRequestDto } from '../request/get-list-inspection-request.request.dto';
import { UpdateInspectionRequestFormDto } from '../request/update-inspection-request-form.request.dto';
import { UpdateStatusInspectionRequestRequestDto } from '../request/update-status-inspection-request.request.dto';
import { InspectionRequestService } from '../service/inspection-request.service';

@Controller('inspection-requests')
export class InspectionRequestController {
  constructor(
    private readonly inspectionRequestService: InspectionRequestService,
  ) {}

  @Get('/:id')
  @ApiOperation({
    tags: ['Inspection-requests'],
    summary: 'Chi tiết Inspection-requests',
    description: 'Chi tiết Inspection-requests',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailInspectionRequestRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionRequestService.getDetail(request);
  }

  @Get('/list')
  @ApiOperation({
    tags: ['Inspection-requests'],
    summary: 'Danh sách Inspection-requests',
    description: 'Danh sách Inspection-requests',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListInspectionRequestRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionRequestService.getList(request);
  }

  @Post('/create')
  @ApiOperation({
    tags: ['Inspection-requests'],
    summary: 'Tạo Inspection-requests mới',
    description: 'Tạo Inspection-requests mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  @ApiConsumes('multipart/form-data')
  public create(@Body() payload: CreateInspectionRequestFormDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionRequestService.create(request);
  }

  @Put()
  @ApiOperation({
    tags: ['Inspection-requests'],
    summary: 'Cập nhật Inspection-requests',
    description: 'Cập nhật Inspection-requests',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  @ApiConsumes('multipart/form-data')
  public async update(
    @Body() body: UpdateInspectionRequestFormDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionRequestService.update(request);
  }

  @Put('/updateStatus')
  @ApiOperation({
    tags: ['Inspection-requests'],
    summary: 'Cập nhật Inspection-requests Status',
    description: 'Cập nhật Inspection-requests Status',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async updateStatus(
    @Body() body: UpdateStatusInspectionRequestRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionRequestService.updateStatus(request);
  }

  @Put('/approved')
  @ApiOperation({
    tags: ['Inspection-requests'],
    summary: 'Approved Inspection-requests',
    description: 'Approved Inspection-requests',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async approve(
    @Body() body: UpdateStatusInspectionRequestRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionRequestService.approve(request);
  }

  @Put('/reject')
  @ApiOperation({
    tags: ['Inspection-requests'],
    summary: 'Rejected Inspection-requests',
    description: 'Rejected Inspection-requests',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async reject(
    @Body() body: UpdateStatusInspectionRequestRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionRequestService.reject(request);
  }

  @Delete('/:id')
  @ApiOperation({
    tags: ['Inspection-requests'],
    summary: 'Xóa Inspection-requests',
    description: 'Xóa Inspection-requests',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(
    @Param() param: DeleteInspectionRequestRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionRequestService.delete(request);
  }
}
