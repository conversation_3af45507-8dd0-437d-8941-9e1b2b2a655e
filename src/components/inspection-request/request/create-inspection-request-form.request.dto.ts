import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { plainToInstance, Transform, Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';
import {
  FileDelete,
  FileUpload,
} from '../../../core/dto/request/file.request.dto';
import { CreateInspectionRequestsDto } from './create-inspection-request.request.dto';

export class CreateInspectionRequestFormDto extends BaseDto {
  @ApiProperty({})
  @ValidateNested({ each: true })
  @Transform(({ value }) => {
    return plainToInstance(CreateInspectionRequestsDto, JSON.parse(value));
  })
  @Type(() => CreateInspectionRequestsDto)
  data: CreateInspectionRequestsDto;

  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => FileUpload)
  uploadFiles: FileUpload[];

  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => {
    return plainToInstance(FileDelete, JSON.parse(value));
  })
  @Type(() => FileDelete)
  deleteFiles: FileDelete[];
}
