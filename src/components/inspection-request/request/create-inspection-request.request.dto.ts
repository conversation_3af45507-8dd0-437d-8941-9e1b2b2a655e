import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsDateString,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { BaseDto } from '../../../core/dto/base.dto';
import { FileRequestDto } from '../../common/request/file.request.dto';

export class CreateInspectionRequestDetailDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  lotNo: string;

  @ApiProperty()
  @IsNotEmpty()
  itemId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  itemStatusId: number;

  @ApiProperty()
  @IsOptional()
  layer: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  verticalCPMId: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  chemicalCPMId: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  desmearMachineId: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  plasmaMachineId: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  ncTypeId: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  co2TypeId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  checksheetId: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  customerId: number;

  @ApiProperty()
  @IsOptional()
  rev: string;

  @ApiProperty()
  @IsNotEmpty()
  productionWeek: string;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  limitDate: Date;
}

export class CreateInspectionRequestsDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  inspectionRequestTypeId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsDateString()
  requestDate: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @MaxLength(255)
  productionTicket?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsDateString()
  returnResultDate: Date;

  @ApiProperty({ type: [FileRequestDto] })
  @IsArray()
  @IsOptional()
  @Type(() => FileRequestDto)
  files: FileRequestDto[];

  @ApiProperty()
  @IsOptional()
  @IsInt()
  approvedBy?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  description?: string;

  @ApiProperty({ type: [CreateInspectionRequestDetailDto] })
  @IsArray()
  @Type(() => CreateInspectionRequestDetailDto)
  inspectionRequestDetails: CreateInspectionRequestDetailDto[];

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  createdBy: number;
}
