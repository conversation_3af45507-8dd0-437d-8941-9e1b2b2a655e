import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt } from 'class-validator';
import { CreateInspectionRequestFormDto } from './create-inspection-request-form.request.dto';

export class UpdateInspectionRequestFormDto extends CreateInspectionRequestFormDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;
}
