import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateInspectionRequestsDto } from './create-inspection-request.request.dto';

export class UpdateInspectionRequestDto extends CreateInspectionRequestsDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  updatedBy?: number;
}
