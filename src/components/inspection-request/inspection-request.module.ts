import { Modu<PERSON> } from '@nestjs/common';
import { FileService } from '../../core/components/file/file.service';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { InspectionRequestController } from './controller/inspection-request.controller';
import { InspectionRequestService } from './service/inspection-request.service';

@Module({
  imports: [AnotherServiceModule, BaseProcessModule],
  providers: [
    InspectionRequestService,
    {
      provide: 'FileServiceInterface',
      useClass: FileService,
    },
  ],
  exports: [InspectionRequestService],
  controllers: [InspectionRequestController],
})
export class InspectionRequestModule {}
