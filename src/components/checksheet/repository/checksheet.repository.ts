import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { isDateString } from 'class-validator';
import { StatusEnum } from '../../../common/enums/status.enum';
import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import {
  escapeCharForSearch,
  parseJSONValueField,
} from '../../../utils/common';
import { ChecksheetEntity } from '../entities/checksheet.entity';
import { GetDetailChecksheetRequestDto } from '../request/get-detail-checksheet.request.dto';
import { GetListChecksheetRequestDto } from '../request/get-list-checksheet.request.dto';
import { UpdateStatusChecksheetRequestDto } from '../request/update-status-checksheet.request.dto';
import { CreateChecksheetRequestDto } from './../request/create-checksheet.request.dto';

@Injectable()
export class ChecksheetRepository extends BaseAbstractRepository<ChecksheetEntity> {
  constructor(
    @InjectRepository(ChecksheetEntity)
    private readonly checksheetRepository: Repository<ChecksheetEntity>,
  ) {
    super(checksheetRepository);
  }

  createEntity(request: CreateChecksheetRequestDto): ChecksheetEntity {
    const checksheetEntity = new ChecksheetEntity();
    checksheetEntity.code = request.code;
    checksheetEntity.name = request.name;
    checksheetEntity.qcRequestTypeId = request.qcRequestTypeId;
    checksheetEntity.itemLineId = request?.itemLineId || null;
    checksheetEntity.itemTypeId = request?.itemTypeId || null;
    checksheetEntity.itemId = request?.itemId || null;
    checksheetEntity.processId = request.processId;
    checksheetEntity.evaluationStandardTypeMainId =
      request.evaluationStandardTypeMainId;
    checksheetEntity.description = request?.description || null;
    checksheetEntity.files = request?.files || [];
    checksheetEntity.dataDetails = request.dataDetails;
    checksheetEntity.status = StatusEnum.ACTIVE;
    checksheetEntity.createdBy = request.userId;
    checksheetEntity.updatedBy = request.userId;

    return checksheetEntity;
  }

  updateEntity(
    request: CreateChecksheetRequestDto,
    entity: ChecksheetEntity,
  ): ChecksheetEntity {
    entity.name = request?.name;
    entity.qcRequestTypeId = request.qcRequestTypeId;
    entity.itemLineId = request?.itemLineId || null;
    entity.itemTypeId = request?.itemTypeId || null;
    entity.itemId = request?.itemId || null;
    entity.processId = request.processId;
    entity.evaluationStandardTypeMainId = request.evaluationStandardTypeMainId;
    entity.description = request?.description || null;
    entity.files = request?.files || [];
    entity.dataDetails = request.dataDetails;
    entity.updatedBy = request.userId;

    return entity;
  }

  async getList(request: GetListChecksheetRequestDto): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.checksheetRepository
      .createQueryBuilder('checksheet')
      .select([
        'checksheet.id AS id',
        'checksheet.code AS "code"',
        'checksheet.name AS "name"',
        'checksheet.description AS "description"',
        'checksheet.status AS "status"',
        'checksheet.created_by AS "createdBy"',
        'checksheet.created_at AS "createdAt"',
        'checksheet.updated_by AS "updatedBy"',
        'checksheet.updated_at AS "updatedAt"',
      ])
      .leftJoin(
        'qc_request_types',
        'qrt',
        'checksheet.qc_request_type_id = qrt.id',
      )
      .leftJoin('processes', 'p', 'checksheet.process_id = p.id')
      .leftJoin('items', 'i', 'checksheet.item_id = i.id')
      .leftJoin('goods_types', 'gt', 'i.goods_type_id = gt.id')
      .leftJoin('item_lines', 'il', 'checksheet.item_line_id = il.id')
      .leftJoin('item_types', 'it', 'checksheet.item_type_id = it.id')
      .leftJoin(
        'evaluation_standard_types',
        'est',
        'checksheet.evaluation_standard_type_main_id = est.id',
      )
      .addSelect([
        `CASE WHEN COUNT(qrt.id) = 0 THEN '{}' ELSE
          (SELECT qrt.id as id,qrt.code as code, qrt.name as name, qrt.qc_type as qcType, qrt.category as category FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "qcRequestType"`,
        `CASE WHEN COUNT(p.id) = 0 THEN '{}' ELSE 
          (SELECT p.id as id, p.code as code, p.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "process"`,
        `CASE WHEN COUNT(i.id) = 0 THEN '{}' ELSE
          (SELECT i.id as id,i.code as code,i.name_en as nameEn, i.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "item"`,
        `CASE WHEN COUNT(gt.id) = 0 THEN '{}' ELSE
        (SELECT gt.id as id,gt.code as code,gt.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "goodsType"`,
        `CASE WHEN COUNT(il.id) = 0 THEN '{}' ELSE
        (SELECT il.id as id,il.code as code,il.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "itemLine"`,
        `CASE WHEN COUNT(it.id) = 0 THEN '{}' ELSE
        (SELECT it.id as id,it.code as code,it.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "itemType"`,
        `CASE WHEN COUNT(est.id) = 0 THEN '{}' ELSE 
        (SELECT est.id as id, est.code as code, est.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "evaluationStandardTypeMain"`,
      ]);

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("checksheet"."code") like lower(:code) escape '\\' OR
              lower("checksheet"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('checksheet.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'code':
            query.andWhere(
              `lower("checksheet"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'name':
            query.andWhere(
              `lower("checksheet"."name") like lower(:name) escape '\\'`,
              {
                name: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'qcRequestTypeId':
            query.andWhere(
              '"checksheet"."qc_request_type_id" = :qcRequestTypeId',
              {
                qcRequestTypeId: Number(value),
              },
            );
            break;
          case 'qcRequestTypeIds':
            query.andWhere(
              '"checksheet"."qc_request_type_id" IN (:...qcRequestTypeIds)',
              {
                qcRequestTypeIds: value.split(',').map((id) => Number(id)),
              },
            );
            break;
          case 'qcType':
            query.andWhere('"qrt"."qc_type" = :qcType', {
              qcType: Number(value),
            });
            break;
          case 'qcTypes':
            query.andWhere('"qrt"."qc_type" IN (:...qcTypes)', {
              qcTypes: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'category':
            query.andWhere('"qrt"."category" = :category', {
              category: Number(value),
            });
            break;
          case 'categories':
            query.andWhere('"qrt"."category" IN (:...categories)', {
              categories: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'itemId':
            query.andWhere('"checksheet"."item_id" = :itemId', {
              itemId: Number(value),
            });
            break;
          case 'itemIds':
            query.andWhere('"checksheet"."item_id" IN (:...itemIds)', {
              itemIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'processId':
            query.andWhere('"checksheet"."process_id" = :processId', {
              processId: Number(value),
            });
            break;
          case 'processIds':
            query.andWhere('"checksheet"."process_id" IN (:...processIds)', {
              processIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'description':
            query.andWhere(
              `lower("checksheet"."description") like lower(:description) escape '\\'`,
              {
                description: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'status':
            query.andWhere('"checksheet"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'createdById':
            query.andWhere('"checksheet"."created_by" = :createdById', {
              createdById: Number(value),
            });
            break;
          case 'createdByIds':
            query.andWhere('"checksheet"."created_by" IN (:...createdByIds)', {
              createdByIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'createdAt':
            const createFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const createTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"checksheet"."created_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: createFrom,
                dateTo: createTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"checksheet"."updated_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: updateFrom,
                dateTo: upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'name':
            query.addOrderBy('"checksheet"."name"', order);
            break;
          case 'code':
            query.addOrderBy('"checksheet"."code"', order);
            break;
          case 'qcRequestType':
            query.addOrderBy('"checksheet"."qc_request_type_id"', order);
            break;
          case 'qcType':
            query.addOrderBy('"qrt"."qc_type"', order);
            break;
          case 'status':
            query.addOrderBy('"checksheet"."status"', order);
            break;
          case 'description':
            query.addOrderBy('"checksheet"."description"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"checksheet"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"checksheet"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"checksheet"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('checksheet.id', 'DESC');
    }

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    query
      .groupBy('checksheet.id')
      .addGroupBy('checksheet.code')
      .addGroupBy('checksheet.name')
      .addGroupBy('checksheet.description')
      .addGroupBy('checksheet.status')
      .addGroupBy('checksheet.created_by')
      .addGroupBy('checksheet.created_at')
      .addGroupBy('checksheet.updated_by')
      .addGroupBy('checksheet.updated_at')
      .addGroupBy('qrt.id')
      .addGroupBy('qrt.code')
      .addGroupBy('qrt.name')
      .addGroupBy('qrt.qc_type')
      .addGroupBy('qrt.category')
      .addGroupBy('p.id')
      .addGroupBy('p.code')
      .addGroupBy('p.name')
      .addGroupBy('i.id')
      .addGroupBy('i.code')
      .addGroupBy('i.name')
      .addGroupBy('i.name_en')
      .addGroupBy('gt.id')
      .addGroupBy('gt.code')
      .addGroupBy('gt.name')
      .addGroupBy('il.id')
      .addGroupBy('il.code')
      .addGroupBy('il.name')
      .addGroupBy('it.id')
      .addGroupBy('it.code')
      .addGroupBy('it.name')
      .addGroupBy('est.id')
      .addGroupBy('est.code')
      .addGroupBy('est.name');

    const data = await query.getRawMany();
    const count = await query.getCount();

    return {
      data: data?.map((item) => {
        return {
          ...item,
          qcRequestType: parseJSONValueField(item.qcRequestType),
          process: parseJSONValueField(item.process),
          item: parseJSONValueField(item.item),
          goodsType: parseJSONValueField(item.goodsType),
          itemLine: parseJSONValueField(item.itemLine),
          itemType: parseJSONValueField(item.itemType),
          evaluationStandardTypeMain: parseJSONValueField(
            item.evaluationStandardTypeMain,
          ),
        };
      }),
      count: count,
    };
  }

  async getListForComboBox(request: GetListChecksheetRequestDto): Promise<any> {
    const { keyword, skip, take, filter, isGetAll } = request;

    const query = this.checksheetRepository
      .createQueryBuilder('checksheet')
      .select([
        'checksheet.id AS id',
        'checksheet.code AS "code"',
        'checksheet.name AS "name"',
        'checksheet.description AS "description"',
        'checksheet.status AS "status"',
        'checksheet.created_by AS "createdBy"',
        'checksheet.created_at AS "createdAt"',
        'checksheet.updated_by AS "updatedBy"',
        'checksheet.updated_at AS "updatedAt"',
      ])
      .leftJoin(
        'qc_request_types',
        'qrt',
        'checksheet.qc_request_type_id = qrt.id',
      )
      .leftJoin('processes', 'p', 'checksheet.process_id = p.id')
      .leftJoin('items', 'i', 'checksheet.item_id = i.id')
      .leftJoin('goods_types', 'gt', 'i.goods_type_id = gt.id')
      .leftJoin('item_lines', 'il', 'checksheet.item_line_id = il.id')
      .leftJoin('item_types', 'it', 'checksheet.item_type_id = it.id')
      .leftJoin(
        'evaluation_standard_types',
        'est',
        'checksheet.evaluation_standard_type_main_id = est.id',
      )
      .addSelect([
        `CASE WHEN COUNT(qrt.id) = 0 THEN '{}' ELSE
          (SELECT qrt.id as id,qrt.code as code, qrt.name as name, qrt.qc_type as qcType, qrt.category as category FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "qcRequestType"`,
        `CASE WHEN COUNT(p.id) = 0 THEN '{}' ELSE 
          (SELECT p.id as id, p.code as code, p.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "process"`,
        `CASE WHEN COUNT(i.id) = 0 THEN '{}' ELSE
          (SELECT i.id as id,i.code as code,i.name_en as nameEn, i.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "item"`,
        `CASE WHEN COUNT(gt.id) = 0 THEN '{}' ELSE
        (SELECT gt.id as id,gt.code as code,gt.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "goodsType"`,
        `CASE WHEN COUNT(il.id) = 0 THEN '{}' ELSE
        (SELECT il.id as id,il.code as code,il.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "itemLine"`,
        `CASE WHEN COUNT(it.id) = 0 THEN '{}' ELSE
        (SELECT it.id as id,it.code as code,it.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "itemType"`,
        `CASE WHEN COUNT(est.id) = 0 THEN '{}' ELSE 
        (SELECT est.id as id, est.code as code, est.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "evaluationStandardTypeMain"`,
      ]);

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
                lower("checksheet"."code") like lower(:code) escape '\\' OR
                lower("checksheet"."name") like lower(:name) escape '\\'
            )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'qcRequestTypeId':
            if (!value) {
              break;
            } else {
              query.andWhere(
                'checksheet.qc_request_type_id = :qcRequestTypeId',
                {
                  qcRequestTypeId: Number(value),
                },
              );
            }
            break;
          case 'itemTypeId':
            if (!value) {
              query.andWhere('checksheet.item_type_id is NULL');
            } else {
              query.andWhere(
                '(checksheet.item_type_id = :itemTypeId OR checksheet.item_type_id is NULL)',
                {
                  itemTypeId: Number(value),
                },
              );
            }
            break;
          case 'itemLineId':
            if (!value) {
              query.andWhere('checksheet.item_line_id is NULL');
            } else {
              query.andWhere(
                '(checksheet.item_line_id = :itemLineId OR checksheet.item_line_id is NULL)',
                {
                  itemLineId: Number(value),
                },
              );
            }
            break;
          case 'itemId':
            if (!value) {
              query.andWhere('checksheet.item_id is NULL');
            } else {
              query.andWhere(
                '(checksheet.item_id = :itemId OR checksheet.item_id is NULL)',
                {
                  itemId: Number(value),
                },
              );
            }
            break;
          case 'processId':
            if (!value) {
              break;
            } else {
              query.andWhere('checksheet.process_id = :processId', {
                processId: Number(value),
              });
            }
            break;
          case 'status':
            query.andWhere('"checksheet"."status" = :status', {
              status: Number(value),
            });
            break;
          default:
            break;
        }
      });
    }

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    query
      .groupBy('checksheet.id')
      .addGroupBy('checksheet.code')
      .addGroupBy('checksheet.name')
      .addGroupBy('checksheet.description')
      .addGroupBy('checksheet.status')
      .addGroupBy('checksheet.created_by')
      .addGroupBy('checksheet.created_at')
      .addGroupBy('checksheet.updated_by')
      .addGroupBy('checksheet.updated_at')
      .addGroupBy('qrt.id')
      .addGroupBy('qrt.code')
      .addGroupBy('qrt.name')
      .addGroupBy('qrt.qc_type')
      .addGroupBy('qrt.category')
      .addGroupBy('p.id')
      .addGroupBy('p.code')
      .addGroupBy('p.name')
      .addGroupBy('i.id')
      .addGroupBy('i.code')
      .addGroupBy('i.name')
      .addGroupBy('i.name_en')
      .addGroupBy('gt.id')
      .addGroupBy('gt.code')
      .addGroupBy('gt.name')
      .addGroupBy('il.id')
      .addGroupBy('il.code')
      .addGroupBy('il.name')
      .addGroupBy('it.id')
      .addGroupBy('it.code')
      .addGroupBy('it.name')
      .addGroupBy('est.id')
      .addGroupBy('est.code')
      .addGroupBy('est.name');

    const data = await query.getRawMany();
    const count = await query.getCount();

    return {
      data: data?.map((item) => {
        return {
          ...item,
          qcRequestType: parseJSONValueField(item.qcRequestType),
          process: parseJSONValueField(item.process),
          item: parseJSONValueField(item.item),
          goodsType: parseJSONValueField(item.goodsType),
          itemLine: parseJSONValueField(item.itemLine),
          itemType: parseJSONValueField(item.itemType),
          evaluationStandardTypeMain: parseJSONValueField(
            item.evaluationStandardTypeMain,
          ),
        };
      }),
      count: count,
    };
  }

  async getDetail(
    request: GetDetailChecksheetRequestDto,
  ): Promise<ChecksheetEntity> {
    const { id } = request;

    const query = this.checksheetRepository
      .createQueryBuilder('checksheet')
      .select([
        'checksheet.id AS id',
        'checksheet.code AS "code"',
        'checksheet.name AS "name"',
        'checksheet.description AS "description"',
        'checksheet.status AS "status"',
        'checksheet.created_by AS "createdBy"',
        'checksheet.created_at AS "createdAt"',
        'checksheet.updated_by AS "updatedBy"',
        'checksheet.updated_at AS "updatedAt"',
      ])
      .leftJoin(
        'qc_request_types',
        'qrt',
        'checksheet.qc_request_type_id = qrt.id',
      )
      .leftJoin('processes', 'p', 'checksheet.process_id = p.id')
      .leftJoin('items', 'i', 'checksheet.item_id = i.id')
      .leftJoin('goods_types', 'gt', 'i.goods_type_id = gt.id')
      .leftJoin('item_lines', 'il', 'checksheet.item_line_id = il.id')
      .leftJoin('item_types', 'it', 'checksheet.item_type_id = it.id')
      .leftJoin(
        'evaluation_standard_types',
        'est',
        'checksheet.evaluation_standard_type_main_id = est.id',
      )
      .addSelect([
        `CASE WHEN COUNT(qrt.id) = 0 THEN '{}' ELSE
          (SELECT qrt.id as id,qrt.code as code, qrt.name as name, qrt.qc_type as qcType, qrt.category as category FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "qcRequestType"`,
        `CASE WHEN COUNT(p.id) = 0 THEN '{}' ELSE 
          (SELECT p.id as id, p.code as code, p.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "process"`,
        `CASE WHEN COUNT(i.id) = 0 THEN '{}' ELSE
        (SELECT i.id as id,i.code as code,i.name_en as nameEn, i.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "item"`,
        `CASE WHEN COUNT(gt.id) = 0 THEN '{}' ELSE
        (SELECT gt.id as id,gt.code as code,gt.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "goodsType"`,
        `CASE WHEN COUNT(il.id) = 0 THEN '{}' ELSE
        (SELECT il.id as id,il.code as code,il.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "itemLine"`,
        `CASE WHEN COUNT(it.id) = 0 THEN '{}' ELSE
        (SELECT it.id as id,it.code as code,it.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "itemType"`,
        `CASE WHEN COUNT(est.id) = 0 THEN '{}' ELSE 
        (SELECT est.id as id, est.code as code, est.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "evaluationStandardTypeMain"`,
      ])
      .andWhere('"checksheet"."id" = :id', {
        id: id,
      })
      .groupBy('checksheet.id')
      .addGroupBy('checksheet.code')
      .addGroupBy('checksheet.name')
      .addGroupBy('checksheet.description')
      .addGroupBy('checksheet.status')
      .addGroupBy('checksheet.created_by')
      .addGroupBy('checksheet.created_at')
      .addGroupBy('checksheet.updated_by')
      .addGroupBy('checksheet.updated_at')
      .addGroupBy('qrt.id')
      .addGroupBy('qrt.code')
      .addGroupBy('qrt.name')
      .addGroupBy('qrt.qc_type')
      .addGroupBy('qrt.category')
      .addGroupBy('p.id')
      .addGroupBy('p.code')
      .addGroupBy('p.name')
      .addGroupBy('i.id')
      .addGroupBy('i.code')
      .addGroupBy('i.name')
      .addGroupBy('i.name_en')
      .addGroupBy('gt.id')
      .addGroupBy('gt.code')
      .addGroupBy('gt.name')
      .addGroupBy('il.id')
      .addGroupBy('il.code')
      .addGroupBy('il.name')
      .addGroupBy('it.id')
      .addGroupBy('it.code')
      .addGroupBy('it.name')
      .addGroupBy('est.id')
      .addGroupBy('est.code')
      .addGroupBy('est.name');

    const data = await query.getRawOne();

    return {
      ...data,
      qcRequestType: parseJSONValueField(data.qcRequestType),
      process: parseJSONValueField(data.process),
      item: parseJSONValueField(data.item),
      goodsType: parseJSONValueField(data.goodsType),
      itemLine: parseJSONValueField(data.itemLine),
      itemType: parseJSONValueField(data.itemType),
      evaluationStandardTypeMain: parseJSONValueField(
        data.evaluationStandardTypeMain,
      ),
    };
  }

  async getDetailList(ids: number[]): Promise<ChecksheetEntity[]> {
    const query = this.checksheetRepository
      .createQueryBuilder('checksheet')
      .select([
        'checksheet.id AS id',
        'checksheet.code AS "code"',
        'checksheet.name AS "name"',
        'checksheet.description AS "description"',
        'checksheet.status AS "status"',
        'checksheet.created_by AS "createdBy"',
        'checksheet.created_at AS "createdAt"',
        'checksheet.updated_by AS "updatedBy"',
        'checksheet.updated_at AS "updatedAt"',
      ])
      .leftJoin(
        'qc_request_types',
        'qrt',
        'checksheet.qc_request_type_id = qrt.id',
      )
      .leftJoin('processes', 'p', 'checksheet.process_id = p.id')
      .leftJoin('items', 'i', 'checksheet.item_id = i.id')
      .leftJoin('goods_types', 'gt', 'i.goods_type_id = gt.id')
      .leftJoin('item_lines', 'il', 'checksheet.item_line_id = il.id')
      .leftJoin('item_types', 'it', 'checksheet.item_type_id = it.id')
      .leftJoin(
        'evaluation_standard_types',
        'est',
        'checksheet.evaluation_standard_type_main_id = est.id',
      )
      .addSelect([
        `CASE WHEN COUNT(qrt.id) = 0 THEN '{}' ELSE
          (SELECT qrt.id as id,qrt.code as code, qrt.name as name, qrt.qc_type as qcType, qrt.category as category FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "qcRequestType"`,
        `CASE WHEN COUNT(p.id) = 0 THEN '{}' ELSE 
          (SELECT p.id as id, p.code as code, p.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "process"`,
        `CASE WHEN COUNT(i.id) = 0 THEN '{}' ELSE
        (SELECT i.id as id,i.code as code,i.name_en as nameEn, i.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "item"`,
        `CASE WHEN COUNT(gt.id) = 0 THEN '{}' ELSE
        (SELECT gt.id as id,gt.code as code,gt.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "goodsType"`,
        `CASE WHEN COUNT(il.id) = 0 THEN '{}' ELSE
        (SELECT il.id as id,il.code as code,il.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "itemLine"`,
        `CASE WHEN COUNT(it.id) = 0 THEN '{}' ELSE
        (SELECT it.id as id,it.code as code,it.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "itemType"`,
        `CASE WHEN COUNT(est.id) = 0 THEN '{}' ELSE 
        (SELECT est.id as id, est.code as code, est.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "evaluationStandardTypeMain"`,
      ])
      .andWhere('"checksheet"."id" IN (:...ids)', {
        ids: ids,
      })
      .groupBy('checksheet.id')
      .addGroupBy('checksheet.code')
      .addGroupBy('checksheet.name')
      .addGroupBy('checksheet.description')
      .addGroupBy('checksheet.status')
      .addGroupBy('checksheet.created_by')
      .addGroupBy('checksheet.created_at')
      .addGroupBy('checksheet.updated_by')
      .addGroupBy('checksheet.updated_at')
      .addGroupBy('qrt.id')
      .addGroupBy('qrt.code')
      .addGroupBy('qrt.name')
      .addGroupBy('qrt.qc_type')
      .addGroupBy('qrt.category')
      .addGroupBy('p.id')
      .addGroupBy('p.code')
      .addGroupBy('p.name')
      .addGroupBy('i.id')
      .addGroupBy('i.code')
      .addGroupBy('i.name')
      .addGroupBy('i.name_en')
      .addGroupBy('gt.id')
      .addGroupBy('gt.code')
      .addGroupBy('gt.name')
      .addGroupBy('il.id')
      .addGroupBy('il.code')
      .addGroupBy('il.name')
      .addGroupBy('it.id')
      .addGroupBy('it.code')
      .addGroupBy('it.name')
      .addGroupBy('est.id')
      .addGroupBy('est.code')
      .addGroupBy('est.name');

    const data = await query.getRawMany();

    return data?.map((item) => ({
      ...item,
      qcRequestType: parseJSONValueField(item.qcRequestType),
      process: parseJSONValueField(item.process),
      item: parseJSONValueField(item.item),
      goodsType: parseJSONValueField(item.goodsType),
      itemLine: parseJSONValueField(item.itemLine),
      itemType: parseJSONValueField(item.itemType),
      evaluationStandardTypeMain: parseJSONValueField(
        item.evaluationStandardTypeMain,
      ),
    }));
  }

  async active(request: UpdateStatusChecksheetRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.checksheetRepository
      .createQueryBuilder()
      .update(ChecksheetEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(request: UpdateStatusChecksheetRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.checksheetRepository
      .createQueryBuilder()
      .update(ChecksheetEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async getChecksheetFiles(checksheetIds: number[]): Promise<any> {
    const data = await this.entity
      .createQueryBuilder('c')
      .innerJoinAndSelect('c.files', 'cf')
      .select([
        'c.id as checksheetId',
        'cf.id as id',
        'cf.file_id as fileId',
        'cf.file_name as fileName',
        'cf.file_url as fileUrl',
        'cf.file_type as fileType',
        'cf.document_name as documentName',
        'cf.note as note',
      ])
      .where('c.id IN (:...checksheetIds)', { checksheetIds })
      .getRawMany();

    return data;
  }
}
