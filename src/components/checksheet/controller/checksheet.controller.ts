import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  ACTIVE_CHECKSHEET_PERMISSION,
  CREATE_CHECKSHEET_PERMISSION,
  DELETE_CHECKSHEET_PERMISSION,
  DETAIL_CHECKSHEET_PERMISSION,
  INACTIVE_CHECKSHEET_PERMISSION,
  LIST_CHECKSHEET_PERMISSION,
  UPDATE_CHECKSHEET_PERMISSION,
} from '../../../utils/permissions/checksheet.permission';
import { CreateChecksheetRequestDto } from '../request/create-checksheet.request.dto';
import { DeleteChecksheetRequestDto } from '../request/delete-checksheet.request.dto';
import { GetDetailChecksheetRequestDto } from '../request/get-detail-checksheet.request.dto';
import { GetListChecksheetRequestDto } from '../request/get-list-checksheet.request.dto';
import { UpdateChecksheetRequestDto } from '../request/update-checksheet.request.dto';
import { UpdateStatusChecksheetRequestDto } from '../request/update-status-checksheet.request.dto';
import { ChecksheetService } from '../service/checksheet.service';

@Controller('checksheets')
export class ChecksheetController {
  constructor(private readonly checksheetService: ChecksheetService) {}

  @PermissionCode(DETAIL_CHECKSHEET_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Checksheets'],
    summary: 'Chi tiết Checksheets',
    description: 'Chi tiết Checksheets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailChecksheetRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.checksheetService.getDetail(request);
  }

  @PermissionCode(DETAIL_CHECKSHEET_PERMISSION.code)
  @Get('/inspections/:id')
  @ApiOperation({
    tags: ['Checksheets'],
    summary: 'List Nội dung kiểm tra theo ChecksheetId và Loại kiểm tra',
    description: 'List Nội dung kiểm tra theo ChecksheetId và Loại kiểm tra',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getInspectionByChecksheetId(
    @Param() param: GetDetailChecksheetRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.checksheetService.getInspectionByChecksheetId(request);
  }

  @PermissionCode(LIST_CHECKSHEET_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Checksheets'],
    summary: 'Danh sách Checksheets',
    description: 'Danh sách Checksheets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(@Query() query: GetListChecksheetRequestDto): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.checksheetService.getList(request);
  }

  @PermissionCode(CREATE_CHECKSHEET_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Checksheets'],
    summary: 'Tạo Checksheets mới',
    description: 'Tạo Checksheets mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateChecksheetRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.checksheetService.create(request);
  }

  @PermissionCode(UPDATE_CHECKSHEET_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Checksheets'],
    summary: 'Cập nhật Checksheets',
    description: 'Cập nhật Checksheets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(@Body() body: UpdateChecksheetRequestDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.checksheetService.update(request);
  }

  @PermissionCode(ACTIVE_CHECKSHEET_PERMISSION.code)
  @Put('/active')
  @ApiOperation({
    tags: ['Checksheets'],
    summary: 'Cập nhật Checksheets Status Active',
    description: 'Cập nhật Checksheets Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async active(
    @Body() body: UpdateStatusChecksheetRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.checksheetService.active(request);
  }

  @PermissionCode(INACTIVE_CHECKSHEET_PERMISSION.code)
  @Put('/inactive')
  @ApiOperation({
    tags: ['Checksheets'],
    summary: 'Cập nhật Checksheets Status Inactive',
    description: 'Cập nhật Checksheets Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async inactive(
    @Body() body: UpdateStatusChecksheetRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.checksheetService.inactive(request);
  }

  @PermissionCode(DELETE_CHECKSHEET_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Checksheets'],
    summary: 'Xóa Checksheets',
    description: 'Xóa Checksheets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(@Param() param: DeleteChecksheetRequestDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.checksheetService.delete(request);
  }

  @PermissionCode(LIST_CHECKSHEET_PERMISSION.code)
  @Get('/list-for-combo-box')
  @ApiOperation({
    tags: ['Checksheets'],
    summary: 'Danh sách Checksheets',
    description: 'Danh sách Checksheets',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getListForComboBox(
    @Query() query: GetListChecksheetRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.checksheetService.getListForComboBox(request);
  }
}
