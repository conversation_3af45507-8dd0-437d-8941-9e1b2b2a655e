import { Column, <PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON>um<PERSON>, <PERSON>To<PERSON>any } from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { ChecksheetDetailEntity } from '../../checksheet-detail/entities/checksheet-detail.entity';
import { FileEntity } from '../../common/entities/file.entity';

@Entity({ name: 'checksheets' })
export class ChecksheetEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    unique: true,
    nullable: false,
    length: 255,
  })
  code: string;

  @Column({
    type: 'nvarchar',
    nullable: false,
    length: 255,
  })
  name: string;

  @Column({
    type: 'int',
    nullable: false,
  })
  qcRequestTypeId: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  itemTypeId: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  itemLineId: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  itemId: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  processId: number;

  @Column({
    type: 'int',
    nullable: false,
  })
  evaluationStandardTypeMainId: number;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  description: string;

  @Column({
    type: 'tinyint',
  })
  status: number;

  @OneToMany(() => FileEntity, (s) => s.checksheet, {
    cascade: ['insert', 'update'],
    eager: true,
  })
  @JoinColumn({ name: 'id', referencedColumnName: 'checksheet_id' })
  files: FileEntity[];

  @OneToMany(() => ChecksheetDetailEntity, (s) => s.checksheet, {
    cascade: ['insert', 'update'],
    eager: true,
  })
  @JoinColumn({ name: 'id', referencedColumnName: 'checksheet_id' })
  dataDetails: ChecksheetDetailEntity[];
}
