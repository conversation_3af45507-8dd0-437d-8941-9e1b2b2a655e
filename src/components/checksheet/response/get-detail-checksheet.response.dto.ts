import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { BaseCommonResponseDto } from '../../../common/dtos/response/base.common.dto';
import { QcRequestTypeResponseDto } from '../../../common/dtos/response/qc-request-type.common.dto';
import { ResponseCommonDto } from '../../../common/dtos/response/response.common.dto';
import { FileInforResponeDto } from '../../../core/components/file/dto/file-infor.respone';
import { GetDetailChecksheetDataResponseDto } from './get-detail-checksheet-data.response.dto';

export class GetDetailChecksheetResponseDto extends BaseCommonResponseDto {
  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  qcRequestType: QcRequestTypeResponseDto;

  @ApiProperty()
  @Expose()
  item: ResponseCommonDto;

  @ApiProperty()
  @Expose()
  itemType: ResponseCommonDto;

  @ApiProperty()
  @Expose()
  itemLine: ResponseCommonDto;

  @ApiProperty()
  @Expose()
  goodsType: ResponseCommonDto;

  @ApiProperty()
  @Expose()
  process: ResponseCommonDto;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty()
  @Expose()
  status: number;

  @ApiProperty({ type: GetDetailChecksheetDataResponseDto })
  @Expose()
  @Type(() => GetDetailChecksheetDataResponseDto)
  dataDetails: GetDetailChecksheetDataResponseDto[];

  @ApiProperty({ type: FileInforResponeDto, isArray: true })
  @Type(() => FileInforResponeDto)
  @Expose()
  files: FileInforResponeDto[];
}
