import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { InspectionGroupResponseDto } from '../../../common/dtos/response/inspection-group.common.dto';
import { ResponseCommonDto } from '../../../common/dtos/response/response.common.dto';

export class GetDetailChecksheetDataResponseDto {
  @ApiProperty()
  @Expose()
  id: number;

  @ApiProperty()
  @Expose()
  inspection: ResponseCommonDto;

  @ApiProperty()
  @Expose()
  inspectionType: ResponseCommonDto;

  @ApiProperty()
  @Expose()
  inspectionGroup: InspectionGroupResponseDto;

  @ApiProperty()
  @Expose()
  unit: ResponseCommonDto;

  @ApiProperty()
  @Expose()
  spec: number;

  @ApiProperty()
  @Expose()
  plusAbove: number;

  @ApiProperty()
  @Expose()
  minusBelow: number;

  @ApiProperty()
  @Expose()
  isScoring: number;

  @ApiProperty()
  @Expose()
  scoringScale: number;

  @ApiProperty()
  @Expose()
  checksheetDetailId: number;
}
