import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BaseCommonResponseDto } from '../../../common/dtos/response/base.common.dto';
import { QcRequestTypeResponseDto } from '../../../common/dtos/response/qc-request-type.common.dto';
import { ResponseCommonDto } from '../../../common/dtos/response/response.common.dto';

export class GetListChecksheetResponseDto extends BaseCommonResponseDto {
  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  qcRequestType: QcRequestTypeResponseDto;

  @ApiProperty()
  @Expose()
  item: ResponseCommonDto;

  @ApiProperty()
  @Expose()
  itemType: ResponseCommonDto;

  @ApiProperty()
  @Expose()
  itemLine: ResponseCommonDto;

  @ApiProperty()
  @Expose()
  goodsType: ResponseCommonDto;

  @ApiProperty()
  @Expose()
  process: ResponseCommonDto;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty()
  @Expose()
  status: number;
}
