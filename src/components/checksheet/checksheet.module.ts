import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { ChecksheetDetailModule } from '../checksheet-detail/checksheet-detail.module';
import { EvaluationStandardTypeModule } from '../evaluation-standard-type/evaluation-standard-type.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { UnitModule } from '../unit/unit.module';
import { ChecksheetController } from './controller/checksheet.controller';
import { ChecksheetEntity } from './entities/checksheet.entity';
import { ChecksheetRepository } from './repository/checksheet.repository';
import { ChecksheetService } from './service/checksheet.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([ChecksheetEntity]),
    AnotherServiceModule,
    ChecksheetDetailModule,
    MasterDataReferenceModule,
    UnitModule,
    EvaluationStandardTypeModule,
  ],
  providers: [ChecksheetService, ChecksheetRepository],
  exports: [ChecksheetService],
  controllers: [ChecksheetController],
})
export class ChecksheetModule {}
