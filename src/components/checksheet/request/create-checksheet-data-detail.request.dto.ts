import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Max,
  Min,
  ValidateNested,
} from 'class-validator';
import { ChecksheetDetailErrorEntity } from '../../checksheet-detail/entities/checksheet-detail-error.entity';

export class CreateChecksheetDetailErrorRequestDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  id: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  checksheetDetailId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  errorId: number;
}

export class CreateChecksheetDataDetailRequestDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  id: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  checksheetId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  checkType: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  inspectionGroupId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  inspectionTypeId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  inspectionId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  unitId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  evaluationStandardTypeId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  // @Min(0)
  spec: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  // @Min(0)
  plusAbove: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  minusBelow: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  isScoring: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  scoringScale: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  category: number;

  @ApiProperty({ type: [CreateChecksheetDetailErrorRequestDto] })
  @IsArray()
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateChecksheetDetailErrorRequestDto)
  errors: ChecksheetDetailErrorEntity[];
}
