import {
  IsInt,
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON>ber,
  <PERSON>Optional,
  IsString,
  Matches,
  MaxLength,
} from 'class-validator';

export class ImportChecksheetDetailRequestDto {
  @IsNotEmpty()
  @IsString()
  keyMapping: string;

  @IsNotEmpty()
  @IsInt()
  checkType: number;

  @IsNotEmpty()
  @IsString()
  inspectionTypeCode: string;

  @IsOptional()
  @IsString()
  inspectionCode: string;

  @IsOptional()
  @IsString()
  errorCode: string;

  @IsOptional()
  @IsString()
  unitCode: string;

  @IsNotEmpty()
  @IsString()
  evaluationStandardTypeCode: string;

  @IsOptional()
  @IsNumber()
  category: number;

  @IsOptional()
  @IsNumber()
  spec: number;

  @IsOptional()
  @IsNumber()
  plusAbove: number;

  @IsOptional()
  @IsNumber()
  minusBelow: number;

  @IsOptional()
  @IsNumber()
  isScoring: number;

  @IsOptional()
  @IsNumber()
  scoringScale: number;
}

export class ImportChecksheetRequestDto {
  @IsNotEmpty()
  @IsString()
  keyMapping: string;

  @IsNotEmpty()
  @MaxLength(50)
  @Matches(/^[a-zA-Z0-9_.-]+$/)
  code: string;

  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @IsNotEmpty()
  @IsString()
  qcRequestTypeCode: string;

  @IsOptional()
  @IsString()
  itemTypeCode: string;

  @IsOptional()
  @IsString()
  itemLineCode: string;

  @IsOptional()
  @IsString()
  itemCode: string;

  @IsNotEmpty()
  @IsString()
  processCode: string;

  @IsNotEmpty()
  @IsString()
  evaluationStandardTypeCode: string;

  @IsOptional()
  @MaxLength(255)
  description?: string;
}
