import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Max<PERSON>ength,
  ValidateNested,
} from 'class-validator';
import { BaseDto } from '../../../core/dto/base.dto';
import { ChecksheetDetailEntity } from '../../checksheet-detail/entities/checksheet-detail.entity';
import { FileEntity } from '../../common/entities/file.entity';
import { FileRequestDto } from '../../common/request/file.request.dto';
import { CreateChecksheetDataDetailRequestDto } from './create-checksheet-data-detail.request.dto';

export class CreateChecksheetRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  code: string;

  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  qcRequestTypeId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  itemTypeId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  itemLineId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  itemId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  processId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  evaluationStandardTypeMainId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  description?: string;

  @ApiProperty({ type: [CreateChecksheetDataDetailRequestDto] })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested()
  @Type(() => CreateChecksheetDataDetailRequestDto)
  dataDetails: ChecksheetDetailEntity[];

  @ApiProperty({ type: [FileRequestDto] })
  @IsArray()
  @IsOptional()
  @ValidateNested()
  @Type(() => FileRequestDto)
  files: FileEntity[];
}
