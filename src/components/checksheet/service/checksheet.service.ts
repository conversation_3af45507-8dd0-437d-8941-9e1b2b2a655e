import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { ChecksheetEntity } from '../entities/checksheet.entity';
import { CreateChecksheetRequestDto } from './../request/create-checksheet.request.dto';
import { DeleteChecksheetRequestDto } from './../request/delete-checksheet.request.dto';
import { GetDetailChecksheetRequestDto } from './../request/get-detail-checksheet.request.dto';
import { GetListChecksheetRequestDto } from './../request/get-list-checksheet.request.dto';
import { UpdateChecksheetRequestDto } from './../request/update-checksheet.request.dto';
import { UpdateStatusChecksheetRequestDto } from './../request/update-status-checksheet.request.dto';

import { GetDetailChecksheetResponseDto } from './../response/get-detail-checksheet.response.dto';
import { GetListChecksheetResponseDto } from './../response/get-list-checksheet.response.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { ValidateResultCommonDto } from '../../../common/dtos/validate.result.common.dto';
import { StatusEnum } from '../../../common/enums/status.enum';
import { BaseService } from '../../../common/service/base.service';
import { CHECK_TYPE_ENUM } from '../../../constant/common';
import { UserService } from '../../another-service/services/user-service';
import { ChecksheetDetailService } from '../../checksheet-detail/service/checksheet-detail.service';
import { EvaluationStandardTypeService } from '../../evaluation-standard-type/service/evaluation-standard-type.service';
import { MasterDataReferenceService } from '../../master-data-reference/service/master-data-reference.service';
import { UnitService } from '../../unit/service/unit.service';
import { ChecksheetRepository } from '../repository/checksheet.repository';

@Injectable()
export class ChecksheetService extends BaseService {
  private readonly logger = new Logger(ChecksheetService.name);

  constructor(
    private readonly checksheetRepository: ChecksheetRepository,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly masterReferenceService: MasterDataReferenceService,

    private readonly unitService: UnitService,

    private readonly checksheetDetailService: ChecksheetDetailService,

    private readonly evaluationStandardTypeService: EvaluationStandardTypeService,
  ) {
    super(userService);
  }

  async create(request: CreateChecksheetRequestDto): Promise<any> {
    const existCode = await this.checksheetRepository.findOneByCode(
      request?.code,
    );
    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate save checksheet
    const resultValidate = await this.validateSaveChecksheet(request);
    if (resultValidate?.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(resultValidate.messageError))
        .build();
    }

    const checksheetEntity = this.checksheetRepository.createEntity(request);
    const checksheet = await this.checksheetRepository.create(checksheetEntity);

    return new ResponseBuilder(checksheet)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListChecksheetRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.checksheetRepository.getList(request);

    const dataMap = await this.mapChecksheetDetailToResponse(data);
    const dataMapUser = await this.mapUserInfoToResponse(dataMap);
    const response = plainToInstance(GetListChecksheetResponseDto, dataMapUser);

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailChecksheetRequestDto): Promise<any> {
    const existChecksheet = await this.checksheetRepository.findOneById(
      request?.id,
    );
    if (isEmpty(existChecksheet)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const checksheet = await this.checksheetRepository.getDetail(request);

    const dataMap = await this.mapChecksheetDetailToResponse([checksheet]);
    const dataMapUser = await this.mapUserInfoToResponse(dataMap);
    const response = plainToInstance(
      GetDetailChecksheetResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getInspectionByChecksheetId(
    request: GetDetailChecksheetRequestDto,
  ): Promise<any> {
    const inspections =
      await this.checksheetDetailService.getInspectionByChecksheetIdsAndCheckType(
        [request?.id],
      );

    return new ResponseBuilder(inspections)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetailList(
    ids: number[],
  ): Promise<GetDetailChecksheetResponseDto[]> {
    const checksheets = await this.checksheetRepository.getDetailList(ids);

    const dataMap = await this.mapChecksheetDetailToResponse(checksheets);

    const response = plainToInstance(GetDetailChecksheetResponseDto, dataMap);

    return response;
  }

  async update(request: UpdateChecksheetRequestDto): Promise<any> {
    const { id } = request;
    const checksheet = await this.checksheetRepository.findOneById(id);

    if (isEmpty(checksheet)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate checksheet in used
    const usedList = await this.validateChecksheetInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.CHECKSHEET_IN_USED'))
        .build();
    }

    // Validate Code unique
    const existCode = await this.checksheetRepository.findOneByCode(
      request?.code,
    );
    if (!isEmpty(existCode) && existCode.id !== checksheet.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate save checksheet
    const resultValidate = await this.validateSaveChecksheet(request);
    if (resultValidate?.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(resultValidate.messageError))
        .build();
    }

    const dataUpdate = this.checksheetRepository.updateEntity(
      request,
      checksheet,
    );
    const data = await this.checksheetRepository.update(dataUpdate);

    return new ResponseBuilder(data)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteChecksheetRequestDto): Promise<any> {
    const { id } = request;
    const checksheet = await this.checksheetRepository.findOneById(id);

    if (isEmpty(checksheet)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate checksheet in used
    const usedList = await this.validateChecksheetInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.CHECKSHEET_IN_USED'))
        .build();
    }

    await this.checksheetDetailService.deleteByChecksheetIds([id]);
    await this.checksheetRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(request: UpdateStatusChecksheetRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.checksheetRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.checksheetRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(request: UpdateStatusChecksheetRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.checksheetRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.checksheetRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async findOneById(id: number): Promise<ChecksheetEntity> {
    return await this.checksheetRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<ChecksheetEntity[]> {
    return await this.checksheetRepository.findAllByIds(ids);
  }

  async validateSaveChecksheet(
    request: CreateChecksheetRequestDto,
  ): Promise<ValidateResultCommonDto> {
    const inspectionGroupSetIds = new Set<number>();
    const inspectionTypeSetIds = new Set<number>();
    const inspectionSetIds = new Set<number>();
    const unitSetIds = new Set<number>();
    const evaluationStandardTypeSetIds = new Set<number>();
    const errorSetIds = new Set<number>();
    let checkTypeValidate: boolean = false;
    const measurementCheckIds = new Set<number>();
    let specAndLowValidate: boolean = false;
    let specIsNull: boolean = false;
    let categoryIsNull: boolean = false;
    // let plusAboveIsNull: boolean = false;
    let minusBelowIsNull: boolean = false;
    // let minusBelowGtSpecValidate: boolean = false;
    let scoringScaleEqualZero: boolean = false;

    request?.dataDetails?.map((item) => {
      if (item?.inspectionId !== undefined && item?.inspectionId !== null) {
        inspectionSetIds.add(item.inspectionId);
      }

      inspectionTypeSetIds.add(item.inspectionTypeId);
      inspectionGroupSetIds.add(item.inspectionGroupId);

      if (item?.unitId !== undefined && item?.unitId !== null) {
        unitSetIds.add(item.unitId);
      }

      if (item.scoringScale === 0) {
        scoringScaleEqualZero = true;
      }

      evaluationStandardTypeSetIds.add(item.evaluationStandardTypeId);
      if (item?.checkType === CHECK_TYPE_ENUM.MEASUREMENT) {
        checkTypeValidate = true;

        if (
          item.evaluationStandardTypeId === request.evaluationStandardTypeMainId
        ) {
          measurementCheckIds.add(item.evaluationStandardTypeId);
        }
      }

      // Validate spec, plus_above, minus_below
      if (
        item?.checkType === CHECK_TYPE_ENUM.MEASUREMENT ||
        item?.checkType === CHECK_TYPE_ENUM.ENVIRONMENTAL_TOXICITY
      ) {
        specAndLowValidate = true;

        if (item?.spec === null || item?.spec === undefined) {
          specIsNull = true;
        }
        //  else if (item?.plusAbove === null || item?.plusAbove === undefined) {
        //   plusAboveIsNull = true;
        // }
        else if (item?.minusBelow === null || item?.minusBelow === undefined) {
          minusBelowIsNull = true;
        }
        //  else if (item?.spec < item?.minusBelow) {
        //   minusBelowGtSpecValidate = true;
        // }
      }

      // Validate category is not null
      if (
        (item?.checkType === CHECK_TYPE_ENUM.MEASUREMENT ||
          item?.checkType === CHECK_TYPE_ENUM.EXPANSION) &&
        (item?.category === null || item?.category === undefined)
      ) {
        categoryIsNull = true;
      }

      if (!isEmpty(item?.errors)) {
        item?.errors?.forEach((error) => {
          if (error?.errorId !== undefined && error?.errorId !== null) {
            errorSetIds.add(error.errorId);
          }
        });
      }
    });

    // Validate check-type and evaluation-standard-types
    if (checkTypeValidate && isEmpty(measurementCheckIds)) {
      return {
        result: true,
        messageError: 'error.CHECKSHEET_DETAIL_EVALUATION_STANDARD_TYPE_ERROR',
      };
    }
    evaluationStandardTypeSetIds.add(request?.evaluationStandardTypeMainId);
    const evaluationStandardTypeIds: number[] = Array.from(
      evaluationStandardTypeSetIds,
    );

    // Validate category is not null
    if (categoryIsNull) {
      return {
        result: true,
        messageError: 'error.CHECKSHEET_DETAIL_CATEGORY_IS_NOT_NULL',
      };
    }

    // Validate spec, plus_above, minus_below
    if (specAndLowValidate) {
      if (specIsNull) {
        return {
          result: true,
          messageError: 'error.SPEC_IS_NOT_NULL',
        };
      }

      // if (plusAboveIsNull) {
      //   return {
      //     result: true,
      //     messageError: 'error.PLUS_ABOVE_IS_NOT_NULL',
      //   };
      // }

      if (minusBelowIsNull) {
        return {
          result: true,
          messageError: 'error.MINUS_BELOW_IS_NOT_NULL',
        };
      }

      // if (minusBelowGtSpecValidate) {
      //   return {
      //     result: true,
      //     messageError: 'error.MINUS_BELOW_GREATER_THAN_SPEC',
      //   };
      // }
    }

    // Validate spec, plus_above, minus_below, scoring-scale
    if (scoringScaleEqualZero) {
      return {
        result: true,
        messageError: 'error.SCORING_SCALE_VALIDATE_ERROR',
      };
    }

    // Validate exist qc_request_types
    const qcRequestType =
      await this.masterReferenceService.findOneQcRequestTypeById(
        request?.qcRequestTypeId,
      );
    if (
      isEmpty(qcRequestType) ||
      qcRequestType?.status === StatusEnum.IN_ACTIVE
    ) {
      return {
        result: true,
        messageError: 'error.QC_REQUEST_TYPE_IS_NOT_EXISTS',
      };
    }

    // Validate exist items
    if (request?.itemId !== undefined && request?.itemId !== null) {
      const item = await this.masterReferenceService.findOneItemById(
        request?.itemId,
      );
      if (isEmpty(item) || item?.status === StatusEnum.IN_ACTIVE) {
        return { result: true, messageError: 'error.ITEM_IS_NOT_EXISTS' };
      }
    }

    // Validate exist item-types
    if (request?.itemTypeId !== undefined && request?.itemTypeId !== null) {
      const itemType = await this.masterReferenceService.findOneItemTypeById(
        request?.itemTypeId,
      );
      if (isEmpty(itemType) || itemType?.status === StatusEnum.IN_ACTIVE) {
        return { result: true, messageError: 'error.ITEM_TYPE_IS_NOT_EXISTS' };
      }
    }

    // Validate exist items
    if (request?.itemLineId !== undefined && request?.itemLineId !== null) {
      const itemLine = await this.masterReferenceService.findOneItemLineById(
        request?.itemLineId,
      );
      if (isEmpty(itemLine) || itemLine?.status === StatusEnum.IN_ACTIVE) {
        return { result: true, messageError: 'error.ITEM_LINE_IS_NOT_EXISTS' };
      }
    }

    // Validate exist processes
    const process = await this.masterReferenceService.findOneProcessById(
      request?.processId,
    );
    if (isEmpty(process) || process?.status === StatusEnum.IN_ACTIVE) {
      return { result: true, messageError: 'error.PROCESS_IS_NOT_EXISTS' };
    }

    // Validate exist inspection-types
    const inspectionTypeIds: number[] = Array.from(inspectionTypeSetIds);
    const existInspectionTypes =
      await this.masterReferenceService.findAllInspectionTypeByIds(
        inspectionTypeIds,
      );
    if (
      isEmpty(inspectionTypeIds) ||
      isEmpty(existInspectionTypes) ||
      inspectionTypeIds?.length !== existInspectionTypes?.length ||
      existInspectionTypes.some((s) => s.status === StatusEnum.IN_ACTIVE)
    ) {
      return {
        result: true,
        messageError: 'error.INSPECTION_TYPE_IS_NOT_EXISTS',
      };
    }

    // Validate exist inspection-groups
    const inspectionGroupIds: number[] = Array.from(inspectionGroupSetIds);
    const existInspectionGroups =
      await this.masterReferenceService.findAllInspectionGroupByIds(
        inspectionGroupIds,
      );
    if (
      isEmpty(inspectionGroupIds) ||
      isEmpty(existInspectionGroups) ||
      inspectionGroupIds?.length !== existInspectionGroups?.length ||
      existInspectionGroups.some((s) => s.status === StatusEnum.IN_ACTIVE)
    ) {
      return {
        result: true,
        messageError: 'error.INSPECTION_GROUP_IS_NOT_EXISTS',
      };
    }

    // Validate exist inspections
    const inspectionIds: number[] = Array.from(inspectionSetIds);
    if (!isEmpty(inspectionIds)) {
      const existInspections =
        await this.masterReferenceService.findAllInspectionByIds(inspectionIds);
      const inspectionInactive = existInspections?.filter(
        (inspection) => inspection.status === StatusEnum.IN_ACTIVE,
      );
      if (
        isEmpty(existInspections) ||
        !isEmpty(inspectionInactive) ||
        inspectionIds?.length !== existInspections?.length
      ) {
        return { result: true, messageError: 'error.INSPECTION_IS_NOT_EXISTS' };
      }
    }

    // Validate exist units
    const unitIds: number[] = Array.from(unitSetIds);
    if (!isEmpty(unitIds)) {
      const existUnits = await this.unitService.findAllByIds(unitIds);
      const unitWithStatusInactive = existUnits?.filter(
        (unit) => unit.status === StatusEnum.IN_ACTIVE,
      );
      if (
        isEmpty(existUnits) ||
        !isEmpty(unitWithStatusInactive) ||
        unitIds?.length !== existUnits?.length
      ) {
        return { result: true, messageError: 'error.UNIT_IS_NOT_EXISTS' };
      }
    }

    // Validate exist errors
    const errorIds: number[] = Array.from(errorSetIds);
    if (!isEmpty(errorIds)) {
      const existErrors = await this.masterReferenceService.findAllErrorIds(
        errorIds,
      );
      const errorWithStatusInactive = existErrors?.filter(
        (error) => error.status === StatusEnum.IN_ACTIVE,
      );
      if (
        isEmpty(existErrors) ||
        !isEmpty(errorWithStatusInactive) ||
        errorIds?.length !== existErrors?.length
      ) {
        return { result: true, messageError: 'error.ERROR_IS_NOT_EXISTS' };
      }
    }

    // Validate exist evaluation-standard-types
    const existEvaluationStandardTypes =
      await this.evaluationStandardTypeService.findAllByIds(
        evaluationStandardTypeIds,
      );
    const evaluationStandardTypeInactive = existEvaluationStandardTypes?.filter(
      (inspection) => inspection.status === StatusEnum.IN_ACTIVE,
    );
    if (
      isEmpty(evaluationStandardTypeIds) ||
      isEmpty(existEvaluationStandardTypes) ||
      !isEmpty(evaluationStandardTypeInactive) ||
      evaluationStandardTypeIds?.length !== existEvaluationStandardTypes?.length
    ) {
      return {
        result: true,
        messageError: 'error.EVALUATION_STANDARD_TYPE_IS_NOT_EXISTS',
      };
    }

    // Validate key duplicate
    const keyCheckDuplicates = new Set<string>();
    let msgErrorDuplicate: string;
    request?.dataDetails.some((item) => {
      let key = `${item.checkType}-${item.inspectionGroupId}-${item.inspectionTypeId}-${item.evaluationStandardTypeId}`;
      if (item?.inspectionId) {
        key += `${item.inspectionId}`;
      }
      if (keyCheckDuplicates.has(key)) {
        msgErrorDuplicate = 'error.CHECKSHEET_DETAIL_HAS_DUPLICATE_KEY';
      } else {
        keyCheckDuplicates.add(key);
      }
    });

    if (!isEmpty(msgErrorDuplicate)) {
      return { result: true, messageError: msgErrorDuplicate };
    }

    return { result: false, messageError: '' };
  }

  async mapChecksheetDetailToResponse(data: ChecksheetEntity[]) {
    const checksheetIds: number[] = Array.from(
      new Set(data?.flatMap((item) => +item.id)),
    );
    if (isEmpty(checksheetIds)) return data;

    // Get checksheet-details
    const dataDetails =
      await this.checksheetDetailService.findAllByChecksheetIds(checksheetIds);
    if (isEmpty(dataDetails)) return data;

    const dataDetailMap = dataDetails?.reduce((acc, item) => {
      const key = `${item.checksheetId}`;
      if (isEmpty(acc[key])) {
        acc[key] = { dataDetails: [item] };
      } else {
        acc[key].dataDetails.push(item);
      }
      return acc;
    }, {});

    // Get checksheet-files
    const checksheetFiles = await this.checksheetRepository.getChecksheetFiles(
      checksheetIds,
    );
    let fileMap = [];
    if (!isEmpty(checksheetFiles)) {
      fileMap = checksheetFiles?.reduce((acc, item) => {
        const key = `${item.checksheetId}`;
        if (isEmpty(acc[key])) {
          acc[key] = { files: [item] };
        } else {
          acc[key].files.push(item);
        }
        return acc;
      }, {});
    }

    return (
      data?.map((item) => {
        return {
          ...item,
          dataDetails: dataDetailMap[item.id]?.dataDetails,
          files: fileMap[item.id]?.files || [],
        };
      }) || []
    );
  }

  async getListForComboBox(request: GetListChecksheetRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.checksheetRepository.getListForComboBox(
      request,
    );

    const dataMap = await this.mapChecksheetDetailToResponse(data);
    const dataMapUser = await this.mapUserInfoToResponse(dataMap);
    const response = plainToInstance(GetListChecksheetResponseDto, dataMapUser);

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async validateChecksheetInUsed(id: number): Promise<boolean> {
    // Validate used in spc-by-qc-type
    const spcByQctypeList =
      await this.masterReferenceService.findAllSpcByQcTypeByChecksheetIds([id]);
    if (!isEmpty(spcByQctypeList)) {
      return true;
    }

    // Validate used in another service
    const isUse =
      await this.masterReferenceService.validateDataMasterUseAnotherService(
        new ValidateMasterRequestDto('checksheet_id', id),
      );
    if (isUse) {
      return true;
    }

    return false;
  }
}
