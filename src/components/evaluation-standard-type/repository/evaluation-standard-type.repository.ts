import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { isDateString } from 'class-validator';
import { StatusEnum } from '../../../common/enums/status.enum';
import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { escapeCharForSearch } from '../../../utils/common';
import { EvaluationStandardTypeEntity } from '../entities/evaluation-standard-type.entity';
import { GetDetailEvaluationStandardTypeRequestDto } from '../request/get-detail-evaluation-standard-type.request.dto';
import { GetListEvaluationStandardTypeRequestDto } from '../request/get-list-evaluation-standard-type.request.dto';
import { UpdateEvaluationStandardTypeRequestDto } from '../request/update-evaluation-standard-type.request.dto';
import { UpdateStatusEvaluationStandardTypeRequestDto } from '../request/update-status-evaluation-standard-type.request.dto';
import { CreateEvaluationStandardTypeRequestDto } from './../request/create-evaluation-standard-type.request.dto';

@Injectable()
export class EvaluationStandardTypeRepository extends BaseAbstractRepository<EvaluationStandardTypeEntity> {
  constructor(
    @InjectRepository(EvaluationStandardTypeEntity)
    private readonly evaluationStandardTypeRepository: Repository<EvaluationStandardTypeEntity>,
  ) {
    super(evaluationStandardTypeRepository);
  }

  createEntity(
    request: CreateEvaluationStandardTypeRequestDto,
  ): EvaluationStandardTypeEntity {
    const evaluationStandardTypeEntity = new EvaluationStandardTypeEntity();
    evaluationStandardTypeEntity.code = request.code;
    evaluationStandardTypeEntity.name = request.name;
    evaluationStandardTypeEntity.description = request.description;
    evaluationStandardTypeEntity.status = StatusEnum.ACTIVE;
    evaluationStandardTypeEntity.createdBy = request.userId;
    evaluationStandardTypeEntity.updatedBy = request.userId;

    return evaluationStandardTypeEntity;
  }

  updateEntity(
    request: UpdateEvaluationStandardTypeRequestDto,
    entity: EvaluationStandardTypeEntity,
  ): EvaluationStandardTypeEntity {
    entity.name = request?.name;
    entity.description = request?.description;
    entity.updatedBy = request?.userId;

    return entity;
  }

  async getList(
    request: GetListEvaluationStandardTypeRequestDto,
  ): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.evaluationStandardTypeRepository.createQueryBuilder(
      'evaluationstandardtype',
    );

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("evaluationstandardtype"."code") like lower(:code) escape '\\' OR
              lower("evaluationstandardtype"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('evaluationstandardtype.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'code':
            query.andWhere(
              `lower("evaluationstandardtype"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'name':
            query.andWhere(
              `lower("evaluationstandardtype"."name") like lower(:name) escape '\\'`,
              {
                name: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'description':
            query.andWhere(
              `lower("evaluationstandardtype"."description") like lower(:description) escape '\\'`,
              {
                description: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'status':
            query.andWhere('"evaluationstandardtype"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'createdById':
            query.andWhere(
              '"evaluationstandardtype"."created_by" = :createdById',
              {
                createdById: Number(value),
              },
            );
            break;
          case 'createdByIds':
            query.andWhere(
              '"evaluationstandardtype"."created_by" IN (:...createdByIds)',
              {
                createdByIds: value.split(',').map((id) => Number(id)),
              },
            );
            break;
          case 'createdAt':
            const createFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const createTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"evaluationstandardtype"."created_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: createFrom,
                dateTo: createTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"evaluationstandardtype"."updated_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: updateFrom,
                dateTo: upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'name':
            query.addOrderBy('"evaluationstandardtype"."name"', order);
            break;
          case 'code':
            query.addOrderBy('"evaluationstandardtype"."code"', order);
            break;
          case 'status':
            query.addOrderBy('"evaluationstandardtype"."status"', order);
            break;
          case 'description':
            query.addOrderBy('"evaluationstandardtype"."description"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"evaluationstandardtype"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"evaluationstandardtype"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"evaluationstandardtype"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('evaluationstandardtype.id', 'DESC');
    }

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const [data, count] = await query.getManyAndCount();

    return { data, count };
  }

  async getDetail(
    request: GetDetailEvaluationStandardTypeRequestDto,
  ): Promise<EvaluationStandardTypeEntity> {
    const { id } = request;

    const data = await this.evaluationStandardTypeRepository.findOne({
      where: { id: id },
    });

    return data;
  }

  async active(
    request: UpdateStatusEvaluationStandardTypeRequestDto,
  ): Promise<any> {
    const { ids } = request;
    const data = await this.evaluationStandardTypeRepository
      .createQueryBuilder()
      .update(EvaluationStandardTypeEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(
    request: UpdateStatusEvaluationStandardTypeRequestDto,
  ): Promise<any> {
    const { ids } = request;
    const data = await this.evaluationStandardTypeRepository
      .createQueryBuilder()
      .update(EvaluationStandardTypeEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }
}
