import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';
import { DataSource } from 'typeorm';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { EvaluationStandardTypeEntity } from '../entities/evaluation-standard-type.entity';
import { CreateEvaluationStandardTypeRequestDto } from './../request/create-evaluation-standard-type.request.dto';
import { DeleteEvaluationStandardTypeRequestDto } from './../request/delete-evaluation-standard-type.request.dto';
import { GetDetailEvaluationStandardTypeRequestDto } from './../request/get-detail-evaluation-standard-type.request.dto';
import { GetListEvaluationStandardTypeRequestDto } from './../request/get-list-evaluation-standard-type.request.dto';
import { UpdateEvaluationStandardTypeRequestDto } from './../request/update-evaluation-standard-type.request.dto';
import { UpdateStatusEvaluationStandardTypeRequestDto } from './../request/update-status-evaluation-standard-type.request.dto';

import { GetDetailEvaluationStandardTypeResponseDto } from './../response/get-detail-evaluation-standard-type.response.dto';
import { GetListEvaluationStandardTypeResponseDto } from './../response/get-list-evaluation-standard-type.response.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { BaseService } from '../../../common/service/base.service';
import { UserService } from '../../another-service/services/user-service';
import { MasterDataReferenceService } from '../../master-data-reference/service/master-data-reference.service';
import { EvaluationStandardTypeRepository } from '../repository/evaluation-standard-type.repository';

@Injectable()
export class EvaluationStandardTypeService extends BaseService {
  private readonly logger = new Logger(EvaluationStandardTypeService.name);

  constructor(
    private readonly evaluationStandardTypeRepository: EvaluationStandardTypeRepository,

    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    protected readonly masterDataReferenceService: MasterDataReferenceService,
  ) {
    super(userService);
  }

  async create(request: CreateEvaluationStandardTypeRequestDto): Promise<any> {
    const existCode = await this.evaluationStandardTypeRepository.findOneByCode(
      request.code,
    );
    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    const evaluationStandardTypeEntity =
      this.evaluationStandardTypeRepository.createEntity(request);
    const evaluationStandardType =
      await this.evaluationStandardTypeRepository.create(
        evaluationStandardTypeEntity,
      );

    const response = plainToInstance(
      GetDetailEvaluationStandardTypeResponseDto,
      evaluationStandardType,
    );

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(
    request: GetListEvaluationStandardTypeRequestDto,
  ): Promise<any> {
    const { page } = request;
    const { data, count } = await this.evaluationStandardTypeRepository.getList(
      request,
    );

    const dataMapUser = await this.mapUserInfoToResponse(data);
    const response = plainToInstance(
      GetListEvaluationStandardTypeResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(
    request: GetDetailEvaluationStandardTypeRequestDto,
  ): Promise<any> {
    const evaluationStandardType =
      await this.evaluationStandardTypeRepository.getDetail(request);
    if (isEmpty(evaluationStandardType)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMapUser = await this.mapUserInfoToResponse([
      evaluationStandardType,
    ]);
    const response = plainToInstance(
      GetDetailEvaluationStandardTypeResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateEvaluationStandardTypeRequestDto): Promise<any> {
    const { id } = request;
    const evaluationStandardType =
      await this.evaluationStandardTypeRepository.findOneById(id);

    if (isEmpty(evaluationStandardType)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate evaluation-standard-type in used
    const usedList = await this.validateEvaluationStandardTypeInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.EVALUATION_STANDARD_TYPE_IN_USED'),
        )
        .build();
    }

    // Validate Code unique
    const existCode = await this.evaluationStandardTypeRepository.findOneByCode(
      request.code,
    );
    if (!isEmpty(existCode) && existCode.id !== evaluationStandardType.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    const dataUpdate = this.evaluationStandardTypeRepository.updateEntity(
      request,
      evaluationStandardType,
    );
    const data = await this.evaluationStandardTypeRepository.update(dataUpdate);

    const response = plainToInstance(
      GetDetailEvaluationStandardTypeResponseDto,
      data,
    );

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteEvaluationStandardTypeRequestDto): Promise<any> {
    const { id } = request;
    const evaluationStandardType =
      await this.evaluationStandardTypeRepository.findOneById(id);

    if (isEmpty(evaluationStandardType)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate evaluation-standard-type in used
    const usedList = await this.validateEvaluationStandardTypeInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.EVALUATION_STANDARD_TYPE_IN_USED'),
        )
        .build();
    }

    await this.evaluationStandardTypeRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(
    request: UpdateStatusEvaluationStandardTypeRequestDto,
  ): Promise<any> {
    const { ids } = request;

    const listExitsInDB =
      await this.evaluationStandardTypeRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.evaluationStandardTypeRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(
    request: UpdateStatusEvaluationStandardTypeRequestDto,
  ): Promise<any> {
    const { ids } = request;

    const listExitsInDB =
      await this.evaluationStandardTypeRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.evaluationStandardTypeRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async validateEvaluationStandardTypeInUsed(id: number): Promise<boolean> {
    // Validate used in checksheet
    const checksheetList =
      await this.masterDataReferenceService.findAllChecksheetByEvaluationStandardTypeIds(
        [id],
      );
    if (!isEmpty(checksheetList)) {
      return true;
    }

    // Validate used in another service
    const isUse =
      await this.masterDataReferenceService.validateDataMasterUseAnotherService(
        new ValidateMasterRequestDto('evaluation_standard_type_id', id),
      );
    if (isUse) {
      return true;
    }

    return false;
  }

  async findOneById(id: number): Promise<EvaluationStandardTypeEntity> {
    return await this.evaluationStandardTypeRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<EvaluationStandardTypeEntity[]> {
    return await this.evaluationStandardTypeRepository.findAllByIds(ids);
  }
}
