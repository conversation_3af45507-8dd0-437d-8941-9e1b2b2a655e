import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  ACTIVE_EVALUATION_STANDARD_TYPE_PERMISSION,
  CREATE_EVALUATION_STANDARD_TYPE_PERMISSION,
  DELETE_EVALUATION_STANDARD_TYPE_PERMISSION,
  DETAIL_EVALUATION_STANDARD_TYPE_PERMISSION,
  INACTIVE_EVALUATION_STANDARD_TYPE_PERMISSION,
  LIST_EVALUATION_STANDARD_TYPE_PERMISSION,
  UPDATE_EVALUATION_STANDARD_TYPE_PERMISSION,
} from '../../../utils/permissions/evaluation-standard-type.permission';
import { CreateEvaluationStandardTypeRequestDto } from '../request/create-evaluation-standard-type.request.dto';
import { DeleteEvaluationStandardTypeRequestDto } from '../request/delete-evaluation-standard-type.request.dto';
import { GetDetailEvaluationStandardTypeRequestDto } from '../request/get-detail-evaluation-standard-type.request.dto';
import { GetListEvaluationStandardTypeRequestDto } from '../request/get-list-evaluation-standard-type.request.dto';
import { UpdateEvaluationStandardTypeRequestDto } from '../request/update-evaluation-standard-type.request.dto';
import { UpdateStatusEvaluationStandardTypeRequestDto } from '../request/update-status-evaluation-standard-type.request.dto';
import { EvaluationStandardTypeService } from '../service/evaluation-standard-type.service';

@Controller('evaluation-standard-types')
export class EvaluationStandardTypeController {
  constructor(
    private readonly evaluationStandardTypeService: EvaluationStandardTypeService,
  ) {}

  @PermissionCode(DETAIL_EVALUATION_STANDARD_TYPE_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Evaluation-standard-types'],
    summary: 'Chi tiết Evaluation-standard-types',
    description: 'Chi tiết Evaluation-standard-types',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailEvaluationStandardTypeRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.evaluationStandardTypeService.getDetail(request);
  }

  @PermissionCode(LIST_EVALUATION_STANDARD_TYPE_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Evaluation-standard-types'],
    summary: 'Danh sách Evaluation-standard-types',
    description: 'Danh sách Evaluation-standard-types',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListEvaluationStandardTypeRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.evaluationStandardTypeService.getList(request);
  }

  @PermissionCode(CREATE_EVALUATION_STANDARD_TYPE_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Evaluation-standard-types'],
    summary: 'Tạo Evaluation-standard-types mới',
    description: 'Tạo Evaluation-standard-types mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateEvaluationStandardTypeRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.evaluationStandardTypeService.create(request);
  }

  @PermissionCode(UPDATE_EVALUATION_STANDARD_TYPE_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Evaluation-standard-types'],
    summary: 'Cập nhật Evaluation-standard-types',
    description: 'Cập nhật Evaluation-standard-types',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateEvaluationStandardTypeRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.evaluationStandardTypeService.update(request);
  }

  @PermissionCode(ACTIVE_EVALUATION_STANDARD_TYPE_PERMISSION.code)
  @Put('/active')
  @ApiOperation({
    tags: ['Evaluation-standard-types'],
    summary: 'Cập nhật Evaluation-standard-types Status Active',
    description: 'Cập nhật Evaluation-standard-types Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async active(
    @Body() body: UpdateStatusEvaluationStandardTypeRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.evaluationStandardTypeService.active(request);
  }

  @PermissionCode(INACTIVE_EVALUATION_STANDARD_TYPE_PERMISSION.code)
  @Put('/inactive')
  @ApiOperation({
    tags: ['Evaluation-standard-types'],
    summary: 'Cập nhật Evaluation-standard-types Status Inactive',
    description: 'Cập nhật Evaluation-standard-types Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async inactive(
    @Body() body: UpdateStatusEvaluationStandardTypeRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.evaluationStandardTypeService.inactive(request);
  }

  @PermissionCode(DELETE_EVALUATION_STANDARD_TYPE_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Evaluation-standard-types'],
    summary: 'Xóa Evaluation-standard-types',
    description: 'Xóa Evaluation-standard-types',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(
    @Param() param: DeleteEvaluationStandardTypeRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.evaluationStandardTypeService.delete(request);
  }
}
