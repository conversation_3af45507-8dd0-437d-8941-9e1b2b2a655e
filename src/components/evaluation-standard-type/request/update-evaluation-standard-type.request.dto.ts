import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt } from 'class-validator';
import { CreateEvaluationStandardTypeRequestDto } from './create-evaluation-standard-type.request.dto';

export class UpdateEvaluationStandardTypeRequestDto extends CreateEvaluationStandardTypeRequestDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;
}
