import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { EvaluationStandardTypeController } from './controller/evaluation-standard-type.controller';
import { EvaluationStandardTypeEntity } from './entities/evaluation-standard-type.entity';
import { EvaluationStandardTypeRepository } from './repository/evaluation-standard-type.repository';
import { EvaluationStandardTypeService } from './service/evaluation-standard-type.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([EvaluationStandardTypeEntity]),
    AnotherServiceModule,
    MasterDataReferenceModule,
  ],
  providers: [EvaluationStandardTypeService, EvaluationStandardTypeRepository],
  exports: [EvaluationStandardTypeService],
  controllers: [EvaluationStandardTypeController],
})
export class EvaluationStandardTypeModule {}
