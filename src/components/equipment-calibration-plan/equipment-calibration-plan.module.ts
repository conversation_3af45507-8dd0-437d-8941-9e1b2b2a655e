import { Module } from '@nestjs/common';
import { FileService } from '../../core/components/file/file.service';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { EquipmentCalibrationPlanController } from './controller/equipment-calibration-plan.controller';
import { EquipmentCalibrationPlanService } from './service/equipment-calibration-plan.service';

@Module({
  imports: [AnotherServiceModule, BaseProcessModule],
  providers: [
    EquipmentCalibrationPlanService,
    {
      provide: 'FileServiceInterface',
      useClass: FileService,
    },
  ],
  exports: [EquipmentCalibrationPlanService],
  controllers: [EquipmentCalibrationPlanController],
})
export class EquipmentCalibrationPlanModule {}
