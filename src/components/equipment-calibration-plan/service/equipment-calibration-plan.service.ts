import { Inject, Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { DeleteEquipmentCalibrationPlanRequestDto } from '../request/delete-equipment-calibration-plan.request.dto';
import { GetDetailEquipmentCalibrationPlanRequestDto } from '../request/get-detail-equipment-calibration-plan.request.dto';
import { GetListEquipmentCalibrationPlanRequestDto } from '../request/get-list-equipment-calibration-plan.request.dto';
import { UpdateEquipmentCalibrationPlanFormDto } from '../request/update-equipment-calibration-plan-form.request.dto';
import { UpdateStatusEquipmentCalibrationPlanRequestDto } from '../request/update-status-equipment-calibration-plan.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { FileResource } from '../../../core/components/file/constant/file-upload.constant';
import { FileService } from '../../../core/components/file/file.service';
import {
  FileDelete,
  FileUpload,
} from '../../../core/dto/request/file.request.dto';
import { QmsxPlanService } from '../../another-service/services/qmsx-plan-service';
import { QmsxTicketService } from '../../another-service/services/qmsx-ticket-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { FileRequestDto } from '../../common/request/file.request.dto';
import {
  EQUIPMENT_CALIBRATION_TICKET_STATUS_ENUM,
  EQUIPMENT_CALIBRATION_TYPE_ENUM,
} from '../../equipment-calibration-ticket/enums/equipment-calibration-ticket-status.enum';
import { CreateEquipmentCalibrationTicketsDto } from '../../equipment-calibration-ticket/request/create-equipment-calibration-ticket.request.dto';
import { CreateEquipmentCalibrationPlansDto } from '../request/create-equipment-calibration-plan.request.dto';
import { UpdateEquipmentCalibrationPlanDto } from '../request/update-equipment-calibration-plan.request.dto';

@Injectable()
export class EquipmentCalibrationPlanService {
  private readonly logger = new Logger(EquipmentCalibrationPlanService.name);

  constructor(
    private readonly i18n: I18nService,

    @Inject('FileServiceInterface')
    private readonly fileService: FileService,

    private readonly qmsxPlanService: QmsxPlanService,

    private readonly qmsxTicketService: QmsxTicketService,

    private readonly baseService: BaseProcessService,
  ) {}

  async validateEquipmentCalibrationPlan(data: any) {
    const keyDup = ['equipmentId'];
    if (
      this.baseService.checkDuplicateByKey(
        data.equipmentCalibrationPlanDetails,
        keyDup,
      )
    ) {
      return {
        result: false,
        messageError: 'error.EQUIPMENT_CALIBRATION_PLAN_DETAIL_DUPLICATE_KEY',
      };
    }

    const { itemIds, checksheetIds } =
      data.equipmentCalibrationPlanDetails.reduce(
        (result, item) => {
          if (item.itemId !== null && item.itemId !== undefined) {
            result.itemIds.add(item.itemId);
          }

          if (item.checksheetId !== null && item.checksheetId !== undefined) {
            result.checksheetIds.add(item.checksheetId);
          }
          return result;
        },
        {
          itemIds: new Set<number>(),
          checksheetIds: new Set<number>(),
        },
      );

    return await this.baseService.validateMaster({
      itemIds: Array.from(itemIds),
      checksheetIds: Array.from(checksheetIds),
    });
  }

  async handleFile(uploadFiles: FileUpload[], deleteFiles: FileDelete[]) {
    const savedFileResponse = await this.fileService.handleSaveFiles({
      resource: FileResource.QMSX_PLAN,
      deleteFiles: deleteFiles,
      uploadFiles: uploadFiles,
    });

    if (savedFileResponse.statusCode !== ResponseCodeEnum.SUCCESS) {
      return savedFileResponse;
    }

    const filesResponse = await this.fileService.getFilesByIds(
      savedFileResponse.data,
    );

    return filesResponse?.map((res) => {
      const fileRequestDto = new FileRequestDto();
      fileRequestDto.fileId = res.id;
      fileRequestDto.fileName = res.fileNameRaw;
      fileRequestDto.fileUrl = res.fileUrl;
      return fileRequestDto;
    });
  }

  async createTicket(
    request: GetDetailEquipmentCalibrationPlanRequestDto,
  ): Promise<any> {
    const equipmentCalibrationPlan = await this.getDetail(request);

    if (
      isEmpty(equipmentCalibrationPlan) ||
      isEmpty(equipmentCalibrationPlan.data) ||
      isEmpty(equipmentCalibrationPlan?.data.equipmentCalibrationPlanDetail) ||
      equipmentCalibrationPlan.statusCode != ResponseCodeEnum.SUCCESS
    ) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const equipmentCalibrationPlanDetailData =
      equipmentCalibrationPlan.data.equipmentCalibrationPlanDetail;

    const ticketData = await Promise.all(
      equipmentCalibrationPlanDetailData.map(async (detailData) =>
        this.mapPlanDataToTicketDetail(
          detailData,
          equipmentCalibrationPlan.data,
        ),
      ),
    );

    await this.qmsxTicketService.createEquipmentCalibrationTicket(ticketData);

    return new ResponseBuilder(ticketData)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  private async mapPlanDataToTicketDetail(
    equipmentCalibrationPlanDetail: any,
    equipmentCalibrationPlan: any,
  ): Promise<any> {
    return plainToInstance(CreateEquipmentCalibrationTicketsDto, {
      status: EQUIPMENT_CALIBRATION_TICKET_STATUS_ENUM.PENDING,
      equipmentId: equipmentCalibrationPlanDetail.equipmentId,
      equipmentName: equipmentCalibrationPlanDetail.equipmentName,
      equipmentCode: equipmentCalibrationPlanDetail.equipmentCode,
      lastCalibrationDate: equipmentCalibrationPlanDetail.lastCalibrationDate,
      expectedCalibrationDate:
        equipmentCalibrationPlanDetail.expectedCalibrationDate,
      equipmentDetail: equipmentCalibrationPlanDetail.equipmentDetail,
      planDateFrom: equipmentCalibrationPlan.planDateFrom,
      planDateTo: equipmentCalibrationPlan.planDateTo,
      planId: equipmentCalibrationPlan.id,
      planCode: equipmentCalibrationPlan.code,
      planName: equipmentCalibrationPlan.name,
      createdBy:
        equipmentCalibrationPlan.approvedBy?.id ??
        equipmentCalibrationPlan.approvedBy,
      equipmentCalibrationTicketDetails: Object.values(
        EQUIPMENT_CALIBRATION_TYPE_ENUM,
      )
        .filter((value) => typeof value === 'number')
        .map((value) => ({
          calibrationType: value,
        })),
    });
  }

  async create(request: CreateEquipmentCalibrationPlansDto): Promise<any> {
    const equipmentCalibrationPlanExist =
      await this.qmsxPlanService.getDetailQmsxEquipmentCalibrationPlanByCode({
        code: request.code,
      });

    if (!isEmpty(equipmentCalibrationPlanExist)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    const { result, messageError } =
      await this.validateEquipmentCalibrationPlan(request);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }
    const equipmentCalibrationPlan = plainToInstance(
      CreateEquipmentCalibrationPlansDto,
      request,
    );
    equipmentCalibrationPlan.createdBy = request.userId;
    const response = await this.qmsxPlanService.createEquipmentCalibrationPlan(
      equipmentCalibrationPlan,
    );

    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(
    request: GetListEquipmentCalibrationPlanRequestDto,
  ): Promise<any> {
    const { page } = request;
    const { data, count } =
      await this.qmsxPlanService.getListQmsxEquipmentCalibrationRequest(
        request,
      );

    const dataMapUser = await this.baseService.mapMasterInfoToResponse(data);

    return new ResponseBuilder<PaginationResponse>({
      items: dataMapUser,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(
    request: GetDetailEquipmentCalibrationPlanRequestDto,
  ): Promise<any> {
    const equipmentCalibrationPlan =
      await this.qmsxPlanService.getDetailQmsxEquipmentCalibrationPlanById(
        request,
      );

    if (isEmpty(equipmentCalibrationPlan)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMapUser = await this.baseService.mapMasterInfoToResponse([
      equipmentCalibrationPlan,
    ]);
    const details = await this.baseService.mapMasterInfoToResponse(
      equipmentCalibrationPlan.equipmentCalibrationPlanDetail,
    );
    dataMapUser.forEach((s) => {
      s.equipmentCalibrationPlanDetail = details;
    });
    return new ResponseBuilder(dataMapUser[0] ? dataMapUser[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateEquipmentCalibrationPlanFormDto): Promise<any> {
    const { id } = request;

    const equipmentCalibrationPlan =
      await this.qmsxPlanService.getDetailQmsxEquipmentCalibrationPlanById({
        id: id,
      });

    if (isEmpty(equipmentCalibrationPlan)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const { result, messageError } =
      await this.validateEquipmentCalibrationPlan(request);

    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate(messageError))
        .build();
    }
    const equipmentCalibrationPlanUpdate = plainToInstance(
      UpdateEquipmentCalibrationPlanDto,
      request,
    );
    equipmentCalibrationPlanUpdate.id = id;
    equipmentCalibrationPlanUpdate.updatedBy = request.userId;
    const response = await this.qmsxPlanService.updateEquipmentCalibrationPlan(
      equipmentCalibrationPlanUpdate,
    );
    if (isEmpty(response)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .build();
    }
    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(
    request: DeleteEquipmentCalibrationPlanRequestDto,
  ): Promise<any> {
    const equipmentCalibrationPlan =
      await this.qmsxPlanService.getDetailQmsxEquipmentCalibrationPlanById(
        request,
      );

    if (isEmpty(equipmentCalibrationPlan)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const { result, messageError } =
      await this.qmsxPlanService.deleteEquipmentCalibration(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    await this.fileService.handleSaveFiles({
      resource: FileResource.QMSX_PLAN,
      deleteFiles: equipmentCalibrationPlan.files,
      uploadFiles: [],
    });
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async updateStatus(
    request: UpdateStatusEquipmentCalibrationPlanRequestDto,
  ): Promise<any> {
    const { result, messageError } =
      await this.qmsxPlanService.updateStatusEquipmentCalibration(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async approve(
    request: GetDetailEquipmentCalibrationPlanRequestDto,
  ): Promise<any> {
    const equipmentCalibrationPlan =
      await this.qmsxPlanService.getDetailQmsxEquipmentCalibrationPlanById(
        request,
      );

    if (isEmpty(equipmentCalibrationPlan)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }
    const equipmentCodes = [];
    // validate equipment
    for (const detail of equipmentCalibrationPlan.equipmentCalibrationPlanDetail) {
      equipmentCodes.push(detail.equipmentCode);
      const checkCalibrationOverlapResult =
        await this.qmsxTicketService.checkEquipmentCalibrationOverlap({
          equipmentCode: detail.equipmentCode,
          lastCalibrationDate: detail.lastCalibrationDate,
          expectedCalibrationDate: detail.expectedCalibrationDate,
        });
      if (checkCalibrationOverlapResult > 0) {
        return new ResponseBuilder()
          .withCode(ResponseCodeEnum.BAD_REQUEST)
          .withMessage(
            await this.i18n.translate('error.ERROR_CALIBRATION_OVERLAP', {
              args: { code: detail.equipmentCode },
            }),
          )
          .build();
      }
    }

    const existPlan = await this.qmsxPlanService.checkCalibrationOverlap({
      equipmentCodes,
      planFrom: equipmentCalibrationPlan?.planDateFrom,
      planTo: equipmentCalibrationPlan?.planDateTo,
      planId: equipmentCalibrationPlan?.id,
    });

    if (existPlan.length > 0) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate(
            'error.ERROR_CALIBRATION_OVERLAP_DEVICE_PLAN',
            {
              args: {
                equipmentCode: existPlan?.[0]?.equipmentCode,
                planCode: existPlan?.[0]?.planCode,
              },
            },
          ),
        )
        .build();
    }

    const { result, messageError } =
      await this.qmsxPlanService.approveEquipmentCalibration(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }

    const { id } = request;

    const planRequest = new GetDetailEquipmentCalibrationPlanRequestDto();
    planRequest.id = id;
    await this.createTicket(planRequest);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async reject(
    request: GetDetailEquipmentCalibrationPlanRequestDto,
  ): Promise<any> {
    const { result, messageError } =
      await this.qmsxPlanService.rejectEquipmentCalibration(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async cancel(
    request: GetDetailEquipmentCalibrationPlanRequestDto,
  ): Promise<any> {
    const { result, messageError } =
      await this.qmsxPlanService.cancelEquipmentCalibrationPlan(request);
    if (!result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(messageError)
        .build();
    }

    await this.qmsxTicketService.cancelEquipmentCalibrationTicketByPlan(
      request,
    );

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }
}
