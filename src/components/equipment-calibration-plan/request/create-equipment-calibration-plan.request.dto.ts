import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsDateString,
  IsEnum,
  IsNumber,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { BaseDto } from '../../../core/dto/base.dto';
import { EQUIPMENT_CALIBRATION_PLAN_STATUS_ENUM } from '../enums/equipment-calibration-plan-status.enum';

export class CreateEquipmentCalibrationPlanDetailDto {
  @IsOptional()
  equipmentCalibrationPlanId: number;

  @ApiProperty()
  @IsOptional()
  equipmentId: number;

  @ApiProperty()
  @IsOptional()
  equipmentName: string;

  @ApiProperty()
  @IsOptional()
  equipmentCode: string;

  @ApiProperty()
  @IsOptional()
  lastCalibrationDate: Date;

  @ApiProperty()
  @IsOptional()
  expectedCalibrationDate: Date;

  @ApiProperty()
  @IsOptional()
  dDay: number;

  @ApiProperty()
  @IsOptional()
  equipmentDetail: string;
}

export class CreateEquipmentCalibrationPlansDto extends BaseDto {
  @ApiProperty()
  @IsOptional()
  code: string;

  @ApiProperty()
  @IsOptional()
  name: string;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  planDateFrom: Date;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  planDateTo: Date;

  @IsOptional()
  @IsNumber()
  createdBy: number;

  @IsOptional()
  @IsNumber()
  approvedBy: number;

  @IsOptional()
  @IsEnum(EQUIPMENT_CALIBRATION_PLAN_STATUS_ENUM)
  status: EQUIPMENT_CALIBRATION_PLAN_STATUS_ENUM;

  @ApiProperty()
  @IsOptional()
  description: string;

  @ApiProperty({ type: [CreateEquipmentCalibrationPlanDetailDto] })
  @ValidateNested({ each: true })
  @ArrayNotEmpty()
  @Type(() => CreateEquipmentCalibrationPlanDetailDto)
  equipmentCalibrationPlanDetails: CreateEquipmentCalibrationPlanDetailDto[];
}
