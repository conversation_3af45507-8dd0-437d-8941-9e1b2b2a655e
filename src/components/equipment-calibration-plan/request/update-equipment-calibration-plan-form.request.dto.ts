import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt } from 'class-validator';
import { CreateEquipmentCalibrationPlansDto } from './create-equipment-calibration-plan.request.dto';

export class UpdateEquipmentCalibrationPlanFormDto extends CreateEquipmentCalibrationPlansDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;
}
