import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { CreateEquipmentCalibrationPlansDto } from './create-equipment-calibration-plan.request.dto';

export class UpdateEquipmentCalibrationPlanDto extends CreateEquipmentCalibrationPlansDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsInt()
  id?: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  updatedBy?: number;
}
