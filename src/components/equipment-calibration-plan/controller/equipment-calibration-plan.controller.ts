import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  APPROVE_EQUIPMENT_CALIBRATION_PLAN_PERMISSION,
  CANCEL_EQUIPMENT_CALIBRATION_PLAN_PERMISSION,
  CREATE_EQUIPMENT_CALIBRATION_PLAN_PERMISSION,
  DELETE_EQUIPMENT_CALIBRATION_PLAN_PERMISSION,
  DETAIL_EQUIPMENT_CALIBRATION_PLAN_PERMISSION,
  LIST_EQUIPMENT_CALIBRATION_PLAN_PERMISSION,
  REJECT_EQUIPMENT_CALIBRATION_PLAN_PERMISSION,
  UPDATE_EQUIPMENT_CALIBRATION_PLAN_PERMISSION,
  UPDATE_STATUS_EQUIPMENT_CALIBRATION_PLAN_PERMISSION,
} from '../../../utils/permissions/equipment-calibration-plan.permission';
import { CreateEquipmentCalibrationPlansDto } from '../request/create-equipment-calibration-plan.request.dto';
import { DeleteEquipmentCalibrationPlanRequestDto } from '../request/delete-equipment-calibration-plan.request.dto';
import { GetDetailEquipmentCalibrationPlanRequestDto } from '../request/get-detail-equipment-calibration-plan.request.dto';
import { GetListEquipmentCalibrationPlanRequestDto } from '../request/get-list-equipment-calibration-plan.request.dto';
import { UpdateEquipmentCalibrationPlanFormDto } from '../request/update-equipment-calibration-plan-form.request.dto';
import { UpdateStatusEquipmentCalibrationPlanRequestDto } from '../request/update-status-equipment-calibration-plan.request.dto';
import { EquipmentCalibrationPlanService } from '../service/equipment-calibration-plan.service';

@Controller('equipment-calibration-plans')
export class EquipmentCalibrationPlanController {
  constructor(
    private readonly equipmentCalibrationPlanService: EquipmentCalibrationPlanService,
  ) {}

  @PermissionCode(DETAIL_EQUIPMENT_CALIBRATION_PLAN_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Equipment-calibration-plans'],
    summary: 'Chi tiết Equipment-calibration-plans',
    description: 'Chi tiết Equipment-calibration-plans',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailEquipmentCalibrationPlanRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.equipmentCalibrationPlanService.getDetail(request);
  }

  @PermissionCode(LIST_EQUIPMENT_CALIBRATION_PLAN_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Equipment-calibration-plans'],
    summary: 'Danh sách Equipment-calibration-plans',
    description: 'Danh sách Equipment-calibration-plans',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(
    @Query() query: GetListEquipmentCalibrationPlanRequestDto,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.equipmentCalibrationPlanService.getList(request);
  }

  @PermissionCode(CREATE_EQUIPMENT_CALIBRATION_PLAN_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Equipment-calibration-plans'],
    summary: 'Tạo Equipment-calibration-plans mới',
    description: 'Tạo Equipment-calibration-plans mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateEquipmentCalibrationPlansDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.equipmentCalibrationPlanService.create(request);
  }

  @PermissionCode(UPDATE_EQUIPMENT_CALIBRATION_PLAN_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Equipment-calibration-plans'],
    summary: 'Cập nhật Equipment-calibration-plans',
    description: 'Cập nhật Equipment-calibration-plans',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(
    @Body() body: UpdateEquipmentCalibrationPlanFormDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.equipmentCalibrationPlanService.update(request);
  }

  @PermissionCode(UPDATE_STATUS_EQUIPMENT_CALIBRATION_PLAN_PERMISSION.code)
  @Put('/updateStatus')
  @ApiOperation({
    tags: ['Equipment-calibration-plans'],
    summary: 'Cập nhật Equipment-calibration-plans Status',
    description: 'Cập nhật Equipment-calibration-plans Status',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async updateStatus(
    @Body() body: UpdateStatusEquipmentCalibrationPlanRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.equipmentCalibrationPlanService.updateStatus(request);
  }

  @PermissionCode(APPROVE_EQUIPMENT_CALIBRATION_PLAN_PERMISSION.code)
  @Put('/approved')
  @ApiOperation({
    tags: ['Equipment-calibration-plans'],
    summary: 'Approved Equipment-calibration-plans',
    description: 'Approved Equipment-calibration-plans',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async approve(
    @Body() body: GetDetailEquipmentCalibrationPlanRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.equipmentCalibrationPlanService.approve(request);
  }

  @PermissionCode(REJECT_EQUIPMENT_CALIBRATION_PLAN_PERMISSION.code)
  @Put('/reject')
  @ApiOperation({
    tags: ['Equipment-calibration-plans'],
    summary: 'Rejected Equipment-calibration-plans',
    description: 'Rejected Equipment-calibration-plans',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async reject(
    @Body() body: GetDetailEquipmentCalibrationPlanRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.equipmentCalibrationPlanService.reject(request);
  }

  @PermissionCode(DELETE_EQUIPMENT_CALIBRATION_PLAN_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Equipment-calibration-plans'],
    summary: 'Xóa Equipment-calibration-plans',
    description: 'Xóa Equipment-calibration-plans',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(
    @Param() param: DeleteEquipmentCalibrationPlanRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.equipmentCalibrationPlanService.delete(request);
  }

  @PermissionCode(CANCEL_EQUIPMENT_CALIBRATION_PLAN_PERMISSION.code)
  @Put('/canceled')
  @ApiOperation({
    tags: ['Equipment-calibration-plans'],
    summary: 'Canceled Equipment-calibration-plans',
    description: 'Canceled Equipment-calibration-plans',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async cancel(
    @Body() body: GetDetailEquipmentCalibrationPlanRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.equipmentCalibrationPlanService.cancel(request);
  }
}
