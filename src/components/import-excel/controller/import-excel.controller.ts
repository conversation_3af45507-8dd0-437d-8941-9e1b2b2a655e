import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiConsumes, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';
import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import { IMPORT_CHECKSHEET_PERMISSION } from '../../../utils/permissions/checksheet.permission';
import { IMPORT_CUSTOMER_PERMISSION } from '../../../utils/permissions/customer.permission';
import { IMPORT_ERROR_GROUP_PERMISSION } from '../../../utils/permissions/error-group.permission';
import { IMPORT_ERROR_HANDLING_STATUS_PERMISSION } from '../../../utils/permissions/error-handling-status.permission';
import { IMPORT_ERROR_TYPE_PERMISSION } from '../../../utils/permissions/error-type.permission';
import { IMPORT_ERROR_PERMISSION } from '../../../utils/permissions/error.permission';
import { IMPORT_EVALUATION_STANDARD_TYPE_PERMISSION } from '../../../utils/permissions/evaluation-standard-type.permission';
import { IMPORT_GOODS_TYPE_PERMISSION } from '../../../utils/permissions/goods-type.permission';
import { IMPORT_INSPECTION_GROUP_PERMISSION } from '../../../utils/permissions/inspection-group.permission';
import { IMPORT_INSPECTION_SAMPLE_QUANTITY_PERMISSION } from '../../../utils/permissions/inspection-sample-quantity.permission';
import { IMPORT_INSPECTION_STANDARD_PERMISSION } from '../../../utils/permissions/inspection-standard.permission';
import { IMPORT_INSPECTION_TYPE_PERMISSION } from '../../../utils/permissions/inspection-type.permission';
import { IMPORT_INSPECTION_PERMISSION } from '../../../utils/permissions/inspection.permission';
import { IMPORT_IQC_REQUEST_PERMISSION } from '../../../utils/permissions/iqc-request.permission';
import { IMPORT_ITEM_LINE_PERMISSION } from '../../../utils/permissions/item-line.permission';
import { IMPORT_ITEM_TYPE_PERMISSION } from '../../../utils/permissions/item-type.permission';
import { IMPORT_ITEM_PERMISSION } from '../../../utils/permissions/item.permission';
import { IMPORT_P_CODE_PERMISSION } from '../../../utils/permissions/p-code.permission';
import { IMPORT_PROCESS_PERMISSION } from '../../../utils/permissions/process.permission';
import { IMPORT_PRODUCT_PROCESS_PERMISSION } from '../../../utils/permissions/product-process.permission';
import { IMPORT_QC_REQUEST_TYPE_PERMISSION } from '../../../utils/permissions/qc-request-type.permission';
import { IMPORT_SAMPLING_RATE_PERMISSION } from '../../../utils/permissions/sampling-rate.permission';
import { IMPORT_UNIT_PERMISSION } from '../../../utils/permissions/unit.permission';
import { IMPORT_VENDOR_PERMISSION } from '../../../utils/permissions/vendor';
import { GetImportExcelTemplateRequest } from '../request/get-import-excel-template.request.dto';
import { ImportExcelRequest } from '../request/import-excel.request.dto';
import { ImportExcelService } from '../service/import-excel.service';

@Controller('import-excels')
export class ImportExcelController {
  constructor(private readonly importExcelService: ImportExcelService) {}
  @Post('')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async import(@Body() payload: ImportExcelRequest): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_IQC_REQUEST_PERMISSION.code)
  @Post('/iqc-request')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importIqcRequest(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_PRODUCT_PROCESS_PERMISSION.code)
  @Post('/production-process')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importProductionProcess(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_ERROR_GROUP_PERMISSION.code)
  @Post('/error-group')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importErrorGroup(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_ERROR_TYPE_PERMISSION.code)
  @Post('/error-type')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importErrorType(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_ERROR_PERMISSION.code)
  @Post('/error')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importError(@Body() payload: ImportExcelRequest): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_QC_REQUEST_TYPE_PERMISSION.code)
  @Post('/qc-request-type')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importQcRequestType(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_ITEM_TYPE_PERMISSION.code)
  @Post('/item-type')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importItemType(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_ITEM_LINE_PERMISSION.code)
  @Post('/item-line')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importItemLine(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_GOODS_TYPE_PERMISSION.code)
  @Post('/goods-type')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importGoodsType(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_UNIT_PERMISSION.code)
  @Post('/unit')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importUnit(@Body() payload: ImportExcelRequest): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_PROCESS_PERMISSION.code)
  @Post('/process')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async imporProcess(@Body() payload: ImportExcelRequest): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_INSPECTION_GROUP_PERMISSION.code)
  @Post('/inspection-group')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importInspectionGroup(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_INSPECTION_TYPE_PERMISSION.code)
  @Post('/inspection-type')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importInspectionType(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_INSPECTION_PERMISSION.code)
  @Post('/inspection')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importInspection(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_CUSTOMER_PERMISSION.code)
  @Post('/customer')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importCustomer(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_VENDOR_PERMISSION.code)
  @Post('/vendor')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importVendor(@Body() payload: ImportExcelRequest): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_SAMPLING_RATE_PERMISSION.code)
  @Post('/sampling-rate')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importSamplingRate(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_INSPECTION_STANDARD_PERMISSION.code)
  @Post('/inspection-standard')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importInspectionStandard(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_ERROR_HANDLING_STATUS_PERMISSION.code)
  @Post('/error-handling-status')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importErrHandlingStatus(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_EVALUATION_STANDARD_TYPE_PERMISSION.code)
  @Post('/evaluation-standard-type')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importEvaluationStandardType(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_CHECKSHEET_PERMISSION.code)
  @Post('/checksheet')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importChecksheet(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_INSPECTION_SAMPLE_QUANTITY_PERMISSION.code)
  @Post('/inspection-sample-quantity')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importInspectionSampleQuantity(
    @Body() payload: ImportExcelRequest,
  ): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_ITEM_PERMISSION.code)
  @Post('/item')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importItem(@Body() payload: ImportExcelRequest): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @PermissionCode(IMPORT_P_CODE_PERMISSION.code)
  @Post('/p-code')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Import Data - Nhập dữ liệu',
    description: 'Import Data - Nhập dữ liệu',
  })
  @ApiResponse({
    status: 200,
    description: 'Post import successfully',
  })
  @ApiConsumes('multipart/form-data')
  public async importPCode(@Body() payload: ImportExcelRequest): Promise<any> {
    const { request, responseError } = payload;
    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return await this.importExcelService.import(request);
  }

  @Get('/download-template')
  @ApiOperation({
    tags: ['Import Data - Nhập dữ liệu'],
    summary: 'Download import template',
    description: 'Download template file for importing data',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getTemplate(
    @Query() query: GetImportExcelTemplateRequest,
  ): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }
    return this.importExcelService.getTemplate(request);
  }
}
