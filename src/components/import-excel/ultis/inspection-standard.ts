import { ValidationError, isEmpty } from 'class-validator';
import {
  buildErrorMessage,
  getDataImport,
  mapEntityCode,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { InspectionStandardEntity } from '../../inspection-standard/entities/inspection-standard.entity';
import { ImportInspectionStandardRequestDto } from '../../inspection-standard/request/import-inspection-standard.request.dto';
import {
  PRE_FIX,
  TABLE_CODE,
  TABLE_NAME,
  TEMPLATE_INSPECTION_STANDARD,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importInspectionStandard(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportInspectionStandardRequestDto,
    columns: [TABLE_CODE.SAMPLING_RATE_CODE],
    prefix: PRE_FIX.INSPECTION_STANDARD,
    sheetName: await i18n.translate('import.screen.inspectionStandard.sheet1'),
    tables: [TABLE_NAME.SAMPLING_RATE],
    templateHeader: TEMPLATE_INSPECTION_STANDARD,
    tableMain: TABLE_NAME.INSPECTION_STANDARD,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );
  await Promise.all(
    dataSheet1.data.map(async (s) => {
      const errors: ValidationError[] = [];
      if (s.isAql !== 0 && s.isAql !== 1) {
        const error = new ValidationError();
        error.property = 'isAql';
        error.constraints = {
          error: await i18n.translate('error.INVALID_VALUE', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.INSPECTION_STANDARD}${error.property}`,
              ),
              number: '0, 1',
            },
          }),
        };
        errors.push(error);
      }
      if (
        s.inpectionStandardType &&
        s.inpectionStandardType !== 0 &&
        s.inpectionStandardType !== 1
      ) {
        const error = new ValidationError();
        error.property = 'inpectionStandardType';
        error.constraints = {
          error: await i18n.translate('error.INVALID_VALUE', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.INSPECTION_STANDARD}${error.property}`,
              ),
              number: '0, 1',
            },
          }),
        };
        errors.push(error);
      }
      if (
        s.isAql === 1 &&
        (s.inpectionStandardType === null ||
          s.inpectionStandardType === undefined)
      ) {
        const error = new ValidationError();
        error.property = 'inpectionStandardType';
        error.constraints = {
          error: await i18n.translate('error.FIELD_REQUIRED', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.INSPECTION_STANDARD}${error.property}`,
              ),
            },
          }),
        };
        errors.push(error);
      }
      if (
        s.isAql === 0 &&
        (s.samplingRateCode === null || s.samplingRateCode === undefined)
      ) {
        const error = new ValidationError();
        error.property = 'samplingRateCode';
        error.constraints = {
          error: await i18n.translate('error.FIELD_REQUIRED', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.INSPECTION_STANDARD}${error.property}`,
              ),
            },
          }),
        };
        errors.push(error);
      }
      if (errors && errors.length > 0) {
        s.error =
          (await buildErrorMessage(errors, PRE_FIX.INSPECTION_STANDARD, i18n)) +
          (isEmpty(s.error) ? '' : '\r\n' + s.error);
      }
      return s;
    }),
  );
  const samplingRateMap = dataSheet1.dataMap.get(TABLE_NAME.SAMPLING_RATE);
  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  const result = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const dataCreate = result.map((x) => {
    const entity = new InspectionStandardEntity();
    entity.code = x.code ? x.code.trim().replace(/\s+/g, '_') : '';
    entity.name = x.name;
    entity.isAql = x.isAql;

    if (x.isAql === 1) {
      entity.inpectionStandardType = x.inpectionStandardType;
      entity.samplingRateId = null;
    } else if (x.isAql === 0) {
      entity.samplingRateId = mapEntityCode(
        x.samplingRateCode,
        samplingRateMap,
      );
      entity.inpectionStandardType = null;
    }

    entity.description = x.description;
    entity.createdBy = userId;

    return entity;
  });

  if (dataCreate || dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();
    try {
      const metadata = connection.getMetadata(InspectionStandardEntity);
      const numberOfParameters = metadata.columns.length;
      const maxEntitiesPerBatch = Math.floor(2000 / numberOfParameters);
      for (let i = 0; i < dataCreate.length; i += maxEntitiesPerBatch) {
        const chunk = dataCreate.slice(i, i + maxEntitiesPerBatch);
        await queryRunner.manager.save(chunk);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // const worksheet = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove = result.map((item) => item.line);
  // if (rowsToRemove.length > 0) {
  //   await removeLineSuccess(worksheet, rowsToRemove);
  // }
  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
