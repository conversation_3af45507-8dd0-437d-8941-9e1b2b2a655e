import { ValidationError, isEmpty } from 'class-validator';
import {
  buildErrorMessage,
  getDataImport,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { QcRequestTypeEntity } from '../../qc-request-type/entities/qc-request-type.entity';
import { ImportQcRequestTypeRequestDto } from '../../qc-request-type/request/import-qc-request-type.request.dto';
import {
  PRE_FIX,
  TABLE_NAME,
  TEMPLATE_QC_REQUEST_TYPE,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importQcRequestType(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportQcRequestTypeRequestDto,
    columns: [],
    prefix: PRE_FIX.QC_REQUEST_TYPE,
    sheetName: await i18n.translate('import.screen.qcRequestType.sheet1'),
    tables: [],
    templateHeader: TEMPLATE_QC_REQUEST_TYPE,
    tableMain: TABLE_NAME.QC_REQUEST_TYPE,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );

  await Promise.all(
    dataSheet1.data.map(async (s) => {
      const errors: ValidationError[] = [];
      if (![0, 1, 2, 3, 4, 5, 6].includes(s.qcType)) {
        const error = new ValidationError();
        error.property = 'qcType';
        error.constraints = {
          error: await i18n.translate('error.INVALID_VALUE', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.QC_REQUEST_TYPE}${error.property}`,
              ),
              number: '0, 1, 2, 3, 4, 5, 6',
            },
          }),
        };
        errors.push(error);
      }
      if (![0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].includes(s.category)) {
        const error = new ValidationError();
        error.property = 'category';
        error.constraints = {
          error: await i18n.translate('error.INVALID_VALUE', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.QC_REQUEST_TYPE}${error.property}`,
              ),
              number: '0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12',
            },
          }),
        };
        errors.push(error);
      }
      if (![0, 1].includes(s.isQcProcess)) {
        const error = new ValidationError();
        error.property = 'isQcProcess';
        error.constraints = {
          error: await i18n.translate('error.INVALID_VALUE', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.QC_REQUEST_TYPE}${error.property}`,
              ),
              number: '0, 1',
            },
          }),
        };
        errors.push(error);
      }
      if (errors && errors.length > 0) {
        s.error =
          (await buildErrorMessage(errors, PRE_FIX.QC_REQUEST_TYPE, i18n)) +
          (isEmpty(s.error) ? '' : '\r\n' + s.error);
      }
      return s;
    }),
  );

  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  const result = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const dataCreate = result.map((x) => {
    const entity = new QcRequestTypeEntity();
    entity.code = x.code ? x.code.trim().replace(/\s+/g, '_') : '';
    entity.name = x.name;
    entity.qcType = x.qcType;
    entity.category = x.category;
    entity.isQcProcess = x.isQcProcess ?? 0;
    entity.description = x.description;
    entity.createdBy = userId;

    return entity;
  });

  if (dataCreate || dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();
    try {
      const metadata = connection.getMetadata(QcRequestTypeEntity);
      const numberOfParameters = metadata.columns.length;
      const maxEntitiesPerBatch = Math.floor(2000 / numberOfParameters);
      for (let i = 0; i < dataCreate.length; i += maxEntitiesPerBatch) {
        const chunk = dataCreate.slice(i, i + maxEntitiesPerBatch);
        await queryRunner.manager.save(chunk);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // const worksheet = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove = result.map((item) => item.line);
  // if (rowsToRemove.length > 0) {
  //   await removeLineSuccess(worksheet, rowsToRemove);
  // }
  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
