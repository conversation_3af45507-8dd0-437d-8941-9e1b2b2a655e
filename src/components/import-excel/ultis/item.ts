import { ValidationError, isEmpty } from 'class-validator';
import { StatusEnum } from '../../../common/enums/status.enum';
import {
  buildErrorMessage,
  getDataImport,
  mapEntityCode,
  validateMappings,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { ItemUnitEntity } from '../../item-unit/entities/item-unit.entity';
import { ItemCustomerEntity } from '../../item/entities/item-customer.entity';
import { ItemEntity } from '../../item/entities/item.entity';
import {
  ImportItemRequestDto,
  ImportItemUnitDto,
} from '../../item/request/import-item.request.dto';
import {
  PRE_FIX,
  TABLE_CODE,
  TABLE_NAME,
  TEMPLATE_ITEM,
  TEMPLATE_ITEM_UNIT,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importItem(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportItemRequestDto,
    columns: [
      TABLE_CODE.ITEM_LINE_CODE,
      TABLE_CODE.GOODS_TYPE_CODE,
      TABLE_CODE.CUSTOMER,
    ],
    prefix: PRE_FIX.ITEM,
    sheetName: await i18n.translate('import.screen.item.sheet1'),
    tables: [TABLE_NAME.ITEM_LINE, TABLE_NAME.GOODS_TYPE, TABLE_NAME.CUSTOMER],
    templateHeader: TEMPLATE_ITEM,
    tableMain: TABLE_NAME.ITEM,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );
  const importInfoRequestSheet2: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportItemUnitDto,
    columns: ['unitCode1', 'unitCode2'],
    prefix: PRE_FIX.ITEM_UNIT,
    sheetName: await i18n.translate('import.screen.item.sheet2'),
    tables: [TABLE_NAME.UNIT],
    templateHeader: TEMPLATE_ITEM_UNIT,
    tableMain: '',
    keys: ['keyMapping'],
  };
  const dataSheet2 = await getDataImport(
    importInfoRequestSheet2,
    i18n,
    baseProcessService,
  );
  // const importInfoRequestSheet3: ImportInfoRequest = {
  //   workbook: workbook,
  //   classType: ImportItemIqcDto,
  //   columns: [
  //     TABLE_CODE.INSPECTION_STANDARD_CODE,
  //     TABLE_CODE.INSPECTION_GROUP_CODE,
  //   ],
  //   prefix: PRE_FIX.ITEM_IQC,
  //   sheetName: await i18n.translate('import.screen.item.sheet3'),
  //   tables: [TABLE_NAME.INSPECTION_STANDARD, TABLE_NAME.INSPECTION_GROUP],
  //   templateHeader: TEMPLATE_ITEM_IQC,
  //   tableMain: '',
  //   keys: ['keyMapping'],
  // };
  // const dataSheet3 = await getDataImport(
  //   importInfoRequestSheet3,
  //   i18n,
  //   baseProcessService,
  // );
  // const importInfoRequestSheet4: ImportInfoRequest = {
  //   workbook: workbook,
  //   classType: ImportItemOqcDto,
  //   columns: [
  //     TABLE_CODE.INSPECTION_STANDARD_CODE,
  //     TABLE_CODE.INSPECTION_GROUP_CODE,
  //   ],
  //   prefix: PRE_FIX.ITEM_OQC,
  //   sheetName: await i18n.translate('import.screen.item.sheet4'),
  //   tables: [TABLE_NAME.INSPECTION_STANDARD, TABLE_NAME.INSPECTION_GROUP],
  //   templateHeader: TEMPLATE_ITEM_OQC,
  //   tableMain: '',
  //   keys: ['keyMapping'],
  // };
  // const dataSheet4 = await getDataImport(
  //   importInfoRequestSheet4,
  //   i18n,
  //   baseProcessService,
  // );

  await Promise.all(
    dataSheet2.data.map(async (unit) => {
      const errors: ValidationError[] = [];
      if (
        !isEmpty(unit.unitCode1) &&
        !isEmpty(unit.unitCode2) &&
        isEmpty(unit.ratio)
      ) {
        const error = new ValidationError();
        error.property = 'ratio';
        error.constraints = {
          error: await i18n.translate('error.RATIO_REQUIRED'),
        };
        errors.push(error);
      }
      if (errors && errors.length > 0) {
        unit.error =
          (await buildErrorMessage(errors, PRE_FIX.ITEM_UNIT, i18n)) +
          (isEmpty(unit.error) ? '' : '\r\n' + unit.error);
      }
      return unit;
    }),
  );

  await validateMappings(
    dataSheet1.data,
    dataSheet2.data,
    PRE_FIX.ITEM,
    importInfoRequestSheet2.sheetName,
    i18n,
  );
  await validateMappings(
    dataSheet2.data,
    dataSheet1.data,
    PRE_FIX.ITEM_UNIT,
    importInfoRequestSheet1.sheetName,
    i18n,
  );

  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  if (dataSheet2.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet2, dataSheet2.data, i18n);
  // if (dataSheet3.data.some((s) => s.error)) {
  //   isError = true;
  // }
  // await writeDataErrorToExcel(importInfoRequestSheet3, dataSheet3.data, i18n);
  // if (dataSheet4.data.some((s) => s.error)) {
  //   isError = true;
  // }
  // await writeDataErrorToExcel(importInfoRequestSheet4, dataSheet4.data, i18n);

  // map master data
  const itemLineMap = dataSheet1.dataMap.get(TABLE_NAME.ITEM_LINE);
  const goodsTypeMap = dataSheet1.dataMap.get(TABLE_NAME.GOODS_TYPE);
  const unitMap = dataSheet2.dataMap.get(TABLE_NAME.UNIT);
  const customerMap = dataSheet1.dataMap.get(TABLE_NAME.CUSTOMER);
  // const inspectionStandardMap3 = dataSheet3.dataMap.get(
  //   TABLE_NAME.INSPECTION_STANDARD,
  // );
  // const inspectionGroupMap3 = dataSheet3.dataMap.get(
  //   TABLE_NAME.INSPECTION_GROUP,
  // );
  // const inspectionStandardMap4 = dataSheet4.dataMap.get(
  //   TABLE_NAME.INSPECTION_STANDARD,
  // );
  // const inspectionGroupMap4 = dataSheet4.dataMap.get(
  //   TABLE_NAME.INSPECTION_GROUP,
  // );

  const filteredList1 = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const result = filteredList1
    .map((parent) => {
      const units = dataSheet2.data.filter(
        (unit) => unit.keyMapping === parent.keyMapping,
      );
      const validUnits = units.filter((unit) => unit.error);
      // const itemIqcs = dataSheet3.data.filter(
      //   (item) => item.keyMapping === parent.keyMapping,
      // );
      // const validItemIqcs = itemIqcs.filter((item) => item.error);
      // const itemOqcs = dataSheet4.data.filter(
      //   (item) => item.keyMapping === parent.keyMapping,
      // );
      // const validItemOqcs = itemOqcs.filter((item) => item.error);

      if (units.length - validUnits.length === 1) {
        return {
          parent,
          units: units,
          // itemIqcs: itemIqcs,
          // itemOqcs: itemOqcs,
        };
      } else {
        return null;
      }
    })
    .filter((item) => item !== null);
  if (!result || result.length <= 0) {
    return { result: false, recordError: dataSheet1.data.length };
  }
  const dataCreate = result.map((x) => {
    const entity = new ItemEntity();
    entity.code = x.parent.code
      ? x.parent.code.trim().replace(/\s+/g, '_')
      : '';
    entity.name = x.parent.name;
    entity.nameEn = x.parent.nameEn;
    entity.goodsTypeId = mapEntityCode(x.parent.goodsTypeCode, goodsTypeMap);
    entity.itemLineId = mapEntityCode(x.parent.itemLineCode, itemLineMap);
    entity.description = x.parent.description;
    entity.status = StatusEnum.ACTIVE;
    entity.createdBy = userId;

    // build units
    const mainUnit = new ItemUnitEntity();
    mainUnit.unitId = mapEntityCode(x.units[0].unitCode1, unitMap);
    mainUnit.isMainUnit = 1;
    mainUnit.ratio = 1;
    mainUnit.description = x.units[0].description1;

    const units = [mainUnit];

    if (x.units[0].unitCode2) {
      const secondaryUnit = new ItemUnitEntity();
      secondaryUnit.unitId = mapEntityCode(x.units[0].unitCode2, unitMap);
      secondaryUnit.isMainUnit = 0;
      secondaryUnit.ratio = x.units[0].ratio;
      secondaryUnit.description = x.units[0].description2;

      units.push(secondaryUnit);
    }

    entity.units = units;

    // build customers
    if (!isEmpty(x.parent.customerCode)) {
      entity.customers = x.parent.customerCode.split(',').map((x1) => {
        const customer = new ItemCustomerEntity();
        customer.customerId = mapEntityCode(x1, customerMap);

        return customer;
      });
    }

    // build itemQc
    // const itemIqc = new ItemQcEntity();
    // itemIqc.type = ITEM_QC_TYPE_ENUM.IQC;
    // itemIqc.inspectionStandardId = x.itemIqcs[0].inspectionStandardCode
    //   ? inspectionStandardMap3[x.itemIqcs[0].inspectionStandardCode]?.id ?? null
    //   : null;
    // itemIqc.isAppearanceInspection = x.itemIqcs[0].isAppearanceInspection;
    // itemIqc.isMeasurementInspection = x.itemIqcs[0].isMeasurementInspection;
    // itemIqc.inspectionGroupId = x.itemIqcs[0].inspectionGroupCode
    //   ? inspectionGroupMap3[x.itemIqcs[0].inspectionGroupCode]?.id ?? null
    //   : null;
    // itemIqc.isSportTest = x.itemIqcs[0].isSportTest;

    // const itemOqc = new ItemQcEntity();
    // itemOqc.type = ITEM_QC_TYPE_ENUM.OQC;
    // itemOqc.inspectionStandardId = x.itemIqcs[0].inspectionStandardCode
    //   ? inspectionStandardMap4[x.itemOqcs[0].inspectionStandardCode]?.id ?? null
    //   : null;
    // itemOqc.isAppearanceInspection = x.itemOqcs[0].isAppearanceInspection;
    // itemOqc.isMeasurementInspection = x.itemOqcs[0].isMeasurementInspection;
    // itemOqc.inspectionGroupId = x.itemIqcs[0].inspectionGroupCode
    //   ? inspectionGroupMap4[x.itemOqcs[0].inspectionGroupCode]?.id ?? null
    //   : null;
    // entity.itemQcs = [itemIqc, itemOqc];

    return entity;
  });

  // insert into db
  if (dataCreate && dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();

    try {
      const mainMetadata = connection.getMetadata(ItemEntity);
      const unitMetadata = connection.getMetadata(ItemUnitEntity);
      // const itemQcMetadata = connection.getMetadata(ItemQcEntity);
      const mainColumnCount = mainMetadata.columns.length;
      const unitColumnCount = unitMetadata.columns.length;
      // const itemQcColumnCount = itemQcMetadata.columns.length;
      const maxParameters = 2000;
      let currentBatch = [];
      let currentParametersCount = 0;
      for (const main of dataCreate) {
        const mainParametersCount = mainColumnCount;
        const detailParametersCount =
          (main.units?.length || 0) * unitColumnCount;
        const totalProcessParameters =
          mainParametersCount + detailParametersCount;
        if (currentParametersCount + totalProcessParameters > maxParameters) {
          await queryRunner.manager.save(currentBatch);
          currentBatch = [];
          currentParametersCount = 0;
        }
        currentBatch.push(main);
        currentParametersCount += totalProcessParameters;
      }
      if (currentBatch.length > 0) {
        await queryRunner.manager.save(currentBatch);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // // remove error line sheet1
  // const worksheet1 = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove1 = result.map((item) => item.parent.line);
  // if (rowsToRemove1.length > 0) {
  //   await removeLineSuccess(worksheet1, rowsToRemove1);
  // }

  // // remove error line sheet2
  // const worksheet2 = workbook.getWorksheet(importInfoRequestSheet2.sheetName);
  // const rowsToRemove2 = result.flatMap((item) =>
  //   item.units.map((detail) => detail.line),
  // );
  // if (rowsToRemove2.length > 0) {
  //   await removeLineSuccess(worksheet2, rowsToRemove2);
  // }

  // // remove error line sheet3
  // const worksheet3 = workbook.getWorksheet(importInfoRequestSheet3.sheetName);
  // const rowsToRemove3 = result.flatMap((item) =>
  //   item.itemIqcs.map((detail) => detail.line),
  // );
  // if (rowsToRemove3.length > 0) {
  //   await removeLineSuccess(worksheet3, rowsToRemove3);
  // }

  // // remove error line sheet2
  // const worksheet4 = workbook.getWorksheet(importInfoRequestSheet4.sheetName);
  // const rowsToRemove4 = result.flatMap((item) =>
  //   item.units.map((detail) => detail.line),
  // );
  // if (rowsToRemove4.length > 0) {
  //   await removeLineSuccess(worksheet4, rowsToRemove4);
  // }

  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
