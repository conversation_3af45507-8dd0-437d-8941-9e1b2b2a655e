import { ValidationError } from 'class-validator';
import { isEmpty } from 'lodash';
import { StatusEnum } from '../../../common/enums/status.enum';
import {
  buildErrorMessage,
  getDataImport,
  mapEntityCode,
  validateMappings,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { ProductionProcessGoodsTypeEntity } from '../../production-process/entities/production-process-goods-type.entity';
import { ProductionProcessItemEntity } from '../../production-process/entities/production-process-item.entity';
import { ProductionProcessEntity } from '../../production-process/entities/production-process.entity';
import {
  ImportProductionProcessDetailDto,
  ImportProductionProcessDto,
} from '../../production-process/request/import-production-process.request.dto';

import { ProductionProcessListEntity } from '../../production-process-list/entities/production-process-list.entity';
import {
  PRE_FIX,
  TABLE_CODE,
  TABLE_NAME,
  TEMPLATE_PRODUCT_PROCESS,
  TEMPLATE_PRODUCT_PROCESS_DETAIL,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importProductionProcess(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportProductionProcessDto,
    columns: [
      TABLE_CODE.ITEM_LINE_CODE,
      TABLE_CODE.ITEM_CODE,
      TABLE_CODE.GOODS_TYPE_CODE,
    ],
    prefix: PRE_FIX.PRODUCTION_PROCESS,
    sheetName: await i18n.translate('import.screen.productionProcess.sheet1'),
    tables: [TABLE_NAME.GOODS_TYPE, TABLE_NAME.ITEM, TABLE_NAME.ITEM_LINE],
    templateHeader: TEMPLATE_PRODUCT_PROCESS,
    tableMain: TABLE_NAME.PRODUCTION_PROCESS,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );
  const itemMap = dataSheet1.dataMap.get(TABLE_NAME.ITEM);
  const itemLineMap = dataSheet1.dataMap.get(TABLE_NAME.ITEM_LINE);
  const goodsTypeMap = dataSheet1.dataMap.get(TABLE_NAME.GOODS_TYPE);
  let isError = false;
  await Promise.all(
    dataSheet1.data.map(async (s) => {
      if (
        isEmpty(s.itemCode) ||
        isEmpty(s.goodsTypeCode) ||
        isEmpty(s.itemLineCode)
      ) {
        return;
      }
      if (
        !itemMap ||
        itemMap.length <= 0 ||
        !itemLineMap ||
        itemLineMap.length <= 0 ||
        !goodsTypeMap ||
        goodsTypeMap.length <= 0
      ) {
        return;
      }
      const itemLine = itemLineMap[s.itemLineCode];
      const goodsTypeIds = s.goodsTypeCode
        .split(',')
        .map((x) => {
          return goodsTypeMap[x]?.id;
        })
        .filter((id) => id !== null && id !== undefined);
      if (isEmpty(itemLine) || !goodsTypeIds || goodsTypeIds.length <= 0) {
        return;
      }
      const errors: ValidationError[] = [];
      await Promise.all(
        s.itemCode.split(',').map(async (x) => {
          const item = itemMap[x];
          if (item.itemLineId !== itemLine.id) {
            const error = new ValidationError();
            error.property = TABLE_CODE.ITEM_CODE;
            error.constraints = {
              error: await i18n.translate('error.NOT_MAPPING_ITEM_LINE', {
                args: {
                  property: TABLE_CODE.ITEM_CODE,
                  code: x,
                },
              }),
            };
            errors.push(error);
          }
          if (!goodsTypeIds.includes(item.goodsTypeId)) {
            const error = new ValidationError();
            error.property = TABLE_CODE.ITEM_CODE;
            error.constraints = {
              error: await i18n.translate('error.NOT_MAPPING_GOODS_TYPE', {
                args: {
                  property: TABLE_CODE.ITEM_CODE,
                  code: x,
                },
              }),
            };
            errors.push(error);
          }
          return x;
        }),
      );
      if (errors && errors.length > 0) {
        s.error =
          (await buildErrorMessage(errors, PRE_FIX.PRODUCTION_PROCESS, i18n)) +
          (isEmpty(s.error) ? '' : '\r\n' + s.error);
      }
      return s;
    }),
  );
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  const importInfoRequestSheet2: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportProductionProcessDetailDto,
    columns: [TABLE_CODE.PROCESS_CODE],
    prefix: PRE_FIX.PRODUCTION_PROCESS_DETAIL,
    sheetName: await i18n.translate('import.screen.productionProcess.sheet2'),
    tables: [TABLE_NAME.PROCESS],
    templateHeader: TEMPLATE_PRODUCT_PROCESS_DETAIL,
    keys: ['keyMapping', TABLE_CODE.PROCESS_CODE],
  };
  const dataSheet2 = await getDataImport(
    importInfoRequestSheet2,
    i18n,
    baseProcessService,
  );
  const dataDetail = dataSheet2.data;
  const executionNoMap = new Map<string, Set<string>>();

  await Promise.all(
    dataDetail.map(async (s) => {
      const errors: ValidationError[] = [];

      // Kiểm tra giá trị `isProcessQc`
      if (s.isProcessQc !== 0 && s.isProcessQc !== 1) {
        const error = new ValidationError();
        error.property = 'isProcessQc';
        error.constraints = {
          error: await i18n.translate('error.INVALID_VALUE', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.PRODUCTION_PROCESS_DETAIL}${error.property}`,
              ),
              number: '0, 1',
            },
          }),
        };
        errors.push(error);
      }

      // Kiểm tra trùng `executionNo` trong cùng `keyMapping`
      if (s.keyMapping && s.executionNo) {
        const keyMapping = s.keyMapping; // Giả sử có keyMapping trong object s
        if (!executionNoMap.has(keyMapping)) {
          executionNoMap.set(keyMapping, new Set());
        }

        if (executionNoMap.get(keyMapping)?.has(s.executionNo)) {
          const error = new ValidationError();
          error.property = 'executionNo';
          error.constraints = {
            error: await i18n.translate('error.DUPLICATE_KEY_IMPORT', {
              args: {
                property: await i18n.translate(
                  PRE_FIX.PRODUCTION_PROCESS_DETAIL + 'executionNo',
                ),
                key: s.executionNo,
              },
            }),
          };
          errors.push(error);
        } else {
          executionNoMap.get(keyMapping)?.add(s.executionNo);
        }
      }

      // Gán lỗi nếu có
      if (errors.length > 0) {
        s.error =
          (await buildErrorMessage(
            errors,
            PRE_FIX.PRODUCTION_PROCESS_DETAIL,
            i18n,
          )) + (isEmpty(s.error) ? '' : '\r\n' + s.error);
      }
      return s;
    }),
  );

  await validateMappings(
    dataSheet1.data,
    dataSheet2.data,
    PRE_FIX.PRODUCTION_PROCESS,
    importInfoRequestSheet2.sheetName,
    i18n,
  );
  await validateMappings(
    dataSheet2.data,
    dataSheet1.data,
    PRE_FIX.PRODUCTION_PROCESS_DETAIL,
    importInfoRequestSheet1.sheetName,
    i18n,
  );

  if (dataDetail.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet2, dataDetail, i18n);
  const processMap = dataSheet2.dataMap.get(TABLE_NAME.PROCESS);
  const filteredList1 = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const result = filteredList1
    .map((parent) => {
      const details = dataSheet2.data.filter((detail) => {
        if (!detail.keyMapping || !parent.keyMapping) return false;

        return (
          detail.keyMapping.toLowerCase() === parent.keyMapping.toLowerCase()
        );
      });

      if (details.length === 0) {
        return { parent };
      }
      const validDetails = details.filter((detail) => detail.error);
      if (validDetails.length <= 0) {
        return {
          parent,
          details: details,
        };
      } else {
        return null;
      }
    })
    .filter((item) => item !== null);
  if (!result || result.length <= 0) {
    return { result: false, recordError: dataSheet1.data.length };
  }
  const dataCreate = result.map((x) => {
    const entity = new ProductionProcessEntity();
    entity.code = x.parent.code
      ? x.parent.code.trim().replace(/\s+/g, '_')
      : '';
    entity.name = x.parent.name;
    entity.itemLineId = x.parent.itemLineId;
    entity.status = StatusEnum.ACTIVE;
    entity.createdBy = userId;
    entity.items = (x.parent.itemCode ? x.parent.itemCode.split(',') : []).map(
      (x1) => {
        const item = new ProductionProcessItemEntity();
        item.itemId = mapEntityCode(x1, itemMap);
        return item;
      },
    );

    entity.goodsTypes = (
      x.parent.goodsTypeCode ? x.parent.goodsTypeCode.split(',') : []
    ).map((x2) => {
      const goodsType = new ProductionProcessGoodsTypeEntity();
      goodsType.goodsTypeId = mapEntityCode(x2, goodsTypeMap);
      return goodsType;
    });

    if (x.details && x.details.length > 0) {
      entity.dataList = x.details.map((x1) => {
        const detail = new ProductionProcessListEntity();
        detail.processId = mapEntityCode(x1.processCode, processMap);
        detail.isProcessQc = x1.isProcessQc ? x1.isProcessQc : 0;
        detail.executionNo = x1.executionNo;
        detail.description = x1.description;
        return detail;
      });
    }
    return entity;
  });

  // insert into db
  if (dataCreate && dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();

    try {
      const mainMetadata = connection.getMetadata(ProductionProcessEntity);
      const detailMetadata = connection.getMetadata(
        ProductionProcessListEntity,
      );
      const itemMetadata = connection.getMetadata(ProductionProcessItemEntity);
      const goodsTypeMetadata = connection.getMetadata(
        ProductionProcessGoodsTypeEntity,
      );
      const mainColumnCount = mainMetadata.columns.length;
      const detailColumnCount = detailMetadata.columns.length;
      const itemColumnCount = itemMetadata.columns.length;
      const goodsTypeColumnCount = goodsTypeMetadata.columns.length;
      const maxParameters = 2000;
      let currentBatch = [];
      let currentParametersCount = 0;
      for (const main of dataCreate) {
        const mainParametersCount = mainColumnCount;
        const detailParametersCount =
          (main.dataList?.length || 0) * detailColumnCount;
        const itemParametersCount = (main.items?.length || 0) * itemColumnCount;
        const goodsTypeParametersCount =
          (main.goodsTypes?.length || 0) * goodsTypeColumnCount;
        const totalProcessParameters =
          mainParametersCount +
          detailParametersCount +
          itemParametersCount +
          goodsTypeParametersCount;
        if (currentParametersCount + totalProcessParameters > maxParameters) {
          await queryRunner.manager.save(currentBatch);
          currentBatch = [];
          currentParametersCount = 0;
        }
        currentBatch.push(main);
        currentParametersCount += totalProcessParameters;
      }
      if (currentBatch.length > 0) {
        await queryRunner.manager.save(currentBatch);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // // remove error line sheet1
  // const worksheet1 = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove1 = result.map((item) => item.parent.line);
  // if (rowsToRemove1.length > 0) {
  //   await removeLineSuccess(worksheet1, rowsToRemove1);
  // }

  // // remove error line sheet2
  // const worksheet2 = workbook.getWorksheet(importInfoRequestSheet2.sheetName);
  // const rowsToRemove2 = result.flatMap((item) =>
  //   item.details.map((detail) => detail.line),
  // );
  // if (rowsToRemove2.length > 0) {
  //   await removeLineSuccess(worksheet2, rowsToRemove2);
  // }

  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
