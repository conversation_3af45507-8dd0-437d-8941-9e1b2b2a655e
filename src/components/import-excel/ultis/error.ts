import { ValidationError, isEmpty } from 'class-validator';
import {
  buildErrorMessage,
  getDataImport,
  mapEntityCode,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { ErrorEntity } from '../../error/entities/error.entity';
import { ImportErrorRequestDto } from '../../error/request/import-error.request.dto';
import {
  PRE_FIX,
  TABLE_CODE,
  TABLE_NAME,
  TEMPLATE_ERROR,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importError(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportErrorRequestDto,
    columns: [TABLE_CODE.ERROR_TYPE_CODE],
    prefix: PRE_FIX.ERROR,
    sheetName: await i18n.translate('import.screen.error.sheet1'),
    tables: [TABLE_NAME.ERROR_TYPE],
    templateHeader: TEMPLATE_ERROR,
    tableMain: TABLE_NAME.ERROR,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );

  await Promise.all(
    dataSheet1.data.map(async (s) => {
      const errors: ValidationError[] = [];
      if (s.serverity && s.serverity !== 0 && s.serverity !== 1) {
        const error = new ValidationError();
        error.property = 'serverity';
        error.constraints = {
          error: await i18n.translate('error.INVALID_VALUE', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.ERROR}${error.property}`,
              ),
              number: '0, 1',
            },
          }),
        };
        errors.push(error);
      }
      if (errors && errors.length > 0) {
        s.error =
          (await buildErrorMessage(errors, PRE_FIX.ERROR, i18n)) +
          (isEmpty(s.error) ? '' : '\r\n' + s.error);
      }
      return s;
    }),
  );

  const errorTypeMap = dataSheet1.dataMap.get(TABLE_NAME.ERROR_TYPE);
  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  const result = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const dataCreate = result.map((x) => {
    const entity = new ErrorEntity();
    entity.code = x.code ? x.code.trim().replace(/\s+/g, '_') : '';
    entity.name = x.name;
    // set entity.errorTypeId
    entity.errorTypeId = mapEntityCode(x.errorTypeCode, errorTypeMap);
    entity.serverity = x.serverity ?? 0;
    entity.description = x.description;
    entity.createdBy = userId;

    return entity;
  });

  if (dataCreate || dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();
    try {
      const metadata = connection.getMetadata(ErrorEntity);
      const numberOfParameters = metadata.columns.length;
      const maxEntitiesPerBatch = Math.floor(2000 / numberOfParameters);
      for (let i = 0; i < dataCreate.length; i += maxEntitiesPerBatch) {
        const chunk = dataCreate.slice(i, i + maxEntitiesPerBatch);
        await queryRunner.manager.save(chunk);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // const worksheet = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove = result.map((item) => item.line);
  // if (rowsToRemove.length > 0) {
  //   await removeLineSuccess(worksheet, rowsToRemove);
  // }
  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
