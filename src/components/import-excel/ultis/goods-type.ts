import {
  getDataImport,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { GoodsTypeEntity } from '../../goods-type/entities/goods-type.entity';
import { ImportGoodsTypeRequestDto } from '../../goods-type/request/import-goods-type.request.dto';
import {
  PRE_FIX,
  TABLE_NAME,
  TEMPLATE_GOODS_TYPE,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importGoodsType(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportGoodsTypeRequestDto,
    columns: [],
    prefix: PRE_FIX.GOODS_TYPE,
    sheetName: await i18n.translate('import.screen.goodsType.sheet1'),
    tables: [],
    templateHeader: TEMPLATE_GOODS_TYPE,
    tableMain: TABLE_NAME.GOODS_TYPE,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );
  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  const result = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const dataCreate = result.map((x) => {
    const entity = new GoodsTypeEntity();
    entity.code = x.code ? x.code.trim().replace(/\s+/g, '_') : '';
    entity.name = x.name;
    entity.description = x.description;
    entity.createdBy = userId;

    return entity;
  });

  if (dataCreate || dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();
    try {
      const metadata = connection.getMetadata(GoodsTypeEntity);
      const numberOfParameters = metadata.columns.length;
      const maxEntitiesPerBatch = Math.floor(2000 / numberOfParameters);
      for (let i = 0; i < dataCreate.length; i += maxEntitiesPerBatch) {
        const chunk = dataCreate.slice(i, i + maxEntitiesPerBatch);
        await queryRunner.manager.save(chunk);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // const worksheet = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove = result.map((item) => item.line);
  // if (rowsToRemove.length > 0) {
  //   await removeLineSuccess(worksheet, rowsToRemove);
  // }

  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
