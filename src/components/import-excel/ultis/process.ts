import {
  getDataImport,
  mapEntityCode,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { ProcessGoodsTypeEntity } from '../../process/entities/process-goods-type.entity';
import { ProcessEntity } from '../../process/entities/process.entity';
import { ImportProcessRequestDto } from '../../process/request/import-process.request.dto';
import {
  PRE_FIX,
  TABLE_CODE,
  TABLE_NAME,
  TEMPLATE_PROCESS,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importProcess(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportProcessRequestDto,
    columns: [TABLE_CODE.P_CODE, TABLE_CODE.GOODS_TYPE_CODE],
    prefix: PRE_FIX.PROCESS,
    sheetName: await i18n.translate('import.screen.process.sheet1'),
    tables: [TABLE_NAME.P_CODE, TABLE_NAME.GOODS_TYPE],
    templateHeader: TEMPLATE_PROCESS,
    tableMain: TABLE_NAME.PROCESS,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );
  const pCodeMap = dataSheet1.dataMap.get(TABLE_NAME.P_CODE);
  const goodsTypeMap = dataSheet1.dataMap.get(TABLE_NAME.GOODS_TYPE);
  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  const result = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const dataCreate = result.map((x) => {
    const entity = new ProcessEntity();
    entity.code = x.code ? x.code.trim().replace(/\s+/g, '_') : '';
    entity.name = x.name;
    entity.subProcessGroup = x.subProcessGroup;
    // set pCodeId
    if (x.pCode) {
      entity.pCodeId = mapEntityCode(x.pCode, pCodeMap);
    }
    // set goodsTypeId
    if (!x.goodsTypeCode) {
      entity.goodsTypes = [];
    } else {
      entity.goodsTypes = x.goodsTypeCode
        .split(',')
        .map((x1) => {
          if (!goodsTypeMap || goodsTypeMap.length <= 0) {
            return;
          }
          const goodsType = new ProcessGoodsTypeEntity();
          goodsType.goodsTypeId = mapEntityCode(x1, goodsTypeMap);
          return goodsType;
        })
        .filter(Boolean);
    }

    entity.description = x.description;
    entity.createdBy = userId;

    return entity;
  });

  if (dataCreate && dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();

    try {
      const processMetadata = connection.getMetadata(ProcessEntity);
      const processGoodsTypeMetadata = connection.getMetadata(
        ProcessGoodsTypeEntity,
      );
      const processColumnCount = processMetadata.columns.length;
      const processGoodsTypeColumnCount =
        processGoodsTypeMetadata.columns.length;
      const maxParameters = 2000;
      let currentBatch = [];
      let currentParametersCount = 0;
      for (const process of dataCreate) {
        const processParametersCount = processColumnCount;
        const processGoodsTypesParametersCount =
          (process.goodsTypes?.length || 0) * processGoodsTypeColumnCount;
        const totalProcessParameters =
          processParametersCount + processGoodsTypesParametersCount;
        if (currentParametersCount + totalProcessParameters > maxParameters) {
          await queryRunner.manager.save(currentBatch);
          currentBatch = [];
          currentParametersCount = 0;
        }
        currentBatch.push(process);
        currentParametersCount += totalProcessParameters;
      }
      if (currentBatch.length > 0) {
        await queryRunner.manager.save(currentBatch);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // const worksheet = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove = result.map((item) => item.line);
  // if (rowsToRemove.length > 0) {
  //   await removeLineSuccess(worksheet, rowsToRemove);
  // }
  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
