import { ValidationError } from 'class-validator';
import { isEmpty } from 'lodash';
import { StatusEnum } from '../../../common/enums/status.enum';
import {
  buildErrorMessage,
  getDataImport,
  mapEntityCode,
  validateMappings,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { ChecksheetDetailEntity } from '../../checksheet-detail/entities/checksheet-detail.entity';

import { CHECK_TYPE_ENUM } from '../../../constant/common';
import { ChecksheetDetailErrorEntity } from '../../checksheet-detail/entities/checksheet-detail-error.entity';
import { ChecksheetEntity } from '../../checksheet/entities/checksheet.entity';
import {
  ImportChecksheetDetailRequestDto,
  ImportChecksheetRequestDto,
} from '../../checksheet/request/import-checksheet.request.dto';
import {
  PRE_FIX,
  TABLE_CODE,
  TABLE_NAME,
  TEMPLATE_CHECKSHEET,
  TEMPLATE_CHECKSHEET_DETAIL,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importChecksheet(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportChecksheetRequestDto,
    columns: [
      TABLE_CODE.QC_REQUEST_TYPE_CODE,
      TABLE_CODE.ITEM_TYPE_CODE,
      TABLE_CODE.ITEM_LINE_CODE,
      TABLE_CODE.ITEM_CODE,
      TABLE_CODE.PROCESS_CODE,
      TABLE_CODE.EVALUATION_STANDARD_TYPE_CODE,
    ],
    prefix: PRE_FIX.CHECKSHEET,
    sheetName: await i18n.translate('import.screen.checksheet.sheet1'),
    tables: [
      TABLE_NAME.QC_REQUEST_TYPE,
      TABLE_NAME.ITEM_TYPE,
      TABLE_NAME.ITEM_LINE,
      TABLE_NAME.ITEM,
      TABLE_NAME.PROCESS,
      TABLE_NAME.EVALUATION_STANDARD_TYPE,
    ],
    templateHeader: TEMPLATE_CHECKSHEET,
    tableMain: TABLE_NAME.CHECKSHEET,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );
  const importInfoRequestSheet2: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportChecksheetDetailRequestDto,
    columns: [
      TABLE_CODE.INSPECTION_TYPE_CODE,
      TABLE_CODE.INSPECTION_CODE,
      TABLE_CODE.ERROR_CODE,
      TABLE_CODE.UNIT_CODE,
      TABLE_CODE.EVALUATION_STANDARD_TYPE_CODE,
    ],
    prefix: PRE_FIX.CHECKSHEET_DETAIL,
    sheetName: await i18n.translate('import.screen.checksheet.sheet2'),
    tables: [
      TABLE_NAME.INSPECTION_TYPE,
      TABLE_NAME.INSPECTION,
      TABLE_NAME.ERROR,
      TABLE_NAME.UNIT,
      TABLE_NAME.EVALUATION_STANDARD_TYPE,
    ],
    templateHeader: TEMPLATE_CHECKSHEET_DETAIL,
    tableMain: '',
    keys: [
      'keyMapping',
      'checkType',
      TABLE_CODE.INSPECTION_GROUP_CODE,
      TABLE_CODE.INSPECTION_TYPE_CODE,
      TABLE_CODE.INSPECTION_CODE,
      TABLE_CODE.EVALUATION_STANDARD_TYPE_CODE,
    ],
  };
  const dataSheet2 = await getDataImport(
    importInfoRequestSheet2,
    i18n,
    baseProcessService,
  );

  // map master data
  const qcRequestTypeMap = dataSheet1.dataMap.get(TABLE_NAME.QC_REQUEST_TYPE);
  const itemTypeMap = dataSheet1.dataMap.get(TABLE_NAME.ITEM_TYPE);
  const itemLineMap = dataSheet1.dataMap.get(TABLE_NAME.ITEM_LINE);
  const itemMap = dataSheet1.dataMap.get(TABLE_NAME.ITEM);
  const processMap = dataSheet1.dataMap.get(TABLE_NAME.PROCESS);
  const evaluationStandardMainTypeMap = dataSheet1.dataMap.get(
    TABLE_NAME.EVALUATION_STANDARD_TYPE,
  );
  const inspectionTypeMap = dataSheet2.dataMap.get(TABLE_NAME.INSPECTION_TYPE);
  const inspectionMap = dataSheet2.dataMap.get(TABLE_NAME.INSPECTION);
  const errorMap = dataSheet2.dataMap.get(TABLE_NAME.ERROR);
  const unitMap = dataSheet2.dataMap.get(TABLE_NAME.UNIT);
  const evaluationStandardTypeMap = dataSheet2.dataMap.get(
    TABLE_NAME.EVALUATION_STANDARD_TYPE,
  );

  let isError = false;
  await Promise.all(
    dataSheet1.data.map(async (parent) => {
      const mainErrors: ValidationError[] = [];

      const itemType = itemTypeMap?.[parent?.itemTypeCode] ?? null;
      const itemLine = itemLineMap?.[parent?.itemLineCode] ?? null;
      const item = itemMap?.[parent?.itemCode] ?? null;

      if (!isEmpty(itemType) && !isEmpty(itemLine)) {
        if (itemLine.itemTypeId !== itemType.id) {
          const error = new ValidationError();
          error.property = TABLE_CODE.ITEM_LINE_CODE;
          error.constraints = {
            error: await i18n.translate('error.NOT_MAPPING_ITEM_TYPE', {
              args: {
                property: TABLE_CODE.ITEM_LINE_CODE,
                code: parent.itemLineCode,
              },
            }),
          };
          mainErrors.push(error);
        }
      }

      if (!isEmpty(itemLine) && !isEmpty(item)) {
        if (item.itemLineId !== itemLine.id) {
          const error = new ValidationError();
          error.property = TABLE_CODE.ITEM_CODE;
          error.constraints = {
            error: await i18n.translate('error.NOT_MAPPING_ITEM_LINE', {
              args: {
                property: TABLE_CODE.ITEM_CODE,
                code: parent.itemCode,
              },
            }),
          };
          mainErrors.push(error);
        }
      }

      const details = [];
      const measurementCheckIds = new Set<string>(
        dataSheet2.data
          .filter((detail) => {
            if (!detail.keyMapping || !parent.keyMapping) return false; // Bỏ qua nếu keyMapping là null hoặc undefined

            const keyMappingLower = detail.keyMapping.toLowerCase();
            const parentKeyMappingLower = parent.keyMapping.toLowerCase();

            const isMatched =
              keyMappingLower === parentKeyMappingLower &&
              detail.checkType === CHECK_TYPE_ENUM.MEASUREMENT;

            if (keyMappingLower === parentKeyMappingLower) {
              details.push(detail);
            }

            return isMatched;
          })
          .map((detail) => detail.evaluationStandardTypeCode),
      );

      const isScoringRequired = details.some((item) => item.isScoring === 1);

      await Promise.all(
        details.map(async (item) => {
          const subErrors: ValidationError[] = [];

          // get list inspection group by inspection type code
          const inspectionTypeCodeArray = details.map(
            (item) => item.inspectionTypeCode,
          );
          const inspectionGroups =
            await baseProcessService.getInspectionGroupByInspectionTypeCodes(
              inspectionTypeCodeArray,
            ).data;

          // check map HMKT
          const inspectionType =
            inspectionTypeMap?.[item?.inspectionTypeCode] ?? null;
          if (
            !isEmpty(inspectionType) &&
            inspectionGroups &&
            inspectionGroups.length > 0
          ) {
            const inspectionGroup = inspectionGroups.find(
              (group) => group.id === inspectionType.inspectionGroupId,
            );
            if (isEmpty(inspectionGroup)) {
              // error
              const error = new ValidationError();
              error.property = 'inspectionType';
              error.constraints = {
                error: await i18n.translate('error.NOT_MAPPING_CHECK_TYPE', {
                  args: {
                    code: item?.inspectionTypeCode,
                  },
                }),
              };
              subErrors.push(error);
            } else {
              if (inspectionGroup.checkType !== item.checkType) {
                // error
                const error = new ValidationError();
                error.property = 'inspectionType';
                error.constraints = {
                  error: await i18n.translate('error.NOT_MAPPING_CHECK_TYPE', {
                    args: {
                      code: item?.inspectionTypeCode,
                    },
                  }),
                };
                subErrors.push(error);
              }
            }
          }
          // check map NDKT
          const inspection = inspectionMap?.[item?.inspectionCode] ?? null;
          if (!isEmpty(inspection) && !isEmpty(inspectionType)) {
            if (inspection.inspectionTypeId !== inspectionType.id) {
              const error = new ValidationError();
              error.property = TABLE_CODE.INSPECTION_CODE;
              error.constraints = {
                error: await i18n.translate(
                  'error.NOT_MAPPING_INSPECTION_TYPE',
                  {
                    args: {
                      code: item?.inspectionCode,
                    },
                  },
                ),
              };
              subErrors.push(error);
            }
          }

          if (
            measurementCheckIds.size > 0 &&
            !measurementCheckIds.has(parent.evaluationStandardTypeCode)
          ) {
            const error = new ValidationError();
            error.property = TABLE_CODE.EVALUATION_STANDARD_TYPE_CODE;
            error.constraints = {
              error: await i18n.translate(
                'error.CHECKSHEET_DETAIL_EVALUATION_STANDARD_TYPE_ERROR',
              ),
            };
            subErrors.push(error);
          }

          if (isScoringRequired && !item.isScoring) {
            const error = new ValidationError();
            error.property = 'isScoring';
            error.constraints = {
              error: await i18n.translate('error.IS_SCORING_INVALID'),
            };
            subErrors.push(error);
          }

          // Validate scoringScale (bắt buộc nếu có ít nhất 1 item.isScoring = 1)
          if (
            isScoringRequired &&
            (item.scoringScale === null || item.scoringScale === undefined)
          ) {
            const error = new ValidationError();
            error.property = 'scoringScale';
            error.constraints = {
              error: await i18n.translate('error.SCORING_SCALE_REQUIRED'),
            };
            subErrors.push(error);
          } else if (
            isScoringRequired &&
            (item.scoringScale <= 0 || item.scoringScale > 100)
          ) {
            const error = new ValidationError();
            error.property = 'scoringScale';
            error.constraints = {
              error: await i18n.translate('error.SCORING_SCALE_INVALID'),
            };
            subErrors.push(error);
          }

          // Validate spec, minus_below
          if (
            item?.checkType === CHECK_TYPE_ENUM.MEASUREMENT ||
            item?.checkType === CHECK_TYPE_ENUM.ENVIRONMENTAL_TOXICITY
          ) {
            if (item?.spec === null || item?.spec === undefined) {
              const error = new ValidationError();
              error.property = 'spec';
              error.constraints = {
                error: await i18n.translate('error.SPEC_IS_NOT_NULL'),
              };
              subErrors.push(error);
            } else if (
              item?.minusBelow === null ||
              item?.minusBelow === undefined
            ) {
              const error = new ValidationError();
              error.property = 'minusBelow';
              error.constraints = {
                error: await i18n.translate('error.MINUS_BELOW_IS_NOT_NULL'),
              };
              subErrors.push(error);
            }
          }

          if (
            item?.checkType === CHECK_TYPE_ENUM.MEASUREMENT ||
            item?.checkType === CHECK_TYPE_ENUM.EXPANSION
          ) {
            if (item?.category === null || item?.category === undefined) {
              const error = new ValidationError();
              error.property = 'category';
              error.constraints = {
                error: await i18n.translate('error.CATEGOTY_CODE_REQUIRED'),
              };
              subErrors.push(error);
            } else {
              if (item?.category !== 0 && item?.category !== 1) {
                const error = new ValidationError();
                error.property = 'category';
                error.constraints = {
                  error: await i18n.translate('error.INVALID_VALUE', {
                    args: {
                      property: await i18n.translate(
                        `${PRE_FIX.CHECKSHEET_DETAIL}${error.property}`,
                      ),

                      number: '0, 1',
                    },
                  }),
                };
                subErrors.push(error);
              }
            }
          }

          if (subErrors && subErrors.length > 0) {
            item.error =
              (await buildErrorMessage(
                subErrors,
                PRE_FIX.CHECKSHEET_DETAIL,
                i18n,
              )) + (isEmpty(item.error) ? '' : '\r\n' + item.error);
          }
          return item;
        }),
      );

      if (mainErrors && mainErrors.length > 0) {
        parent.error =
          (await buildErrorMessage(mainErrors, PRE_FIX.CHECKSHEET, i18n)) +
          (isEmpty(parent.error) ? '' : '\r\n' + parent.error);
      }
      return parent;
    }),
  );

  await validateMappings(
    dataSheet1.data,
    dataSheet2.data,
    PRE_FIX.CHECKSHEET,
    importInfoRequestSheet2.sheetName,
    i18n,
  );
  await validateMappings(
    dataSheet2.data,
    dataSheet1.data,
    PRE_FIX.CHECKSHEET_DETAIL,
    importInfoRequestSheet1.sheetName,
    i18n,
  );

  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  if (dataSheet2.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet2, dataSheet2.data, i18n);

  const filteredList1 = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const result = filteredList1
    .map((parent) => {
      const details = dataSheet2.data.filter((detail) => {
        if (!detail.keyMapping || !parent.keyMapping) return false;

        return (
          detail.keyMapping.toLowerCase() === parent.keyMapping.toLowerCase()
        );
      });

      if (details.length === 0) {
        return { parent };
      }
      const validDetails = details.filter((detail) => detail.error);
      if (validDetails.length <= 0) {
        return {
          parent,
          details: details,
        };
      } else {
        return null;
      }
    })
    .filter((item) => item !== null);

  if (!result || result.length <= 0) {
    return { result: false, recordError: dataSheet1.data.length };
  }

  // create entity
  const dataCreate = result.map((x) => {
    const entity = new ChecksheetEntity();
    entity.code = x.parent.code
      ? x.parent.code.trim().replace(/\s+/g, '_')
      : '';
    entity.name = x.parent.name;
    entity.qcRequestTypeId = mapEntityCode(
      x.parent.qcRequestTypeCode,
      qcRequestTypeMap,
    );
    entity.itemTypeId = mapEntityCode(x.parent.itemTypeCode, itemTypeMap);
    entity.itemLineId = mapEntityCode(x.parent.itemLineCode, itemLineMap);
    entity.itemId = mapEntityCode(x.parent.itemCode, itemMap);
    entity.processId = mapEntityCode(x.parent.processCode, processMap);
    entity.evaluationStandardTypeMainId = mapEntityCode(
      x.parent.evaluationStandardTypeCode,
      evaluationStandardMainTypeMap,
    );
    entity.description = x.parent.description;
    entity.status = StatusEnum.ACTIVE;
    entity.createdBy = userId;

    if (x.details && x.details.length > 0) {
      entity.dataDetails = x.details.map((x1) => {
        const detail = new ChecksheetDetailEntity();
        detail.checkType = x1.checkType;
        const key = x1.inspectionTypeCode
          ? Object.keys(inspectionTypeMap).find(
              (k) =>
                k.toLowerCase() === x1.inspectionTypeCode.trim().toLowerCase(),
            )
          : null;
        const inspectionType = key ? inspectionTypeMap[key] : null;

        if (inspectionType) {
          detail.inspectionTypeId = inspectionType.id;
          detail.inspectionGroupId = inspectionType.inspectionGroupId;
        } else {
          detail.inspectionTypeId = null;
          detail.inspectionGroupId = null;
        }

        detail.inspectionId = mapEntityCode(x1.inspectionCode, inspectionMap);
        detail.errors = Array.from(
          new Set(x1.errorCode ? x1.errorCode.split(',') : []),
        )
          .map((x2) => {
            if (!errorMap || Object.keys(errorMap).length === 0) {
              return;
            }
            const checksheetDetailErrorEntity =
              new ChecksheetDetailErrorEntity();
            checksheetDetailErrorEntity.errorId = mapEntityCode(x2, errorMap);
            return checksheetDetailErrorEntity;
          })
          .filter(Boolean);

        detail.unitId = mapEntityCode(x1.unitCode, unitMap);
        detail.evaluationStandardTypeId = mapEntityCode(
          x1.evaluationStandardTypeCode,
          evaluationStandardTypeMap,
        );
        if (x1.checkType === 1 || x1.checkType === 3) {
          detail.category = x1.category;
        }
        detail.spec = x1.spec;
        detail.plusAbove = x1.plusAbove;
        detail.minusBelow = x1.minusBelow;
        detail.isScoring = x1.isScoring ?? 0;
        if (x1.isScoring === 1) {
          detail.scoringScale = x1.scoringScale;
        }
        return detail;
      });
    }

    return entity;
  });

  // insert into db
  if (dataCreate && dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    const batchSize = 2000;

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      for (const checksheet of dataCreate) {
        const checksheetToSave = { ...checksheet, dataDetails: [] };

        const saveChecksheet = await queryRunner.manager.save(
          ChecksheetEntity,
          checksheetToSave,
        );
        if (checksheet.dataDetails && checksheet.dataDetails.length > 0) {
          for (const detail of checksheet.dataDetails) {
            const dataDetailToSave = {
              ...detail,
              checksheetId: saveChecksheet.id,
              errors: [],
            };

            const saveDataDetail = await queryRunner.manager.save(
              ChecksheetDetailEntity,
              dataDetailToSave,
            );
            if (detail.errors && detail.errors.length > 0) {
              const errorToSave = detail.errors.map((error) => ({
                ...error,
                checksheetDetailId: saveDataDetail.id,
              }));
              await saveEntitiesInBatches(
                queryRunner,
                ChecksheetDetailErrorEntity,
                errorToSave,
                batchSize,
              );
            }
          }
        }
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
  }

  // // remove error line sheet1
  // const worksheet1 = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove1 = result.map((item) => item.parent.line);
  // if (rowsToRemove1.length > 0) {
  //   await removeLineSuccess(worksheet1, rowsToRemove1);
  // }

  // // remove error line sheet2
  // const worksheet2 = workbook.getWorksheet(importInfoRequestSheet2.sheetName);
  // const rowsToRemove2 = result.flatMap((item) =>
  //   item.details.map((detail) => detail.line),
  // );
  // if (rowsToRemove2.length > 0) {
  //   await removeLineSuccess(worksheet2, rowsToRemove2);
  // }

  // response
  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}

export function getParameterCount(entity: any): number {
  let count = 0;
  for (const key in entity) {
    if (entity[key] !== undefined && entity[key] !== null) {
      count++;
    }
  }
  return count;
}

export async function saveEntitiesInBatches<T>(
  queryRunner: any,
  entityClass: any,
  entities: T[],
  batchSize: number,
) {
  let currentBatch: T[] = [];
  let currentParamCount = 0;

  for (const entity of entities) {
    const paramCount = getParameterCount(entity);
    if (currentParamCount + paramCount > batchSize) {
      await queryRunner.manager.save(entityClass, currentBatch);
      currentBatch = [];
      currentParamCount = 0;
    }
    currentBatch.push(entity);
    currentParamCount += paramCount;
  }

  if (currentBatch.length > 0) {
    await queryRunner.manager.save(entityClass, currentBatch);
  }
}
