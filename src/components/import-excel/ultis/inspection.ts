import {
  getDataImport,
  mapEntityCode,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { InspectionErrorEntity } from '../../inspection-error/entities/inspection-error.entity';
import { InspectionEntity } from '../../inspection/entities/inspection.entity';
import { ImportInspectionRequestDto } from '../../inspection/request/import-inspection.request.dto';
import {
  PRE_FIX,
  TABLE_CODE,
  TABLE_NAME,
  TEMPLATE_INSPECTION,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importInspection(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportInspectionRequestDto,
    columns: [TABLE_CODE.INSPECTION_TYPE_CODE, TABLE_CODE.ERROR_CODE],
    prefix: PRE_FIX.INSPECTION,
    sheetName: await i18n.translate('import.screen.inspection.sheet1'),
    tables: [TABLE_NAME.INSPECTION_TYPE, TABLE_NAME.ERROR],
    tableMain: TABLE_NAME.INSPECTION,
    templateHeader: TEMPLATE_INSPECTION,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );
  const inspectionTypeMap = dataSheet1.dataMap.get(TABLE_NAME.INSPECTION_TYPE);
  const errorMap = dataSheet1.dataMap.get(TABLE_NAME.ERROR);
  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  const result = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const dataCreate = result.map((x) => {
    const entity = new InspectionEntity();
    entity.code = x.code ? x.code.trim().replace(/\s+/g, '_') : '';
    entity.name = x.name;
    entity.inspectionTypeId = mapEntityCode(
      x.inspectionTypeCode,
      inspectionTypeMap,
    );
    entity.managementScope = x.managementScope;
    // set errorIds
    if (x.errorCode) {
      entity.inspectionErrors = x.errorCode.split(',').map((x1) => {
        if (!errorMap || errorMap.length <= 0) {
          return;
        }
        const inspectionErrorEntity = new InspectionErrorEntity();
        inspectionErrorEntity.errorId = mapEntityCode(x1, errorMap);
        return inspectionErrorEntity;
      });
    }

    entity.description = x.description;
    entity.createdBy = userId;

    return entity;
  });

  if (dataCreate && dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();

    try {
      const mainMetadata = connection.getMetadata(InspectionEntity);
      const detailMetadata = connection.getMetadata(InspectionErrorEntity);
      const mainColumnCount = mainMetadata.columns.length;
      const detailColumnCount = detailMetadata.columns.length;
      const maxParameters = 2000;
      let currentBatch = [];
      let currentParametersCount = 0;
      for (const main of dataCreate) {
        const mainParametersCount = mainColumnCount;
        const detailParametersCount =
          (main.inspectionErrors?.length || 0) * detailColumnCount;
        const totalProcessParameters =
          mainParametersCount + detailParametersCount;
        if (currentParametersCount + totalProcessParameters > maxParameters) {
          await queryRunner.manager.save(currentBatch);
          currentBatch = [];
          currentParametersCount = 0;
        }
        currentBatch.push(main);
        currentParametersCount += totalProcessParameters;
      }
      if (currentBatch.length > 0) {
        await queryRunner.manager.save(currentBatch);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // const worksheet = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove = result.map((item) => item.line);
  // if (rowsToRemove.length > 0) {
  //   await removeLineSuccess(worksheet, rowsToRemove);
  // }
  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
