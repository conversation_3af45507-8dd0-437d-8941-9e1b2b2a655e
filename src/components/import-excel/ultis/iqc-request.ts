import {
  getDataImport,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import {
  ImportIqcRequestDetailDto,
  ImportIqcRequestDto,
} from '../../iqc-request/request/import-iqc-request.request.dto';
import {
  PRE_FIX,
  TEMPLATE_IQC_REQUEST,
  TEMPLATE_IQC_REQUEST_DETAIL,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importIqcRequest(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportIqcRequestDto,
    columns: ['qcRequestTypeCode'],
    prefix: PRE_FIX.IQC_REQUEST,
    sheetName: await i18n.translate('import.screen.iqcRequest.sheet1'),
    tables: ['qc_request_types'],
    templateHeader: TEMPLATE_IQC_REQUEST,
    tableMain: '',
    keys: [],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );
  const importInfoRequestSheet2: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportIqcRequestDetailDto,
    columns: ['itemCode', 'itemUnitCode', 'itemUnitQcCode', 'processCode'],
    prefix: PRE_FIX.IQC_REQUEST_DETAIL,
    sheetName: await i18n.translate('import.screen.iqcRequest.sheet2'),
    tables: ['items', 'processes', 'item_units'],
    templateHeader: TEMPLATE_IQC_REQUEST_DETAIL,
    tableMain: '',
    keys: [],
  };
  const dataSheet2 = await getDataImport(
    importInfoRequestSheet2,
    i18n,
    baseProcessService,
  );
  if (dataSheet1.data.some((s) => s.error)) {
    await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1, i18n);
  }
  if (dataSheet2.data.some((s) => s.error)) {
    await writeDataErrorToExcel(importInfoRequestSheet2, dataSheet2, i18n);
  }
  return {
    result: true,
  };
}
