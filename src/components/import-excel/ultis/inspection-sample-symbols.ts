import { StatusEnum } from '../../../common/enums/status.enum';
import {
  getDataImport,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { InspectionSampleQuantityDetailEntity } from '../../inspection-sample-quantity-detail/entities/inspection-sample-quantity-detail.entity';
import { InspectionSampleQuantityEntity } from '../../inspection-sample-quantity/entities/inspection-sample-quantity.entity';
import { InspectionSampleSymbolEntity } from '../../inspection-sample-symbol/entities/inspection-sample-symbol.entity';
import {
  ImportInspectionSampleSymbolDetailRequestDto,
  ImportInspectionSampleSymbolDetailStandardRequestDto,
  ImportInspectionSampleSymbolRequestDto,
} from '../../inspection-sample-symbol/request/import-inspection-sample-symbol.request.dto';
import {
  PRE_FIX,
  TABLE_CODE,
  TABLE_NAME,
  TEMPLATE_INSPECTION_SAMPLE_SYMBOL,
  TEMPLATE_INSPECTION_SAMPLE_SYMBOL_DETAIL,
  TEMPLATE_INSPECTION_SAMPLE_SYMBOL_DETAIL_STANDARD,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importInspectionSampleSymbol(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportInspectionSampleSymbolRequestDto,
    columns: [],
    prefix: PRE_FIX.INSPECTION_SAMPLE_SYMBOL,
    sheetName: await i18n.translate(
      'import.screen.inspectionSampleSymbol.sheet1',
    ),
    tables: [],
    templateHeader: TEMPLATE_INSPECTION_SAMPLE_SYMBOL,
    tableMain: TABLE_NAME.INSPECTION_SAMPLE_SYMBOL,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );
  const importInfoRequestSheet2: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportInspectionSampleSymbolDetailRequestDto,
    columns: [],
    prefix: PRE_FIX.INSPECTION_SAMPLE_QUANTITY_DETAIL,
    sheetName: await i18n.translate(
      'import.screen.inspectionSampleSymbol.sheet2',
    ),
    tables: [],
    templateHeader: TEMPLATE_INSPECTION_SAMPLE_SYMBOL_DETAIL,
    tableMain: '',
    keys: [],
  };
  const dataSheet2 = await getDataImport(
    importInfoRequestSheet2,
    i18n,
    baseProcessService,
  );
  const importInfoRequestSheet3: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportInspectionSampleSymbolDetailStandardRequestDto,
    columns: [
      TABLE_CODE.INSPECTION_STANDARD_CODE,
      TABLE_CODE.INSPECTION_SAMPLE_QUANTITY_CODE,
    ],
    prefix: PRE_FIX.INSPECTION_SAMPLE_QUANTITY_DETAIL,
    sheetName: await i18n.translate(
      'import.screen.inspectionSampleSymbol.sheet3',
    ),
    tables: [
      TABLE_NAME.INSPECTION_STANDARD,
      TABLE_NAME.INSPECTION_SAMPLE_QUANTITY,
    ],
    templateHeader: TEMPLATE_INSPECTION_SAMPLE_SYMBOL_DETAIL_STANDARD,
    tableMain: '',
    keys: [],
  };
  const dataSheet3 = await getDataImport(
    importInfoRequestSheet3,
    i18n,
    baseProcessService,
  );
  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  if (dataSheet2.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet2, dataSheet2.data, i18n);
  if (dataSheet3.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet3, dataSheet3.data, i18n);

  // map master data
  // const inspectionStandardMap = dataSheet3.dataMap.get(
  //   TABLE_NAME.INSPECTION_STANDARD,
  // );
  // const inspectionSampleQuantityMap = dataSheet3.dataMap.get(
  //   TABLE_NAME.INSPECTION_SAMPLE_QUANTITY,
  // );

  const filteredList1 = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const result = filteredList1
    .map((parent) => {
      const details = dataSheet2.data.filter((detail) => {
        if (!detail.keyMapping || !parent.keyMapping) return false; // Kiểm tra null hoặc undefined trước

        return (
          detail.keyMapping.toLowerCase() === parent.keyMapping.toLowerCase()
        );
      });

      if (details.length === 0) {
        return { parent };
      }
      const validDetails = details.filter((detail) => detail.error);
      if (validDetails.length <= 0) {
        return {
          parent,
          details: details,
        };
      } else {
        return null;
      }
    })
    .filter((item) => item !== null);
  if (!result || result.length <= 0) {
    return { result: false, recordError: dataSheet1.data.length };
  }
  const dataCreate = result.map((x) => {
    const entity = new InspectionSampleSymbolEntity();
    entity.code = x.parent.code
      ? x.parent.code.trim().replace(/\s+/g, '_')
      : '';
    entity.name = x.parent.name;
    entity.description = x.parent.description;
    entity.status = StatusEnum.ACTIVE;
    entity.createdBy = userId;

    return entity;
  });

  // insert into db
  if (dataCreate && dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();

    try {
      const mainMetadata = connection.getMetadata(
        InspectionSampleQuantityEntity,
      );
      const detailMetadata = connection.getMetadata(
        InspectionSampleQuantityDetailEntity,
      );
      const mainColumnCount = mainMetadata.columns.length;
      const detailColumnCount = detailMetadata.columns.length;
      const maxParameters = 2000;
      let currentBatch = [];
      let currentParametersCount = 0;
      for (const main of dataCreate) {
        const mainParametersCount = mainColumnCount;
        const detailParametersCount =
          (main.dataDetails?.length || 0) * detailColumnCount;
        const totalProcessParameters =
          mainParametersCount + detailParametersCount;
        if (currentParametersCount + totalProcessParameters > maxParameters) {
          await queryRunner.manager.save(currentBatch);
          currentBatch = [];
          currentParametersCount = 0;
        }
        currentBatch.push(main);
        currentParametersCount += totalProcessParameters;
      }
      if (currentBatch.length > 0) {
        await queryRunner.manager.save(currentBatch);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // // remove error line sheet1
  // const worksheet1 = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove1 = result.map((item) => item.parent.line);
  // if (rowsToRemove1.length > 0) {
  //   await removeLineSuccess(worksheet1, rowsToRemove1);
  // }

  // // remove error line sheet2
  // const worksheet2 = workbook.getWorksheet(importInfoRequestSheet2.sheetName);
  // const rowsToRemove2 = result.flatMap((item) =>
  //   item.details.map((detail) => detail.line),
  // );
  // if (rowsToRemove2.length > 0) {
  //   await removeLineSuccess(worksheet2, rowsToRemove2);
  // }

  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
