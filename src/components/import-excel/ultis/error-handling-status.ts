import { ValidationError, isEmpty } from 'class-validator';
import { StatusEnum } from '../../../common/enums/status.enum';
import {
  buildErrorMessage,
  getDataImport,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { ErrorHandlingStatusEntity } from '../../error-handling-status/entities/error-handling-status.entity';
import { ImportErrorHandlingStatusRequestDto } from '../../error-handling-status/request/import-error-handling-status.request.dto';
import {
  PRE_FIX,
  TABLE_NAME,
  TEMPLATE_ERROR_HANDLING_STATUS,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importErrorHandlingStatus(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportErrorHandlingStatusRequestDto,
    columns: [],
    prefix: PRE_FIX.ERROR_HANDLING_STATUS,
    sheetName: await i18n.translate('import.screen.errorHandlingStatus.sheet1'),
    tables: [],
    templateHeader: TEMPLATE_ERROR_HANDLING_STATUS,
    tableMain: TABLE_NAME.ERROR_HANDLING_STATUS,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );

  await Promise.all(
    dataSheet1.data.map(async (s) => {
      const errors: ValidationError[] = [];
      if (s.applicableObject !== 0 && s.applicableObject !== 1) {
        const error = new ValidationError();
        error.property = 'applicableObject';
        error.constraints = {
          error: await i18n.translate('error.INVALID_VALUE', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.ERROR_HANDLING_STATUS}${error.property}`,
              ),
              number: '0, 1',
            },
          }),
        };
        errors.push(error);
      }
      if (s.isClaim !== 0 && s.isClaim !== 1) {
        const error = new ValidationError();
        error.property = 'isClaim';
        error.constraints = {
          error: await i18n.translate('error.INVALID_VALUE', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.ERROR_HANDLING_STATUS}${error.property}`,
              ),
              number: '0, 1',
            },
          }),
        };
        errors.push(error);
      }
      if (errors && errors.length > 0) {
        s.error =
          (await buildErrorMessage(
            errors,
            PRE_FIX.ERROR_HANDLING_STATUS,
            i18n,
          )) + (isEmpty(s.error) ? '' : '\r\n' + s.error);
      }
      return s;
    }),
  );

  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  const result = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const dataCreate = result.map((x) => {
    const entity = new ErrorHandlingStatusEntity();
    entity.code = x.code ? x.code.trim().replace(/\s+/g, '_') : '';
    entity.name = x.name;
    entity.applicableObject = x.applicableObject;
    entity.isClaim = x.isClaim;
    entity.description = x.description;
    entity.status = StatusEnum.ACTIVE;
    entity.createdBy = userId;

    return entity;
  });

  if (dataCreate || dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();
    try {
      const metadata = connection.getMetadata(ErrorHandlingStatusEntity);
      const numberOfParameters = metadata.columns.length;
      const maxEntitiesPerBatch = Math.floor(2000 / numberOfParameters);
      for (let i = 0; i < dataCreate.length; i += maxEntitiesPerBatch) {
        const chunk = dataCreate.slice(i, i + maxEntitiesPerBatch);
        await queryRunner.manager.save(chunk);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // const worksheet = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove = result.map((item) => item.line);
  // if (rowsToRemove.length > 0) {
  //   await removeLineSuccess(worksheet, rowsToRemove);
  // }
  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
