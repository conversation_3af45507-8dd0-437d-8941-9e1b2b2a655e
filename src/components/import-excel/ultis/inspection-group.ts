import { ValidationError, isEmpty } from 'class-validator';
import {
  buildErrorMessage,
  getDataImport,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { InspectionGroupEntity } from '../../inspection-group/entities/inspection-group.entity';
import { ImportInspectionGroupRequestDto } from '../../inspection-group/request/import-inspection-group.request.dto';
import {
  PRE_FIX,
  TABLE_CODE,
  TABLE_NAME,
  TEMPLATE_INSPECTION_GROUP,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importInspectionGroup(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportInspectionGroupRequestDto,
    columns: [TABLE_CODE.QC_REQUEST_TYPE_CODE],
    prefix: PRE_FIX.INSPECTION_GROUP,
    sheetName: await i18n.translate('import.screen.inspectionGroup.sheet1'),
    tables: [TABLE_NAME.QC_REQUEST_TYPE],
    templateHeader: TEMPLATE_INSPECTION_GROUP,
    tableMain: TABLE_NAME.INSPECTION_GROUP,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );

  const qcRequestTypeMap = dataSheet1.dataMap.get(TABLE_NAME.QC_REQUEST_TYPE);

  await Promise.all(
    dataSheet1.data.map(async (s) => {
      const errors: ValidationError[] = [];
      if (
        s.checkType !== 0 &&
        s.checkType !== 1 &&
        s.checkType !== 2 &&
        s.checkType !== 3
      ) {
        const error = new ValidationError();
        error.property = 'checkType';
        error.constraints = {
          error: await i18n.translate('error.INVALID_VALUE', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.INSPECTION_GROUP}${error.property}`,
              ),

              number: '0, 1, 2, 3',
            },
          }),
        };
        errors.push(error);
      }

      if (s.checkType === 2 && isEmpty(s.chemicalType)) {
        const error = new ValidationError();
        error.property = 'chemicalType';
        error.constraints = {
          error: await i18n.translate('error.CHEMICAL_TYPE_REQUIRED'),
        };
        errors.push(error);
      }

      if (
        s.checkType === 2 &&
        !isEmpty(s.chemicalType) &&
        s.chemicalType !== 0 &&
        s.chemicalType !== 1
      ) {
        const error = new ValidationError();
        error.property = 'chemicalType';
        error.constraints = {
          error: await i18n.translate('error.INVALID_VALUE', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.INSPECTION_GROUP}${error.property}`,
              ),
              number: '0, 1',
            },
          }),
        };
        errors.push(error);
      }

      if (s.qcRequestTypeCode && qcRequestTypeMap) {
        const key = Object.keys(qcRequestTypeMap).find(
          (k) => k.toLowerCase() === s.qcRequestTypeCode.trim().toLowerCase(),
        );
        const qcRequestType = qcRequestTypeMap[key];
        if (qcRequestType && qcRequestType.qcType === 1 && s.checkType === 1) {
          if (isEmpty(s.oqcDeviceType)) {
            // error required
            const error = new ValidationError();
            error.property = 'oqcDeviceType';
            error.constraints = {
              error: await i18n.translate(
                'error.ERROR_OQC_DEVICE_TYPE_REQUIRED',
              ),
            };
            errors.push(error);
          } else if (
            s.oqcDeviceType !== 0 &&
            s.oqcDeviceType !== 1 &&
            s.oqcDeviceType !== 2 &&
            s.oqcDeviceType !== 3 &&
            s.oqcDeviceType !== 4
          ) {
            // error enum
            const error = new ValidationError();
            error.property = 'oqcDeviceType';
            error.constraints = {
              error: await i18n.translate('error.INVALID_VALUE', {
                args: {
                  property: await i18n.translate(
                    `${PRE_FIX.INSPECTION_GROUP}${error.property}`,
                  ),
                  number: '0, 1, 2, 3, 4',
                },
              }),
            };
            errors.push(error);
          }
        }
      }

      if (errors && errors.length > 0) {
        s.error =
          (await buildErrorMessage(errors, PRE_FIX.INSPECTION_GROUP, i18n)) +
          (isEmpty(s.error) ? '' : '\r\n' + s.error);
      }
      return s;
    }),
  );

  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  const result = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const dataCreate = result.map((x) => {
    const entity = new InspectionGroupEntity();
    entity.code = x.code ? x.code.trim().replace(/\s+/g, '_') : '';
    entity.name = x.name;
    if (qcRequestTypeMap) {
      const key = Object.keys(qcRequestTypeMap).find(
        (k) => k.toLowerCase() === x.qcRequestTypeCode.trim().toLowerCase(),
      );
      const qcRequestType = qcRequestTypeMap[key];
      if (qcRequestType) {
        entity.qcRequestTypeId = qcRequestType.id;
        if (qcRequestType && qcRequestType.qcType === 1) {
          entity.oqcDeviceType = x.oqcDeviceType;
        }
      }
    }
    entity.checkType = x.checkType;
    if (x.checkType === 2) {
      entity.chemicalType = x.chemicalType;
    }
    entity.description = x.description;
    entity.createdBy = userId;

    return entity;
  });

  if (dataCreate || dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();
    try {
      const metadata = connection.getMetadata(InspectionGroupEntity);
      const numberOfParameters = metadata.columns.length;
      const maxEntitiesPerBatch = Math.floor(2000 / numberOfParameters);
      for (let i = 0; i < dataCreate.length; i += maxEntitiesPerBatch) {
        const chunk = dataCreate.slice(i, i + maxEntitiesPerBatch);
        await queryRunner.manager.save(chunk);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // const worksheet = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove = result.map((item) => item.line);
  // if (rowsToRemove.length > 0) {
  //   await removeLineSuccess(worksheet, rowsToRemove);
  // }
  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
