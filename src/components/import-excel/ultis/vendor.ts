import { ValidationError, isEmpty } from 'class-validator';
import {
  buildErrorMessage,
  getDataImport,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import {
  PRE_FIX,
  TABLE_NAME,
  TEMPLATE_VENDOR,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';
import { ImportVendorRequestDto } from '../request/import-vendor.request.dto';

export async function importVendor(
  workbook,
  userId,
  i18n,
  baseProcessService,
  vendorService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportVendorRequestDto,
    columns: [],
    prefix: PRE_FIX.VENDOR,
    sheetName: await i18n.translate('import.screen.vendor.sheet1'),
    tables: [],
    templateHeader: TEMPLATE_VENDOR,
    tableMain: TABLE_NAME.VENDOR,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );
  await Promise.all(
    dataSheet1.data.map(async (s) => {
      const errors: ValidationError[] = [];
      if (s.type !== 0 && s.type !== 1) {
        const error = new ValidationError();
        error.property = 'type';
        error.constraints = {
          error: await i18n.translate('error.INVALID_VALUE', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.VENDOR}${error.property}`,
              ),
              number: '0, 1',
            },
          }),
        };
        errors.push(error);
      }
      if (
        s.country !== 0 &&
        s.country !== 1 &&
        s.country !== 2 &&
        s.country !== 3
      ) {
        const error = new ValidationError();
        error.property = 'country';
        error.constraints = {
          error: await i18n.translate('error.INVALID_VALUE', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.VENDOR}${error.property}`,
              ),
              number: '0, 1, 2, 3',
            },
          }),
        };
        errors.push(error);
      }
      if (errors && errors.length > 0) {
        s.error =
          (await buildErrorMessage(errors, PRE_FIX.VENDOR, i18n)) +
          (isEmpty(s.error) ? '' : '\r\n' + s.error);
      }
      return s;
    }),
  );
  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  const result = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const dataCreate = result.map((x) => {
    const entity = new ImportVendorRequestDto();
    entity.code = x.code ? x.code.trim().replace(/\s+/g, '_') : '';
    entity.name = x.name;
    entity.type = x.type;
    entity.country = x.country;
    entity.location = x.location;
    entity.representor = x.representor;
    entity.telFax = x.telFax;
    entity.email = x.email;
    entity.note = x.note;
    entity.userId = userId;

    return entity;
  });
  await vendorService.importVendors(dataCreate);
  // const worksheet = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove = result.map((item) => item.line);
  // if (rowsToRemove.length > 0) {
  //   await removeLineSuccess(worksheet, rowsToRemove);
  // }
  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
