import { ValidationError, isEmpty } from 'class-validator';
import {
  buildErrorMessage,
  getDataImport,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { SamplingRateEntity } from '../../sampling-rate/entities/sampling-rate.entity';
import { ImportSamplingRateRequestDto } from '../../sampling-rate/request/import-sampling-rate.request.dto';
import {
  PRE_FIX,
  TABLE_NAME,
  TEMPLATE_SAMPLING_RATE,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importSamplingRate(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportSamplingRateRequestDto,
    columns: [],
    prefix: PRE_FIX.SAMPLING_RATE,
    sheetName: await i18n.translate('import.screen.samplingRate.sheet1'),
    tables: [],
    templateHeader: TEMPLATE_SAMPLING_RATE,
    tableMain: TABLE_NAME.SAMPLING_RATE,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );

  await Promise.all(
    dataSheet1.data.map(async (s) => {
      const errors: ValidationError[] = [];
      if (s.isAql !== 0 && s.isAql !== 1) {
        const error = new ValidationError();
        error.property = 'isAql';
        error.constraints = {
          error: await i18n.translate('error.INVALID_VALUE', {
            args: {
              property: await i18n.translate(
                `${PRE_FIX.SAMPLING_RATE}${error.property}`,
              ),
              number: '0, 1',
            },
          }),
        };
        errors.push(error);
      }

      if (
        !isEmpty(s.samplingRate) &&
        (s.samplingRate <= 0 || s.samplingRate > 100)
      ) {
        const error = new ValidationError();
        error.property = 'samplingRate';
        error.constraints = {
          error: await i18n.translate('error.SAMPLING_RATE_ERROR'),
        };
        errors.push(error);
      }

      if (errors && errors.length > 0) {
        s.error =
          (await buildErrorMessage(errors, PRE_FIX.SAMPLING_RATE, i18n)) +
          (isEmpty(s.error) ? '' : '\r\n' + s.error);
      }
      return s;
    }),
  );

  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  const result = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const dataCreate = result.map((x) => {
    const entity = new SamplingRateEntity();
    entity.code = x.code ? x.code.trim().replace(/\s+/g, '_') : '';
    entity.samplingRate = x.samplingRate;
    entity.isAql = x.isAql ?? 0;
    entity.description = x.description;
    entity.createdBy = userId;

    return entity;
  });

  if (dataCreate || dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();
    try {
      const metadata = connection.getMetadata(SamplingRateEntity);
      const numberOfParameters = metadata.columns.length;
      const maxEntitiesPerBatch = Math.floor(2000 / numberOfParameters);
      for (let i = 0; i < dataCreate.length; i += maxEntitiesPerBatch) {
        const chunk = dataCreate.slice(i, i + maxEntitiesPerBatch);
        await queryRunner.manager.save(chunk);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // const worksheet = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove = result.map((item) => item.line);
  // if (rowsToRemove.length > 0) {
  //   await removeLineSuccess(worksheet, rowsToRemove);
  // }
  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
