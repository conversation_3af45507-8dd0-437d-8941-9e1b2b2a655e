import { StatusEnum } from '../../../common/enums/status.enum';
import {
  getDataImport,
  mapEntityCode,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { PCodeEntity } from '../../p-code/entities/p-code.entity';
import { ImportPCodeRequestDto } from '../../p-code/request/import-p-code.request.dto';
import {
  PRE_FIX,
  TABLE_CODE,
  TABLE_NAME,
  TEMPLATE_P_CODE,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importPCode(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportPCodeRequestDto,
    columns: [TABLE_CODE.DEPARTMENT],
    prefix: PRE_FIX.P_CODE,
    sheetName: await i18n.translate('import.screen.pCode.sheet1'),
    tables: [TABLE_NAME.DEPARTMENT],
    templateHeader: TEMPLATE_P_CODE,
    tableMain: TABLE_NAME.P_CODE,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );
  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  const departmentMap = dataSheet1.dataMap.get(TABLE_NAME.DEPARTMENT);
  const result = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const dataCreate = result.map((x) => {
    const entity = new PCodeEntity();
    entity.code = x.code ? x.code.trim().replace(/\s+/g, '_') : '';
    entity.name = x.name;
    entity.departmentId = mapEntityCode(x.departmentCode, departmentMap);
    entity.description = x.description;
    entity.createdBy = userId;
    entity.status = StatusEnum.ACTIVE;

    return entity;
  });

  if (dataCreate || dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();
    try {
      const metadata = connection.getMetadata(PCodeEntity);
      const numberOfParameters = metadata.columns.length;
      const maxEntitiesPerBatch = Math.floor(2000 / numberOfParameters);
      for (let i = 0; i < dataCreate.length; i += maxEntitiesPerBatch) {
        const chunk = dataCreate.slice(i, i + maxEntitiesPerBatch);
        await queryRunner.manager.save(chunk);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // const worksheet = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove = result.map((item) => item.line);
  // if (rowsToRemove.length > 0) {
  //   await removeLineSuccess(worksheet, rowsToRemove);
  // }
  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
