import { StatusEnum } from '../../../common/enums/status.enum';
import {
  getDataImport,
  mapEntityCode,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { AqlSampleLimitDetailEntity } from '../../aql-sample-limit-detail/entities/aql-sample-limit-detail.entity';
import { AqlSampleLimitSamplingRateEntity } from '../../aql-sample-limit-sampling-rate/entities/aql-sample-limit-sampling-rate.entity';
import { AqlSampleLimitEntity } from '../../aql-sample-limit/entities/aql-sample-limit.entity';
import { ImportAqlSampleLimitRequestDto } from '../../aql-sample-limit/request/import-aql-sample-limit.request.dto';
import {
  PRE_FIX,
  TABLE_CODE,
  TABLE_NAME,
  TEMPLATE_AQL_SAMPLE_LIMIT,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importAqlSampleLimit(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportAqlSampleLimitRequestDto,
    columns: [
      TABLE_CODE.INSPECTION_SAMPLE_QUANTITY_CODE,
      TABLE_CODE.SAMPLING_RATE_CODE,
    ],
    prefix: PRE_FIX.AQL_SAMPLE_LIMIT,
    sheetName: await i18n.translate('import.screen.aqlSampleLimit.sheet1'),
    tables: [TABLE_NAME.INSPECTION_SAMPLE_QUANTITY, TABLE_NAME.SAMPLING_RATE],
    templateHeader: TEMPLATE_AQL_SAMPLE_LIMIT,
    tableMain: TABLE_NAME.AQL_SAMPLE_LIMIT,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );
  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);

  // map master data
  const inspectionSampleQuantityMap = dataSheet1.dataMap.get(
    TABLE_NAME.INSPECTION_SAMPLE_QUANTITY,
  );
  const samplingRateMap = dataSheet1.dataMap.get(TABLE_NAME.SAMPLING_RATE);

  const result = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  if (!result || result.length <= 0) {
    return { result: false, recordError: dataSheet1.data.length };
  }
  const dataCreate = result.map((x) => {
    const entity = new AqlSampleLimitEntity();
    entity.code = x.code ? x.code.trim().replace(/\s+/g, '_') : '';
    entity.name = x.name;
    entity.description = x.description;
    entity.status = StatusEnum.ACTIVE;
    entity.createdBy = userId;

    if (x.inspectionSampleQuantityCode) {
      entity.dataDetails = x.inspectionSampleQuantityCode
        .split(',')
        .map((x1) => {
          const detail = new AqlSampleLimitDetailEntity();
          detail.inspectionSampleQuantityId = mapEntityCode(
            x1,
            inspectionSampleQuantityMap,
          );
          return detail;
        });
    }

    if (x.samplingRateCode) {
      entity.samplingRates = x.samplingRateCode.split(',').map((x1) => {
        const samplingRate = new AqlSampleLimitSamplingRateEntity();
        samplingRate.samplingRateId = mapEntityCode(x1, samplingRateMap);
        return samplingRate;
      });
    }

    return entity;
  });

  // insert into db
  if (dataCreate && dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();

    try {
      const mainMetadata = connection.getMetadata(AqlSampleLimitEntity);
      const detailMetadata = connection.getMetadata(AqlSampleLimitDetailEntity);
      const samplingRateMetadata = connection.getMetadata(
        AqlSampleLimitSamplingRateEntity,
      );
      const mainColumnCount = mainMetadata.columns.length;
      const detailColumnCount = detailMetadata.columns.length;
      const samplingRateColumnCount = samplingRateMetadata.columns.length;
      const maxParameters = 2000;
      let currentBatch = [];
      let currentParametersCount = 0;
      for (const main of dataCreate) {
        const mainParametersCount = mainColumnCount;
        const detailParametersCount =
          (main.dataDetails?.length || 0) * detailColumnCount;
        const samplingRateParametersCount =
          (main.samplingRates?.length || 0) * samplingRateColumnCount;
        const totalProcessParameters =
          mainParametersCount +
          detailParametersCount +
          samplingRateParametersCount;
        if (currentParametersCount + totalProcessParameters > maxParameters) {
          await queryRunner.manager.save(currentBatch);
          currentBatch = [];
          currentParametersCount = 0;
        }
        currentBatch.push(main);
        currentParametersCount += totalProcessParameters;
      }
      if (currentBatch.length > 0) {
        await queryRunner.manager.save(currentBatch);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
