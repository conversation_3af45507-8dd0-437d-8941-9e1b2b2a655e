import {
  getDataImport,
  mapEntityCode,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { ErrorTypeEntity } from '../../error-type/entities/error-type.entity';
import { ImportErrorTypeRequestDto } from '../../error-type/request/import-error-type.request.dto';
import {
  PRE_FIX,
  TABLE_CODE,
  TABLE_NAME,
  TEMPLATE_ERROR_TYPE,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importErrorType(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const start = performance.now();
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportErrorTypeRequestDto,
    columns: [TABLE_CODE.ERROR_GROUP_CODE],
    prefix: PRE_FIX.ERROR_TYPE,
    sheetName: await i18n.translate('import.screen.errorType.sheet1'),
    tables: [TABLE_NAME.ERROR_GROUP],
    templateHeader: TEMPLATE_ERROR_TYPE,
    tableMain: TABLE_NAME.ERROR_TYPE,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );
  const errorGroupMap = dataSheet1.dataMap.get(TABLE_NAME.ERROR_GROUP);
  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  const result = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const dataCreate = result.map((x) => {
    const entity = new ErrorTypeEntity();
    entity.code = x.code ? x.code.trim().replace(/\s+/g, '_') : '';
    entity.name = x.name;
    // set entity.errorGroupId
    entity.errorGroupId = mapEntityCode(x.errorGroupCode, errorGroupMap);
    entity.description = x.description;
    entity.createdBy = userId;

    return entity;
  });

  if (dataCreate || dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();
    try {
      const metadata = connection.getMetadata(ErrorTypeEntity);
      const numberOfParameters = metadata.columns.length;
      const maxEntitiesPerBatch = Math.floor(2000 / numberOfParameters);
      for (let i = 0; i < dataCreate.length; i += maxEntitiesPerBatch) {
        const chunk = dataCreate.slice(i, i + maxEntitiesPerBatch);
        await queryRunner.manager.save(chunk);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // const worksheet = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove = result.map((item) => item.line);
  // if (rowsToRemove.length > 0) {
  //   await removeLineSuccess(worksheet, rowsToRemove);
  // }
  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  const end = performance.now();
  console.log(`Thời gian gọi API: ${(end - start).toFixed(2)} ms`);
  return { result: true, recordSuccess: dataCreate.length };
}
