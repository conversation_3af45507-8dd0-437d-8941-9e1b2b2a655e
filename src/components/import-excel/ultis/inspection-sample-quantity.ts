import { ValidationError, isEmpty } from 'class-validator';
import { StatusEnum } from '../../../common/enums/status.enum';
import {
  buildErrorMessage,
  getDataImport,
  mapEntityCode,
  validateMappings,
  writeDataErrorToExcel,
} from '../../../helper/import.helper';
import { InspectionSampleQuantityDetailEntity } from '../../inspection-sample-quantity-detail/entities/inspection-sample-quantity-detail.entity';
import { InspectionSampleQuantityEntity } from '../../inspection-sample-quantity/entities/inspection-sample-quantity.entity';
import {
  ImportInspectionSampleQuantityDetailDto,
  ImportInspectionSampleQuantityDto,
} from '../../inspection-sample-quantity/request/import-inspection-sample-quantity.request.dto';
import {
  PRE_FIX,
  TABLE_CODE,
  TABLE_NAME,
  TEMPLATE_INSPECTION_SAMPLE_QUANTITY,
  TEMPLATE_INSPECTION_SAMPLE_QUANTITY_DETAIL,
} from '../constants/import-excel.constants';
import { ImportInfoRequest } from '../request/import-info.request.dto';

export async function importInspectionSampleQuantity(
  workbook,
  userId,
  i18n,
  connection,
  baseProcessService,
) {
  const importInfoRequestSheet1: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportInspectionSampleQuantityDto,
    columns: [],
    prefix: PRE_FIX.INSPECTION_SAMPLE_QUANTITY,
    sheetName: await i18n.translate(
      'import.screen.inspectionSampleQuantity.sheet1',
    ),
    tables: [],
    templateHeader: TEMPLATE_INSPECTION_SAMPLE_QUANTITY,
    tableMain: TABLE_NAME.INSPECTION_SAMPLE_QUANTITY,
    keys: ['code'],
  };
  const dataSheet1 = await getDataImport(
    importInfoRequestSheet1,
    i18n,
    baseProcessService,
  );
  const importInfoRequestSheet2: ImportInfoRequest = {
    workbook: workbook,
    classType: ImportInspectionSampleQuantityDetailDto,
    columns: [TABLE_CODE.SAMPLING_RATE_CODE],
    prefix: PRE_FIX.INSPECTION_SAMPLE_QUANTITY_DETAIL,
    sheetName: await i18n.translate(
      'import.screen.inspectionSampleQuantity.sheet2',
    ),
    tables: [TABLE_NAME.SAMPLING_RATE],
    templateHeader: TEMPLATE_INSPECTION_SAMPLE_QUANTITY_DETAIL,
    tableMain: '',
    keys: ['keyMapping', TABLE_CODE.SAMPLING_RATE_CODE],
  };
  const dataSheet2 = await getDataImport(
    importInfoRequestSheet2,
    i18n,
    baseProcessService,
  );

  // map master data
  const samplingRateMap = dataSheet2.dataMap.get(TABLE_NAME.SAMPLING_RATE);
  await Promise.all(
    dataSheet1.data.map(async (parent) => {
      const mainErrors: ValidationError[] = [];
      const details = dataSheet2.data.filter((detail) => {
        if (!detail.keyMapping || !parent.keyMapping) return false;

        return (
          detail.keyMapping.toLowerCase() === parent.keyMapping.toLowerCase()
        );
      });

      await Promise.all(
        details.map(async (item) => {
          const subErrors: ValidationError[] = [];
          if (!isEmpty(item.samplingRateCode) && samplingRateMap) {
            const key = Object.keys(samplingRateMap).find(
              (k) =>
                k.toLowerCase() === item.samplingRateCode.trim().toLowerCase(),
            );
            const samplingRate = samplingRateMap[key];
            if (!isEmpty(samplingRate)) {
              if (samplingRate.isAql !== 1) {
                const error = new ValidationError();
                error.property = 'samplingRateCode';
                error.constraints = {
                  error: await i18n.translate(
                    'error.AQL_CHECK_RATIO_NOT_CHECKED',
                    {
                      args: {
                        property: item.samplingRateCode,
                      },
                    },
                  ),
                };
                subErrors.push(error);
              }
            }
          }

          if (
            item.acceptQuantity != null &&
            parent.inspectionSampleQuantity != null &&
            item.acceptQuantity > parent.inspectionSampleQuantity
          ) {
            const error = new ValidationError();
            error.property = 'acceptQuantity';
            error.constraints = {
              error: await i18n.translate(
                'error.ACCEPT_QTY_EXCEEDS_SAMPLE_QTY',
              ),
            };
            subErrors.push(error);
          }

          if (
            item.rejectQuantity != null &&
            parent.inspectionSampleQuantity != null &&
            item.rejectQuantity > parent.inspectionSampleQuantity
          ) {
            const error = new ValidationError();
            error.property = 'rejectQuantity';
            error.constraints = {
              error: await i18n.translate(
                'error.REJECT_QTY_EXCEEDS_SAMPLE_QTY',
              ),
            };
            subErrors.push(error);
          }

          if (
            item.acceptQuantity != null &&
            item.rejectQuantity != null &&
            item.acceptQuantity > item.rejectQuantity
          ) {
            const error = new ValidationError();
            error.property = 'acceptQuantity';
            error.constraints = {
              error: await i18n.translate(
                'error.ACCEPT_QTY_EXCEEDS_REJECT_QTY',
              ),
            };
            subErrors.push(error);
          }

          if (subErrors && subErrors.length > 0) {
            item.error =
              (await buildErrorMessage(
                subErrors,
                PRE_FIX.INSPECTION_SAMPLE_QUANTITY_DETAIL,
                i18n,
              )) + (isEmpty(item.error) ? '' : '\r\n' + item.error);
          }
          return item;
        }),
      );

      if (mainErrors && mainErrors.length > 0) {
        parent.error =
          (await buildErrorMessage(
            mainErrors,
            PRE_FIX.INSPECTION_SAMPLE_QUANTITY,
            i18n,
          )) + (isEmpty(parent.error) ? '' : '\r\n' + parent.error);
      }
      return parent;
    }),
  );

  await validateMappings(
    dataSheet1.data,
    dataSheet2.data,
    PRE_FIX.INSPECTION_SAMPLE_QUANTITY,
    importInfoRequestSheet2.sheetName,
    i18n,
  );
  await validateMappings(
    dataSheet2.data,
    dataSheet1.data,
    PRE_FIX.INSPECTION_SAMPLE_QUANTITY_DETAIL,
    importInfoRequestSheet1.sheetName,
    i18n,
  );

  let isError = false;
  if (dataSheet1.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet1, dataSheet1.data, i18n);
  if (dataSheet2.data.some((s) => s.error)) {
    isError = true;
  }
  await writeDataErrorToExcel(importInfoRequestSheet2, dataSheet2.data, i18n);

  const filteredList1 = dataSheet1.data
    .slice()
    .reverse()
    .filter((item) => !item.error);
  const result = filteredList1
    .map((parent) => {
      const details = dataSheet2.data.filter((detail) => {
        if (!detail.keyMapping || !parent.keyMapping) return false; // Kiểm tra null hoặc undefined trước

        return (
          detail.keyMapping.toLowerCase() === parent.keyMapping.toLowerCase()
        );
      });

      if (details.length === 0) {
        return { parent };
      }
      const validDetails = details.filter((detail) => detail.error);
      if (validDetails.length <= 0) {
        return {
          parent,
          details: details,
        };
      } else {
        return null;
      }
    })
    .filter((item) => item !== null);
  if (!result || result.length <= 0) {
    return { result: false, recordError: dataSheet1.data.length };
  }
  const dataCreate = result.map((x) => {
    const entity = new InspectionSampleQuantityEntity();
    entity.code = x.parent.code
      ? x.parent.code.trim().replace(/\s+/g, '_')
      : '';
    entity.inspectionSampleQuantity = x.parent.inspectionSampleQuantity;
    entity.description = x.parent.description;
    entity.status = StatusEnum.ACTIVE;
    entity.createdBy = userId;

    if (x.details && x.details.length > 0) {
      entity.dataDetails = Array.isArray(x.details)
        ? x.details.map((x1) => {
            const detail = new InspectionSampleQuantityDetailEntity();
            detail.samplingRateId = mapEntityCode(
              x1.samplingRateCode,
              samplingRateMap,
            );
            detail.acceptQuantity = x1.acceptQuantity;
            detail.rejectQuantity = x1.rejectQuantity;
            detail.description = x1.description;
            return detail;
          })
        : [];
    }

    return entity;
  });

  // insert into db
  if (dataCreate && dataCreate.length > 0) {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.startTransaction();

    try {
      const mainMetadata = connection.getMetadata(
        InspectionSampleQuantityEntity,
      );
      const detailMetadata = connection.getMetadata(
        InspectionSampleQuantityDetailEntity,
      );
      const mainColumnCount = mainMetadata.columns.length;
      const detailColumnCount = detailMetadata.columns.length;
      const maxParameters = 2000;
      let currentBatch = [];
      let currentParametersCount = 0;
      for (const main of dataCreate) {
        const mainParametersCount = mainColumnCount;
        const detailParametersCount =
          (main.dataDetails?.length || 0) * detailColumnCount;
        const totalProcessParameters =
          mainParametersCount + detailParametersCount;
        if (currentParametersCount + totalProcessParameters > maxParameters) {
          await queryRunner.manager.save(currentBatch);
          currentBatch = [];
          currentParametersCount = 0;
        }
        currentBatch.push(main);
        currentParametersCount += totalProcessParameters;
      }
      if (currentBatch.length > 0) {
        await queryRunner.manager.save(currentBatch);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // // remove error line sheet1
  // const worksheet1 = workbook.getWorksheet(importInfoRequestSheet1.sheetName);
  // const rowsToRemove1 = result.map((item) => item.parent.line);
  // if (rowsToRemove1.length > 0) {
  //   await removeLineSuccess(worksheet1, rowsToRemove1);
  // }

  // // remove error line sheet2
  // const worksheet2 = workbook.getWorksheet(importInfoRequestSheet2.sheetName);
  // const rowsToRemove2 = result.flatMap((item) =>
  //   item.details.map((detail) => detail.line),
  // );
  // if (rowsToRemove2.length > 0) {
  //   await removeLineSuccess(worksheet2, rowsToRemove2);
  // }

  if (isError) {
    return {
      result: false,
      recordSuccess: dataCreate.length,
      recordError: dataSheet1.data.length - dataCreate.length,
    };
  }
  return { result: true, recordSuccess: dataCreate.length };
}
