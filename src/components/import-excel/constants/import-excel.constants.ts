export enum IMPORT_TYPE {
  IQC_REQUEST = 0,
  PRODUCTION_PROCESS = 1,
  ERROR_GROUP = 2,
  ERROR_TYPE = 3,
  ERROR = 4,
  QC_REQUEST_TYPE = 5,
  ITEM_TYPE = 6,
  ITEM_LINE = 7,
  GOODS_TYPE = 8,
  UNIT = 9,
  PROCESS = 10,
  INSPECTION_GROUP = 11,
  INSPECTION_TYPE = 12,
  INSPECTION = 13,
  CUSTOMER = 14,
  VENDOR = 15,
  SAMPLING_RATE = 16,
  INSPECTION_STANDARD = 17,
  ERROR_HANDLING_STATUS = 18,
  EVALUATION_STANDARD_TYPE = 19,
  CHECKSHEET = 20,
  INSPECTION_SAMPLE_QUANTITY = 21,
  INSPECTION_SAMPLE_SYMBOL = 22,
  AQL_SAMPLE_LIMIT = 23,
  ITEM = 24,
  P_CODE = 25,
}

export const TABLE_NAME = {
  ITEM: 'items',
  QC_REQUEST_TYPE: 'qc_request_types',
  ITEM_LINE: 'item_lines',
  GOODS_TYPE: 'goods_types',
  PROCESS: 'processes',
  PRODUCTION_PROCESS: 'production_processes',
  ERROR_GROUP: 'error_groups',
  ERROR_TYPE: 'error_types',
  ERROR: 'errors',
  ITEM_TYPE: 'item_types',
  UNIT: 'units',
  P_CODE: 'p_code',
  INSPECTION_GROUP: 'inspection_groups',
  INSPECTION_TYPE: 'inspection_types',
  INSPECTION: 'inspections',
  CUSTOMER: 'customers',
  VENDOR: 'vendors',
  SAMPLING_RATE: 'sampling_rates',
  INSPECTION_STANDARD: 'inspection_standards',
  INSPECTION_SAMPLE_QUANTITY: 'inspection_sample_quantities',
  ERROR_HANDLING_STATUS: 'error_handling_statuses',
  EVALUATION_STANDARD_TYPE: 'evaluation_standard_types',
  CHECKSHEET: 'checksheets',
  INSPECTION_SAMPLE_SYMBOL: 'inspection_sample_symbols',
  AQL_SAMPLE_LIMIT: 'aql_sample_limits',
  DEPARTMENT: 'departments',
};

export const TABLE_CODE = {
  ITEM_CODE: 'itemCode',
  QC_REQUEST_TYPE_CODE: 'qcRequestTypeCode',
  ITEM_LINE_CODE: 'itemLineCode',
  GOODS_TYPE_CODE: 'goodsTypeCode',
  PROCESS_CODE: 'processCode',
  ERROR_GROUP_CODE: 'errorGroupCode',
  ERROR_TYPE_CODE: 'errorTypeCode',
  ITEM_TYPE_CODE: 'itemTypeCode',
  UNIT_CODE: 'unitCode',
  P_CODE: 'pCode',
  INSPECTION_GROUP_CODE: 'inspectionGroupCode',
  INSPECTION_TYPE_CODE: 'inspectionTypeCode',
  INSPECTION_CODE: 'inspectionCode',
  ERROR_CODE: 'errorCode',
  SAMPLING_RATE_CODE: 'samplingRateCode',
  INSPECTION_STANDARD_CODE: 'inspectionStandardCode',
  CHECKSHEET_CODE: 'checksheetCode',
  EVALUATION_STANDARD_TYPE_CODE: 'evaluationStandardTypeCode',
  INSPECTION_SAMPLE_QUANTITY_CODE: 'inspectionSampleQuantityCode',
  DEPARTMENT: 'departmentCode',
  CUSTOMER: 'customerCode',
};

export const PRE_FIX = {
  IQC_REQUEST: 'import.iqcRequest.',
  IQC_REQUEST_DETAIL: 'import.iqcRequestDetail.',
  PRODUCTION_PROCESS: 'import.productionProcess.',
  PRODUCTION_PROCESS_DETAIL: 'import.productionProcessDetail.',
  ERROR_GROUP: 'import.errorGroup.',
  ERROR_TYPE: 'import.errorType.',
  ERROR: 'import.error.',
  QC_REQUEST_TYPE: 'import.qcRequestType.',
  ITEM_TYPE: 'import.itemType.',
  ITEM_LINE: 'import.itemLine.',
  GOODS_TYPE: 'import.goodsType.',
  UNIT: 'import.unit.',
  PROCESS: 'import.process.',
  INSPECTION_GROUP: 'import.inspectionGroup.',
  INSPECTION_TYPE: 'import.inspectionType.',
  INSPECTION: 'import.inspection.',
  CUSTOMER: 'import.customer.',
  VENDOR: 'import.vendor.',
  SAMPLING_RATE: 'import.samplingRate.',
  INSPECTION_STANDARD: 'import.inspectionStandard.',
  INSPECTION_SAMPLE_QUANTITY: 'import.inspectionSampleQuantity.',
  INSPECTION_SAMPLE_QUANTITY_DETAIL: 'import.inspectionSampleQuantityDetail.',
  ERROR_HANDLING_STATUS: 'import.errorHandlingStatus.',
  EVALUATION_STANDARD_TYPE: 'import.evaluationStandardType.',
  CHECKSHEET: 'import.checksheet.',
  CHECKSHEET_DETAIL: 'import.checksheetDetail.',
  INSPECTION_SAMPLE_SYMBOL: 'import.inspectionSampleSymbol.',
  INSPECTION_SAMPLE_SYMBOL_DETAIL: 'import.inspectionSampleSymbolDetail.',
  INSPECTION_SAMPLE_SYMBOL_DETAIL_STANDARD:
    'import.inspectionSampleSymbolDetailStandard.',
  AQL_SAMPLE_LIMIT: 'import.aqlSampleLimit.',
  ITEM: 'import.item.',
  ITEM_UNIT: 'import.itemUnit.',
  ITEM_IQC: 'import.itemIqc.',
  ITEM_OQC: 'import.itemOqc.',
  P_CODE: 'import.pCode.',
};

export const TEMPLATE_PRODUCT_PROCESS = [
  {
    key: 'keyMapping',
    header: `${PRE_FIX.PRODUCTION_PROCESS}keyMapping`,
    type: 'string',
  },
  {
    key: 'code',
    header: `${PRE_FIX.PRODUCTION_PROCESS}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.PRODUCTION_PROCESS}name`,
    type: 'string',
  },
  {
    key: 'itemLineCode',
    header: `${PRE_FIX.PRODUCTION_PROCESS}itemLineCode`,
    type: 'string',
  },
  {
    key: 'goodsTypeCode',
    header: `${PRE_FIX.PRODUCTION_PROCESS}goodsTypeCode`,
    type: 'string',
  },
  {
    key: 'itemCode',
    header: `${PRE_FIX.PRODUCTION_PROCESS}itemCode`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.PRODUCTION_PROCESS}description`,
    type: 'string',
  },
];

export const TEMPLATE_PRODUCT_PROCESS_DETAIL = [
  {
    key: 'keyMapping',
    header: `${PRE_FIX.PRODUCTION_PROCESS_DETAIL}keyMapping`,
    type: 'string',
  },
  {
    key: 'processCode',
    header: `${PRE_FIX.PRODUCTION_PROCESS_DETAIL}processCode`,
    type: 'string',
  },
  {
    key: 'isProcessQc',
    header: `${PRE_FIX.PRODUCTION_PROCESS_DETAIL}isProcessQc`,
    type: 'number',
  },
  {
    key: 'executionNo',
    header: `${PRE_FIX.PRODUCTION_PROCESS_DETAIL}executionNo`,
    type: 'number',
  },
  {
    key: 'description',
    header: `${PRE_FIX.PRODUCTION_PROCESS_DETAIL}description`,
    type: 'string',
  },
];

export const TEMPLATE_IQC_REQUEST_DETAIL = [
  {
    key: 'keyMapping',
    header: `${PRE_FIX.IQC_REQUEST_DETAIL}keyMapping`,
    type: 'string',
  },
  {
    key: 'itemCode',
    header: `${PRE_FIX.IQC_REQUEST_DETAIL}itemCode`,
    type: 'string',
  },
  {
    key: 'lotNo',
    header: `${PRE_FIX.IQC_REQUEST_DETAIL}lotNo`,
    type: 'string',
  },
  {
    key: 'processCode',
    header: `${PRE_FIX.IQC_REQUEST_DETAIL}processCode`,
    type: 'string',
  },
  {
    key: 'itemUnitCode',
    header: `${PRE_FIX.IQC_REQUEST_DETAIL}itemUnitCode`,
    type: 'string',
  },
  {
    key: 'itemUnitQcCode',
    header: `${PRE_FIX.IQC_REQUEST_DETAIL}itemUnitQcCode`,
    type: 'string',
  },
  {
    key: 'grQuantity',
    header: `${PRE_FIX.IQC_REQUEST_DETAIL}grQuantity`,
    type: 'number',
  },
];

export const TEMPLATE_IQC_REQUEST = [
  {
    key: 'keyMapping',
    header: `${PRE_FIX.IQC_REQUEST}keyMapping`,
    type: 'string',
  },
  {
    key: 'qcRequestTypeCode',
    header: `${PRE_FIX.IQC_REQUEST}qcRequestTypeCode`,
    type: 'string',
  },
  {
    key: 'vendorCode',
    header: `${PRE_FIX.IQC_REQUEST}vendorCode`,
    type: 'string',
  },
  {
    key: 'requestDate',
    header: `${PRE_FIX.IQC_REQUEST}requestDate`,
    type: 'date',
  },
  {
    key: 'purchaseOrder',
    header: `${PRE_FIX.IQC_REQUEST}purchaseOrder`,
    type: 'string',
  },
  {
    key: 'grDate',
    header: `${PRE_FIX.IQC_REQUEST}grDate`,
    type: 'date',
  },
  {
    key: 'grNumber',
    header: `${PRE_FIX.IQC_REQUEST}grNumber`,
    type: 'date',
  },
  {
    key: 'returnResultDate',
    header: `${PRE_FIX.IQC_REQUEST}returnResultDate`,
    type: 'date',
  },
  {
    key: 'description',
    header: `${PRE_FIX.IQC_REQUEST}description`,
    type: 'string',
  },
];

export const TEMPLATE_ERROR_GROUP = [
  {
    key: 'code',
    header: `${PRE_FIX.ERROR_GROUP}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.ERROR_GROUP}name`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.ERROR_GROUP}description`,
    type: 'string',
  },
];

export const TEMPLATE_ERROR_TYPE = [
  {
    key: 'code',
    header: `${PRE_FIX.ERROR_TYPE}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.ERROR_TYPE}name`,
    type: 'string',
  },
  {
    key: 'errorGroupCode',
    header: `${PRE_FIX.ERROR_TYPE}errorGroupCode`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.ERROR_TYPE}description`,
    type: 'string',
  },
];

export const TEMPLATE_ERROR = [
  {
    key: 'code',
    header: `${PRE_FIX.ERROR}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.ERROR}name`,
    type: 'string',
  },
  {
    key: 'errorTypeCode',
    header: `${PRE_FIX.ERROR}errorTypeCode`,
    type: 'string',
  },
  {
    key: 'serverity',
    header: `${PRE_FIX.ERROR}serverity`,
    type: 'number',
  },
  {
    key: 'description',
    header: `${PRE_FIX.ERROR}description`,
    type: 'string',
  },
];

export const TEMPLATE_QC_REQUEST_TYPE = [
  {
    key: 'code',
    header: `${PRE_FIX.QC_REQUEST_TYPE}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.QC_REQUEST_TYPE}name`,
    type: 'string',
  },
  {
    key: 'qcType',
    header: `${PRE_FIX.QC_REQUEST_TYPE}qcType`,
    type: 'number',
  },
  {
    key: 'category',
    header: `${PRE_FIX.QC_REQUEST_TYPE}category`,
    type: 'number',
  },
  {
    key: 'isQcProcess',
    header: `${PRE_FIX.QC_REQUEST_TYPE}isQcProcess`,
    type: 'number',
  },
  {
    key: 'description',
    header: `${PRE_FIX.QC_REQUEST_TYPE}description`,
    type: 'string',
  },
];

export const TEMPLATE_ITEM_TYPE = [
  {
    key: 'code',
    header: `${PRE_FIX.ITEM_TYPE}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.ITEM_TYPE}name`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.ITEM_TYPE}description`,
    type: 'string',
  },
];

export const TEMPLATE_ITEM_LINE = [
  {
    key: 'code',
    header: `${PRE_FIX.ITEM_LINE}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.ITEM_LINE}name`,
    type: 'string',
  },
  {
    key: 'itemTypeCode',
    header: `${PRE_FIX.ITEM_LINE}itemTypeCode`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.ITEM_LINE}description`,
    type: 'string',
  },
];

export const TEMPLATE_GOODS_TYPE = [
  {
    key: 'code',
    header: `${PRE_FIX.GOODS_TYPE}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.GOODS_TYPE}name`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.GOODS_TYPE}description`,
    type: 'string',
  },
];

export const TEMPLATE_UNIT = [
  {
    key: 'code',
    header: `${PRE_FIX.UNIT}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.UNIT}name`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.UNIT}description`,
    type: 'string',
  },
];

export const TEMPLATE_PROCESS = [
  {
    key: 'code',
    header: `${PRE_FIX.PROCESS}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.PROCESS}name`,
    type: 'string',
  },
  {
    key: 'subProcessGroup',
    header: `${PRE_FIX.PROCESS}subProcessGroup`,
    type: 'string',
  },
  {
    key: 'pCode',
    header: `${PRE_FIX.PROCESS}pCode`,
    type: 'string',
  },
  {
    key: 'goodsTypeCode',
    header: `${PRE_FIX.PROCESS}goodsTypeCode`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.PROCESS}description`,
    type: 'string',
  },
];

export const TEMPLATE_INSPECTION_GROUP = [
  {
    key: 'code',
    header: `${PRE_FIX.INSPECTION_GROUP}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.INSPECTION_GROUP}name`,
    type: 'string',
  },
  {
    key: 'qcRequestTypeCode',
    header: `${PRE_FIX.INSPECTION_GROUP}qcRequestTypeCode`,
    type: 'string',
  },
  {
    key: 'checkType',
    header: `${PRE_FIX.INSPECTION_GROUP}checkType`,
    type: 'number',
  },
  {
    key: 'chemicalType',
    header: `${PRE_FIX.INSPECTION_GROUP}chemicalType`,
    type: 'number',
  },
  {
    key: 'oqcDeviceType',
    header: `${PRE_FIX.INSPECTION_GROUP}oqcDeviceType`,
    type: 'number',
  },
  {
    key: 'description',
    header: `${PRE_FIX.INSPECTION_GROUP}description`,
    type: 'string',
  },
];

export const TEMPLATE_INSPECTION_TYPE = [
  {
    key: 'code',
    header: `${PRE_FIX.INSPECTION_TYPE}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.INSPECTION_TYPE}name`,
    type: 'string',
  },
  {
    key: 'inspectionGroupCode',
    header: `${PRE_FIX.INSPECTION_TYPE}inspectionGroupCode`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.INSPECTION_TYPE}description`,
    type: 'string',
  },
];

export const TEMPLATE_INSPECTION = [
  {
    key: 'code',
    header: `${PRE_FIX.INSPECTION}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.INSPECTION}name`,
    type: 'string',
  },
  {
    key: 'inspectionTypeCode',
    header: `${PRE_FIX.INSPECTION}inspectionTypeCode`,
    type: 'string',
  },
  {
    key: 'managementScope',
    header: `${PRE_FIX.INSPECTION}managementScope`,
    type: 'string',
  },
  {
    key: 'errorCode',
    header: `${PRE_FIX.INSPECTION}errorCode`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.INSPECTION}description`,
    type: 'string',
  },
];

export const TEMPLATE_CUSTOMER = [
  {
    key: 'code',
    header: `${PRE_FIX.CUSTOMER}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.CUSTOMER}name`,
    type: 'string',
  },
  {
    key: 'country',
    header: `${PRE_FIX.CUSTOMER}country`,
    type: 'number',
  },
  {
    key: 'address',
    header: `${PRE_FIX.CUSTOMER}address`,
    type: 'string',
  },
  {
    key: 'representative',
    header: `${PRE_FIX.CUSTOMER}representative`,
    type: 'string',
  },
  {
    key: 'tel',
    header: `${PRE_FIX.CUSTOMER}tel`,
    type: 'string',
  },
  {
    key: 'email',
    header: `${PRE_FIX.CUSTOMER}email`,
    type: 'string',
  },
  {
    key: 'note',
    header: `${PRE_FIX.CUSTOMER}note`,
    type: 'string',
  },
];

export const TEMPLATE_VENDOR = [
  {
    key: 'code',
    header: `${PRE_FIX.VENDOR}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.VENDOR}name`,
    type: 'string',
  },
  {
    key: 'type',
    header: `${PRE_FIX.VENDOR}type`,
    type: 'number',
  },
  {
    key: 'country',
    header: `${PRE_FIX.VENDOR}country`,
    type: 'number',
  },
  {
    key: 'location',
    header: `${PRE_FIX.VENDOR}location`,
    type: 'string',
  },
  {
    key: 'representor',
    header: `${PRE_FIX.VENDOR}representor`,
    type: 'string',
  },
  {
    key: 'telFax',
    header: `${PRE_FIX.VENDOR}telFax`,
    type: 'string',
  },
  {
    key: 'email',
    header: `${PRE_FIX.VENDOR}email`,
    type: 'string',
  },
  {
    key: 'note',
    header: `${PRE_FIX.VENDOR}note`,
    type: 'string',
  },
];

export const TEMPLATE_SAMPLING_RATE = [
  {
    key: 'code',
    header: `${PRE_FIX.SAMPLING_RATE}code`,
    type: 'string',
  },
  {
    key: 'samplingRate',
    header: `${PRE_FIX.SAMPLING_RATE}samplingRate`,
    type: 'number',
  },
  {
    key: 'isAql',
    header: `${PRE_FIX.SAMPLING_RATE}isAql`,
    type: 'number',
  },
  {
    key: 'description',
    header: `${PRE_FIX.SAMPLING_RATE}description`,
    type: 'string',
  },
];

export const TEMPLATE_INSPECTION_STANDARD = [
  {
    key: 'code',
    header: `${PRE_FIX.INSPECTION_STANDARD}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.INSPECTION_STANDARD}name`,
    type: 'string',
  },
  {
    key: 'isAql',
    header: `${PRE_FIX.INSPECTION_STANDARD}isAql`,
    type: 'number',
  },
  {
    key: 'inpectionStandardType',
    header: `${PRE_FIX.INSPECTION_STANDARD}inpectionStandardType`,
    type: 'number',
  },
  {
    key: 'samplingRateCode',
    header: `${PRE_FIX.INSPECTION_STANDARD}samplingRateCode`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.INSPECTION_STANDARD}description`,
    type: 'string',
  },
];

export const TEMPLATE_INSPECTION_SAMPLE_QUANTITY = [
  {
    key: 'keyMapping',
    header: `${PRE_FIX.INSPECTION_SAMPLE_QUANTITY}keyMapping`,
    type: 'string',
  },
  {
    key: 'code',
    header: `${PRE_FIX.INSPECTION_SAMPLE_QUANTITY}code`,
    type: 'string',
  },
  {
    key: 'inspectionSampleQuantity',
    header: `${PRE_FIX.INSPECTION_SAMPLE_QUANTITY}inspectionSampleQuantity`,
    type: 'number',
  },
  {
    key: 'description',
    header: `${PRE_FIX.INSPECTION_SAMPLE_QUANTITY}description`,
    type: 'string',
  },
];

export const TEMPLATE_INSPECTION_SAMPLE_QUANTITY_DETAIL = [
  {
    key: 'keyMapping',
    header: `${PRE_FIX.INSPECTION_SAMPLE_QUANTITY_DETAIL}keyMapping`,
    type: 'string',
  },
  {
    key: 'samplingRateCode',
    header: `${PRE_FIX.INSPECTION_SAMPLE_QUANTITY_DETAIL}samplingRateCode`,
    type: 'string',
  },
  {
    key: 'acceptQuantity',
    header: `${PRE_FIX.INSPECTION_SAMPLE_QUANTITY_DETAIL}acceptQuantity`,
    type: 'number',
  },
  {
    key: 'rejectQuantity',
    header: `${PRE_FIX.INSPECTION_SAMPLE_QUANTITY_DETAIL}rejectQuantity`,
    type: 'number',
  },
  {
    key: 'description',
    header: `${PRE_FIX.INSPECTION_SAMPLE_QUANTITY_DETAIL}description`,
    type: 'string',
  },
];

export const TEMPLATE_ERROR_HANDLING_STATUS = [
  {
    key: 'code',
    header: `${PRE_FIX.ERROR_HANDLING_STATUS}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.ERROR_HANDLING_STATUS}name`,
    type: 'string',
  },
  {
    key: 'applicableObject',
    header: `${PRE_FIX.ERROR_HANDLING_STATUS}applicableObject`,
    type: 'number',
  },
  {
    key: 'isClaim',
    header: `${PRE_FIX.ERROR_HANDLING_STATUS}isClaim`,
    type: 'number',
  },
  {
    key: 'description',
    header: `${PRE_FIX.ERROR_HANDLING_STATUS}description`,
    type: 'string',
  },
];

export const TEMPLATE_EVALUATION_STANDARD_TYPE = [
  {
    key: 'code',
    header: `${PRE_FIX.EVALUATION_STANDARD_TYPE}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.EVALUATION_STANDARD_TYPE}name`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.EVALUATION_STANDARD_TYPE}description`,
    type: 'string',
  },
];

export const TEMPLATE_CHECKSHEET = [
  {
    key: 'keyMapping',
    header: `${PRE_FIX.CHECKSHEET}keyMapping`,
    type: 'string',
  },
  {
    key: 'code',
    header: `${PRE_FIX.CHECKSHEET}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.CHECKSHEET}name`,
    type: 'string',
  },
  {
    key: 'qcRequestTypeCode',
    header: `${PRE_FIX.CHECKSHEET}qcRequestTypeCode`,
    type: 'string',
  },
  {
    key: 'itemTypeCode',
    header: `${PRE_FIX.CHECKSHEET}itemTypeCode`,
    type: 'string',
  },
  {
    key: 'itemLineCode',
    header: `${PRE_FIX.CHECKSHEET}itemLineCode`,
    type: 'string',
  },
  {
    key: 'itemCode',
    header: `${PRE_FIX.CHECKSHEET}itemCode`,
    type: 'string',
  },
  {
    key: 'processCode',
    header: `${PRE_FIX.CHECKSHEET}processCode`,
    type: 'string',
  },
  {
    key: 'evaluationStandardTypeCode',
    header: `${PRE_FIX.CHECKSHEET}evaluationStandardTypeCode`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.CHECKSHEET}description`,
    type: 'string',
  },
];

export const TEMPLATE_CHECKSHEET_DETAIL = [
  {
    key: 'keyMapping',
    header: `${PRE_FIX.CHECKSHEET_DETAIL}keyMapping`,
    type: 'string',
  },
  {
    key: 'checkType',
    header: `${PRE_FIX.CHECKSHEET_DETAIL}checkType`,
    type: 'number',
  },
  {
    key: 'inspectionTypeCode',
    header: `${PRE_FIX.CHECKSHEET_DETAIL}inspectionTypeCode`,
    type: 'string',
  },
  {
    key: 'inspectionCode',
    header: `${PRE_FIX.CHECKSHEET_DETAIL}inspectionCode`,
    type: 'string',
  },
  {
    key: 'errorCode',
    header: `${PRE_FIX.CHECKSHEET_DETAIL}errorCode`,
    type: 'string',
  },
  {
    key: 'unitCode',
    header: `${PRE_FIX.CHECKSHEET_DETAIL}unitCode`,
    type: 'string',
  },
  {
    key: 'evaluationStandardTypeCode',
    header: `${PRE_FIX.CHECKSHEET_DETAIL}evaluationStandardTypeCode`,
    type: 'string',
  },
  {
    key: 'category',
    header: `${PRE_FIX.CHECKSHEET_DETAIL}category`,
    type: 'number',
  },
  {
    key: 'spec',
    header: `${PRE_FIX.CHECKSHEET_DETAIL}spec`,
    type: 'number',
  },
  {
    key: 'plusAbove',
    header: `${PRE_FIX.CHECKSHEET_DETAIL}plusAbove`,
    type: 'number',
  },
  {
    key: 'minusBelow',
    header: `${PRE_FIX.CHECKSHEET_DETAIL}minusBelow`,
    type: 'number',
  },
  {
    key: 'isScoring',
    header: `${PRE_FIX.CHECKSHEET_DETAIL}isScoring`,
    type: 'number',
  },
  {
    key: 'scoringScale',
    header: `${PRE_FIX.CHECKSHEET_DETAIL}scoringScale`,
    type: 'number',
  },
];

export const TEMPLATE_INSPECTION_SAMPLE_SYMBOL = [
  {
    key: 'keyMapping',
    header: `${PRE_FIX.INSPECTION_SAMPLE_SYMBOL}keyMapping`,
    type: 'string',
  },
  {
    key: 'code',
    header: `${PRE_FIX.INSPECTION_SAMPLE_SYMBOL}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.INSPECTION_SAMPLE_SYMBOL}name`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.INSPECTION_SAMPLE_SYMBOL}description`,
    type: 'string',
  },
];

export const TEMPLATE_INSPECTION_SAMPLE_SYMBOL_DETAIL = [
  {
    key: 'keyMapping',
    header: `${PRE_FIX.INSPECTION_SAMPLE_SYMBOL_DETAIL}keyMapping`,
    type: 'string',
  },
  {
    key: 'lotSizeFrom',
    header: `${PRE_FIX.INSPECTION_SAMPLE_SYMBOL_DETAIL}lotSizeFrom`,
    type: 'number',
  },
  {
    key: 'lotSizeTo',
    header: `${PRE_FIX.INSPECTION_SAMPLE_SYMBOL_DETAIL}lotSizeTo`,
    type: 'number',
  },
];

export const TEMPLATE_INSPECTION_SAMPLE_SYMBOL_DETAIL_STANDARD = [
  {
    key: 'keyMapping',
    header: `${PRE_FIX.INSPECTION_SAMPLE_SYMBOL_DETAIL_STANDARD}keyMapping`,
    type: 'string',
  },
  {
    key: 'inspectionStandardCode',
    header: `${PRE_FIX.INSPECTION_SAMPLE_SYMBOL_DETAIL_STANDARD}inspectionStandardCode`,
    type: 'string',
  },
  {
    key: 'inspectionSampleQuantityCode',
    header: `${PRE_FIX.INSPECTION_SAMPLE_SYMBOL_DETAIL_STANDARD}inspectionSampleQuantityCode`,
    type: 'string',
  },
];

export const TEMPLATE_AQL_SAMPLE_LIMIT = [
  {
    key: 'code',
    header: `${PRE_FIX.AQL_SAMPLE_LIMIT}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.AQL_SAMPLE_LIMIT}name`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.AQL_SAMPLE_LIMIT}description`,
    type: 'string',
  },
  {
    key: 'samplingRateCode',
    header: `${PRE_FIX.AQL_SAMPLE_LIMIT}samplingRateCode`,
    type: 'string',
  },
  {
    key: 'inspectionSampleQuantityCode',
    header: `${PRE_FIX.AQL_SAMPLE_LIMIT}inspectionSampleQuantityCode`,
    type: 'string',
  },
];

export const TEMPLATE_ITEM = [
  {
    key: 'keyMapping',
    header: `${PRE_FIX.ITEM_UNIT}keyMapping`,
    type: 'string',
  },
  {
    key: 'code',
    header: `${PRE_FIX.ITEM}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.ITEM}name`,
    type: 'string',
  },
  {
    key: 'nameEn',
    header: `${PRE_FIX.ITEM}nameEn`,
    type: 'string',
  },
  {
    key: 'itemLineCode',
    header: `${PRE_FIX.ITEM}itemLineCode`,
    type: 'string',
  },
  {
    key: 'customerCode',
    header: `${PRE_FIX.ITEM}customerCode`,
    type: 'string',
  },
  {
    key: 'goodsTypeCode',
    header: `${PRE_FIX.ITEM}goodsTypeCode`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.ITEM}description`,
    type: 'string',
  },
];

export const TEMPLATE_ITEM_UNIT = [
  {
    key: 'keyMapping',
    header: `${PRE_FIX.ITEM_UNIT}keyMapping`,
    type: 'string',
  },
  {
    key: 'unitCode1',
    header: `${PRE_FIX.ITEM_UNIT}unitCode1`,
    type: 'string',
  },
  {
    key: 'description1',
    header: `${PRE_FIX.ITEM_UNIT}description1`,
    type: 'string',
  },
  {
    key: 'unitCode2',
    header: `${PRE_FIX.ITEM_UNIT}unitCode2`,
    type: 'string',
  },
  {
    key: 'ratio',
    header: `${PRE_FIX.ITEM_UNIT}ratio`,
    type: 'number',
  },
  {
    key: 'description2',
    header: `${PRE_FIX.ITEM_UNIT}description2`,
    type: 'string',
  },
];

export const TEMPLATE_ITEM_IQC = [
  {
    key: 'keyMapping',
    header: `${PRE_FIX.ITEM_IQC}keyMapping`,
    type: 'string',
  },
  {
    key: 'inspectionStandardCode',
    header: `${PRE_FIX.ITEM_IQC}inspectionStandardCode`,
    type: 'string',
  },
  {
    key: 'isAppearanceInspection',
    header: `${PRE_FIX.ITEM_IQC}isAppearanceInspection`,
    type: 'number',
  },
  {
    key: 'isMeasurementInspection',
    header: `${PRE_FIX.ITEM_IQC}isMeasurementInspection`,
    type: 'number',
  },
  {
    key: 'inspectionGroupCode',
    header: `${PRE_FIX.ITEM_IQC}inspectionGroupCode`,
    type: 'string',
  },
  {
    key: 'isSportTest',
    header: `${PRE_FIX.ITEM_IQC}isSportTest`,
    type: 'number',
  },
];

export const TEMPLATE_ITEM_OQC = [
  {
    key: 'keyMapping',
    header: `${PRE_FIX.ITEM_OQC}keyMapping`,
    type: 'string',
  },
  {
    key: 'inspectionStandardCode',
    header: `${PRE_FIX.ITEM_OQC}inspectionStandardCode`,
    type: 'string',
  },
  {
    key: 'isAppearanceInspection',
    header: `${PRE_FIX.ITEM_OQC}isAppearanceInspection`,
    type: 'number',
  },
  {
    key: 'isMeasurementInspection',
    header: `${PRE_FIX.ITEM_OQC}isMeasurementInspection`,
    type: 'number',
  },
  {
    key: 'inspectionGroupCode',
    header: `${PRE_FIX.ITEM_OQC}inspectionGroupCode`,
    type: 'string',
  },
];

export const TEMPLATE_P_CODE = [
  {
    key: 'code',
    header: `${PRE_FIX.P_CODE}code`,
    type: 'string',
  },
  {
    key: 'name',
    header: `${PRE_FIX.P_CODE}name`,
    type: 'string',
  },
  {
    key: 'departmentCode',
    header: `${PRE_FIX.P_CODE}departmentCode`,
    type: 'string',
  },
  {
    key: 'description',
    header: `${PRE_FIX.P_CODE}description`,
    type: 'string',
  },
];
