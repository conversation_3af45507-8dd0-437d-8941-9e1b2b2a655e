import { Injectable, Logger } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { InjectDataSource } from '@nestjs/typeorm';
import { Workbook } from 'exceljs';
import { DataSource } from 'typeorm';
import { ResponseCodeEnum } from '../../../constant/response-code.enum';

import { ResponseBuilder } from '../../../utils/response-builder';

import * as ExcelJS from 'exceljs';
import * as fs from 'fs';
import * as path from 'path';
import { ErrorData } from '../../../common/errors/base.error';
import { SRC_DIR } from '../../../main';
import { VendorService } from '../../another-service/services/vendor-service';
import { BaseProcessService } from '../../base-process-service/base-process.service';
import { IMPORT_TYPE } from '../constants/import-excel.constants';
import { GetImportExcelTemplateRequest } from '../request/get-import-excel-template.request.dto';
import { ImportExcelRequest } from '../request/import-excel.request.dto';
import { importChecksheet } from '../ultis/checksheet';
import { importCustomer } from '../ultis/customer';
import { importError } from '../ultis/error';
import { importErrorGroup } from '../ultis/error-group';
import { importErrorHandlingStatus } from '../ultis/error-handling-status';
import { importErrorType } from '../ultis/error-type';
import { importEvaluationStandardType } from '../ultis/evaluation-standard-type';
import { importGoodsType } from '../ultis/goods-type';
import { importInspection } from '../ultis/inspection';
import { importInspectionGroup } from '../ultis/inspection-group';
import { importInspectionSampleQuantity } from '../ultis/inspection-sample-quantity';
import { importInspectionStandard } from '../ultis/inspection-standard';
import { importInspectionType } from '../ultis/inspection-type';
import { importIqcRequest } from '../ultis/iqc-request';
import { importItem } from '../ultis/item';
import { importItemLine } from '../ultis/item-line';
import { importItemType } from '../ultis/item-type';
import { importPCode } from '../ultis/p-code';
import { importProcess } from '../ultis/process';
import { importProductionProcess } from '../ultis/production-process';
import { importQcRequestType } from '../ultis/qc-request-type';
import { importSamplingRate } from '../ultis/sampling-rate';
import { importUnit } from '../ultis/unit';
import { importVendor } from '../ultis/vendor';
@Injectable()
export class ImportExcelService {
  private readonly logger = new Logger(ImportExcelService.name);

  constructor(
    private readonly i18n: I18nService,
    private readonly baseProcessService: BaseProcessService,
    private readonly vendorService: VendorService,

    @InjectDataSource()
    private readonly connection: DataSource,
  ) {}

  private pathFileName(fileName: string) {
    return path.join(SRC_DIR, '..', 'import-template', fileName);
  }

  async import(payload: ImportExcelRequest): Promise<any> {
    const { type, files } = payload;
    const workbook = new Workbook();
    try {
      await workbook.xlsx.load(Buffer.from(files[0].data));
    } catch (error) {
      throw ErrorData.Validate.fileUploadDataError(this.i18n);
    }
    let response;
    switch (type) {
      case IMPORT_TYPE.IQC_REQUEST:
        response = await importIqcRequest(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.PRODUCTION_PROCESS:
        response = await importProductionProcess(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.ERROR_GROUP:
        response = await importErrorGroup(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.ERROR_TYPE:
        response = await importErrorType(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.ERROR:
        response = await importError(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.QC_REQUEST_TYPE:
        response = await importQcRequestType(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.ITEM_TYPE:
        response = await importItemType(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.ITEM_LINE:
        response = await importItemLine(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.GOODS_TYPE:
        response = await importGoodsType(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.UNIT:
        response = await importUnit(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.PROCESS:
        response = await importProcess(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.INSPECTION_GROUP:
        response = await importInspectionGroup(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.INSPECTION_TYPE:
        response = await importInspectionType(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.INSPECTION:
        response = await importInspection(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.CUSTOMER:
        response = await importCustomer(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.VENDOR:
        response = await importVendor(
          workbook,
          payload.userId,
          this.i18n,
          this.baseProcessService,
          this.vendorService,
        );
        break;
      case IMPORT_TYPE.SAMPLING_RATE:
        response = await importSamplingRate(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.INSPECTION_STANDARD:
        response = await importInspectionStandard(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.ERROR_HANDLING_STATUS:
        response = await importErrorHandlingStatus(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.EVALUATION_STANDARD_TYPE:
        response = await importEvaluationStandardType(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.CHECKSHEET:
        response = await importChecksheet(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.INSPECTION_SAMPLE_QUANTITY:
        response = await importInspectionSampleQuantity(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      // case IMPORT_TYPE.INSPECTION_SAMPLE_SYMBOL:
      //   response = await importInspectionSampleSymbol(
      //     workbook,
      //     payload.userId,
      //     this.i18n,
      //     this.connection,
      //     this.baseProcessService,
      //   );
      //   break;
      // case IMPORT_TYPE.AQL_SAMPLE_LIMIT:
      //   response = await importAqlSampleLimit(
      //     workbook,
      //     payload.userId,
      //     this.i18n,
      //     this.connection,
      //     this.baseProcessService,
      //   );
      //   break;
      case IMPORT_TYPE.ITEM:
        response = await importItem(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
      case IMPORT_TYPE.P_CODE:
        response = await importPCode(
          workbook,
          payload.userId,
          this.i18n,
          this.connection,
          this.baseProcessService,
        );
        break;
    }
    // await workbook.xlsx.writeFile('excel-template.xlsx');
    response.file = await workbook.xlsx.writeBuffer();
    if (!response.result) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(await this.i18n.translate('error.BAD_REQUEST'))
        .withData(response)
        .build();
    }
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .withData(response)
      .build();
  }

  async getTemplate(request: GetImportExcelTemplateRequest): Promise<any> {
    let fileName = '';

    switch (request.type) {
      case IMPORT_TYPE.PRODUCTION_PROCESS:
        fileName = 'import-production-process.xlsx';
        break;
      case IMPORT_TYPE.ERROR_GROUP:
        fileName = 'import-error-group.xlsx';
        break;
      case IMPORT_TYPE.ERROR_TYPE:
        fileName = 'import-error-type.xlsx';
        break;
      case IMPORT_TYPE.ERROR:
        fileName = 'import-error.xlsx';
        break;
      case IMPORT_TYPE.QC_REQUEST_TYPE:
        fileName = 'import-qc-request-type.xlsx';
        break;
      case IMPORT_TYPE.ITEM_TYPE:
        fileName = 'import-item-type.xlsx';
        break;
      case IMPORT_TYPE.ITEM_LINE:
        fileName = 'import-item-line.xlsx';
        break;
      case IMPORT_TYPE.GOODS_TYPE:
        fileName = 'import-goods-type.xlsx';
        break;
      case IMPORT_TYPE.UNIT:
        fileName = 'import-unit.xlsx';
        break;
      case IMPORT_TYPE.PROCESS:
        fileName = 'import-process.xlsx';
        break;
      case IMPORT_TYPE.INSPECTION_GROUP:
        fileName = 'import-inspection-group.xlsx';
        break;
      case IMPORT_TYPE.INSPECTION_TYPE:
        fileName = 'import-inspection-type.xlsx';
        break;
      case IMPORT_TYPE.INSPECTION:
        fileName = 'import-inspection.xlsx';
        break;
      case IMPORT_TYPE.CUSTOMER:
        fileName = 'import-customer.xlsx';
        break;
      case IMPORT_TYPE.VENDOR:
        fileName = 'import-vendor.xlsx';
        break;
      case IMPORT_TYPE.SAMPLING_RATE:
        fileName = 'import-sampling-rate.xlsx';
        break;
      case IMPORT_TYPE.INSPECTION_STANDARD:
        fileName = 'import-inspection-standard.xlsx';
        break;
      case IMPORT_TYPE.ERROR_HANDLING_STATUS:
        fileName = 'import-error-handling-status.xlsx';
        break;
      case IMPORT_TYPE.EVALUATION_STANDARD_TYPE:
        fileName = 'import-evaluation-standard-type.xlsx';
        break;
      case IMPORT_TYPE.CHECKSHEET:
        fileName = 'import-checksheet.xlsx';
        break;
      case IMPORT_TYPE.INSPECTION_SAMPLE_QUANTITY:
        fileName = 'import-inspection-sample-quantity.xlsx';
        break;
      case IMPORT_TYPE.ITEM:
        fileName = 'import-item.xlsx';
        break;
      case IMPORT_TYPE.P_CODE:
        fileName = 'import-p-code.xlsx';
        break;
      default:
        throw new Error('Invalid import type');
    }

    const filePath = this.pathFileName(fileName);
    if (!fs.existsSync(filePath)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.FILE_NOT_FOUND'))
        .build();
    }
    try {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.readFile(filePath);

      const buffer = await workbook.xlsx.writeBuffer();

      return new ResponseBuilder(buffer)
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(await this.i18n.translate('success.SUCCESS'))
        .build();
    } catch (error) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.INTERNAL_SERVER_ERROR)
        .withMessage(await this.i18n.translate('error.DOWNLOAD_FAILED'))
        .build();
    }
  }
}
