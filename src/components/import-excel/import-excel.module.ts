import { Module } from '@nestjs/common';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { BaseProcessModule } from '../base-process-service/base-process.module';
import { ImportExcelController } from './controller/import-excel.controller';
import { ImportExcelService } from './service/import-excel.service';

@Module({
  imports: [AnotherServiceModule, BaseProcessModule],
  providers: [ImportExcelService],
  exports: [ImportExcelService],
  controllers: [ImportExcelController],
})
export class ImportExcelModule {}
