import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { BaseDto } from '../../../core/dto/base.dto';

export class File {
  filename: string;
  data: ArrayBuffer;
  encoding: string;
  mimetype: string;
  limit: boolean;
}

export class ImportExcelRequest extends BaseDto {
  @ApiProperty()
  @Transform(({ value }) => Number(value))
  @IsNotEmpty()
  type: number;

  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'binary' },
  })
  @IsNotEmpty()
  files: File[];
}
