import {
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Matches,
  MaxLength,
  ValidateIf,
} from 'class-validator';
import { BaseDto } from '../../../core/dto/base.dto';

export class ImportVendorRequestDto extends BaseDto {
  @IsNotEmpty()
  @MaxLength(50)
  @Matches(/^[a-zA-Z0-9_.-]+$/)
  code: string;

  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @IsNotEmpty()
  @IsNumber()
  type: number;

  @IsNotEmpty()
  @IsNumber()
  country: number;

  @IsOptional()
  @MaxLength(255)
  location: string;

  @IsOptional()
  @MaxLength(50)
  representor?: string;

  @IsOptional()
  @MaxLength(15)
  telFax?: string;

  @IsOptional()
  @MaxLength(50)
  @ValidateIf((o) => o.email !== '')
  @IsEmail()
  email?: string;

  @IsOptional()
  @MaxLength(255)
  note?: string;
}
