import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>any } from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { InspectionErrorEntity } from '../../inspection-error/entities/inspection-error.entity';

@Entity({ name: 'inspections' })
export class InspectionEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    unique: true,
    nullable: false,
    length: 255,
  })
  code: string;

  @Column({
    type: 'nvarchar',
    nullable: false,
    length: 1000,
  })
  name: string;

  @Column({
    type: 'int',
    nullable: false,
  })
  inspectionTypeId: number;

  @Column({
    type: 'nvarchar',
    length: 1000,
  })
  managementScope: string;

  @Column({
    type: 'nvarchar',
    length: 255,
  })
  description: string;

  @Column({
    type: 'tinyint',
  })
  status: number;

  @OneToMany(() => InspectionErrorEntity, (s) => s.inspection, {
    cascade: ['insert', 'update'],
    eager: true,
  })
  @JoinColumn({ name: 'id', referencedColumnName: 'inspection_id' })
  inspectionErrors: InspectionErrorEntity[];
}
