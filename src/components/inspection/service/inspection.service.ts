import { Injectable, Logger } from '@nestjs/common';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';
import { In } from 'typeorm';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { InspectionEntity } from '../entities/inspection.entity';
import { DeleteInspectionRequestDto } from '../request/delete-inspection.request.dto';
import { CreateInspectionRequestDto } from './../request/create-inspection.request.dto';
import { GetDetailInspectionRequestDto } from './../request/get-detail-inspection.request.dto';
import { GetListInspectionRequestDto } from './../request/get-list-inspection.request.dto';
import { UpdateInspectionRequestDto } from './../request/update-inspection.request.dto';
import { UpdateStatusInspectionRequestDto } from './../request/update-status-inspection.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { StatusEnum } from '../../../common/enums/status.enum';
import { BaseService } from '../../../common/service/base.service';
import { UserService } from '../../another-service/services/user-service';
import { ChecksheetDetailService } from '../../checksheet-detail/service/checksheet-detail.service';
import { ErrorService } from '../../error/service/error.service';
import { InspectionErrorEntity } from '../../inspection-error/entities/inspection-error.entity';
import { InspectionErrorService } from '../../inspection-error/service/inspection-error.service';
import { InspectionTypeService } from '../../inspection-type/service/inspection-type.service';
import { MasterDataReferenceService } from '../../master-data-reference/service/master-data-reference.service';
import { InspectionRepository } from '../repository/inspection.repository';

@Injectable()
export class InspectionService extends BaseService {
  private readonly logger = new Logger(InspectionService.name);

  constructor(
    private readonly inspectionRepository: InspectionRepository,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly inspectionTypeService: InspectionTypeService,

    private readonly errorService: ErrorService,

    private readonly inspectionErrorService: InspectionErrorService,

    private readonly checksheetDetailService: ChecksheetDetailService,

    private readonly masterDataReferenceService: MasterDataReferenceService,
  ) {
    super(userService);
  }

  async create(request: CreateInspectionRequestDto): Promise<any> {
    const existCode = await this.inspectionRepository.findOneByCode(
      request.code,
    );

    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate exist inspection-types
    const inspectionType = await this.inspectionTypeService.findOneById(
      request?.inspectionTypeId,
    );
    if (
      isEmpty(inspectionType) ||
      inspectionType.status === StatusEnum.IN_ACTIVE
    ) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.INSPECTION_TYPE_IS_NOT_EXISTS'),
        )
        .build();
    }

    // Validate exist errors
    if (request?.errorIds && request?.errorIds.length > 0) {
      const errors = await this.errorService.findAllByIds(request?.errorIds);
      const errorWithStatusInactive = errors?.filter(
        (error) => error.status === StatusEnum.IN_ACTIVE,
      );
      if (isEmpty(errors) || !isEmpty(errorWithStatusInactive)) {
        return new ResponseBuilder()
          .withCode(ResponseCodeEnum.BAD_REQUEST)
          .withMessage(await this.i18n.translate('error.ERROR_IS_NOT_EXISTS'))
          .build();
      }
    }

    const inspectionEntity = this.inspectionRepository.createEntity(request);
    const inspectionErrors = [];
    request?.errorIds?.forEach((error) => {
      const inspectionErrorEntity = new InspectionErrorEntity();
      inspectionErrorEntity.errorId = error;
      inspectionErrors.push(inspectionErrorEntity);
    });
    inspectionEntity.inspectionErrors = inspectionErrors;
    const inspection = await this.inspectionRepository.create(inspectionEntity);

    return new ResponseBuilder(inspection)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListInspectionRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.inspectionRepository.getList(request);

    const errorMap = await this.mapErrorToResponse(data);
    const response = await this.mapUserInfoToResponse(errorMap);

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailInspectionRequestDto): Promise<any> {
    const inspectionExist = await this.inspectionRepository.findOneById(
      request?.id,
    );

    if (isEmpty(inspectionExist)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const inspection = await this.inspectionRepository.getDetail(request);
    const errorMap = await this.mapErrorToResponse([inspection]);
    const response = await this.mapUserInfoToResponse(errorMap);

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateInspectionRequestDto): Promise<any> {
    const { id } = request;
    const inspection = await this.inspectionRepository.findOneById(id);

    if (isEmpty(inspection)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate inspection in used
    const usedList = await this.validateInspectionInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.INSPECTION_IN_USED'))
        .build();
    }

    // Validate Code unique
    const existCode = await this.inspectionRepository.findOneByCode(
      request.code,
    );

    if (!isEmpty(existCode) && existCode.id !== inspection.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    // Validate exist inspection-types
    const inspectionType = await this.inspectionTypeService.findOneById(
      request?.inspectionTypeId,
    );
    if (
      isEmpty(inspectionType) ||
      inspectionType.status === StatusEnum.IN_ACTIVE
    ) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(
          await this.i18n.translate('error.INSPECTION_TYPE_IS_NOT_EXISTS'),
        )
        .build();
    }

    // Validate exist errors
    if (request?.errorIds && request?.errorIds.length > 0) {
      const errors = await this.errorService.findAllByIds(request?.errorIds);
      const errorWithStatusInactive = errors?.filter(
        (error) => error.status === StatusEnum.IN_ACTIVE,
      );
      if (isEmpty(errors) || !isEmpty(errorWithStatusInactive)) {
        return new ResponseBuilder()
          .withCode(ResponseCodeEnum.BAD_REQUEST)
          .withMessage(await this.i18n.translate('error.ERROR_IS_NOT_EXISTS'))
          .build();
      }
    }

    const dataUpdate = this.inspectionRepository.updateEntity(
      request,
      inspection,
    );

    const inspectionErrors = [];
    request?.errorIds?.forEach((error) => {
      const inspectionErrorEntity = new InspectionErrorEntity();
      inspectionErrorEntity.errorId = error;
      inspectionErrors.push(inspectionErrorEntity);
    });
    dataUpdate.inspectionErrors = inspectionErrors;

    const data = await this.inspectionRepository.update(dataUpdate);

    const errorMap = await this.mapErrorToResponse([data]);
    const response = await this.mapUserInfoToResponse(errorMap);

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteInspectionRequestDto): Promise<any> {
    const { id } = request;
    const inspection = await this.inspectionRepository.findOneById(id);

    if (isEmpty(inspection)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate inspection in used
    const usedList = await this.validateInspectionInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.INSPECTION_IN_USED'))
        .build();
    }

    await this.inspectionErrorService.delete([id]);
    await this.inspectionRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(request: UpdateStatusInspectionRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.inspectionRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.inspectionRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(request: UpdateStatusInspectionRequestDto): Promise<any> {
    const { ids } = request;

    const listExitsInDB = await this.inspectionRepository.findAllByIds(ids);

    if (isEmpty(listExitsInDB) || listExitsInDB?.length !== ids?.length) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.inspectionRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async validateInspectionInUsed(id: number): Promise<boolean> {
    // Validate used in checksheet-details
    const checksheetDetailList =
      await this.checksheetDetailService.findAllByInspectionIds([id]);
    if (!isEmpty(checksheetDetailList)) {
      return true;
    }

    // Validate used in spc-by-qc-type
    const spcByQctypeList =
      await this.masterDataReferenceService.findAllSpcByQcTypeByInspectionIds([
        id,
      ]);
    if (!isEmpty(spcByQctypeList)) {
      return true;
    }

    // Validate used in another service
    const isUse =
      await this.masterDataReferenceService.validateDataMasterUseAnotherService(
        new ValidateMasterRequestDto('inspection_id', id),
      );
    if (isUse) {
      return true;
    }

    return false;
  }

  async findOneById(id: number): Promise<InspectionEntity> {
    return await this.inspectionRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<InspectionEntity[]> {
    return await this.inspectionRepository.findAllByIds(ids);
  }

  async getAllInfoByIds(ids: number[]): Promise<any> {
    const joinedString: string = ids.join(',');
    const request = new GetListInspectionRequestDto();
    request.queryIds = joinedString;
    const data = await this.inspectionRepository.getList(request);

    const response = await this.mapErrorToResponse(data.data);

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async mapErrorToResponse(data: InspectionEntity[]) {
    const ids: number[] = Array.from(
      new Set(
        data
          .flatMap((item) => item.id)
          .filter((id) => id !== null && id !== undefined),
      ),
    );
    if (isEmpty(ids)) return data;

    const errorOfInspection =
      await this.inspectionErrorService.findErrorByInspectionIds(ids);

    if (isEmpty(errorOfInspection)) return data;

    const inspectionErrorMap = errorOfInspection.reduce(
      (acc, { inspectionId, errorId, errorCode, errorName }) => {
        if (!acc[inspectionId]) {
          acc[inspectionId] = [];
        }
        acc[inspectionId].push({ errorId, errorCode, errorName });
        return acc;
      },
      {} as Record<
        number,
        { errorId: number; errorCode: string; errorName: string }[]
      >,
    );

    return (
      data?.map((item) => {
        return {
          ...item,
          errors: inspectionErrorMap[item.id],
        };
      }) || []
    );
  }

  async findAllByInspectionTypeIds(ids: number[]): Promise<InspectionEntity[]> {
    const filterCondition: any = {
      inspectionTypeId: In(ids),
    };
    return await this.inspectionRepository.findByCondition(filterCondition);
  }
}
