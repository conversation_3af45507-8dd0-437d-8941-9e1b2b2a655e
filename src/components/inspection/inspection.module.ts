import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { ChecksheetDetailModule } from '../checksheet-detail/checksheet-detail.module';
import { ErrorModule } from '../error/error.module';
import { InspectionErrorModule } from '../inspection-error/inspection-error.module';
import { InspectionTypeModule } from '../inspection-type/inspection-type.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { InspectionController } from './controller/inspection.controller';
import { InspectionEntity } from './entities/inspection.entity';
import { InspectionRepository } from './repository/inspection.repository';
import { InspectionService } from './service/inspection.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([InspectionEntity]),
    AnotherServiceModule,
    InspectionTypeModule,
    ErrorModule,
    InspectionErrorModule,
    ChecksheetDetailModule,
    MasterDataReferenceModule,
  ],
  providers: [InspectionService, InspectionRepository],
  exports: [InspectionService],
  controllers: [InspectionController],
})
export class InspectionModule {}
