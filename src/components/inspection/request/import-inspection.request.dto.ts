import {
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
} from 'class-validator';

export class ImportInspectionRequestDto {
  @IsNotEmpty()
  @MaxLength(50)
  @Matches(/^[a-zA-Z0-9_.-]+$/)
  code: string;

  @IsNotEmpty()
  @MaxLength(1000)
  name: string;

  @IsNotEmpty()
  @IsString()
  inspectionTypeCode: string;

  @IsOptional()
  @MaxLength(1000)
  managementScope?: string;

  @IsOptional()
  @IsString()
  errorCode: string;

  @IsOptional()
  @MaxLength(255)
  description?: string;
}
