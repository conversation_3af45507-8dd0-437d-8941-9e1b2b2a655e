import { BaseDto } from '@core/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  MaxLength,
} from 'class-validator';

export class CreateInspectionRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(255)
  code: string;

  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(1000)
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  inspectionTypeId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(1000)
  managementScope?: string;

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  errorIds: number[];

  @ApiPropertyOptional()
  @IsOptional()
  @MaxLength(255)
  description?: string;
}
