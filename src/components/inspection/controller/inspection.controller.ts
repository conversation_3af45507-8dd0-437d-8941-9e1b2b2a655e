import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  ACTIVE_INSPECTION_PERMISSION,
  CREATE_INSPECTION_PERMISSION,
  DELETE_INSPECTION_PERMISSION,
  DETAIL_INSPECTION_PERMISSION,
  INACTIVE_INSPECTION_PERMISSION,
  LIST_INSPECTION_PERMISSION,
  UPDATE_INSPECTION_PERMISSION,
} from '../../../utils/permissions/inspection.permission';
import { CreateInspectionRequestDto } from '../request/create-inspection.request.dto';
import { DeleteInspectionRequestDto } from '../request/delete-inspection.request.dto';
import { GetDetailInspectionRequestDto } from '../request/get-detail-inspection.request.dto';
import { GetListInspectionRequestDto } from '../request/get-list-inspection.request.dto';
import { UpdateInspectionRequestDto } from '../request/update-inspection.request.dto';
import { UpdateStatusInspectionRequestDto } from '../request/update-status-inspection.request.dto';
import { InspectionService } from '../service/inspection.service';

@Controller('inspections')
export class InspectionController {
  constructor(private readonly inspectionService: InspectionService) {}

  @PermissionCode(DETAIL_INSPECTION_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Inspections'],
    summary: 'Chi tiết Inspections',
    description: 'Chi tiết Inspections',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailInspectionRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionService.getDetail(request);
  }

  @PermissionCode(LIST_INSPECTION_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Inspections'],
    summary: 'Danh sách Inspections',
    description: 'Danh sách Inspections',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(@Query() query: GetListInspectionRequestDto): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionService.getList(request);
  }

  @PermissionCode(CREATE_INSPECTION_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Inspections'],
    summary: 'Tạo Inspections mới',
    description: 'Tạo Inspections mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateInspectionRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionService.create(request);
  }

  @PermissionCode(UPDATE_INSPECTION_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Inspections'],
    summary: 'Cập nhật Inspections',
    description: 'Cập nhật Inspections',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(@Body() body: UpdateInspectionRequestDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionService.update(request);
  }

  @PermissionCode(ACTIVE_INSPECTION_PERMISSION.code)
  @Put('/active')
  @ApiOperation({
    tags: ['Inspections'],
    summary: 'Cập nhật Inspections Status Active',
    description: 'Cập nhật Inspections Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async active(
    @Body() body: UpdateStatusInspectionRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionService.active(request);
  }

  @PermissionCode(INACTIVE_INSPECTION_PERMISSION.code)
  @Put('/inactive')
  @ApiOperation({
    tags: ['Inspections'],
    summary: 'Cập nhật Inspections Status Inactive',
    description: 'Cập nhật Inspections Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async inactive(
    @Body() body: UpdateStatusInspectionRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.inspectionService.inactive(request);
  }

  @PermissionCode(DELETE_INSPECTION_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Inspections'],
    summary: 'Xóa Inspections',
    description: 'Xóa Inspections',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(@Param() param: DeleteInspectionRequestDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.inspectionService.delete(request);
  }
}
