import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { isDateString } from 'class-validator';
import { Repository } from 'typeorm';
import { StatusEnum } from '../../../common/enums/status.enum';

import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import {
  escapeCharForSearch,
  parseJSONValueField,
} from '../../../utils/common';
import { InspectionEntity } from '../entities/inspection.entity';
import { GetDetailInspectionRequestDto } from '../request/get-detail-inspection.request.dto';
import { GetListInspectionRequestDto } from '../request/get-list-inspection.request.dto';
import { UpdateInspectionRequestDto } from '../request/update-inspection.request.dto';
import { UpdateStatusInspectionRequestDto } from '../request/update-status-inspection.request.dto';
import { CreateInspectionRequestDto } from './../request/create-inspection.request.dto';

@Injectable()
export class InspectionRepository extends BaseAbstractRepository<InspectionEntity> {
  constructor(
    @InjectRepository(InspectionEntity)
    private readonly inspectionRepository: Repository<InspectionEntity>,
  ) {
    super(inspectionRepository);
  }

  createEntity(request: CreateInspectionRequestDto): InspectionEntity {
    const inspectionEntity = new InspectionEntity();
    inspectionEntity.code = request.code;
    inspectionEntity.name = request.name;
    inspectionEntity.inspectionTypeId = request.inspectionTypeId;
    inspectionEntity.managementScope = request?.managementScope;
    inspectionEntity.description = request?.description;
    inspectionEntity.status = StatusEnum.ACTIVE;
    inspectionEntity.createdBy = request.userId;
    inspectionEntity.updatedBy = request.userId;

    return inspectionEntity;
  }

  updateEntity(
    request: UpdateInspectionRequestDto,
    entity: InspectionEntity,
  ): InspectionEntity {
    entity.name = request.name;
    entity.inspectionTypeId = request.inspectionTypeId;
    entity.managementScope = request?.managementScope;
    entity.description = request?.description;
    entity.updatedBy = request.userId;

    return entity;
  }

  async getList(request: GetListInspectionRequestDto): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.inspectionRepository
      .createQueryBuilder('inspection')
      .select([
        'inspection.id AS id',
        'inspection.code AS "code"',
        'inspection.name AS "name"',
        'inspection.inspection_type_id AS "inspectionTypeId"',
        'inspection.description AS "description"',
        'inspection.management_scope AS "managementScope"',
        'inspection.status AS "status"',
        'inspection.created_by AS "createdBy"',
        'inspection.created_at AS "createdAt"',
        'inspection.updated_by AS "updatedBy"',
        'inspection.updated_at AS "updatedAt"',
      ])
      .leftJoin(
        'inspection_types',
        'it',
        'inspection.inspection_type_id = it.id',
      )
      .leftJoin('inspection_groups', 'ig', 'it.inspection_group_id = ig.id')
      .leftJoin('qc_request_types', 'qrt', 'ig.qc_request_type_id = qrt.id')
      .addSelect([
        `CASE WHEN COUNT(it.id) = 0 THEN '{}' ELSE
          (SELECT it.id as id, it.code as code,it.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "inspectionType"`,
        `CASE WHEN COUNT(ig.id) = 0 THEN '{}' ELSE
            (SELECT ig.id as id,ig.code as code,ig.name as name,ig.check_type as checkType,ig.oqc_device_type as oqcDeviceType FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "inspectionGroup"`,
        `CASE WHEN COUNT(qrt.id) = 0 THEN '{}' ELSE
          (SELECT qrt.id as id,qrt.code as code, qrt.name as name, qrt.qc_type as qcType FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "qcRequestType"`,
      ]);

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("inspection"."code") like lower(:code) escape '\\' OR
              lower("inspection"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('inspection.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'code':
            query.andWhere(
              `lower("inspection"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'name':
            query.andWhere(
              `lower("inspection"."name") like lower(:name) escape '\\'`,
              {
                name: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'description':
            query.andWhere(
              `lower("inspection"."description") like lower(:description) escape '\\'`,
              {
                description: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'managementScope':
            query.andWhere(
              `lower("inspection"."management_scope") like lower(:managementScope) escape '\\'`,
              {
                managementScope: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'status':
            query.andWhere('"inspection"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'qcType':
            query.andWhere('qrt.qc_type = :qcType', {
              qcType: Number(value),
            });
            break;
          case 'qcTypes':
            query.andWhere('qrt.qc_type IN (:...qcTypes)', {
              qcTypes: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'inspectionTypeId':
            query.andWhere(
              '"inspection"."inspection_type_id" = :inspectionTypeId',
              {
                inspectionTypeId: Number(value),
              },
            );
            break;
          case 'inspectionTypeIds':
            query.andWhere(
              '"inspection"."inspection_type_id" IN (:...inspectionTypeIds)',
              {
                inspectionTypeIds: value.split(',').map((id) => Number(id)),
              },
            );
            break;
          case 'inspectionGroupId':
            query.andWhere('ig.id = :inspectionGroupId', {
              inspectionGroupId: Number(value),
            });
            break;
          case 'inspectionGroupIds':
            query.andWhere('ig.id IN (:...inspectionGroupIds)', {
              inspectionGroupIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'qcRequestTypeId':
            query.andWhere('"qrt"."id" = :qcRequestTypeId', {
              qcRequestTypeId: Number(value),
            });
            break;
          case 'qcRequestTypeIds':
            query.andWhere('"qrt"."id" IN (:...qcRequestTypeIds)', {
              qcRequestTypeIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'createdById':
            query.andWhere('"inspection"."created_by" = :createdById', {
              createdById: Number(value),
            });
            break;
          case 'createdByIds':
            query.andWhere('"inspection"."created_by" IN (:...createdByIds)', {
              createdByIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'createdAt':
            const createFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const createTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"inspection"."created_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: createFrom,
                dateTo: createTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"inspection"."updated_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: updateFrom,
                dateTo: upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'name':
            query.addOrderBy('"inspection"."name"', order);
            break;
          case 'code':
            query.addOrderBy('"inspection"."code"', order);
            break;
          case 'status':
            query.addOrderBy('"inspection"."status"', order);
            break;
          case 'inspectionType':
            query.addOrderBy('"it"."id"', order);
            break;
          case 'inspectionGroup':
            query.addOrderBy('"ig"."id"', order);
            break;
          case 'qcRequestType':
            query.addOrderBy('"qrt"."id"', order);
            break;
          case 'description':
            query.addOrderBy('"inspection"."description"', order);
            break;
          case 'managementScope':
            query.addOrderBy('"inspection"."management_scope"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"inspection"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"inspection"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"inspection"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('inspection.id', 'DESC');
    }

    query
      .groupBy('inspection.id')
      .addGroupBy('inspection.code')
      .addGroupBy('inspection.name')
      .addGroupBy('inspection.inspection_type_id')
      .addGroupBy('inspection.management_scope')
      .addGroupBy('inspection.description')
      .addGroupBy('inspection.status')
      .addGroupBy('inspection.created_by')
      .addGroupBy('inspection.created_at')
      .addGroupBy('inspection.updated_by')
      .addGroupBy('inspection.updated_at')
      .addGroupBy('it.id')
      .addGroupBy('it.code')
      .addGroupBy('it.name')
      .addGroupBy('ig.id')
      .addGroupBy('ig.code')
      .addGroupBy('ig.name')
      .addGroupBy('ig.check_type')
      .addGroupBy('ig.oqc_device_type')
      .addGroupBy('qrt.id')
      .addGroupBy('qrt.code')
      .addGroupBy('qrt.name')
      .addGroupBy('qrt.qc_type');

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const data = await query.getRawMany();
    const count = await query.getCount();

    return {
      data: data?.map((item) => {
        return {
          ...item,
          id: Number(item.id),
          inspectionTypeId: Number(item.inspectionTypeId),
          inspectionType: parseJSONValueField(item.inspectionType),
          inspectionGroup: parseJSONValueField(item.inspectionGroup),
          qcRequestType: parseJSONValueField(item.qcRequestType),
        };
      }),
      count: count,
    };
  }

  async getDetail(
    request: GetDetailInspectionRequestDto,
  ): Promise<InspectionEntity> {
    const { id } = request;

    const query = this.inspectionRepository
      .createQueryBuilder('inspection')
      .select([
        'inspection.id AS id',
        'inspection.code AS "code"',
        'inspection.name AS "name"',
        'inspection.inspection_type_id AS "inspectionTypeId"',
        'inspection.description AS "description"',
        'inspection.management_scope AS "managementScope"',
        'inspection.status AS "status"',
        'inspection.created_by AS "createdBy"',
        'inspection.created_at AS "createdAt"',
        'inspection.updated_by AS "updatedBy"',
        'inspection.updated_at AS "updatedAt"',
      ])
      .leftJoin(
        'inspection_types',
        'it',
        'inspection.inspection_type_id = it.id',
      )
      .leftJoin('inspection_groups', 'ig', 'it.inspection_group_id = ig.id')
      .leftJoin('qc_request_types', 'qrt', 'ig.qc_request_type_id = qrt.id')
      .addSelect([
        `CASE WHEN COUNT(it.id) = 0 THEN '{}' ELSE
          (SELECT it.id as id, it.code as code,it.name as name FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "inspectionType"`,
        `CASE WHEN COUNT(ig.id) = 0 THEN '{}' ELSE
            (SELECT ig.id as id,ig.code as code,ig.name as name,ig.check_type as checkType,ig.oqc_device_type as oqcDeviceType FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "inspectionGroup"`,
        `CASE WHEN COUNT(qrt.id) = 0 THEN '{}' ELSE
          (SELECT qrt.id as id,qrt.code as code, qrt.name as name, qrt.qc_type as qcType FOR JSON PATH, WITHOUT_ARRAY_WRAPPER ) END AS "qcRequestType"`,
      ])
      .andWhere('"inspection"."id" = :id', {
        id: id,
      })
      .groupBy('inspection.id')
      .addGroupBy('inspection.code')
      .addGroupBy('inspection.name')
      .addGroupBy('inspection.inspection_type_id')
      .addGroupBy('inspection.management_scope')
      .addGroupBy('inspection.description')
      .addGroupBy('inspection.status')
      .addGroupBy('inspection.created_by')
      .addGroupBy('inspection.created_at')
      .addGroupBy('inspection.updated_by')
      .addGroupBy('inspection.updated_at')
      .addGroupBy('it.id')
      .addGroupBy('it.code')
      .addGroupBy('it.name')
      .addGroupBy('ig.id')
      .addGroupBy('ig.code')
      .addGroupBy('ig.name')
      .addGroupBy('ig.check_type')
      .addGroupBy('ig.oqc_device_type')
      .addGroupBy('qrt.id')
      .addGroupBy('qrt.code')
      .addGroupBy('qrt.name')
      .addGroupBy('qrt.qc_type');

    const data = await query.getRawOne();

    return {
      ...data,
      id: Number(data.id),
      inspectionTypeId: Number(data.inspectionTypeId),
      inspectionType: parseJSONValueField(data.inspectionType),
      inspectionGroup: parseJSONValueField(data.inspectionGroup),
      qcRequestType: parseJSONValueField(data.qcRequestType),
    };
  }

  async active(request: UpdateStatusInspectionRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.inspectionRepository
      .createQueryBuilder()
      .update(InspectionEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(request: UpdateStatusInspectionRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.inspectionRepository
      .createQueryBuilder()
      .update(InspectionEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }
}
