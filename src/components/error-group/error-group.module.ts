import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnotherServiceModule } from '../another-service/another-service.module';
import { MasterDataReferenceModule } from '../master-data-reference/master-data-reference.module';
import { ErrorGroupController } from './controller/error-group.controller';
import { ErrorGroupEntity } from './entities/error-group.entity';
import { ErrorGroupRepository } from './repository/error-group.repository';
import { ErrorGroupService } from './service/error-group.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([ErrorGroupEntity]),
    AnotherServiceModule,
    MasterDataReferenceModule,
  ],
  providers: [ErrorGroupService, ErrorGroupRepository],
  exports: [ErrorGroupService],
  controllers: [ErrorGroupController],
})
export class ErrorGroupModule {}
