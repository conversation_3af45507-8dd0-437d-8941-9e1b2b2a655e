import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { isEmpty } from 'lodash';

import { PermissionCode } from '../../../core/decorator/get-code.decorator';
import {
  ACTIVE_ERROR_GROUP_PERMISSION,
  CREATE_ERROR_GROUP_PERMISSION,
  DELETE_ERROR_GROUP_PERMISSION,
  DETAIL_ERROR_GROUP_PERMISSION,
  INACTIVE_ERROR_GROUP_PERMISSION,
  LIST_ERROR_GROUP_PERMISSION,
  UPDATE_ERROR_GROUP_PERMISSION,
} from '../../../utils/permissions/error-group.permission';
import { CreateErrorGroupRequestDto } from '../request/create-error-group.request.dto';
import { DeleteErrorGroupRequestDto } from '../request/delete-error-group.request.dto';
import { GetDetailErrorGroupRequestDto } from '../request/get-detail-error-group.request.dto';
import { GetListErrorGroupRequestDto } from '../request/get-list-error-group.request.dto';
import { UpdateErrorGroupStatusRequestDto } from '../request/update-error-group-status.request.dto';
import { UpdateErrorGroupRequestDto } from '../request/update-error-group.request.dto';
import { ErrorGroupService } from '../service/error-group.service';

@Controller('error-groups')
export class ErrorGroupController {
  constructor(private readonly errorGroupService: ErrorGroupService) {}

  @PermissionCode(DETAIL_ERROR_GROUP_PERMISSION.code)
  @Get('/:id')
  @ApiOperation({
    tags: ['Error-groups'],
    summary: 'Chi tiết Error-groups',
    description: 'Chi tiết Error-groups',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async getDetail(
    @Param() param: GetDetailErrorGroupRequestDto,
  ): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorGroupService.getDetail(request);
  }

  @PermissionCode(LIST_ERROR_GROUP_PERMISSION.code)
  @Get('/list')
  @ApiOperation({
    tags: ['Error-groups'],
    summary: 'Danh sách Error-groups',
    description: 'Danh sách Error-groups',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public getList(@Query() query: GetListErrorGroupRequestDto): Promise<any> {
    const { request, responseError } = query;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.errorGroupService.getList(request);
  }

  @PermissionCode(CREATE_ERROR_GROUP_PERMISSION.code)
  @Post('/create')
  @ApiOperation({
    tags: ['Error-groups'],
    summary: 'Tạo Error-groups mới',
    description: 'Tạo Error-groups mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public create(@Body() payload: CreateErrorGroupRequestDto) {
    const { request, responseError } = payload;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.errorGroupService.create(request);
  }

  @PermissionCode(UPDATE_ERROR_GROUP_PERMISSION.code)
  @Put()
  @ApiOperation({
    tags: ['Error-groups'],
    summary: 'Cập nhật Error-groups',
    description: 'Cập nhật Error-groups',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async update(@Body() body: UpdateErrorGroupRequestDto): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorGroupService.update(request);
  }

  @PermissionCode(ACTIVE_ERROR_GROUP_PERMISSION.code)
  @Put('/active')
  @ApiOperation({
    tags: ['Error-groups'],
    summary: 'Cập nhật Error-groups Status Active',
    description: 'Cập nhật Error-groups Status Active',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async active(
    @Body() body: UpdateErrorGroupStatusRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorGroupService.active(request);
  }

  @PermissionCode(INACTIVE_ERROR_GROUP_PERMISSION.code)
  @Put('/inactive')
  @ApiOperation({
    tags: ['Error-groups'],
    summary: 'Cập nhật Error-groups Status Inactive',
    description: 'Cập nhật Error-groups Status Inactive',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public async inactive(
    @Body() body: UpdateErrorGroupStatusRequestDto,
  ): Promise<any> {
    const { request, responseError } = body;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return await this.errorGroupService.inactive(request);
  }

  @PermissionCode(DELETE_ERROR_GROUP_PERMISSION.code)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Error-groups'],
    summary: 'Xóa Error-groups',
    description: 'Xóa Error-groups',
  })
  @ApiResponse({
    status: 200,
    description: 'Thành công',
  })
  public delete(@Param() param: DeleteErrorGroupRequestDto): Promise<any> {
    const { request, responseError } = param;

    if (responseError && !isEmpty(responseError)) {
      return responseError;
    }

    return this.errorGroupService.delete(request);
  }
}
