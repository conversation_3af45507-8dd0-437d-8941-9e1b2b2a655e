import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';
import { DataSource } from 'typeorm';

import { ResponseBuilder } from '@utils/response-builder';
import { PaginationResponse } from '../../../utils/dto/response/pagination.response';

import { CreateErrorGroupRequestDto } from '../request/create-error-group.request.dto';
import { DeleteErrorGroupRequestDto } from '../request/delete-error-group.request.dto';
import { GetDetailErrorGroupRequestDto } from '../request/get-detail-error-group.request.dto';
import { GetListErrorGroupRequestDto } from '../request/get-list-error-group.request.dto';
import { UpdateErrorGroupRequestDto } from '../request/update-error-group.request.dto';

import { ResponseCodeEnum } from '@constant/response-code.enum';
import { plainToInstance } from 'class-transformer';
import { ValidateMasterRequestDto } from '../../../common/dtos/request/validate-master.request.dto';
import { BaseService } from '../../../common/service/base.service';
import { UserService } from '../../another-service/services/user-service';
import { MasterDataReferenceService } from '../../master-data-reference/service/master-data-reference.service';
import { ErrorGroupEntity } from '../entities/error-group.entity';
import { ErrorGroupRepository } from '../repository/error-group.repository';
import { UpdateErrorGroupStatusRequestDto } from '../request/update-error-group-status.request.dto';
import { GetDetailErrorGroupResponseDto } from '../response/get-detail-error-group.response.dto';
import { GetListErrorGroupResponseDto } from '../response/get-list-error-group.response.dto';

@Injectable()
export class ErrorGroupService extends BaseService {
  private readonly logger = new Logger(ErrorGroupService.name);

  constructor(
    private readonly errorGroupRepository: ErrorGroupRepository,

    @InjectDataSource()
    private readonly connection: DataSource,

    private readonly i18n: I18nService,

    protected readonly userService: UserService,

    private readonly masterDataReferenceService: MasterDataReferenceService,
  ) {
    super(userService);
  }

  async create(request: CreateErrorGroupRequestDto): Promise<any> {
    const existCode = await this.errorGroupRepository.findOneByCode(
      request.code,
    );

    if (!isEmpty(existCode)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    const errorGroupEntity = this.errorGroupRepository.createEntity(request);
    const errorGroup = await this.errorGroupRepository.create(errorGroupEntity);

    const dataMapUser = await this.mapUserInfoToResponse([errorGroup]);
    const response = plainToInstance(
      GetDetailErrorGroupResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getList(request: GetListErrorGroupRequestDto): Promise<any> {
    const { page } = request;
    const { data, count } = await this.errorGroupRepository.getList(request);

    const dataMapUser = await this.mapUserInfoToResponse(data);
    const response = plainToInstance(GetListErrorGroupResponseDto, dataMapUser);

    return new ResponseBuilder<PaginationResponse>({
      items: response,
      meta: { total: count, page: page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailErrorGroupRequestDto): Promise<any> {
    const errorGroup = await this.errorGroupRepository.getDetail(request);

    if (isEmpty(errorGroup)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    const dataMapUser = await this.mapUserInfoToResponse([errorGroup]);
    const response = plainToInstance(
      GetDetailErrorGroupResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async update(request: UpdateErrorGroupRequestDto): Promise<any> {
    const { id } = request;
    const errorGroup = await this.errorGroupRepository.findOneById(id);

    if (isEmpty(errorGroup)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate error-groups in used
    const usedList = await this.validateErrorGroupInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.ERROR_GROUP_IN_USED'))
        .build();
    }

    // Validate Code unique
    const existCode = await this.errorGroupRepository.findOneByCode(
      request.code,
    );

    if (!isEmpty(existCode) && existCode.id !== errorGroup.id) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.EXISTS_IN_DATA_BASE'))
        .build();
    }

    const dataUpdate = this.errorGroupRepository.updateEntity(
      request,
      errorGroup,
    );
    const data = await this.errorGroupRepository.update(dataUpdate);

    const dataMapUser = await this.mapUserInfoToResponse([data]);
    const response = plainToInstance(
      GetDetailErrorGroupResponseDto,
      dataMapUser,
    );

    return new ResponseBuilder(response ? response[0] : {})
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async delete(request: DeleteErrorGroupRequestDto): Promise<any> {
    const { id } = request;
    const errorGroup = await this.errorGroupRepository.findOneById(id);

    if (isEmpty(errorGroup)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    // Validate error-groups in used
    const usedList = await this.validateErrorGroupInUsed(id);
    if (usedList) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.ERROR_GROUP_IN_USED'))
        .build();
    }

    await this.errorGroupRepository.remove(id);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async active(request: UpdateErrorGroupStatusRequestDto): Promise<any> {
    const { ids } = request;

    const errorGroupExitsInDB = await this.errorGroupRepository.findAllByIds(
      ids,
    );

    if (
      isEmpty(errorGroupExitsInDB) ||
      errorGroupExitsInDB?.length !== ids?.length
    ) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.errorGroupRepository.active(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async inactive(request: UpdateErrorGroupStatusRequestDto): Promise<any> {
    const { ids } = request;

    const errorGroupExitsInDB = await this.errorGroupRepository.findAllByIds(
      ids,
    );

    if (
      isEmpty(errorGroupExitsInDB) ||
      errorGroupExitsInDB?.length !== ids?.length
    ) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(await this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.errorGroupRepository.inactive(request);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(await this.i18n.translate('success.SUCCESS'))
      .build();
  }

  async validateErrorGroupInUsed(id: number): Promise<boolean> {
    // Validate used in error-types
    const errorTypeList =
      await this.masterDataReferenceService.findAllErrorTypeByErrorGroupIds([
        id,
      ]);
    if (!isEmpty(errorTypeList)) {
      return true;
    }

    // Validate used in another service
    const isUse =
      await this.masterDataReferenceService.validateDataMasterUseAnotherService(
        new ValidateMasterRequestDto('error_group_id', id),
      );
    if (isUse) {
      return true;
    }

    return false;
  }

  async findOneById(id: number): Promise<ErrorGroupEntity> {
    return await this.errorGroupRepository.findOneById(id);
  }

  async findAllByIds(ids: number[]): Promise<ErrorGroupEntity[]> {
    return await this.errorGroupRepository.findAllByIds(ids);
  }
}
