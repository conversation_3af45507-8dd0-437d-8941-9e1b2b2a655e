import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { isDateString } from 'class-validator';
import { StatusEnum } from '../../../common/enums/status.enum';
import { GET_ALL_ENUM } from '../../../constant/common';
import { BaseAbstractRepository } from '../../../core/repository/base.abstract.repository';
import { escapeCharForSearch } from '../../../utils/common';
import { ErrorGroupEntity } from '../entities/error-group.entity';
import { CreateErrorGroupRequestDto } from '../request/create-error-group.request.dto';
import { GetDetailErrorGroupRequestDto } from '../request/get-detail-error-group.request.dto';
import { GetListErrorGroupRequestDto } from '../request/get-list-error-group.request.dto';
import { UpdateErrorGroupStatusRequestDto } from '../request/update-error-group-status.request.dto';
import { UpdateErrorGroupRequestDto } from '../request/update-error-group.request.dto';

@Injectable()
export class ErrorGroupRepository extends BaseAbstractRepository<ErrorGroupEntity> {
  constructor(
    @InjectRepository(ErrorGroupEntity)
    private readonly errorGroupRepository: Repository<ErrorGroupEntity>,
  ) {
    super(errorGroupRepository);
  }

  createEntity(request: CreateErrorGroupRequestDto): ErrorGroupEntity {
    const errorGroupEntity = new ErrorGroupEntity();
    errorGroupEntity.code = request.code;
    errorGroupEntity.name = request.name;
    errorGroupEntity.description = request.description;
    errorGroupEntity.status = StatusEnum.ACTIVE;
    errorGroupEntity.createdBy = request.userId;
    errorGroupEntity.updatedBy = request.userId;
    return errorGroupEntity;
  }

  updateEntity(
    request: UpdateErrorGroupRequestDto,
    entity: ErrorGroupEntity,
  ): ErrorGroupEntity {
    entity.name = request.name;
    entity.description = request.description;
    entity.updatedBy = request.userId;
    return entity;
  }

  async getList(request: GetListErrorGroupRequestDto): Promise<any> {
    const { keyword, skip, take, sort, filter, queryIds, isGetAll } = request;

    let query = this.errorGroupRepository.createQueryBuilder('errorgroup');

    if (keyword) {
      const keywordSearch = `%${escapeCharForSearch(keyword)}%`;
      query.where(
        `(
              lower("errorgroup"."code") like lower(:code) escape '\\' OR
              lower("errorgroup"."name") like lower(:name) escape '\\'
          )`,
        {
          code: keywordSearch,
          name: keywordSearch,
        },
      );
    }

    if (queryIds) {
      const ids = queryIds.split(',').map((id) => Number(id));
      query.andWhere('errorgroup.id IN (:...ids)', { ids });
    }

    if (filter) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item?.column) {
          case 'code':
            query.andWhere(
              `lower("errorgroup"."code") like lower(:code) escape '\\'`,
              {
                code: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'name':
            query.andWhere(
              `lower("errorgroup"."name") like lower(:name) escape '\\'`,
              {
                name: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'description':
            query.andWhere(
              `lower("errorgroup"."description") like lower(:description) escape '\\'`,
              {
                description: `%${escapeCharForSearch(value)}%`,
              },
            );
            break;
          case 'status':
            query.andWhere('"errorgroup"."status" = :status', {
              status: Number(value),
            });
            break;
          case 'createdById':
            query.andWhere('"errorgroup"."created_by" = :createdById', {
              createdById: Number(value),
            });
            break;
          case 'createdByIds':
            query.andWhere('"errorgroup"."created_by" IN (:...createdByIds)', {
              createdByIds: value.split(',').map((id) => Number(id)),
            });
            break;
          case 'createdAt':
            const createFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const createTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"errorgroup"."created_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: createFrom,
                dateTo: createTo,
              },
            );
            break;
          case 'updatedAt':
            const updateFrom = isDateString(item.text.split('|')[0])
              ? item.text.split('|')[0]
              : new Date();

            const upateTo = isDateString(item.text.split('|')[1])
              ? item.text.split('|')[1]
              : new Date();

            query.andWhere(
              `"errorgroup"."updated_at" BETWEEN :dateFrom AND :dateTo`,
              {
                dateFrom: updateFrom,
                dateTo: upateTo,
              },
            );
            break;
          default:
            break;
        }
      });
    }

    if (sort) {
      sort.forEach((item) => {
        const order = item.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
        switch (item.column) {
          case 'name':
            query.addOrderBy('"errorgroup"."name"', order);
            break;
          case 'code':
            query.addOrderBy('"errorgroup"."code"', order);
            break;
          case 'status':
            query.addOrderBy('"errorgroup"."status"', order);
            break;
          case 'description':
            query.addOrderBy('"errorgroup"."description"', order);
            break;
          case 'createdAt':
            query.addOrderBy('"errorgroup"."created_at"', order);
            break;
          case 'createdBy':
            query.addOrderBy('"errorgroup"."created_by"', order);
            break;
          case 'updatedAt':
            query.addOrderBy('"errorgroup"."updated_at"', order);
            break;
          default:
            break;
        }
      });
    } else {
      query = query.orderBy('"errorgroup"."id"', 'DESC');
    }

    if (isGetAll !== GET_ALL_ENUM.YES) {
      query.offset(skip).limit(take);
    }

    const [data, count] = await query.getManyAndCount();

    return { data, count };
  }

  async getDetail(
    request: GetDetailErrorGroupRequestDto,
  ): Promise<ErrorGroupEntity> {
    const { id } = request;

    const data = await this.errorGroupRepository.findOne({
      where: { id: id },
    });

    return data;
  }

  async active(request: UpdateErrorGroupStatusRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.errorGroupRepository
      .createQueryBuilder()
      .update(ErrorGroupEntity)
      .set({ status: StatusEnum.ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }

  async inactive(request: UpdateErrorGroupStatusRequestDto): Promise<any> {
    const { ids } = request;
    const data = await this.errorGroupRepository
      .createQueryBuilder()
      .update(ErrorGroupEntity)
      .set({ status: StatusEnum.IN_ACTIVE, updatedBy: request.userId })
      .where('id IN (:...ids)', { ids })
      .execute();

    return data;
  }
}
