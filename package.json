{"name": "qmsx-service", "version": "0.0.1", "description": "", "author": "VTI.SnP", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build  &&  npm run copy-assets", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug 0.0.0.0:9229 --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "correct": "", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "npx ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js -d src/database/database.config.ts", "migrate:generate": "npm run typeorm migration:generate -- -n", "migrate:create": "cd src/database/migrations && typeorm migration:create", "migrate:run": "./node_modules/.bin/ts-node ./node_modules/.bin/typeorm migration:run -d src/database/database.config.ts", "migrate:revert": "./node_modules/.bin/ts-node ./node_modules/.bin/typeorm migration:revert -d src/database/database.config.ts", "package": "npm run build && npx pkg . -t alpine --output package/app_pkg", "copy-assets": "cp -rv src/i18n dist", "g:module": "hygen new module"}, "dependencies": {"@fastify/cors": "8.0.0", "@fastify/multipart": "7.6.0", "@fastify/static": "6.10.2", "@fastify/swagger": "8.5.1", "@handfish/hygen": "^6.1.6", "@nestcloud/boot": "0.7.17", "@nestcloud/common": "0.7.17", "@nestcloud/consul": "0.7.17", "@nestcloud/etcd": "0.7.17", "@nestcloud/service": "0.7.17", "@nestjs/axios": "2.0.0", "@nestjs/bull": "0.6.3", "@nestjs/common": "9.4.2", "@nestjs/config": "2.3.2", "@nestjs/core": "9.4.2", "@nestjs/event-emitter": "^1.3.1", "@nestjs/microservices": "9.4.2", "@nestjs/mongoose": "9.2.2", "@nestjs/platform-fastify": "9.4.2", "@nestjs/schedule": "2.2.2", "@nestjs/swagger": "6.3.0", "@nestjs/typeorm": "^10.0.2", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.8", "big.js": "6.2.0", "bson": "4.6.4", "bull": "3.29.3", "class-transformer": "0.5.1", "class-validator": "0.13.2", "consul": "0.34.1", "exceljs": "4.3.0", "husky": "^8.0.1", "moment": "2.29.3", "moment-timezone": "^0.5.38", "mongoose": "6.0.13", "mssql": "^9.3.2", "nats": "2.15.1", "nest-winston": "^1.9.4", "nestjs-i18n": "9.1.3", "typeorm": "^0.3.20", "typeorm-naming-strategies": "^4.1.0", "uuid": "8.3.2", "winston": "^3.13.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@nestjs/cli": "^9.4.2", "@nestjs/schematics": "^9.2.0", "@nestjs/testing": "^9.4.2", "@types/express": "^4.17.13", "@types/jest": "27.5.0", "@types/lodash": "^4.14.182", "@types/node": "^16.18.34", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-unused-imports": "^3.0.0", "jest": "28.0.3", "lint-staged": "^13.0.3", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.1", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.0.0", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "bin": "dist/main.js", "pkg": {"scripts": ["dist/**/*.js", "src/i18n/**/*.{js,json}"], "assets": ["logs/**/*", "config.yaml", "dist/i18n/**/*.json", "dist/static/**/*", "import-template/**/*", "node_modules/remote-content/**/*", "node_modules/vm2/**/*", "node_modules/axios/**/*", "node_modules/bull/**/*"]}}